import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module.js';
import * as dotenv from 'dotenv';
// import nacl from 'tweetnacl';

dotenv.config();

async function bootstrap() {
  const { createAndStartBootstrapNodes } = await import(
    '../lib/libp2p.bootstrap.bundle.js'
  ); // 路径视实际存放而定
  await createAndStartBootstrapNodes();
  await new Promise((resolve) => setTimeout(resolve, 700));

  // 启动 Nest 应用
  const app = await NestFactory.create(AppModule);

  const IS_GATEWAY = Number(process.env.IS_GATEWAY) == 1 ? true : false;
  const LIBP2P_PORT = process.env.LIBP2P_PORT
    ? Number(process.env.LIBP2P_PORT)
    : 4012;
  const NODE_PORT = process.env.NODE_PORT
    ? Number(process.env.NODE_PORT)
    : 15052;

  const API_PORT_P2P = process.env.API_PORT_P2P
    ? Number(process.env.API_PORT_P2P)
    : 8718;
  const BOOTSTRAP_ADDRS = process.env.BOOTSTRAP_ADDRS;
  const bootstrapList = BOOTSTRAP_ADDRS
    ? BOOTSTRAP_ADDRS.split(',')
        .map((addr) => addr.trim())
        .filter(Boolean)
    : [];
  console.log(`bootstrap: ${bootstrapList}`);
  const expressPort = LIBP2P_PORT;
  const nodePort = NODE_PORT;
  const tunnelPort = API_PORT_P2P;
  console.log(
    `expressPort: ${expressPort}, nodePort: ${nodePort}, tunnelPort: ${tunnelPort}`,
  );

  console.log(
    `expressPort: ${expressPort}, nodePort: ${nodePort}, tunnelPort: ${tunnelPort}`,
  );

  const tunnelAPI = `http://localhost:${tunnelPort}/libp2p/message`;
  // 处理 keySeed
  const nacl = await import('tweetnacl');
  const KEY_SEED = process.env.KEY_SEED
    ? Uint8Array.from(JSON.parse(process.env.KEY_SEED))
    : nacl.randomBytes(32);

  const keyPair = nacl.sign.keyPair.fromSeed(KEY_SEED);

  // 组装 options 对象
  const options = {
    expressPort,
    nodePort,
    keyPair,
    tunnelAPI,
    isGateway: IS_GATEWAY,
    bootstrapList: bootstrapList,
  };

  // 动态 import libp2p.bundle.js
  const { startLibP2PServer } = await import('../lib/libp2p.bundle.js'); // 路径视实际存放而定

  // 启动 libp2p
  startLibP2PServer(options);

  // 启动 Nest
  await app.listen(3030);
  console.log('NestJS is running on http://localhost:3030');
}
bootstrap();
