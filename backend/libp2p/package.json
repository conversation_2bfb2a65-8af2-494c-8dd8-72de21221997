{"name": "test-libp2p-bundle", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json"}, "dependencies": {"@chainsafe/libp2p-gossipsub": "^14.1.1", "@chainsafe/libp2p-noise": "^16.1.4", "@chainsafe/libp2p-yamux": "^7.0.4", "@libp2p/autonat": "^2.0.37", "@libp2p/bootstrap": "^11.0.43", "@libp2p/crypto": "^5.1.7", "@libp2p/identify": "^3.0.37", "@libp2p/interface": "^2.10.5", "@libp2p/interface-pubsub": "^4.0.1", "@libp2p/kad-dht": "^15.1.7", "@libp2p/peer-id": "^5.1.8", "@libp2p/peer-id-factory": "^4.2.4", "@libp2p/ping": "^2.0.36", "@libp2p/tcp": "^10.1.17", "@multiformats/multiaddr": "^12.5.1", "@nestjs/common": "^11.0.1", "@nestjs/core": "^11.0.1", "@nestjs/platform-express": "^11.0.1", "axios": "^1.10.0", "body-parser": "^2.2.0", "bs58": "^6.0.0", "dotenv": "^16.6.0", "express": "^5.1.0", "libp2p": "^2.8.12", "reflect-metadata": "^0.2.2", "rxjs": "^7.8.1", "tweetnacl": "^1.0.3"}, "devDependencies": {"@eslint/eslintrc": "^3.2.0", "@eslint/js": "^9.18.0", "@nestjs/cli": "^11.0.0", "@nestjs/schematics": "^11.0.0", "@nestjs/testing": "^11.0.1", "@swc/cli": "^0.6.0", "@swc/core": "^1.10.7", "@types/body-parser": "^1.19.6", "@types/express": "^5.0.0", "@types/jest": "^29.5.14", "@types/node": "^22.10.7", "@types/supertest": "^6.0.2", "esbuild": "^0.25.5", "eslint": "^9.18.0", "eslint-config-prettier": "^10.0.1", "eslint-plugin-prettier": "^5.2.2", "globals": "^16.0.0", "jest": "^29.7.0", "prettier": "^3.4.2", "source-map-support": "^0.5.21", "supertest": "^7.0.0", "ts-jest": "^29.2.5", "ts-loader": "^9.5.2", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "tsx": "^4.20.3", "typescript": "^5.7.3", "typescript-eslint": "^8.20.0", "vitest": "^3.2.4"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}