#!/usr/bin/env node

/* eslint-disable @typescript-eslint/no-var-requires */
const http = require('http');
const https = require('https');
const fs = require('fs');

async function downloadFile(fileUrl, outputLocationPath) {
  return new Promise((resolve, reject) => {
    const file = fs.createWriteStream(outputLocationPath);
    (fileUrl.startsWith('https') ? https : http).get(
      fileUrl,
      {},
      function (response) {
        if (response.statusCode === 302 || response.statusCode === 301) {
          downloadFile(response.headers.location, outputLocationPath)
            .then(resolve)
            .catch(reject);
          return;
        }

        response.pipe(file);

        // after download completed close file stream
        file.on('finish', () => {
          file.close();
          resolve();
        });
        file.on('error', err => {
          file.close();
          console.error(err);
          reject(err);
        });
      },
    );
  });
}

if (process.argv.length !== 4) {
  console.error('Usage: ./download <url> <output-path>');
}

const url = process.argv[2];
const output = process.argv[3];
downloadFile(url, output).then(
  () => {
    process.exit(0);
  },
  e => {
    console.error(`Fail to download file from ${url}, error: ${e}`);
    process.exit(1);
  },
);
