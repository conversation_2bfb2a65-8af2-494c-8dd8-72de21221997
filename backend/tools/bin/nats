#!/usr/bin/env bash

DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" >/dev/null 2>&1 && pwd)"
CLI_PATH="${DIR}/cli_nats_${NATS_VERSION}"

# download cli if not found
if [ ! -e "${CLI_PATH}" ]; then
  echo "nats cli not found, downloading..."
  rm -rf "$DIR/.download"
  mkdir -p "$DIR/.download"
  CLI_ZIP="$DIR/.download/cli_nats.zip"
  $DIR/download "https://github.com/nats-io/natscli/releases/download/v${NATS_VERSION}/nats-${NATS_VERSION}-$($DIR/osarch --platform)-$($DIR/osarch --arch).zip" "$CLI_ZIP"
  unzip "$CLI_ZIP" -d "$DIR/.download"
  rm -f ${DIR}/cli_nats_*
  mv "$DIR/.download/nats-${NATS_VERSION}-$($DIR/osarch --platform)-$($DIR/osarch --arch)/nats" "$CLI_PATH"
  rm -rf "$DIR/.download"
fi

$CLI_PATH "$@"
