{"root": true, "ignorePatterns": ["**/*"], "plugins": ["@nx"], "overrides": [{"files": ["*.ts", "*.tsx", "*.js", "*.jsx"], "rules": {"@nx/enforce-module-boundaries": ["error", {"enforceBuildableLibDependency": true, "allow": [], "depConstraints": [{"sourceTag": "*", "onlyDependOnLibsWithTags": ["*"]}]}]}}, {"files": ["*.ts", "*.tsx"], "extends": ["plugin:@nx/typescript"], "rules": {"no-useless-escape": "off", "@typescript-eslint/no-unused-vars": "off"}}, {"files": ["*.js", "*.jsx"], "extends": ["plugin:@nx/javascript"], "rules": {}}, {"files": ["*.spec.ts", "*.spec.tsx", "*.spec.js", "*.spec.jsx"], "env": {"jest": true}, "rules": {}}]}