FROM --platform=linux/amd64 node:22.16.0

# 更新包管理器
RUN apt-get update

# 暴露端口：8718为API服务端口，4010为libp2p服务端口
EXPOSE 8718 4010 15050 8716 15051 15001 15002 15003 15004 15005 15006 15007 15008 15009

# 设置工作目录并复制环境变量文件
WORKDIR /
COPY .env /

# 复制并安装主项目依赖
WORKDIR /
COPY package.json pnpm-lock.yaml ./
RUN npm install -g pnpm && pnpm install

# 复制构建后的主项目文件
WORKDIR /dist/packages/
COPY ./dist/packages/ .

# 设置工作目录为 /libp2p
WORKDIR /libp2p

# 复制整个 libp2p 项目（包括 package.json、pnpm-lock.yaml、src、配置文件等）
COPY ./libp2p ./


# 安装依赖
RUN pnpm install

# 构建项目（如果你的项目用 Nest.js 或其他构建系统）
RUN pnpm run build

# 创建启动脚本
RUN echo '#!/bin/bash' > /start.sh && \
    echo 'set -e' >> /start.sh && \
    echo '' >> /start.sh && \
    echo '# 启动libp2p服务（后台运行）' >> /start.sh && \
    echo 'cd /libp2p' >> /start.sh && \
    echo 'pnpm run start &' >> /start.sh && \
    echo 'LIBP2P_PID=$!' >> /start.sh && \
    echo 'echo "libp2p服务已启动，PID: $LIBP2P_PID"' >> /start.sh && \
    echo '' >> /start.sh && \
    echo '# 等待libp2p服务启动' >> /start.sh && \
    echo 'sleep 3' >> /start.sh && \
    echo '' >> /start.sh && \
    echo '# 启动API服务（前台运行）' >> /start.sh && \
    echo 'cd /dist/packages/apps/api-server' >> /start.sh && \
    echo 'echo "启动API服务..."' >> /start.sh && \
    echo 'node /dist/packages/apps/api-server/main.js' >> /start.sh && \
    chmod +x /start.sh

# 使用启动脚本启动两个服务
CMD ["/start.sh"]
