{"$schema": "./node_modules/nx/schemas/nx-schema.json", "targetDefaults": {"pre-build": {"dependsOn": ["^pre-build"], "inputs": ["production", "^production"]}, "build": {"dependsOn": ["pre-build", "^build"], "inputs": ["production", "^production"], "cache": true}, "lint": {"inputs": ["default", "{workspaceRoot}/.eslintrc.json", "{workspaceRoot}/.eslintignore"], "cache": true}, "container": {"dependsOn": ["^container", "build"]}, "@nx/jest:jest": {"cache": true, "inputs": ["default", "^production", "{workspaceRoot}/jest.preset.js"], "dependsOn": ["pre-build"], "options": {"passWithNoTests": true}, "configurations": {"ci": {"ci": true, "codeCoverage": true}}}}, "namedInputs": {"default": ["{projectRoot}/**/*", "sharedGlobals"], "production": ["default", "!{projectRoot}/**/?(*.)+(spec|test).[jt]s?(x)?(.snap)", "!{projectRoot}/tsconfig.spec.json", "!{projectRoot}/.eslintrc.json", "!{projectRoot}/jest.config.[jt]s", "!{projectRoot}/src/test-setup.[jt]s", "!{projectRoot}/test-setup.[jt]s", "!{projectRoot}/eslint.config.js"], "sharedGlobals": []}, "workspaceLayout": {"appsDir": "packages", "libsDir": "packages"}, "parallel": 10}