import * as nacl from "tweetnacl";
import { chacha20poly1305 } from "@noble/ciphers/chacha";

// 浏览器兼容的随机字节生成函数
function randomBytes(length: number): Uint8Array {
  if (typeof window !== "undefined" && window.crypto?.getRandomValues) {
    const array = new Uint8Array(length);
    window.crypto.getRandomValues(array);
    return array;
  } else {
    const crypto = require("crypto");
    return crypto.randomBytes(length);
  }
}

// base64 编码
function arrayToBase64(array: Uint8Array): string {
  if (typeof window !== "undefined" && window.btoa) {
    return window.btoa(String.fromCharCode(...array));
  } else {
    return Buffer.from(array).toString("base64");
  }
}

export interface EncryptedApiKeyResult {
  encryptedKey: string;
  nonce: string;
  tag: string;
  ephemeralPubKey: string;
}

export function encryptApiKey(apiKey: string, executorPublicKeyBase64: string) {
  const encoder = new TextEncoder();

  // ✅ Step 1: decode executor public key
  // executorPublicKey是从Gateway的REST API取得的，每次前端share key到时候做加密前调用一次以便之后做KEY轮换。
  const executorPublicKey = Buffer.from(executorPublicKeyBase64, "base64");

  // ✅ Step 2: generate new ephemeral keypair (random for every request)
  // ephemeralKeypair是本地随机生成的，一次性的，用完不要保存，用完后立刻在将该变量设为null从内存中抹去
  const ephemeralKeypair = nacl.box.keyPair();

  // ✅ Step 3: derive shared key (ECDH)
  // sharedSecret是用来加密API KEY得实际秘密
  const sharedSecret = nacl.scalarMult(
    ephemeralKeypair.secretKey,
    executorPublicKey
  );

  // ✅ Step 4: generate 12-byte nonce
  const nonce = randomBytes(12);

  // ✅ Step 5: encrypt using ChaCha20-Poly1305
  // 用sharedSecret和nonce来初始化chacha20poly1305
  const aead = chacha20poly1305(sharedSecret, nonce);

  // 得到加密后的结果
  const encrypted = aead.encrypt(encoder.encode(apiKey));
  // ChaCha20-Poly1305 输出: [ciphertext | tag]，tag为最后16字节
  const tagLength = 16;
  const ciphertext = encrypted.slice(0, encrypted.length - tagLength);
  const tag = encrypted.slice(encrypted.length - tagLength);

  // 将前面随机生成的ephemeralKeypair中的publicKey提交上去，千万不要提交privateKey，privateKey必须要保证变量设置为null被销毁。
  // 这些提交的都要在前端给用户看到，以保证我们没有偷偷提交明文
  return {
    encryptedKey: Buffer.from(ciphertext).toString("base64"), // 加密后的API KEY
    nonce: Buffer.from(nonce).toString("base64"), // 随机nonce
    tag: Buffer.from(tag).toString("base64"), // 加密生成的tag
    ephemeralPubKey: Buffer.from(ephemeralKeypair.publicKey).toString("base64"), // 前面随机生成的ephemeralKeypair中的publicKey
  };
}
/**
 * 获取加密信息的摘要（用于显示给用户）
 * @param result - 加密结果
 * @returns 加密信息摘要
 */
export function getEncryptionSummary(result: EncryptedApiKeyResult) {
  return {
    encryptedKeyLength: result.encryptedKey.length,
    nonceLength: result.nonce.length,
    tagLength: result.tag.length,
    ephemeralPubKeyLength: result.ephemeralPubKey.length,
    ephemeralPubKeyPreview: `${result.ephemeralPubKey.slice(
      0,
      8
    )}...${result.ephemeralPubKey.slice(-8)}`,
  };
}


// decryptApiKey.ts
// executorPrivateKeyBase64 是从GCP KEY Management Service中获取的，在Executor的生命周期内，会在需要解密KEY的时候持续存在，用完同样变量设置为null
// 例外：当第一次初始化时会获取很多个KEY，这里可以统一使用，只要保证初始化结束后内存中没有这个executorPrivateKeyBase64即可。
export function decryptApiKey(
  payload: {
    encryptedKey: string;
    nonce: string;
    tag: string;
    ephemeralPubKey: string;
  },
  executorPrivateKeyBase64: string
): string {
  const decoder = new TextDecoder();

  const ciphertext = Buffer.from(payload.encryptedKey, 'base64');
  const nonce = Buffer.from(payload.nonce, 'base64');
  const tag = Buffer.from(payload.tag, 'base64');
  const ephPubKey = Buffer.from(payload.ephemeralPubKey, 'base64');
  const executorPrivKey = Buffer.from(executorPrivateKeyBase64, 'base64');

  // ✅ Step 1: derive shared key (ECDH)
  // 从用户提交的ephemeralPubKey 和自己的executorPrivKey中还原用于加密的秘密
  const sharedSecret = nacl.scalarMult(executorPrivKey, ephPubKey);

  // ✅ Step 2: decrypt
  // 做解密，这里会自己验证nonce、tag、ciphertext是否match
  const aead = chacha20poly1305(sharedSecret);
  const plaintext = aead.decrypt(nonce, ciphertext, tag);

  return decoder.decode(plaintext); // 最终的API KEY
}