#!/usr/bin/env node

/**
 * 🔍 etcd 密钥状态检查脚本
 *
 * 功能：检查 etcd 中存储的 API Key 和公钥状态
 *
 * 检查内容：
 * 1. 加密的 API Key 列表及其状态 (waiting-to-verify/active/inactive)
 * 2. Executor 公钥列表及其状态
 * 3. 密钥的创建时间和基本信息
 *
 * 使用方法：
 * node scripts/check-etcd-keys.js
 *
 * 用途：调试和监控密钥管理系统的状态
 */

const { Etcd3 } = require('etcd3');

async function checkEtcdKeys() {
  const etcd = new Etcd3({
    hosts: 'localhost:2379',
  });

  try {
    console.log('🔍 Checking etcd for API keys...');
    
    // Check for encrypted API keys
    const apiKeys = await etcd.getAll().prefix('/api-keys/');
    console.log(`\n📦 Found ${Object.keys(apiKeys).length} encrypted API keys:`);
    
    for (const [path, value] of Object.entries(apiKeys)) {
      try {
        const data = JSON.parse(value);
        console.log(`\n🔑 Path: ${path}`);
        console.log(`   UUID: ${path.split('/').pop()}`);
        console.log(`   Key ID: ${data.keyId}`);
        console.log(`   Status: ${data.status}`);
        console.log(`   Created: ${data.createdAt}`);
        console.log(`   Encrypted Key Length: ${data.encryptedKey?.length || 0}`);
      } catch (error) {
        console.log(`\n❌ Invalid JSON in ${path}: ${error.message}`);
      }
    }

    // Check for public keys
    const publicKeys = await etcd.getAll().prefix('/keys/public/');
    console.log(`\n🔐 Found ${Object.keys(publicKeys).length} public keys:`);
    
    for (const [path, value] of Object.entries(publicKeys)) {
      try {
        const data = JSON.parse(value);
        console.log(`\n🗝️  Path: ${path}`);
        console.log(`   Key ID: ${data.keyId}`);
        console.log(`   Provider: ${data.provider}`);
        console.log(`   Region: ${data.region}`);
        console.log(`   Status: ${data.status}`);
        console.log(`   Public Key: ${data.publicKey?.substring(0, 20)}...`);
      } catch (error) {
        console.log(`\n❌ Invalid JSON in ${path}: ${error.message}`);
      }
    }

    console.log('\n✅ etcd check completed');

  } catch (error) {
    console.error('❌ Error checking etcd:', error.message);
  } finally {
    etcd.close();
  }
}

checkEtcdKeys();
