#!/usr/bin/env node

/**
 * 🔐 OpenAI API Key 加密存储脚本
 *
 * 功能：将 OpenAI API Key 加密后存储到 etcd 中
 *
 * 工作流程：
 * 1. 从 etcd 获取 Executor 的公钥
 * 2. 使用 X25519 + ChaCha20-Poly1305 加密 API Key
 * 3. 将加密后的 API Key 存储到 etcd
 * 4. 状态初始为 'waiting-to-verify'，Executor 解密成功后会自动改为 'active'
 *
 * 使用方法：
 * node scripts/add-openai-key.js <YOUR_OPENAI_API_KEY>
 *
 * 示例：
 * node scripts/add-openai-key.js sk-proj-xxxxx...
 */

const axios = require('axios');
const nacl = require('tweetnacl');
const { chacha20poly1305 } = require('@noble/ciphers/chacha');

// Configuration
const GATEWAY_URL = 'http://localhost:8718';
const PROVIDER = 'openai';
const REGION = 'asia';

// Your OpenAI API Key (pass as command line argument)
const API_KEY = process.argv[2];

if (!API_KEY) {
  console.error('❌ Usage: node add-openai-key.js <your-openai-api-key>');
  console.error('   Example: node add-openai-key.js sk-1234567890abcdefghijklmnopqrstuvwxyz1234567890');
  process.exit(1);
}

async function addEncryptedApiKey() {
  try {
    console.log('🔍 Step 1: Getting public keys...');
    
    // 1. Get public keys
    const publicKeysResponse = await axios.get(`${GATEWAY_URL}/public-keys`, {
      params: {
        provider: PROVIDER,
        region: REGION
      }
    });
    
    if (!publicKeysResponse.data.success || publicKeysResponse.data.data.keys.length === 0) {
      throw new Error('No public keys found for provider: ' + PROVIDER + ', region: ' + REGION);
    }
    
    const publicKeyData = publicKeysResponse.data.data.keys[0];
    console.log('✅ Found public key:', publicKeyData.keyId);
    console.log('   Public Key:', publicKeyData.publicKey);
    
    // 2. Encrypt API Key using X25519 + ChaCha20-Poly1305
    console.log('🔐 Step 2: Encrypting API key...');

    // Generate ephemeral key pair
    const ephemeralKeyPair = nacl.box.keyPair();

    // Decode executor's public key
    const executorPubKey = new Uint8Array(Buffer.from(publicKeyData.publicKey, 'base64'));

    // Derive shared secret using X25519 ECDH
    const sharedSecret = nacl.scalarMult(ephemeralKeyPair.secretKey, executorPubKey);

    // Generate random nonce (12 bytes for ChaCha20-Poly1305)
    const nonce = nacl.randomBytes(12);

    // Encrypt using ChaCha20-Poly1305
    const apiKeyBytes = new Uint8Array(Buffer.from(API_KEY, 'utf8'));
    const aead = chacha20poly1305(sharedSecret, nonce);
    const encrypted = aead.encrypt(apiKeyBytes);

    if (!encrypted) {
      throw new Error('Failed to encrypt API key');
    }

    // Split encrypted data into ciphertext and tag
    const ciphertext = encrypted.slice(0, -16); // All but last 16 bytes
    const tag = encrypted.slice(-16); // Last 16 bytes
    
    // 3. Prepare encrypted data
    const encryptedData = {
      provider: PROVIDER,
      region: REGION,
      keyId: publicKeyData.keyId,
      encryptedKey: Buffer.from(ciphertext).toString('base64'),
      nonce: Buffer.from(nonce).toString('base64'),
      tag: Buffer.from(tag).toString('base64'),
      ephemeralPubKey: Buffer.from(ephemeralKeyPair.publicKey).toString('base64')
    };
    
    console.log('📦 Encrypted data prepared:');
    console.log('   Key ID:', encryptedData.keyId);
    console.log('   Encrypted Key Length:', encryptedData.encryptedKey.length);
    console.log('   Nonce:', encryptedData.nonce);
    console.log('   Ephemeral Public Key:', encryptedData.ephemeralPubKey);
    
    // 4. Submit encrypted API key
    console.log('📤 Step 3: Submitting encrypted API key...');
    
    const response = await axios.post(`${GATEWAY_URL}/third-party/encrypted`, encryptedData, {
      headers: {
        'Content-Type': 'application/json',
        // Note: In production, you would need a valid JWT token
        // 'Authorization': 'Bearer your-jwt-token'
      }
    });
    
    console.log('✅ Success! API key added successfully:');
    console.log(JSON.stringify(response.data, null, 2));
    
  } catch (error) {
    console.error('❌ Error:', error.response?.data || error.message);
    
    if (error.response?.status === 401) {
      console.error('💡 Note: You may need to provide a valid JWT token for authentication');
    }
    
    process.exit(1);
  }
}

// Run the script
addEncryptedApiKey();
