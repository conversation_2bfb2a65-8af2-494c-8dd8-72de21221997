#!/usr/bin/env node

/**
 * 🔍 Executor 注册状态检查脚本
 * 
 * 功能：检查 etcd 中注册的 Executor 状态
 * 
 * 检查内容：
 * 1. 注册的 Executor 列表
 * 2. Executor 的状态和配置
 * 3. Executor 的心跳时间
 * 4. Gateway 服务发现相关信息
 * 
 * 使用方法：
 * node scripts/check-executors.js
 */

const { Etcd3 } = require('etcd3');

async function checkExecutors() {
  const etcd = new Etcd3({
    hosts: 'localhost:2379',
  });

  try {
    console.log('🔍 Checking etcd for registered Executors...\n');

    // 检查 Executor 注册
    console.log('📋 Checking Executor registrations...');
    const executors = await etcd.getAll().prefix('/executors/');
    
    if (Object.keys(executors).length === 0) {
      console.log('❌ No Executors found in etcd');
    } else {
      console.log(`📦 Found ${Object.keys(executors).length} registered Executors:\n`);
      
      for (const [path, value] of Object.entries(executors)) {
        try {
          const data = JSON.parse(value);
          const pathParts = path.split('/');
          const region = pathParts[2];
          const provider = pathParts[3];
          const executorId = pathParts[4];
          
          console.log(`🚀 Executor: ${executorId}`);
          console.log(`   Path: ${path}`);
          console.log(`   Region: ${region}`);
          console.log(`   Provider: ${provider}`);
          console.log(`   URL: ${data.url || 'N/A'}`);
          console.log(`   Status: ${data.status || 'N/A'}`);
          console.log(`   Last Heartbeat: ${data.lastHeartbeat || 'N/A'}`);
          console.log(`   Created: ${data.createdAt || 'N/A'}`);
          console.log('');
        } catch (error) {
          console.log(`❌ Invalid JSON in ${path}: ${error.message}`);
        }
      }
    }

    // 检查服务发现相关的键
    console.log('🔍 Checking service discovery keys...');
    const serviceKeys = await etcd.getAll().prefix('/services/');
    
    if (Object.keys(serviceKeys).length === 0) {
      console.log('❌ No service discovery keys found');
    } else {
      console.log(`📦 Found ${Object.keys(serviceKeys).length} service keys:\n`);
      
      for (const [path, value] of Object.entries(serviceKeys)) {
        console.log(`🔗 Service: ${path}`);
        console.log(`   Value: ${value}`);
        console.log('');
      }
    }

    // 检查其他可能的注册路径
    console.log('🔍 Checking other registration paths...');
    const otherPaths = [
      '/registry/',
      '/discovery/',
      '/nodes/',
      '/instances/'
    ];

    for (const prefix of otherPaths) {
      const keys = await etcd.getAll().prefix(prefix);
      if (Object.keys(keys).length > 0) {
        console.log(`📦 Found keys under ${prefix}:`);
        for (const [path, value] of Object.entries(keys)) {
          console.log(`   ${path}: ${value.substring(0, 100)}${value.length > 100 ? '...' : ''}`);
        }
        console.log('');
      }
    }

    console.log('✅ Executor check completed');

  } catch (error) {
    console.error('❌ Error checking executors:', error.message);
  } finally {
    etcd.close();
  }
}

checkExecutors();
