#!/usr/bin/env bash
set -euo pipefail
__prevEnv__="$(env)"

DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" >/dev/null 2>&1 && pwd)"
export NX_WORKSPACE_ROOT=${DIR}

PATH_add node_modules/.bin
PATH_add tools/bin
PATH_add packages/dev/database/bin

export HASURA_VERSION="2.35.1"
export NATS_VERSION="0.1.1"

export FIXTURE_DIR="${NX_WORKSPACE_ROOT}/tests/fixtures"

export WORKSPACE_NAME="saito"
export DEV_NETWORK_NAME="saito_network"
export HASURA_GRAPHQL_SERVER_PORT="28717"
export HASURA_GRAPHQL_ADMIN_SECRET="9AgJckEMHPRgrasj7Ey8jR"

# export NODE_DATABASE_URL="***************************************************/postgres"
export NODE_DATABASE_URL="postgres://postgres:postgres@127.0.0.1:7543/saito_db"

export ETH_RPC_URL="https://rpc.beta.testnet.l2.quarkchain.io:8545"
export ETHSTORAGE_RPC_URL="https://rpc.beta.testnet.l2.ethstorage.io:9596"

export NATS_URL="nats://localhost:4222"


export PRIVATE_KEY=""
export WALLET_ADDRESS="******************************************"
export FLAT_DIR_ADDRESS="******************************************"
export VERIFICATION_CONTRACT_ADDRESS="******************************************"
export CHAIN_ID=3335
export REQUESTER_ADDRESS="******************************************"

export JWT_SECRET="123123"
export REDIS_DB=0
export REDIS_HOST=localhost
export REDIS_PORT=6379

export API_PORT=8718

export EXECUTOR_SECRET_KEY='executor-secret-key-sight'

# libp2p
export LIBP2P_PORT=4012
export BOOTSTRAP_ADDRS="/ip4/127.0.0.1/tcp/15001/p2p/****************************************************,/ip4/*************/tcp/15001/p2p/****************************************************,/ip4/***********/tcp/15001/p2p/****************************************************,/ip4/127.0.0.1/tcp/15002/p2p/****************************************************,/ip4/*************/tcp/15002/p2p/****************************************************,/ip4/***********/tcp/15002/p2p/****************************************************,/ip4/127.0.0.1/tcp/15003/p2p/12D3KooWQYhTNQdmr3ArTeUHRYzFg94BKyTkoWBDWez9kSCVe2Xo,/ip4/*************/tcp/15003/p2p/12D3KooWQYhTNQdmr3ArTeUHRYzFg94BKyTkoWBDWez9kSCVe2Xo,/ip4/***********/tcp/15003/p2p/12D3KooWQYhTNQdmr3ArTeUHRYzFg94BKyTkoWBDWez9kSCVe2Xo,/ip4/127.0.0.1/tcp/15004/p2p/12D3KooWLJtG8fd2hkQzTn96MrLvThmnNQjTUFZwGEsLRz5EmSzc,/ip4/*************/tcp/15004/p2p/12D3KooWLJtG8fd2hkQzTn96MrLvThmnNQjTUFZwGEsLRz5EmSzc,/ip4/***********/tcp/15004/p2p/12D3KooWLJtG8fd2hkQzTn96MrLvThmnNQjTUFZwGEsLRz5EmSzc,/ip4/127.0.0.1/tcp/15005/p2p/12D3KooWSHj3RRbBjD15g6wekV8y3mm57Pobmps2g2WJm6F67Lay,/ip4/*************/tcp/15005/p2p/12D3KooWSHj3RRbBjD15g6wekV8y3mm57Pobmps2g2WJm6F67Lay,/ip4/***********/tcp/15005/p2p/12D3KooWSHj3RRbBjD15g6wekV8y3mm57Pobmps2g2WJm6F67Lay,/ip4/127.0.0.1/tcp/15006/p2p/12D3KooWDMCQbZZvLgHiHntG1KwcHoqHPAxL37KvhgibWqFtpqUY,/ip4/*************/tcp/15006/p2p/12D3KooWDMCQbZZvLgHiHntG1KwcHoqHPAxL37KvhgibWqFtpqUY,/ip4/***********/tcp/15006/p2p/12D3KooWDMCQbZZvLgHiHntG1KwcHoqHPAxL37KvhgibWqFtpqUY,/ip4/127.0.0.1/tcp/15007/p2p/12D3KooWLnZUpcaBwbz9uD1XsyyHnbXUrJRmxnsMiRnuCmvPix67,/ip4/*************/tcp/15007/p2p/12D3KooWLnZUpcaBwbz9uD1XsyyHnbXUrJRmxnsMiRnuCmvPix67,/ip4/***********/tcp/15007/p2p/12D3KooWLnZUpcaBwbz9uD1XsyyHnbXUrJRmxnsMiRnuCmvPix67,/ip4/127.0.0.1/tcp/15008/p2p/12D3KooWQ8vrERR8bnPByEjjtqV6hTWehaf8TmK7qR1cUsyrPpfZ,/ip4/*************/tcp/15008/p2p/12D3KooWQ8vrERR8bnPByEjjtqV6hTWehaf8TmK7qR1cUsyrPpfZ,/ip4/***********/tcp/15008/p2p/12D3KooWQ8vrERR8bnPByEjjtqV6hTWehaf8TmK7qR1cUsyrPpfZ,/ip4/127.0.0.1/tcp/15009/p2p/12D3KooWNRk8VBuTJTYyTbnJC7Nj2UN5jij4dJMo8wtSGT2hRzRP,/ip4/*************/tcp/15009/p2p/12D3KooWNRk8VBuTJTYyTbnJC7Nj2UN5jij4dJMo8wtSGT2hRzRP,/ip4/***********/tcp/15009/p2p/12D3KooWNRk8VBuTJTYyTbnJC7Nj2UN5jij4dJMo8wtSGT2hRzRP"
export COMMUNICATION_TYPE=libp2p
export NODE_PORT='15052'
export IS_GATEWAY=1

if [[ -f .envrc.override ]]; then
  source_env .envrc.override
fi

# export updated ENV of this file
node "${NX_WORKSPACE_ROOT}/tools/bin/get-env" "${__prevEnv__}" "$(env)" >"${NX_WORKSPACE_ROOT}/.env" &
