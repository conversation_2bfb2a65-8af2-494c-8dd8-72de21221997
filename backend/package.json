{"name": "@saito-miner/source", "version": "0.0.0", "license": "UNLICENSED", "scripts": {"postinstall": "patch-package", "check-kms-config": "tsx scripts/check-kms-config.ts", "generate-sight-keypair": "tsx scripts/generate-sight-keypair.ts", "decrypt-sight-privatekey": "tsx scripts/decrypt-sight-privatekey.ts", "encrypt-apikey-demo": "tsx scripts/encrypt-apikey-demo.ts"}, "private": true, "dependencies": {"@chainsafe/libp2p-gossipsub": "^14.1.1", "@chainsafe/libp2p-noise": "^16.1.4", "@chainsafe/libp2p-yamux": "^7.0.4", "@libp2p/bootstrap": "^11.0.43", "@libp2p/crypto": "^5.1.7", "@libp2p/identify": "^3.0.37", "@libp2p/interface": "^2.10.5", "@libp2p/interface-pubsub": "^4.0.1", "@libp2p/kad-dht": "^15.1.7", "@libp2p/peer-id": "^5.1.8", "@libp2p/peer-id-factory": "^4.2.4", "@libp2p/ping": "^2.0.36", "@libp2p/tcp": "^10.1.17", "@multiformats/multiaddr": "^12.5.1", "@nestjs/axios": "^4.0.0", "@nestjs/common": "^10.2.10", "@nestjs/config": "^4.0.1", "@nestjs/core": "^10.2.10", "@nestjs/event-emitter": "^3.0.1", "@nestjs/jwt": "^11.0.0", "@nestjs/microservices": "^10.2.10", "@nestjs/passport": "^11.0.5", "@nestjs/platform-express": "^10.2.10", "@nestjs/platform-socket.io": "^11.0.12", "@nestjs/schedule": "^5.0.1", "@nestjs/swagger": "7.1.16", "@nestjs/websockets": "^11.0.12", "@noble/ciphers": "^1.3.0", "@oclif/core": "3.12.0", "@oclif/plugin-help": "6.0.7", "@oclif/plugin-plugins": "4.1.8", "@t3-oss/env-core": "^0.7.1", "@temporalio/activity": "1.8.6", "@temporalio/client": "1.8.6", "@temporalio/worker": "1.8.6", "@temporalio/workflow": "1.8.6", "@types/crypto-js": "^4.2.2", "@types/passport-jwt": "^4.0.1", "@uniswap/sdk-core": "^4.0.9", "@uniswap/v3-core": "^1.0.1", "@uniswap/v3-periphery": "^1.4.4", "@uniswap/v3-sdk": "^3.10.0", "axios": "^1.10.0", "body-parser": "^2.2.0", "bs58": "^6.0.0", "class-transformer": "^0.5.1", "class-validator": "^0.14.0", "crypto": "^1.0.1", "crypto-js": "^4.2.0", "dedent": "^1.5.1", "dnum-cjs": "^2.9.0", "dotenv": "^16.5.0", "etcd3": "^1.1.2", "ethers": "^6.13.5", "ethstorage-sdk": "^2.0.0", "express": "^5.1.0", "fast-json-stable-stringify": "^2.1.0", "got-cjs": "^12.5.4", "ioredis": "^5.6.0", "jsonwebtoken": "^9.0.2", "libp2p": "^2.8.12", "lodash": "^4.17.21", "memoizee": "^0.4.15", "nanoid": "^3.3.4", "nats": "^2.18.0", "nestjs-pino": "^3.5.0", "nestjs-zod": "^3.0.0", "oclif": "^4.0.4", "openai": "^4.17.5", "openpipe": "^0.6.2", "p-queue": "^6.6.2", "p-retry": "^4.6.2", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "pino": "^8.16.2", "pino-http": "^8.5.1", "pino-pretty": "^10.2.3", "ramda": "^0.30.1", "reflect-metadata": "^0.1.13", "rxjs": "^7.8.1", "safe-json-stringify": "^1.2.0", "slonik": "^37.2.0", "slonik-interceptor-query-logging": "^1.4.7", "socket.io": "^4.8.1", "telegraf": "^4.15.0", "telegramify-markdown": "^1.1.0", "tslib": "^2.6.2", "tweetnacl": "^1.0.3", "typechat": "^0.0.10", "uuid": "^9.0.1", "viem": "^1.19.9", "zod": "^3.22.4", "zod-to-json-schema": "^3.22.0"}, "devDependencies": {"@milahu/patch-package": "^6.4.14", "@nestjs/schematics": "^10.0.3", "@nestjs/testing": "^10.2.10", "@nx-tools/container-metadata": "^5.1.0", "@nx-tools/nx-container": "^5.1.0", "@nx/devkit": "17.1.3", "@nx/eslint": "17.1.3", "@nx/eslint-plugin": "17.1.3", "@nx/jest": "17.1.3", "@nx/js": "17.1.3", "@nx/nest": "17.1.3", "@nx/node": "17.1.3", "@nx/nx-linux-x64-gnu": "^20.5.0", "@nx/webpack": "17.1.3", "@nx/workspace": "17.1.3", "@swc-node/register": "1.6.8", "@swc/core": "^1.3.99", "@swc/helpers": "0.5.3", "@telegraf/types": "^6.9.1", "@types/body-parser": "^1.19.6", "@types/express": "^5.0.3", "@types/ioredis": "^4.28.10", "@types/jest": "^30.0.0", "@types/jsonwebtoken": "^9.0.8", "@types/lodash": "^4.14.202", "@types/memoizee": "^0.4.11", "@types/node": "20.10.0", "@types/ramda": "^0.29.12", "@types/safe-json-stringify": "^1.1.5", "@types/supertest": "^2.0.16", "@types/uuid": "^9.0.7", "@typescript-eslint/eslint-plugin": "6.12.0", "@typescript-eslint/parser": "6.12.0", "esbuild": "^0.25.5", "eslint": "~8.54.0", "eslint-config-prettier": "9.0.0", "fs-extra": "^11.1.1", "jest": "^30.0.3", "jest-environment-node": "^29.7.0", "npm-check-updates": "^16.14.11", "nx": "^17.1.3", "nx-cloud": "16.5.2", "prettier": "^2.8.8", "prettier-plugin-organize-imports": "^3.2.4", "source-map-support": "^0.5.21", "supertest": "^6.3.3", "ts-jest": "^29.4.0", "ts-loader": "^9.5.1", "ts-node": "10.9.1", "tsconfig-paths": "4.2.0", "tsconfig-paths-webpack-plugin": "^4.1.0", "tsx": "^4.20.3", "typescript": "~5.8.3", "vitest": "^3.2.4"}, "prettier": {"singleQuote": true, "semi": true, "tabWidth": 2, "arrowParens": "avoid", "trailingComma": "all", "printWidth": 80, "plugins": ["prettier-plugin-organize-imports"]}}