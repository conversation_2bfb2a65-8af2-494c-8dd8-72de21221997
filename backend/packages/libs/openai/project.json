{"name": "openai", "$schema": "../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "packages/libs/openai/src", "projectType": "library", "targets": {"build": {"executor": "@nx/js:tsc", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/packages/libs/openai", "main": "packages/libs/openai/src/index.ts", "tsConfig": "packages/libs/openai/tsconfig.lib.json", "assets": ["packages/libs/openai/*.md"]}}, "lint": {"executor": "@nx/linter:eslint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["packages/libs/openai/**/*.ts"]}}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "packages/libs/openai/jest.config.ts", "passWithNoTests": true}, "configurations": {"ci": {"ci": true, "coverageReporters": ["text"]}}}}, "tags": []}