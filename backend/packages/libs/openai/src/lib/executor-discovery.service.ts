import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Etcd3 } from 'etcd3';

export interface ExecutorInstance {
  id: string;
  url: string;
  region: string;
  modelType: string;
  status: string;
  lastHeartbeat: number;
  maxConcurrentRequests: number;
  currentRequests: number;
  supportedModels: string[];
  // 向后兼容的字段
  latency?: number;
  load?: number;
  started_at?: number;
  last_heartbeat?: number;
}

@Injectable()
export class ExecutorDiscoveryService implements OnModuleInit {
  private readonly logger = new Logger(ExecutorDiscoveryService.name);
  private etcdClient!: Etcd3;
  private executorCache: Map<string, ExecutorInstance> = new Map();
  private lastCacheUpdate = 0;
  private readonly CACHE_TTL = 30000; // 30秒缓存

  constructor(private configService: ConfigService) {}

  async onModuleInit() {
    try {
      const etcdHost = this.configService.get<string>('ETCD_HOST', 'localhost');
      const etcdPort = this.configService.get<number>('ETCD_PORT', 2379);
      
      this.etcdClient = new Etcd3({
        hosts: [`${etcdHost}:${etcdPort}`],
      });

      // 测试连接
      await this.etcdClient.get('test').string();
      this.logger.log(`ExecutorDiscoveryService connected to etcd at ${etcdHost}:${etcdPort}`);
      
      // 初始化缓存
      await this.refreshExecutorCache();
    } catch (error) {
      this.logger.error('Failed to connect to etcd:', error);
      throw error;
    }
  }

  /**
   * 获取可用的OpenAI Executor实例
   */
  async getAvailableExecutors(): Promise<ExecutorInstance[]> {
    // 检查缓存是否需要刷新
    const now = Date.now();
    if (now - this.lastCacheUpdate > this.CACHE_TTL) {
      await this.refreshExecutorCache();
    }

    // 过滤健康的实例（最近30分钟内有心跳）
    const healthyExecutors = Array.from(this.executorCache.values()).filter(
      executor => {
        const heartbeat = executor.lastHeartbeat || executor.last_heartbeat || 0;
        return now - heartbeat < 1800000; // 30分钟
      }
    );

    // 按当前请求数和负载排序
    return healthyExecutors.sort((a, b) => {
      // 首先按当前请求数排序
      if (a.currentRequests !== b.currentRequests) {
        return a.currentRequests - b.currentRequests;
      }
      // 然后按负载排序（如果有的话）
      const aLoad = a.load || a.currentRequests;
      const bLoad = b.load || b.currentRequests;
      if (aLoad !== bLoad) {
        return aLoad - bLoad;
      }
      // 最后按延迟排序（如果有的话）
      const aLatency = a.latency || 0;
      const bLatency = b.latency || 0;
      return aLatency - bLatency;
    });
  }

  /**
   * 选择最佳的Executor实例
   */
  async selectBestExecutor(): Promise<ExecutorInstance | null> {
    const availableExecutors = await this.getAvailableExecutors();
    
    if (availableExecutors.length === 0) {
      this.logger.warn('No healthy executor instances found');
      return null;
    }

    // 返回负载最低、延迟最小的实例
    const bestExecutor = availableExecutors[0];
    this.logger.debug(`Selected executor ${bestExecutor.id} with load ${bestExecutor.load} and latency ${bestExecutor.latency}ms`);
    
    return bestExecutor;
  }

  /**
   * 刷新Executor缓存
   */
  private async refreshExecutorCache(): Promise<void> {
    try {
      this.logger.debug('Refreshing executor cache from etcd');
      
      // 获取所有OpenAI executor
      const prefix = '/executors/';
      const result = await this.etcdClient.getAll().prefix(prefix);
      
      const newCache = new Map<string, ExecutorInstance>();
      
      for (const [key, value] of Object.entries(result)) {
        try {
          // 只处理OpenAI executor
          if (!key.includes('/openai/')) {
            continue;
          }
          
          const executorData = JSON.parse(value as string);
          const pathParts = key.split('/');
          const executorId = pathParts[pathParts.length - 1];
          const region = pathParts[2]; // /executors/[region]/openai/[id]
          
          const executor: ExecutorInstance = {
            id: executorData.id || executorId,
            url: executorData.publicUrl || executorData.url,
            region: executorData.region || region,
            modelType: executorData.modelType || 'openai',
            status: executorData.status || 'active',
            lastHeartbeat: executorData.lastHeartbeat || executorData.last_heartbeat || 0,
            maxConcurrentRequests: executorData.maxConcurrentRequests || 10,
            currentRequests: executorData.currentRequests || 0,
            supportedModels: executorData.supportedModels || [],
            // 向后兼容字段
            latency: executorData.latency || 0,
            load: executorData.load || executorData.currentRequests || 0,
            started_at: executorData.started_at,
            last_heartbeat: executorData.lastHeartbeat || executorData.last_heartbeat
          };
          
          newCache.set(executorId, executor);
        } catch (parseError) {
          this.logger.warn(`Failed to parse executor data for key ${key}:`, parseError);
        }
      }
      
      this.executorCache = newCache;
      this.lastCacheUpdate = Date.now();
      
      this.logger.log(`Refreshed executor cache with ${newCache.size} instances`);
    } catch (error) {
      this.logger.error('Failed to refresh executor cache:', error);
      throw error;
    }
  }

  /**
   * 获取特定区域的Executor实例
   */
  async getExecutorsByRegion(region: string): Promise<ExecutorInstance[]> {
    const allExecutors = await this.getAvailableExecutors();
    return allExecutors.filter(executor => executor.region === region);
  }

  /**
   * 检查Executor实例是否健康
   */
  isExecutorHealthy(executor: ExecutorInstance): boolean {
    const now = Date.now();
    const heartbeat = executor.lastHeartbeat || executor.last_heartbeat || 0;
    const timeSinceLastHeartbeat = now - heartbeat;

    // 30分钟内有心跳认为是健康的
    return timeSinceLastHeartbeat < 1800000;
  }

  /**
   * 获取缓存统计信息
   */
  getCacheStats(): { total: number; healthy: number; lastUpdate: number } {
    const total = this.executorCache.size;
    const healthy = Array.from(this.executorCache.values()).filter(
      executor => this.isExecutorHealthy(executor)
    ).length;
    
    return {
      total,
      healthy,
      lastUpdate: this.lastCacheUpdate
    };
  }
}
