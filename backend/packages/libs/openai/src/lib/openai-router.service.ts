import { Injectable, Logger, HttpException, HttpStatus } from '@nestjs/common';
import { Response, Request } from 'express';
import { ExecutorDiscoveryService, ExecutorInstance } from './executor-discovery.service';
import { ExecutorClientService } from './executor-client.service';
import { 
  OpenAIChatCompletionRequest, 
  OpenAICompletionRequest,
  OpenAIChatCompletionResponse,
  OpenAICompletionResponse 
} from '@saito/models';

export interface RouteDecision {
  shouldRouteToExecutor: boolean;
  originalModel: string;
  executorModel: string;
  reason: string;
}

@Injectable()
export class OpenAIRouterService {
  private readonly logger = new Logger(OpenAIRouterService.name);

  constructor(
    private readonly executorDiscovery: ExecutorDiscoveryService,
    private readonly executorClient: ExecutorClientService
  ) {}

  /**
   * 决定是否应该路由到Executor服务
   */
  makeRouteDecision(model: string): RouteDecision {
    const shouldRoute = model.startsWith('openai/');
    
    if (shouldRoute) {
      // 移除 openai/ 前缀，获取实际的OpenAI模型名
      const executorModel = model.substring(7); // 移除 "openai/" 前缀
      
      return {
        shouldRouteToExecutor: true,
        originalModel: model,
        executorModel: executorModel,
        reason: `Model ${model} matches openai/ prefix pattern`
      };
    }

    return {
      shouldRouteToExecutor: false,
      originalModel: model,
      executorModel: model,
      reason: `Model ${model} does not match openai/ prefix pattern`
    };
  }

  /**
   * 路由Chat Completions请求
   */
  async routeChatCompletion(
    request: OpenAIChatCompletionRequest,
    res: Response,
    req?: Request
  ): Promise<void> {
    const routeDecision = this.makeRouteDecision(request.model);
    
    this.logger.log(`Chat completion route decision: ${routeDecision.reason}`);

    if (!routeDecision.shouldRouteToExecutor) {
      throw new HttpException(
        {
          error: {
            message: `Model ${request.model} should be handled by local inference service`,
            type: 'routing_error',
            code: 'model_not_routable'
          }
        },
        HttpStatus.BAD_REQUEST
      );
    }

    // 选择最佳的Executor实例
    const executor = await this.selectExecutor();
    
    // 修改请求中的模型名，移除openai/前缀
    const executorRequest = {
      ...request,
      model: routeDecision.executorModel
    };

    // 获取API key（如果有的话）
    const apiKey = this.extractApiKey(req);

    try {
      if (request.stream) {
        await this.handleStreamingChatCompletion(executor, executorRequest, res, apiKey);
      } else {
        await this.handleNonStreamingChatCompletion(executor, executorRequest, res, apiKey);
      }
    } catch (error) {
      this.logger.error(`Failed to route chat completion to executor ${executor.id}:`, error);
      throw error;
    }
  }

  /**
   * 路由Completions请求
   */
  async routeCompletion(
    request: OpenAICompletionRequest,
    res: Response,
    req?: Request
  ): Promise<void> {
    const routeDecision = this.makeRouteDecision(request.model);
    
    this.logger.log(`Completion route decision: ${routeDecision.reason}`);

    if (!routeDecision.shouldRouteToExecutor) {
      throw new HttpException(
        {
          error: {
            message: `Model ${request.model} should be handled by local inference service`,
            type: 'routing_error',
            code: 'model_not_routable'
          }
        },
        HttpStatus.BAD_REQUEST
      );
    }

    // 选择最佳的Executor实例
    const executor = await this.selectExecutor();
    
    // 修改请求中的模型名，移除openai/前缀
    const executorRequest = {
      ...request,
      model: routeDecision.executorModel
    };

    // 获取API key（如果有的话）
    const apiKey = this.extractApiKey(req);

    try {
      if (request.stream) {
        await this.handleStreamingCompletion(executor, executorRequest, res, apiKey);
      } else {
        await this.handleNonStreamingCompletion(executor, executorRequest, res, apiKey);
      }
    } catch (error) {
      this.logger.error(`Failed to route completion to executor ${executor.id}:`, error);
      throw error;
    }
  }

  /**
   * 选择最佳的Executor实例
   */
  private async selectExecutor(): Promise<ExecutorInstance> {
    const executor = await this.executorDiscovery.selectBestExecutor();
    
    if (!executor) {
      throw new HttpException(
        {
          error: {
            message: 'No available executor services found',
            type: 'service_unavailable',
            code: 'no_executors'
          }
        },
        HttpStatus.SERVICE_UNAVAILABLE
      );
    }

    this.logger.log(`Selected executor ${executor.id} at ${executor.url}`);
    return executor;
  }

  /**
   * 处理非流式Chat Completion
   */
  private async handleNonStreamingChatCompletion(
    executor: ExecutorInstance,
    request: OpenAIChatCompletionRequest,
    res: Response,
    apiKey?: string
  ): Promise<void> {
    try {
      const response = await this.executorClient.sendChatCompletionRequest(
        executor,
        request,
        { apiKey }
      );

      res.json(response);
    } catch (error) {
      if (error instanceof HttpException) {
        const errorResponse = error.getResponse();
        res.status(error.getStatus()).json(errorResponse);
      } else {
        res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
          error: {
            message: 'Internal server error',
            type: 'internal_error'
          }
        });
      }
    }
  }

  /**
   * 处理流式Chat Completion
   */
  private async handleStreamingChatCompletion(
    executor: ExecutorInstance,
    request: OpenAIChatCompletionRequest,
    res: Response,
    apiKey?: string
  ): Promise<void> {
    // 设置SSE响应头
    res.setHeader('Content-Type', 'text/event-stream');
    res.setHeader('Cache-Control', 'no-cache');
    res.setHeader('Connection', 'keep-alive');
    res.setHeader('Access-Control-Allow-Origin', '*');

    await this.executorClient.sendStreamingChatCompletionRequest(
      executor,
      request,
      (chunk: string) => {
        res.write(chunk);
      },
      () => {
        res.end();
      },
      (error: Error) => {
        this.logger.error('Streaming error:', error);
        res.write(`data: {"error": {"message": "${error.message}", "type": "stream_error"}}\n\n`);
        res.end();
      },
      { apiKey }
    );
  }

  /**
   * 处理非流式Completion
   */
  private async handleNonStreamingCompletion(
    executor: ExecutorInstance,
    request: OpenAICompletionRequest,
    res: Response,
    apiKey?: string
  ): Promise<void> {
    try {
      const response = await this.executorClient.sendCompletionRequest(
        executor,
        request,
        { apiKey }
      );

      res.json(response);
    } catch (error) {
      if (error instanceof HttpException) {
        const errorResponse = error.getResponse();
        res.status(error.getStatus()).json(errorResponse);
      } else {
        res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
          error: {
            message: 'Internal server error',
            type: 'internal_error'
          }
        });
      }
    }
  }

  /**
   * 处理流式Completion
   */
  private async handleStreamingCompletion(
    executor: ExecutorInstance,
    request: OpenAICompletionRequest,
    res: Response,
    apiKey?: string
  ): Promise<void> {
    // 设置SSE响应头
    res.setHeader('Content-Type', 'text/event-stream');
    res.setHeader('Cache-Control', 'no-cache');
    res.setHeader('Connection', 'keep-alive');
    res.setHeader('Access-Control-Allow-Origin', '*');

    // 注意：这里需要实现流式completion的客户端方法
    // 目前先用chat completion的方法作为占位符
    await this.executorClient.sendStreamingChatCompletionRequest(
      executor,
      request as any, // 临时类型转换
      (chunk: string) => {
        res.write(chunk);
      },
      () => {
        res.end();
      },
      (error: Error) => {
        this.logger.error('Streaming completion error:', error);
        res.write(`data: {"error": {"message": "${error.message}", "type": "stream_error"}}\n\n`);
        res.end();
      },
      { apiKey }
    );
  }

  /**
   * 从请求中提取API key
   */
  private extractApiKey(req?: Request): string | undefined {
    if (!req) return undefined;

    // 从Authorization header提取
    const authHeader = req.headers.authorization;
    if (authHeader && authHeader.startsWith('Bearer ')) {
      return authHeader.substring(7);
    }

    // 从自定义header提取（如果有的话）
    const apiKeyHeader = req.headers['x-api-key'] as string;
    if (apiKeyHeader) {
      return apiKeyHeader;
    }

    return undefined;
  }

  /**
   * 获取路由统计信息
   */
  async getRouteStats(): Promise<{
    executors: { total: number; healthy: number; lastUpdate: number };
    routing: { openaiRequests: number; localRequests: number };
  }> {
    const executorStats = this.executorDiscovery.getCacheStats();
    
    return {
      executors: executorStats,
      routing: {
        openaiRequests: 0, // 这里可以添加计数器
        localRequests: 0   // 这里可以添加计数器
      }
    };
  }
}
