import { Injectable, Logger, Inject } from '@nestjs/common';
import {
  OpenAIChatCompletionRequest,
  OpenAICompletionRequest,
  StreamChatRequestMessage,
  NoStreamChatRequestMessage,
  StreamCompletionRequestMessage,
  NoStreamCompletionRequestMessage
} from '@saito/models';
import { TunnelService } from '@saito/tunnel';

/**
 * OpenAI 消息构建器
 * 职责：构建各种tunnel消息，消除重复代码
 * 设计原则：建造者模式 - 专门负责消息的构建和发送
 */
@Injectable()
export class OpenAIMessageBuilder {
  private readonly logger = new Logger(OpenAIMessageBuilder.name);

  constructor(
    @Inject('TunnelService') private readonly tunnelService: TunnelService,
    @Inject('PEER_ID') private readonly peerId: string
  ) {}

  /**
   * 发送Chat Tunnel消息 - 建造者模式
   */
  async sendChatTunnelMessage(
    taskId: string,
    targetDeviceId: string,
    body: OpenAIChatCompletionRequest,
    isStreamRequest: boolean
  ): Promise<void> {
    if (isStreamRequest) {
      await this.sendStreamChatTunnelMessage(taskId, targetDeviceId, body);
    } else {
      await this.sendNonStreamChatTunnelMessage(taskId, targetDeviceId, body);
    }
  }

  /**
   * 发送Completion Tunnel消息 - 建造者模式
   */
  async sendCompletionTunnelMessage(
    taskId: string,
    targetDeviceId: string,
    body: OpenAICompletionRequest,
    isStreamRequest: boolean
  ): Promise<void> {
    if (isStreamRequest) {
      await this.sendStreamCompletionTunnelMessage(taskId, targetDeviceId, body);
    } else {
      await this.sendNonStreamCompletionTunnelMessage(taskId, targetDeviceId, body);
    }
  }

  /**
   * 发送流式Chat Tunnel消息
   */
  private async sendStreamChatTunnelMessage(
    taskId: string,
    targetDeviceId: string,
    body: OpenAIChatCompletionRequest
  ): Promise<void> {
    const tunnelMessage: StreamChatRequestMessage = {
      from: this.peerId,
      to: targetDeviceId,
      type: 'chat_request_stream',
      payload: {
        taskId,
        path: '/openai/chat/completions',
        data: {
          model: body.model,
          messages: body.messages,
          stream: true,
          // 使用schema默认值
          temperature: body.temperature ?? 0.7,
          top_p: body.top_p ?? 1.0,
          n: body.n ?? 1,
          presence_penalty: body.presence_penalty ?? 0,
          frequency_penalty: body.frequency_penalty ?? 0,
          // 可选字段
          max_tokens: body.max_tokens,
          stop: body.stop,
          logit_bias: body.logit_bias,
          user: body.user,
          response_format: body.response_format
        }
      }
    };

    await this.tunnelService.handleMessage(tunnelMessage);
  }

  /**
   * 发送非流式Chat Tunnel消息
   */
  private async sendNonStreamChatTunnelMessage(
    taskId: string,
    targetDeviceId: string,
    body: OpenAIChatCompletionRequest
  ): Promise<void> {
    const tunnelMessage: NoStreamChatRequestMessage = {
      from: this.peerId,
      to: targetDeviceId,
      type: 'chat_request_no_stream',
      payload: {
        taskId,
        data: {
          model: body.model,
          messages: body.messages,
          stream: false
        }
      }
    };

    await this.tunnelService.handleMessage(tunnelMessage);
  }

  /**
   * 发送流式Completion Tunnel消息
   */
  private async sendStreamCompletionTunnelMessage(
    taskId: string,
    targetDeviceId: string,
    body: OpenAICompletionRequest
  ): Promise<void> {
    const tunnelMessage: StreamCompletionRequestMessage = {
      from: this.peerId,
      to: targetDeviceId,
      type: 'completion_request_stream',
      payload: {
        taskId,
        path: '/openai/completions',
        data: {
          model: body.model,
          prompt: body.prompt,
          stream: true,
          // 使用schema默认值
          temperature: body.temperature ?? 0.7,
          top_p: body.top_p ?? 1.0,
          n: body.n ?? 1,
          presence_penalty: body.presence_penalty ?? 0,
          frequency_penalty: body.frequency_penalty ?? 0,
          // 可选字段
          max_tokens: body.max_tokens,
          stop: body.stop,
          logit_bias: body.logit_bias,
          user: body.user,
          suffix: body.suffix,
          echo: body.echo,
          logprobs: body.logprobs,
          best_of: body.best_of
        }
      }
    };

    await this.tunnelService.handleMessage(tunnelMessage);
  }

  /**
   * 发送非流式Completion Tunnel消息
   */
  private async sendNonStreamCompletionTunnelMessage(
    taskId: string,
    targetDeviceId: string,
    body: OpenAICompletionRequest
  ): Promise<void> {
    const tunnelMessage: NoStreamCompletionRequestMessage = {
      from: this.peerId,
      to: targetDeviceId,
      type: 'completion_request_no_stream',
      payload: {
        taskId,
        data: {
          model: body.model,
          prompt: body.prompt,
          stream: false
        }
      }
    };

    await this.tunnelService.handleMessage(tunnelMessage);
  }
}
