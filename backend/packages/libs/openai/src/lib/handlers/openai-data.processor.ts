import { Injectable, Logger } from '@nestjs/common';
import { Response } from 'express';
import { OpenAIChatCompletionChunk } from '@saito/models';
import { OpenAIResponseManager } from './openai-response.manager';
import { OpenAIErrorHandler } from './openai-error.handler';

/**
 * OpenAI 数据处理器
 * 职责：处理来自设备的响应数据
 * 设计原则：单一职责原则 - 只负责数据的提取、验证和处理
 */
@Injectable()
export class OpenAIDataProcessor {
  private readonly logger = new Logger(OpenAIDataProcessor.name);

  constructor(
    private readonly responseManager: OpenAIResponseManager,
    private readonly errorHandler: OpenAIErrorHandler
  ) {}

  /**
   * 处理来自设备的流式响应
   * 应用单一职责原则：只负责流式数据的转发和格式化
   */
  async handleStreamResponse(taskId: string, payload: unknown): Promise<void> {
    this.logger.debug(`[OpenAIDataProcessor] Processing stream response for task ${taskId}`);

    const res = this.responseManager.getStreamResponse(taskId);
    if (!res) {
      this.logger.debug(`[OpenAIDataProcessor] No active stream found for task ${taskId}`);
      return;
    }

    try {
      // 1. 数据提取和验证 - 策略模式
      const chunkData = this.extractChunkData(payload);
      if (!chunkData) {
        this.logger.warn(`[OpenAIDataProcessor] Invalid chunk data for task ${taskId}`);
        return;
      }

      // 2. 数据处理 - 模板方法模式
      await this.processStreamChunk(taskId, chunkData, res);

    } catch (error) {
      await this.errorHandler.handleStreamError(taskId, error, res);
    }
  }

  /**
   * 处理来自设备的非流式响应
   * 应用单一职责原则：只负责非流式数据的转发和格式化
   */
  async handleNonStreamResponse(taskId: string, payload: unknown): Promise<void> {
    this.logger.debug(`[OpenAIDataProcessor] Processing non-stream response for task ${taskId}`);

    const res = this.responseManager.getNonStreamResponse(taskId);
    if (!res) {
      this.logger.debug(`[OpenAIDataProcessor] No active non-stream found for task ${taskId}`);
      return;
    }

    try {
      // 提取和验证数据
      const responseData = this.extractNonStreamData(payload);
      if (!responseData) {
        this.logger.warn(`[OpenAIDataProcessor] Invalid non-stream data for task ${taskId}`);
        return;
      }

      // 处理非流式响应数据
      await this.responseManager.processNonStreamResponse(taskId, responseData, res);

    } catch (error) {
      await this.errorHandler.handleNonStreamError(taskId, error, res);
    }
  }

  /**
   * 处理来自设备的流式 Completion 响应
   * 专门处理 completion 格式的数据
   */
  async handleCompletionStreamResponse(taskId: string, payload: unknown): Promise<void> {
    this.logger.debug(`[OpenAIDataProcessor] Processing completion stream response for task ${taskId}`);

    const res = this.responseManager.getStreamResponse(taskId);
    if (!res) {
      this.logger.debug(`[OpenAIDataProcessor] No active stream found for task ${taskId}`);
      return;
    }

    try {
      // 提取和验证 completion 数据
      const chunkData = this.extractCompletionChunkData(payload);
      if (!chunkData) {
        this.logger.warn(`[OpenAIDataProcessor] Invalid completion chunk data for task ${taskId}`);
        return;
      }

      // 处理 completion 流式数据块
      await this.processCompletionStreamChunk(taskId, chunkData, res);

    } catch (error) {
      await this.errorHandler.handleStreamError(taskId, error, res);
    }
  }

  /**
   * 处理来自设备的非流式 Completion 响应
   * 专门处理 completion 格式的数据
   */
  async handleCompletionNonStreamResponse(taskId: string, payload: unknown): Promise<void> {
    this.logger.debug(`[OpenAIDataProcessor] Processing completion non-stream response for task ${taskId}`);

    const res = this.responseManager.getNonStreamResponse(taskId);
    if (!res) {
      this.logger.debug(`[OpenAIDataProcessor] No active non-stream found for task ${taskId}`);
      return;
    }

    try {
      // 提取和验证 completion 数据
      const responseData = this.extractCompletionNonStreamData(payload);
      if (!responseData) {
        this.logger.warn(`[OpenAIDataProcessor] Invalid completion non-stream data for task ${taskId}`);
        return;
      }

      // 处理 completion 非流式响应数据
      await this.responseManager.processNonStreamResponse(taskId, responseData, res);

    } catch (error) {
      await this.errorHandler.handleNonStreamError(taskId, error, res);
    }
  }

  /**
   * 提取chunk数据 - 策略模式
   */
  private extractChunkData(payload: unknown): OpenAIChatCompletionChunk | null {
    try {
      // 支持不同的payload结构
      let data = payload;
      if (payload && typeof payload === 'object' && 'data' in payload) {
        data = (payload as { data: unknown }).data;
      }

      // 基本验证
      if (!data || typeof data !== 'object') {
        return null;
      }

      // 这里可以添加更严格的Zod验证
      return data as OpenAIChatCompletionChunk;
    } catch {
      return null;
    }
  }

  /**
   * 提取非流式响应数据 - 策略模式
   */
  private extractNonStreamData(payload: unknown): any | null {
    try {
      // 支持不同的payload结构
      let data = payload;
      if (payload && typeof payload === 'object' && 'data' in payload) {
        data = (payload as { data: unknown }).data;
      }

      // 基本验证
      if (!data || typeof data !== 'object') {
        return null;
      }

      // 这里可以添加更严格的Zod验证
      return data;
    } catch {
      return null;
    }
  }

  /**
   * 提取 completion 流式数据块 - 策略模式
   */
  private extractCompletionChunkData(payload: unknown): any | null {
    try {
      // 支持不同的payload结构
      let data = payload;
      if (payload && typeof payload === 'object' && 'data' in payload) {
        data = (payload as { data: unknown }).data;
      }

      this.logger.debug(`[OpenAIDataProcessor] Extracting completion chunk data: ${JSON.stringify(data).substring(0, 200)}...`);

      // 基本验证
      if (!data || typeof data !== 'object') {
        this.logger.warn(`[OpenAIDataProcessor] Invalid data type for completion chunk: ${typeof data}`);
        return null;
      }

      // 验证是否为 completion 格式
      const dataObj = data as any;
      if (dataObj.object !== 'text_completion') {
        this.logger.warn(`[OpenAIDataProcessor] Invalid object type for completion chunk: ${dataObj.object}`);
        return null;
      }

      this.logger.debug(`[OpenAIDataProcessor] Successfully extracted completion chunk data with text: "${dataObj.choices?.[0]?.text || 'N/A'}"`);
      return dataObj;
    } catch (error) {
      this.logger.error(`[OpenAIDataProcessor] Error extracting completion chunk data: ${error}`);
      return null;
    }
  }

  /**
   * 提取 completion 非流式响应数据 - 策略模式
   */
  private extractCompletionNonStreamData(payload: unknown): any | null {
    try {
      // 支持不同的payload结构
      let data = payload;
      if (payload && typeof payload === 'object' && 'data' in payload) {
        data = (payload as { data: unknown }).data;
      }

      // 基本验证
      if (!data || typeof data !== 'object') {
        return null;
      }

      // 验证是否为 completion 格式
      const dataObj = data as any;
      if (dataObj.object !== 'text_completion') {
        return null;
      }

      return dataObj;
    } catch {
      return null;
    }
  }

  /**
   * 处理流式数据块 - 模板方法模式
   */
  private async processStreamChunk(
    taskId: string,
    chunkData: OpenAIChatCompletionChunk,
    res: Response
  ): Promise<void> {
    const isCompleted = this.isStreamCompleted(chunkData);

    if (isCompleted) {
      await this.responseManager.completeStream(taskId, res);
    } else {
      await this.responseManager.writeStreamChunk(chunkData, res);
    }
  }

  /**
   * 处理 completion 流式数据块 - 模板方法模式
   */
  private async processCompletionStreamChunk(
    taskId: string,
    chunkData: any,
    res: Response
  ): Promise<void> {
    const isCompleted = this.isCompletionStreamCompleted(chunkData);

    if (isCompleted) {
      await this.responseManager.completeStream(taskId, res);
    } else {
      await this.responseManager.writeCompletionStreamChunk(chunkData, res);
    }
  }

  /**
   * 判断流是否完成 - 策略模式
   */
  private isStreamCompleted(chunkData: OpenAIChatCompletionChunk): boolean {
    return chunkData.choices?.[0]?.finish_reason === 'stop';
  }

  /**
   * 判断 completion 流是否完成 - 策略模式
   */
  private isCompletionStreamCompleted(chunkData: any): boolean {
    return chunkData.choices?.[0]?.finish_reason === 'stop';
  }
}
