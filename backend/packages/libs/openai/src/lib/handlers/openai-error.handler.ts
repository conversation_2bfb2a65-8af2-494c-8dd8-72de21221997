import { Injectable, Logger, HttpException, HttpStatus } from '@nestjs/common';
import { Response } from 'express';
import { OpenAIErrorResponse, Task, InferenceError } from '@saito/models';
import { OpenAIResponseManager } from './openai-response.manager';

/**
 * OpenAI 错误处理器
 * 职责：统一的错误处理逻辑
 * 设计原则：单一职责原则 - 只负责错误的处理和响应
 */
@Injectable()
export class OpenAIErrorHandler {
  private readonly logger = new Logger(OpenAIErrorHandler.name);

  constructor(
    private readonly responseManager: OpenAIResponseManager
  ) {}

  /**
   * 处理请求错误 - 单一职责原则
   */
  async handleRequestError(error: unknown, task: Task | null, res: Response): Promise<void> {
    this.logger.error(`[OpenAIErrorHandler] Error handling request: ${error}`);

    // 清理资源
    if (task?.id) {
      this.responseManager.cleanupResponse(task.id);
    }

    // 发送错误响应
    if (!res.headersSent) {
      const errorResponse = this.createErrorResponse(error);
      const statusCode = error instanceof HttpException ? error.getStatus() : HttpStatus.INTERNAL_SERVER_ERROR;
      res.status(statusCode).json(errorResponse);
    }
  }

  /**
   * 处理流式错误 - 单一职责原则
   */
  async handleStreamError(taskId: string, error: unknown, res: Response): Promise<void> {
    this.logger.error(`[OpenAIErrorHandler] Stream error for task ${taskId}: ${error}`);

    // 对于数据格式错误，不关闭流
    if (error instanceof TypeError) {
      this.logger.warn(`[OpenAIErrorHandler] Data format error for task ${taskId}, continuing stream`);
      return;
    }

    // 关闭流并发送错误响应
    this.responseManager.cleanupResponse(taskId);
    if (!res.headersSent) {
      const errorResponse = this.createErrorResponse(error);
      res.status(HttpStatus.INTERNAL_SERVER_ERROR).json(errorResponse);
    }
  }

  /**
   * 处理非流式错误 - 单一职责原则
   */
  async handleNonStreamError(taskId: string, error: unknown, res: Response): Promise<void> {
    this.logger.error(`[OpenAIErrorHandler] Non-stream error for task ${taskId}: ${error}`);

    // 清理存储的响应对象
    this.responseManager.cleanupResponse(taskId);

    if (!res.headersSent) {
      const errorResponse = this.createErrorResponse(error);
      res.status(HttpStatus.INTERNAL_SERVER_ERROR).json(errorResponse);
    }
  }

  /**
   * 处理错误响应 - 单一职责原则
   * 使用强类型替代any
   */
  async handleErrorResponse(taskId: string, error: InferenceError): Promise<void> {
    const res = this.responseManager.getStreamResponse(taskId);
    if (!res) {
      this.logger.debug(`[OpenAIErrorHandler] No active stream for error response, task ${taskId}`);
      return;
    }

    this.logger.error(`[OpenAIErrorHandler] Error response for task ${taskId}: ${error.error.message}`);
    this.responseManager.cleanupResponse(taskId);

    if (!res.headersSent) {
      const errorResponse: OpenAIErrorResponse = {
        error: {
          message: error.error.message,
          type: error.error.type || "api_error",
          param: null,
          code: error.error.code || null
        }
      };
      res.status(HttpStatus.INTERNAL_SERVER_ERROR).json(errorResponse);
    }
  }

  /**
   * 创建错误响应 - 工厂模式
   */
  private createErrorResponse(error: unknown): OpenAIErrorResponse {
    if (error instanceof HttpException) {
      return {
        error: {
          message: error.message,
          type: "api_error",
          param: null,
          code: null
        }
      };
    }

    return {
      error: {
        message: 'Internal server error',
        type: "api_error",
        param: null,
        code: null
      }
    };
  }

  /**
   * 创建未实现错误响应 - 工厂模式
   */
  createNotImplementedError(message: string): OpenAIErrorResponse {
    return {
      error: {
        message,
        type: "api_error",
        param: null,
        code: null
      }
    };
  }
}
