import { Injectable, Logger, Inject } from '@nestjs/common';
import { Response } from 'express';
import { ResponseAdapterService } from '@saito/tunnel';

/**
 * OpenAI 响应管理器
 * 职责：管理HTTP响应的设置和生命周期
 * 设计原则：单一职责原则 - 只负责响应的管理
 */
@Injectable()
export class OpenAIResponseManager {
  private readonly logger = new Logger(OpenAIResponseManager.name);

  // 存储活跃的流式响应
  private readonly activeStreams = new Map<string, Response>();

  // 存储活跃的非流式响应
  private readonly activeNonStreams = new Map<string, Response>();

  constructor(
    @Inject('ResponseAdapterService') private readonly responseAdapter: ResponseAdapterService
  ) {}

  /**
   * 设置流式响应 - 单一职责原则
   */
  setupStreamingResponse(taskId: string, res: Response): void {
    this.activeStreams.set(taskId, res);
    this.responseAdapter.registerTaskService(taskId, 'openai');

    // 设置SSE响应头
    res.setHeader('Content-Type', 'text/event-stream');
    res.setHeader('Cache-Control', 'no-cache');
    res.setHeader('Connection', 'keep-alive');
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Headers', 'Cache-Control');

    this.logger.debug(`[OpenAIResponseManager] Configured streaming response for task ${taskId}`);
  }

  /**
   * 设置非流式响应 - 单一职责原则
   */
  setupNonStreamingResponse(taskId: string, res: Response): void {
    this.activeNonStreams.set(taskId, res);
    this.responseAdapter.registerTaskService(taskId, 'openai');

    // 设置JSON响应头
    res.setHeader('Content-Type', 'application/json');
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Headers', 'Cache-Control');

    this.logger.debug(`[OpenAIResponseManager] Configured non-streaming response for task ${taskId}`);
  }

  /**
   * 获取流式响应对象
   */
  getStreamResponse(taskId: string): Response | undefined {
    return this.activeStreams.get(taskId);
  }

  /**
   * 获取非流式响应对象
   */
  getNonStreamResponse(taskId: string): Response | undefined {
    return this.activeNonStreams.get(taskId);
  }

  /**
   * 完成流式响应 - 单一职责原则
   */
  async completeStream(taskId: string, res: Response): Promise<void> {
    this.logger.log(`[OpenAIResponseManager] Stream completed for task ${taskId}`);

    // 确保 [DONE] 标记有正确的 SSE 格式
    res.write('data: [DONE]\n\n');
    res.end();
    this.activeStreams.delete(taskId);

    this.logger.debug(`[OpenAIResponseManager] Stream response ended and cleaned up for task ${taskId}`);
  }

  /**
   * 写入流式数据块 - 单一职责原则
   */
  async writeStreamChunk(chunkData: any, res: Response): Promise<void> {
    const jsonData = JSON.stringify(chunkData);
    res.write(`data: ${jsonData}`);
  }

  /**
   * 写入 completion 流式数据块 - 单一职责原则
   */
  async writeCompletionStreamChunk(chunkData: any, res: Response): Promise<void> {
    const jsonData = JSON.stringify(chunkData);
    const sseData = `data: ${jsonData}\n\n`;

    this.logger.debug(`[OpenAIResponseManager] Writing completion stream chunk: ${sseData.substring(0, 100)}...`);
    this.logger.debug(`[OpenAIResponseManager] Response headers sent: ${res.headersSent}, writable: ${res.writable}`);

    if (!res.writable) {
      this.logger.error(`[OpenAIResponseManager] Response is not writable for completion stream chunk`);
      return;
    }

    try {
      res.write(sseData);
      this.logger.debug(`[OpenAIResponseManager] Successfully wrote completion stream chunk`);
    } catch (error) {
      this.logger.error(`[OpenAIResponseManager] Error writing completion stream chunk: ${error}`);
    }
  }

  /**
   * 处理非流式响应数据 - 模板方法模式
   */
  async processNonStreamResponse(taskId: string, responseData: any, res: Response): Promise<void> {
    this.logger.debug(`[OpenAIResponseManager] Processing complete non-stream response for task ${taskId}`);

    // 发送完整的JSON响应
    if (!res.headersSent) {
      res.json(responseData);
    }

    // 清理存储的响应对象
    this.activeNonStreams.delete(taskId);

    this.logger.log(`[OpenAIResponseManager] Non-stream response sent for task ${taskId}`);
  }

  /**
   * 清理响应资源
   */
  cleanupResponse(taskId: string): void {
    this.activeStreams.delete(taskId);
    this.activeNonStreams.delete(taskId);
  }

  /**
   * 检查是否有活跃的流式响应
   */
  hasActiveStream(taskId: string): boolean {
    return this.activeStreams.has(taskId);
  }

  /**
   * 检查是否有活跃的非流式响应
   */
  hasActiveNonStream(taskId: string): boolean {
    return this.activeNonStreams.has(taskId);
  }
}
