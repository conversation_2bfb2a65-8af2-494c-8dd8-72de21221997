import { Response, Request } from 'express';
import { OpenAIChatCompletionRequest, OpenAICompletionRequest } from '@saito/models';

/**
 * OpenAI 服务接口
 * 职责：定义 OpenAI 服务的核心方法
 * 设计原则：单一职责原则，只负责 OpenAI 相关的业务逻辑
 */
export interface OpenAIService {
  /**
   * 处理聊天完成请求
   * @param path 请求路径
   * @param body 请求体
   * @param res 响应对象
   * @param req 请求对象（可选，用于获取API密钥信息）
   */
  handleChatCompletion(path: string, body: OpenAIChatCompletionRequest, res: Response, req?: Request): Promise<void>;

  /**
   * 处理完成请求
   * @param path 请求路径
   * @param body 请求体
   * @param res 响应对象
   * @param req 请求对象（可选，用于获取API密钥信息）
   */
  handleCompletion(path: string, body: OpenAICompletionRequest, res: Response, req?: Request): Promise<void>;

  /**
   * 代理请求
   * @param req 请求对象
   */
  proxyRequest(req: Request): Promise<any>;
}
