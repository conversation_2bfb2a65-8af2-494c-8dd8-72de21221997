import { Injectable, Logger, HttpException, HttpStatus } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import axios, { AxiosInstance, AxiosResponse, AxiosError } from 'axios';
import { ExecutorInstance } from './executor-discovery.service';
import { 
  OpenAIChatCompletionRequest, 
  OpenAICompletionRequest,
  OpenAIChatCompletionResponse,
  OpenAICompletionResponse 
} from '@saito/models';

export interface ExecutorRequestOptions {
  timeout?: number;
  retries?: number;
  apiKey?: string;
}

@Injectable()
export class ExecutorClientService {
  private readonly logger = new Logger(ExecutorClientService.name);
  private readonly httpClient: AxiosInstance;
  private readonly defaultTimeout: number;
  private readonly defaultRetries: number;

  constructor(private configService: ConfigService) {
    this.defaultTimeout = this.configService.get<number>('EXECUTOR_TIMEOUT', 30000);
    this.defaultRetries = this.configService.get<number>('EXECUTOR_RETRIES', 2);

    this.httpClient = axios.create({
      timeout: this.defaultTimeout,
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'Saito-Gateway/1.0'
      }
    });

    // 添加请求拦截器
    this.httpClient.interceptors.request.use(
      (config) => {
        this.logger.debug(`Making request to ${config.url}`);
        return config;
      },
      (error) => {
        this.logger.error('Request interceptor error:', error);
        return Promise.reject(error);
      }
    );

    // 添加响应拦截器
    this.httpClient.interceptors.response.use(
      (response) => {
        this.logger.debug(`Response received from ${response.config.url} with status ${response.status}`);
        return response;
      },
      (error) => {
        this.logger.error(`Response error from ${error.config?.url}:`, error.message);
        return Promise.reject(error);
      }
    );
  }

  /**
   * 发送Chat Completions请求到Executor
   */
  async sendChatCompletionRequest(
    executor: ExecutorInstance,
    request: OpenAIChatCompletionRequest,
    options: ExecutorRequestOptions = {}
  ): Promise<OpenAIChatCompletionResponse> {
    const url = `${executor.url}/v1/chat/completions`;
    
    try {
      this.logger.log(`Sending chat completion request to executor ${executor.id} at ${url}`);
      
      const response = await this.makeRequest<OpenAIChatCompletionResponse>(
        'POST',
        url,
        request,
        options
      );

      this.logger.log(`Chat completion request successful for executor ${executor.id}`);
      return response.data;
    } catch (error) {
      this.logger.error(`Chat completion request failed for executor ${executor.id}:`, error);
      throw this.handleExecutorError(error, executor);
    }
  }

  /**
   * 发送Completions请求到Executor
   */
  async sendCompletionRequest(
    executor: ExecutorInstance,
    request: OpenAICompletionRequest,
    options: ExecutorRequestOptions = {}
  ): Promise<OpenAICompletionResponse> {
    const url = `${executor.url}/v1/completions`;
    
    try {
      this.logger.log(`Sending completion request to executor ${executor.id} at ${url}`);
      
      const response = await this.makeRequest<OpenAICompletionResponse>(
        'POST',
        url,
        request,
        options
      );

      this.logger.log(`Completion request successful for executor ${executor.id}`);
      return response.data;
    } catch (error) {
      this.logger.error(`Completion request failed for executor ${executor.id}:`, error);
      throw this.handleExecutorError(error, executor);
    }
  }

  /**
   * 发送流式Chat Completions请求到Executor
   */
  async sendStreamingChatCompletionRequest(
    executor: ExecutorInstance,
    request: OpenAIChatCompletionRequest,
    onData: (chunk: string) => void,
    onEnd: () => void,
    onError: (error: Error) => void,
    options: ExecutorRequestOptions = {}
  ): Promise<void> {
    const url = `${executor.url}/v1/chat/completions`;
    
    try {
      this.logger.log(`Sending streaming chat completion request to executor ${executor.id} at ${url}`);
      
      const response = await this.httpClient.post(url, request, {
        timeout: options.timeout || this.defaultTimeout,
        responseType: 'stream',
        headers: {
          'Authorization': options.apiKey ? `Bearer ${options.apiKey}` : undefined,
          'Accept': 'text/event-stream',
        }
      });

      response.data.on('data', (chunk: Buffer) => {
        const chunkStr = chunk.toString();
        onData(chunkStr);
      });

      response.data.on('end', () => {
        this.logger.log(`Streaming chat completion completed for executor ${executor.id}`);
        onEnd();
      });

      response.data.on('error', (error: Error) => {
        this.logger.error(`Streaming error for executor ${executor.id}:`, error);
        onError(this.handleExecutorError(error, executor));
      });

    } catch (error) {
      this.logger.error(`Streaming chat completion request failed for executor ${executor.id}:`, error);
      onError(this.handleExecutorError(error, executor));
    }
  }

  /**
   * 通用HTTP请求方法
   */
  private async makeRequest<T>(
    method: 'GET' | 'POST' | 'PUT' | 'DELETE',
    url: string,
    data?: any,
    options: ExecutorRequestOptions = {}
  ): Promise<AxiosResponse<T>> {
    const config = {
      method,
      url,
      data,
      timeout: options.timeout || this.defaultTimeout,
      headers: {
        'Authorization': options.apiKey ? `Bearer ${options.apiKey}` : undefined,
      }
    };

    let lastError: any;
    const retries = options.retries || this.defaultRetries;

    for (let attempt = 0; attempt <= retries; attempt++) {
      try {
        if (attempt > 0) {
          this.logger.warn(`Retrying request to ${url}, attempt ${attempt + 1}/${retries + 1}`);
          // 指数退避
          await this.sleep(Math.pow(2, attempt) * 1000);
        }

        const response = await this.httpClient.request<T>(config);
        return response;
      } catch (error) {
        lastError = error;
        
        // 如果是最后一次尝试或者是不可重试的错误，直接抛出
        if (attempt === retries || !this.isRetryableError(error)) {
          break;
        }
      }
    }

    throw lastError;
  }

  /**
   * 判断错误是否可以重试
   */
  private isRetryableError(error: any): boolean {
    if (axios.isAxiosError(error)) {
      // 网络错误或超时可以重试
      if (error.code === 'ECONNABORTED' || error.code === 'ENOTFOUND' || error.code === 'ECONNREFUSED') {
        return true;
      }
      
      // 5xx服务器错误可以重试
      if (error.response && error.response.status >= 500) {
        return true;
      }
      
      // 429 Too Many Requests可以重试
      if (error.response && error.response.status === 429) {
        return true;
      }
    }
    
    return false;
  }

  /**
   * 处理Executor错误
   */
  private handleExecutorError(error: any, executor: ExecutorInstance): HttpException {
    if (axios.isAxiosError(error)) {
      const axiosError = error as AxiosError;
      console.log(axiosError)
      if (axiosError.response) {
        // 服务器返回了错误响应
        const status = axiosError.response.status;
        const message = axiosError.response.data || axiosError.message;
        
        this.logger.error(`Executor ${executor.id} returned error ${status}: ${JSON.stringify(message)}`);
        
        return new HttpException(
          {
            error: {
              message: `Executor service error: ${JSON.stringify(message)}`,
              type: 'executor_error',
              executor_id: executor.id,
              status: status
            }
          },
          status
        );
      } else if (axiosError.request) {
        // 请求发送了但没有收到响应
        this.logger.error(`No response from executor ${executor.id}: ${axiosError.message}`);
        
        return new HttpException(
          {
            error: {
              message: `Executor service unavailable: ${executor.id}`,
              type: 'executor_unavailable',
              executor_id: executor.id
            }
          },
          HttpStatus.SERVICE_UNAVAILABLE
        );
      }
    }
    
    // 其他错误
    this.logger.error(`Unexpected error with executor ${executor.id}:`, error);
    
    return new HttpException(
      {
        error: {
          message: 'Internal server error',
          type: 'internal_error',
          executor_id: executor.id
        }
      },
      HttpStatus.INTERNAL_SERVER_ERROR
    );
  }

  /**
   * 睡眠函数
   */
  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * 健康检查
   */
  async healthCheck(executor: ExecutorInstance): Promise<boolean> {
    try {
      const response = await this.httpClient.get(`${executor.url}/health`, {
        timeout: 5000
      });
      return response.status === 200;
    } catch (error) {
      this.logger.warn(`Health check failed for executor ${executor.id}:`, error instanceof Error ? error.message : String(error));
      return false;
    }
  }
}
