import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { ApiKeyService } from './apikey.service';
import { ApiKeyRepository } from './apikey.repository';
import { PersistentModule } from '@saito/persistent';

@Module({
  imports: [
    PersistentModule,
    ConfigModule,
  ],
  providers: [
    ApiKeyService,
    ApiKeyRepository,
  ],
  exports: [
    ApiKeyService,
    ApiKeyRepository,
  ],
})
export class ApiKeyModule {}
