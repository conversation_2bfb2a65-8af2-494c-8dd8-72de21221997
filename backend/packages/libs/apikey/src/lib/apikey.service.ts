import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { ApiKeyServiceInterface } from './apikey.interface';
import { ApiKeyRepository } from './apikey.repository';
import {
  CreateApiKeyRequest,
  CreateApiKeyResponse,
  UpdateApiKeyRequest,
  GetApiKeysRequest,
  GetApiKeysResponse,

  GetApiKeyDetailResponse,
  GetApiKeyUsageRequest,
  GetApiKeyUsageResponse,
  GetChannelUsageRequest,
  GetChannelUsageResponse
} from '@saito/models';

@Injectable()
export class ApiKeyService implements ApiKeyServiceInterface {
  private readonly logger = new Logger(ApiKeyService.name);

  constructor(
    private readonly apiKeyRepository: ApiKeyRepository,
  ) {}

  /**
   * Create a new API key
   * @param request The create API key request
   * @param userId The user ID
   * @returns The created API key
   */
  async createApiKey(request: CreateApiKeyRequest, userId: string): Promise<CreateApiKeyResponse> {
    try {
      const { name } = request;

      // Generate a new API key
      const { key, prefix } = await this.apiKeyRepository.generateApiKey();

      // Create the API key in the database
      const apiKey = await this.apiKeyRepository.createApiKey(
        userId,
        name,
        key,
        prefix
      );

      this.logger.log(`Created platform API key ${apiKey.id} for user ${userId}`);

      // Return the created API key
      return {
        id: apiKey.id,
        key, // Only returned once
        name: apiKey.name || '',
        category: 'platform', // All keys are now platform keys
        provider: 'Saito Gateway',
        createdAt: apiKey.createdAt
      };
    } catch (error) {
      this.logger.error(`Error creating API key: ${error}`);
      throw error;
    }
  }

  /**
   * Update an API key
   * @param keyId The key ID
   * @param request The update API key request
   * @param userId The user ID
   */
  async updateApiKey(keyId: string, request: UpdateApiKeyRequest, userId: string): Promise<void> {
    try {
      const { name, status } = request;

      // Update the API key in database
      await this.apiKeyRepository.updateApiKey(keyId, userId, name, status);
    } catch (error) {
      this.logger.error(`Error updating API key: ${error}`);
      throw error;
    }
  }

  /**
   * Delete an API key
   * @param keyId The key ID
   * @param userId The user ID
   */
  async deleteApiKey(keyId: string, userId: string): Promise<void> {
    try {
      // Delete the API key
      await this.apiKeyRepository.deleteApiKey(keyId, userId);
    } catch (error) {
      this.logger.error(`Error deleting API key: ${error}`);
      throw error;
    }
  }

  /**
   * Get API keys for a user
   * @param request The get API keys request
   * @param userId The user ID
   * @returns The API keys
   */
  async getApiKeys(request: GetApiKeysRequest, userId: string): Promise<GetApiKeysResponse> {
    try {
      const { page, pageSize, status, search } = request;

      // Get API keys
      const result = await this.apiKeyRepository.getApiKeys(
        userId,
        page,
        pageSize,
        status,
        search
      );

      // Return the API keys
      return {
        data: result.data,
        total: result.total,
        page,
        pageSize
      };
    } catch (error) {
      this.logger.error(`Error getting API keys: ${error}`);
      throw error;
    }
  }



  /**
   * Get API key detail
   * @param keyId The key ID
   * @param userId The user ID
   * @returns The API key detail
   */
  async getApiKeyDetail(keyId: string, userId: string): Promise<GetApiKeyDetailResponse> {
    try {
      // Get API key detail
      const data = await this.apiKeyRepository.getApiKeyDetail(keyId, userId);

      if (!data) {
        throw new NotFoundException(`API key with ID ${keyId} not found`);
      }

      // Return the API key detail
      return { data };
    } catch (error) {
      this.logger.error(`Error getting API key detail: ${error}`);
      throw error;
    }
  }

  /**
   * Get API key usage
   * @param request The get API key usage request
   * @param userId The user ID
   * @returns The API key usage
   */
  async getApiKeyUsage(request: GetApiKeyUsageRequest, userId: string): Promise<GetApiKeyUsageResponse> {
    try {
      const { keyId, timeRange } = request;

      // Get API key usage
      const data = await this.apiKeyRepository.getApiKeyUsage(keyId, userId, timeRange);

      // Return the API key usage
      return { data };
    } catch (error) {
      this.logger.error(`Error getting API key usage: ${error}`);
      throw error;
    }
  }

  /**
   * Get channel usage
   * @param request The get channel usage request
   * @param userId The user ID
   * @returns The channel usage
   */
  async getChannelUsage(request: GetChannelUsageRequest, userId: string): Promise<GetChannelUsageResponse> {
    try {
      const { timeRange } = request;

      // Get channel usage
      const data = await this.apiKeyRepository.getChannelUsage(userId, timeRange);

      // Return the channel usage
      return { data };
    } catch (error) {
      this.logger.error(`Error getting channel usage: ${error}`);
      throw error;
    }
  }

  /**
   * Get usage summary
   * @param request The get usage summary request
   * @param userId The user ID
   * @returns The usage summary
   */
  async getUsageSummary(request: { timeRange?: string }, userId: string): Promise<{
    data: {
      totalRequests: number;
      totalTokens: number;
      avgResponseTime: number;
      costThisMonth: number;
    }
  }> {
    try {
      const { timeRange } = request;

      // Get usage summary
      const data = await this.apiKeyRepository.getUsageSummary(userId, timeRange);

      // Return the usage summary
      return { data };
    } catch (error) {
      this.logger.error(`Error getting usage summary: ${error}`);
      throw error;
    }
  }

  /**
   * Get overall usage
   * @param request The get overall usage request
   * @param userId The user ID
   * @returns The overall usage
   */
  async getOverallUsage(request: { timeRange?: string }, userId: string): Promise<{
    data: {
      dailyData: {
        date: string;
        requests: number;
        tokens: number;
        cost: number;
      }[];
      monthlyData: {
        month: string;
        year: number;
        requests: number;
        tokens: number;
        cost: number;
      }[];
    }
  }> {
    try {
      const { timeRange } = request;

      // Get overall usage
      const data = await this.apiKeyRepository.getOverallUsage(userId, timeRange);

      // Return the overall usage
      return { data };
    } catch (error) {
      this.logger.error(`Error getting overall usage: ${error}`);
      throw error;
    }
  }

  /**
   * Validate an API key
   * @param params The validation parameters
   * @returns The validation result
   */
  async validateApiKey(params: { keyHash: string; keyPrefix: string }): Promise<{
    valid: boolean;
    keyId?: string;
    userId?: string;
    status?: string;
  }> {
    try {
      const { keyHash, keyPrefix } = params;

      // 查询数据库验证API密钥
      const result = await this.apiKeyRepository.validateApiKey(keyHash, keyPrefix);

      if (!result) {
        return { valid: false };
      }

      return {
        valid: true,
        keyId: result.id,
        userId: result.userId,
        status: result.status
      };
    } catch (error) {
      this.logger.error(`Error validating API key: ${error}`);
      return { valid: false };
    }
  }

  /**
   * Log API usage
   * @param params The log parameters
   */
  async logApiUsage(params: {
    apiKeyId: string;
    userId?: string;
    categoryId: string;
    endpoint: string;
    method: string;
    model: string;
    requestSize: number;
    statusCode: number;
  }): Promise<void> {
    try {
      await this.apiKeyRepository.logApiUsage(params);
    } catch (error) {
      this.logger.error(`Error logging API usage: ${error}`);
    }
  }

  /**
   * Update API usage log
   * @param params The update parameters
   */
  async updateApiUsageLog(params: {
    apiKeyId: string;
    responseTime: number;
    statusCode: number;
    responseSize: number;
  }): Promise<void> {
    try {
      await this.apiKeyRepository.updateApiUsageLog(params);
    } catch (error) {
      this.logger.error(`Error updating API usage log: ${error}`);
    }
  }
}
