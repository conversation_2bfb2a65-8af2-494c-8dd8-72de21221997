{"name": "lib-apikey", "$schema": "../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "packages/libs/apikey/src", "projectType": "library", "targets": {"build": {"executor": "@nx/js:tsc", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/packages/libs/apikey", "main": "packages/libs/apikey/src/index.ts", "tsConfig": "packages/libs/apikey/tsconfig.lib.json", "assets": ["packages/libs/apikey/*.md"]}}, "lint": {"executor": "@nx/eslint:lint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["packages/libs/apikey/**/*.ts", "packages/libs/apikey/package.json"]}}}, "tags": []}