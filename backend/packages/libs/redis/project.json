{"name": "lib-redis", "$schema": "../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "packages/libs/redis/src", "projectType": "library", "targets": {"build": {"executor": "@nx/js:tsc", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/packages/libs/redis", "main": "packages/libs/redis/src/index.ts", "tsConfig": "packages/libs/redis/tsconfig.lib.json", "assets": ["packages/libs/redis/*.md"]}}, "lint": {"executor": "@nx/eslint:lint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["packages/libs/redis/**/*.ts", "packages/libs/redis/package.json"]}}}, "tags": []}