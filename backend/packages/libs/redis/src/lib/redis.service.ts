import { Injectable, Logger, OnModuleDestroy, OnModuleInit } from '@nestjs/common';
import Redis from 'ioredis';
import { env } from '../env';
import { z } from 'zod';

// 定义错误类型
const RedisErrorSchema = z.object({
  message: z.string(),
  name: z.string().optional(),
  stack: z.string().optional(),
  code: z.string().optional(),
});

type RedisError = z.infer<typeof RedisErrorSchema>;

// 辅助函数：处理Redis错误
function handleRedisError(error: unknown): RedisError {
  // 尝试验证错误对象
  try {
    return RedisErrorSchema.parse(error);
  } catch {
    // 如果验证失败，返回一个默认的错误对象
    return {
      message: error instanceof Error ? error.message : String(error),
      name: error instanceof Error ? error.name : 'UnknownError',
    };
  }
}
@Injectable()
export class RedisService implements OnModuleInit, OnModuleDestroy {
  private readonly logger = new Logger(RedisService.name);
  private client: Redis;

  constructor() {
    // 从环境变量获取Redis配置，如果没有则使用默认值
    const redisHost = env().REDIS_HOST || 'localhost';
    const redisPort = parseInt(env().REDIS_PORT || '6379', 10);
    const redisPassword = env().REDIS_PASSWORD || '';
    const redisDb = parseInt(env().REDIS_DB || '0', 10);

    this.client = new Redis({
      host: redisHost,
      port: redisPort,
      password: redisPassword,
      db: redisDb,
    });
  }

  async onModuleInit() {
    try {
      await this.client.ping();
    } catch (error: unknown) {
      const redisError = handleRedisError(error);
      this.logger.error(`Failed to connect to Redis: ${redisError.message}`);
    }
  }

  async onModuleDestroy() {
    await this.client.quit();
  }

  /**
   * 设置键值对，带过期时间
   * @param key 键
   * @param value 值
   * @param ttlSeconds 过期时间（秒）
   */
  async set(key: string, value: string, ttlSeconds?: number): Promise<void> {
    try {
      if (ttlSeconds) {
        await this.client.set(key, value, 'EX', ttlSeconds);
      } else {
        await this.client.set(key, value);
      }
    } catch (error: unknown) {
      const redisError = handleRedisError(error);
      this.logger.error(`Redis set error: ${redisError.message}`);
      throw redisError;
    }
  }

  /**
   * 获取键值
   * @param key 键
   * @returns 值或null
   */
  async get(key: string): Promise<string | null> {
    try {
      return await this.client.get(key);
    } catch (error: unknown) {
      const redisError = handleRedisError(error);
      this.logger.error(`Redis get error: ${redisError.message}`);
      throw redisError;
    }
  }

  /**
   * 删除键
   * @param key 键
   */
  async del(key: string): Promise<void> {
    try {
      await this.client.del(key);
    } catch (error: unknown) {
      const redisError = handleRedisError(error);
      this.logger.error(`Redis del error: ${redisError.message}`);
      throw redisError;
    }
  }

  /**
   * 设置哈希表字段
   * @param key 哈希表键
   * @param field 字段
   * @param value 值
   */
  async hset(key: string, field: string, value: string): Promise<void> {
    try {
      await this.client.hset(key, field, value);
    } catch (error: unknown) {
      const redisError = handleRedisError(error);
      this.logger.error(`Redis hset error: ${redisError.message}`);
      throw redisError;
    }
  }

  /**
   * 获取哈希表字段
   * @param key 哈希表键
   * @param field 字段
   * @returns 值或null
   */
  async hget(key: string, field: string): Promise<string | null> {
    try {
      return await this.client.hget(key, field);
    } catch (error: unknown) {
      const redisError = handleRedisError(error);
      this.logger.error(`Redis hget error: ${redisError.message}`);
      throw redisError;
    }
  }

  /**
   * 获取哈希表所有字段
   * @param key 哈希表键
   * @returns 字段值对象
   */
  async hgetall(key: string): Promise<Record<string, string>> {
    try {
      return await this.client.hgetall(key);
    } catch (error: unknown) {
      const redisError = handleRedisError(error);
      this.logger.error(`Redis hgetall error: ${redisError.message}`);
      throw redisError;
    }
  }

  /**
   * 设置哈希表多个字段
   * @param key 哈希表键
   * @param data 字段值对象
   */
  async hmset(key: string, data: Record<string, string>): Promise<void> {
    try {
      await this.client.hmset(key, data);
    } catch (error: unknown) {
      const redisError = handleRedisError(error);
      this.logger.error(`Redis hmset error: ${redisError.message}`);
      throw redisError;
    }
  }

  /**
   * 设置键的过期时间
   * @param key 键
   * @param ttlSeconds 过期时间（秒）
   */
  async expire(key: string, ttlSeconds: number): Promise<void> {
    try {
      await this.client.expire(key, ttlSeconds);
    } catch (error: unknown) {
      const redisError = handleRedisError(error);
      this.logger.error(`Redis expire error: ${redisError.message}`);
      throw redisError;
    }
  }

  /**
   * 获取所有匹配的键
   * @param pattern 匹配模式
   * @returns 键数组
   */
  async keys(pattern: string): Promise<string[]> {
    try {
      return await this.client.keys(pattern);
    } catch (error: unknown) {
      const redisError = handleRedisError(error);
      this.logger.error(`Redis keys error: ${redisError.message}`);
      throw redisError;
    }
  }

  /**
   * 检查键是否存在
   * @param key 键
   * @returns 是否存在
   */
  async exists(key: string): Promise<boolean> {
    try {
      const result = await this.client.exists(key);
      return result === 1;
    } catch (error: unknown) {
      const redisError = handleRedisError(error);
      this.logger.error(`Redis exists error: ${redisError.message}`);
      throw redisError;
    }
  }

  /**
   * 获取Redis客户端实例
   * @returns Redis客户端
   */
  getClient(): Redis {
    return this.client;
  }
}
