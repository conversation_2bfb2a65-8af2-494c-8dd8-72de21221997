# Node Metrics Module

节点性能统计模块，用于收集、分析和管理去中心化网络中节点的性能数据。

## 功能特性

- **多时间窗口统计**：支持 5分钟、1小时、24小时和生命周期四个时间窗口
- **实时性能监控**：实时收集任务执行结果并计算性能指标
- **智能数据管理**：自动清理过期数据，优化存储空间
- **高性能查询**：支持单节点和批量节点性能查询
- **网络整体分析**：提供网络级别的性能统计和分析
- **事件驱动架构**：基于事件的松耦合设计

## 核心组件

### NodeMetricsService
核心业务服务，提供：
- 任务执行结果记录
- 性能数据查询
- 性能评分计算
- 阈值检查

### NodeMetricsRepository
数据访问层，负责：
- 数据库操作
- 性能统计更新
- 数据清理
- 复杂查询

### NodeMetricsScheduler
定时任务调度器，执行：
- 每30秒更新性能统计
- 每小时清理过期数据
- 每5分钟记录系统状态

## 使用方法

### 1. 导入模块

```typescript
import { NodeMetricsModule } from '@saito/node-metrics';

@Module({
  imports: [NodeMetricsModule],
  // ...
})
export class AppModule {}
```

### 2. 记录任务执行结果

```typescript
import { NodeMetricsService, TaskExecutionResult } from '@saito/node-metrics';

@Injectable()
export class TaskService {
  constructor(private nodeMetricsService: NodeMetricsService) {}

  async completeTask(taskId: string, deviceId: string, result: any) {
    // 记录任务执行结果
    const executionResult: TaskExecutionResult = {
      taskId,
      deviceId,
      model: 'gpt-3.5-turbo',
      status: 'completed',
      responseTime: 2.5,
      totalTokens: 150,
      tokensPerSecond: 60,
      totalDuration: 2.5,
      timestamp: new Date()
    };

    await this.nodeMetricsService.recordTaskExecution(executionResult);
  }
}
```

### 3. 查询性能数据

```typescript
// 获取单个节点性能
const performance = await nodeMetricsService.getNodePerformance({
  deviceId: 'device-123',
  timeWindow: '5min'
});

// 获取网络整体性能
const networkPerformance = await nodeMetricsService.getNetworkPerformance();

// 检查节点是否满足性能阈值
const meetsThresholds = await nodeMetricsService.isDeviceMeetingThresholds(
  'device-123',
  0.9,  // 最小成功率 90%
  60,   // 最大响应时间 60秒
  10    // 最小TPS 10
);
```

## 数据模型

### 时间窗口
- `5min`: 5分钟滑动窗口
- `1h`: 1小时滑动窗口  
- `24h`: 24小时滑动窗口
- `lifetime`: 生命周期统计

### 性能指标
- **成功率**: 成功任务数 / 总任务数
- **响应时间**: 平均、最小、最大响应时间
- **TPS**: 每秒处理的token数量
- **任务统计**: 总任务数、成功数、失败数

## 配置选项

### 环境变量
```bash
# 性能阈值
AIO_MIN_SUCCESS_RATE=0.9
AIO_MAX_RESPONSE_TIME=60
AIO_MIN_TOKENS_PER_SECOND=10

# 统计更新间隔
AIO_METRICS_UPDATE_INTERVAL=30
```

### 数据保留策略
- 5分钟数据：保留1天
- 1小时数据：保留7天
- 24小时数据：保留30天
- 生命周期数据：永久保留

## 性能优化

- 使用内存缓存减少数据库访问
- 批量更新提高写入效率
- 索引优化加速查询
- 定期清理过期数据

## 监控和告警

模块提供详细的日志记录和系统状态监控，支持：
- 性能统计更新日志
- 数据清理日志
- 网络状态定期报告
- 错误和异常监控
