import { Injectable, Logger } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';
import { NodeMetricsService } from './node-metrics.service';

/**
 * 节点性能统计定时任务调度器
 * 负责定期更新性能统计数据和清理过期数据
 */
@Injectable()
export class NodeMetricsScheduler {
  private readonly logger = new Logger(NodeMetricsScheduler.name);

  constructor(private readonly nodeMetricsService: NodeMetricsService) {}

  /**
   * 每30秒更新一次性能统计数据
   * 处理缓存中的任务执行结果并更新到数据库
   */
  @Cron('*/30 * * * * *', {
    name: 'updatePerformanceStats',
    timeZone: 'UTC'
  })
  async handlePerformanceStatsUpdate(): Promise<void> {
    try {
      this.logger.debug('Starting scheduled performance stats update');
      await this.nodeMetricsService.updatePerformanceStats();
      this.logger.debug('Scheduled performance stats update completed');
    } catch (error) {
      this.logger.error('Failed to update performance stats in scheduled job', error);
    }
  }

  /**
   * 每小时清理一次过期的性能统计数据
   * 根据配置的保留期限删除旧数据
   */
  @Cron(CronExpression.EVERY_HOUR, {
    name: 'cleanupExpiredMetrics',
    timeZone: 'UTC'
  })
  async handleExpiredMetricsCleanup(): Promise<void> {
    try {
      this.logger.debug('Starting scheduled expired metrics cleanup');
      await this.nodeMetricsService.cleanupExpiredMetrics();
      this.logger.debug('Scheduled expired metrics cleanup completed');
    } catch (error) {
      this.logger.error('Failed to cleanup expired metrics in scheduled job', error);
    }
  }

  /**
   * 每5分钟记录一次系统状态日志
   * 用于监控和调试
   */
  @Cron('0 */5 * * * *', {
    name: 'logSystemStatus',
    timeZone: 'UTC'
  })
  async handleSystemStatusLogging(): Promise<void> {
    try {
      const networkPerformance = await this.nodeMetricsService.getNetworkPerformance();
      
      this.logger.log(`Network Status - Total Nodes: ${networkPerformance.totalNodes}, ` +
        `Active Nodes: ${networkPerformance.activeNodes}, ` +
        `5min Avg Success Rate: ${(networkPerformance.averageMetrics['5min']?.avgSuccessRate || 0).toFixed(3)}, ` +
        `5min Avg Response Time: ${(networkPerformance.averageMetrics['5min']?.avgResponseTime || 0).toFixed(2)}s`);
    } catch (error) {
      this.logger.error('Failed to log system status', error);
    }
  }
}
