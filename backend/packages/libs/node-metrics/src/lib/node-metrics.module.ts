import { Module } from '@nestjs/common';
import { ScheduleModule } from '@nestjs/schedule';
import { EventEmitterModule } from '@nestjs/event-emitter';
import { PersistentModule } from '@saito/persistent';
import { NodeMetricsService } from './node-metrics.service';
import { NodeMetricsRepository } from './node-metrics.repository';
import { NodeMetricsScheduler } from './node-metrics.scheduler';

/**
 * 节点性能统计模块
 * 提供节点性能数据收集、统计和查询功能
 */
@Module({
  imports: [
    // 导入持久化模块以访问数据库
    PersistentModule,
    
    // 导入调度模块以支持定时任务
    ScheduleModule.forRoot(),
    
    // 导入事件发射器模块以支持事件驱动
    EventEmitterModule.forRoot({
      // 设置最大监听器数量
      maxListeners: 20,
      // 启用通配符
      wildcard: false,
      // 分隔符
      delimiter: '.',
      // 启用新监听器警告
      newListener: false,
      // 启用移除监听器警告
      removeListener: false,
      // 最大监听器数量警告
      verboseMemoryLeak: false
    })
  ],
  providers: [
    // 核心服务
    NodeMetricsService,
    
    // 数据访问层
    NodeMetricsRepository,
    
    // 定时任务调度器
    NodeMetricsScheduler
  ],
  exports: [
    // 导出核心服务供其他模块使用
    NodeMetricsService,
    
    // 导出数据访问层供需要直接数据库访问的模块使用
    NodeMetricsRepository
  ]
})
export class NodeMetricsModule {}
