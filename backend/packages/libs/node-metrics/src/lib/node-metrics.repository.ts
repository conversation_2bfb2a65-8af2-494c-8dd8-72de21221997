import { Injectable, Logger } from '@nestjs/common';
import { PersistentService } from '@saito/persistent';
import { sql } from 'slonik';
import {
  NodePerformanceMetrics,
  NodePerformanceMetricsDb,
  NodePerformanceMetricsDbSchema,
  TimeWindow,
  PerformanceStatsUpdate,
  NetworkPerformanceMetrics
} from '@saito/models';

/**
 * 节点性能统计数据访问层
 * 负责处理与 node_performance_metrics 表的所有数据库交互
 */
@Injectable()
export class NodeMetricsRepository {
  private readonly logger = new Logger(NodeMetricsRepository.name);

  constructor(private readonly persistentService: PersistentService) {}

  /**
   * 创建或更新节点性能统计记录
   */
  async upsertPerformanceMetrics(update: PerformanceStatsUpdate): Promise<NodePerformanceMetrics> {
    return await this.persistentService.pgPool.transaction(async (conn) => {
      const windowEnd = this.calculateWindowEnd(update.windowStart, update.timeWindow);
      
      const result = await conn.query(
        sql.type(NodePerformanceMetricsDbSchema)`
          INSERT INTO saito_gateway.node_performance_metrics (
            device_id,
            time_window,
            window_start,
            window_end,
            total_tasks,
            successful_tasks,
            failed_tasks,
            avg_response_time,
            min_response_time,
            max_response_time,
            total_tokens,
            tokens_per_second,
            total_duration,
            updated_at
          ) VALUES (
            ${update.deviceId},
            ${update.timeWindow},
            ${update.windowStart.toISOString()},
            ${windowEnd?.toISOString() || null},
            ${update.stats.totalTasks},
            ${update.stats.successfulTasks},
            ${update.stats.failedTasks},
            ${update.stats.avgResponseTime || 0},
            ${update.stats.minResponseTime || 0},
            ${update.stats.maxResponseTime || 0},
            ${update.stats.totalTokens || 0},
            ${update.stats.tokensPerSecond || 0},
            ${update.stats.totalDuration || 0},
            NOW()
          )
          ON CONFLICT (device_id, time_window, window_start)
          DO UPDATE SET
            window_end = EXCLUDED.window_end,
            total_tasks = EXCLUDED.total_tasks,
            successful_tasks = EXCLUDED.successful_tasks,
            failed_tasks = EXCLUDED.failed_tasks,
            avg_response_time = EXCLUDED.avg_response_time,
            min_response_time = EXCLUDED.min_response_time,
            max_response_time = EXCLUDED.max_response_time,
            total_tokens = EXCLUDED.total_tokens,
            tokens_per_second = EXCLUDED.tokens_per_second,
            total_duration = EXCLUDED.total_duration,
            updated_at = NOW()
          RETURNING *
        `
      );

      if (result.rows.length === 0) {
        throw new Error('Failed to upsert performance metrics');
      }

      return this.mapDbToModel(result.rows[0]);
    });
  }

  /**
   * 获取指定设备在指定时间窗口的性能统计
   */
  async getNodePerformance(
    deviceId: string,
    timeWindow: TimeWindow,
    startTime?: Date,
    endTime?: Date
  ): Promise<NodePerformanceMetrics | null> {
    let baseQuery = sql.type(NodePerformanceMetricsDbSchema)`
      SELECT * FROM saito_gateway.node_performance_metrics
      WHERE device_id = ${deviceId} AND time_window = ${timeWindow}
    `;

    // 添加时间条件
    if (startTime && endTime) {
      baseQuery = sql.type(NodePerformanceMetricsDbSchema)`
        SELECT * FROM saito_gateway.node_performance_metrics
        WHERE device_id = ${deviceId}
          AND time_window = ${timeWindow}
          AND window_start >= ${startTime.toISOString()}
          AND window_start <= ${endTime.toISOString()}
        ORDER BY window_start DESC LIMIT 1
      `;
    } else if (startTime) {
      baseQuery = sql.type(NodePerformanceMetricsDbSchema)`
        SELECT * FROM saito_gateway.node_performance_metrics
        WHERE device_id = ${deviceId}
          AND time_window = ${timeWindow}
          AND window_start >= ${startTime.toISOString()}
        ORDER BY window_start DESC LIMIT 1
      `;
    } else if (endTime) {
      baseQuery = sql.type(NodePerformanceMetricsDbSchema)`
        SELECT * FROM saito_gateway.node_performance_metrics
        WHERE device_id = ${deviceId}
          AND time_window = ${timeWindow}
          AND window_start <= ${endTime.toISOString()}
        ORDER BY window_start DESC LIMIT 1
      `;
    } else {
      baseQuery = sql.type(NodePerformanceMetricsDbSchema)`
        SELECT * FROM saito_gateway.node_performance_metrics
        WHERE device_id = ${deviceId} AND time_window = ${timeWindow}
        ORDER BY window_start DESC LIMIT 1
      `;
    }

    const result = await this.persistentService.pgPool.query(baseQuery);

    if (result.rows.length === 0) {
      return null;
    }

    return this.mapDbToModel(result.rows[0]);
  }

  /**
   * 批量获取多个设备的性能统计
   */
  async getBatchNodePerformance(
    deviceIds: string[],
    timeWindow: TimeWindow,
    startTime?: Date,
    endTime?: Date
  ): Promise<Map<string, NodePerformanceMetrics>> {
    if (deviceIds.length === 0) {
      return new Map();
    }

    let baseQuery;

    // 根据时间条件构建不同的查询
    if (startTime && endTime) {
      baseQuery = sql.type(NodePerformanceMetricsDbSchema)`
        SELECT DISTINCT ON (device_id) *
        FROM saito_gateway.node_performance_metrics
        WHERE device_id = ANY(${sql.array(deviceIds, 'text')})
          AND time_window = ${timeWindow}
          AND window_start >= ${startTime.toISOString()}
          AND window_start <= ${endTime.toISOString()}
        ORDER BY device_id, window_start DESC
      `;
    } else if (startTime) {
      baseQuery = sql.type(NodePerformanceMetricsDbSchema)`
        SELECT DISTINCT ON (device_id) *
        FROM saito_gateway.node_performance_metrics
        WHERE device_id = ANY(${sql.array(deviceIds, 'text')})
          AND time_window = ${timeWindow}
          AND window_start >= ${startTime.toISOString()}
        ORDER BY device_id, window_start DESC
      `;
    } else if (endTime) {
      baseQuery = sql.type(NodePerformanceMetricsDbSchema)`
        SELECT DISTINCT ON (device_id) *
        FROM saito_gateway.node_performance_metrics
        WHERE device_id = ANY(${sql.array(deviceIds, 'text')})
          AND time_window = ${timeWindow}
          AND window_start <= ${endTime.toISOString()}
        ORDER BY device_id, window_start DESC
      `;
    } else {
      baseQuery = sql.type(NodePerformanceMetricsDbSchema)`
        SELECT DISTINCT ON (device_id) *
        FROM saito_gateway.node_performance_metrics
        WHERE device_id = ANY(${sql.array(deviceIds, 'text')})
          AND time_window = ${timeWindow}
        ORDER BY device_id, window_start DESC
      `;
    }

    const result = await this.persistentService.pgPool.query(baseQuery);

    const metricsMap = new Map<string, NodePerformanceMetrics>();
    for (const row of result.rows) {
      const metrics = this.mapDbToModel(row);
      metricsMap.set(metrics.deviceId, metrics);
    }

    return metricsMap;
  }

  /**
   * 获取网络整体性能统计
   */
  async getNetworkPerformance(timeWindow: TimeWindow): Promise<NetworkPerformanceMetrics> {
    const result = await this.persistentService.pgPool.query(sql.unsafe`
      SELECT
        COUNT(DISTINCT device_id) as active_nodes,
        COALESCE(SUM(total_tasks), 0) as total_tasks,
        COALESCE(SUM(successful_tasks), 0) as successful_tasks,
        COALESCE(SUM(failed_tasks), 0) as failed_tasks,
        COALESCE(AVG(avg_response_time), 0) as avg_response_time,
        COALESCE(SUM(total_tokens), 0) as total_tokens,
        COALESCE(AVG(tokens_per_second), 0) as avg_tokens_per_second
      FROM saito_gateway.node_performance_metrics
      WHERE time_window = ${timeWindow}
        AND window_start >= NOW() - INTERVAL '1 hour'
    `);

    const row = result.rows[0];
    return {
      timeWindow,
      totalNodes: parseInt(row.active_nodes) || 0,
      activeNodes: parseInt(row.active_nodes) || 0,
      totalTasks: parseInt(row.total_tasks) || 0,
      totalSuccessfulTasks: parseInt(row.successful_tasks) || 0,
      totalFailedTasks: parseInt(row.failed_tasks) || 0,
      avgSuccessRate: row.total_tasks > 0 ? row.successful_tasks / row.total_tasks : 0,
      avgResponseTime: parseFloat(row.avg_response_time) || 0,
      avgTokensPerSecond: parseFloat(row.avg_tokens_per_second) || 0,
      calculatedAt: new Date()
    };
  }

  /**
   * 清理过期的性能统计数据
   */
  async cleanupExpiredMetrics(): Promise<number> {
    let totalDeleted = 0;

    // 清理不同时间窗口的过期数据
    const cleanupRules = [
      { timeWindow: '5min' as TimeWindow, retentionDays: 1 },
      { timeWindow: '1h' as TimeWindow, retentionDays: 7 },
      { timeWindow: '24h' as TimeWindow, retentionDays: 30 }
    ];

    for (const rule of cleanupRules) {
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - rule.retentionDays);

      const result = await this.persistentService.pgPool.query(sql.unsafe`
        DELETE FROM saito_gateway.node_performance_metrics
        WHERE time_window = ${rule.timeWindow}
          AND window_start < ${cutoffDate.toISOString()}
      `);

      const deleted = result.rowCount || 0;
      totalDeleted += deleted;
      
      if (deleted > 0) {
        this.logger.debug(`清理了 ${deleted} 条 ${rule.timeWindow} 时间窗口的过期数据`);
      }
    }

    return totalDeleted;
  }

  /**
   * 计算时间窗口结束时间
   */
  private calculateWindowEnd(windowStart: Date, timeWindow: TimeWindow): Date | null {
    const start = new Date(windowStart);
    
    switch (timeWindow) {
      case '5min':
        return new Date(start.getTime() + 5 * 60 * 1000);
      case '1h':
        return new Date(start.getTime() + 60 * 60 * 1000);
      case '24h':
        return new Date(start.getTime() + 24 * 60 * 60 * 1000);
      case 'lifetime':
        return null; // 生命周期没有结束时间
      default:
        return null;
    }
  }

  /**
   * 将数据库行映射为业务模型
   */
  private mapDbToModel(row: NodePerformanceMetricsDb): NodePerformanceMetrics {
    return {
      id: row.id,
      deviceId: row.device_id,
      timeWindow: row.time_window as TimeWindow,
      windowStart: new Date(row.window_start),
      windowEnd: row.window_end ? new Date(row.window_end) : undefined,
      totalTasks: row.total_tasks,
      successfulTasks: row.successful_tasks,
      failedTasks: row.failed_tasks,
      successRate: row.total_tasks > 0 ? row.successful_tasks / row.total_tasks : 0,
      avgResponseTime: row.avg_response_time || 0,
      minResponseTime: row.min_response_time || 0,
      maxResponseTime: row.max_response_time || 0,
      totalTokens: row.total_tokens || 0,
      tokensPerSecond: row.tokens_per_second || 0,
      totalDuration: row.total_duration || 0,
      createdAt: new Date(row.created_at),
      updatedAt: new Date(row.updated_at)
    };
  }
}
