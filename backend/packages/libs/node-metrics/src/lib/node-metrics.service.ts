import { Injectable, Logger } from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';
import {
  TaskExecutionResult,
  NodePerformanceMetrics,
  NetworkPerformanceMetrics,
  TimeWindow,
  NodePerformanceQuery,
  BatchNodePerformanceQuery,
  NodePerformanceResponse,
  NetworkPerformanceResponse,
  PerformanceStatsUpdate
} from '@saito/models';
import { NodeMetricsRepository } from './node-metrics.repository';

/**
 * 节点性能统计服务
 * 负责收集、计算和管理节点性能数据
 */
@Injectable()
export class NodeMetricsService {
  private readonly logger = new Logger(NodeMetricsService.name);
  
  // 内存缓存，用于临时存储任务执行结果
  private readonly taskResultsCache = new Map<string, TaskExecutionResult[]>();
  
  // 时间窗口配置
  private readonly timeWindows: TimeWindow[] = ['5min', '1h', '24h', 'lifetime'];

  constructor(
    private readonly repository: NodeMetricsRepository,
    private readonly eventEmitter: EventEmitter2
  ) {}

  /**
   * 记录任务执行结果
   * 这是性能统计的入口点，由任务完成时调用
   */
  async recordTaskExecution(result: TaskExecutionResult): Promise<void> {
    try {
      this.logger.debug(`Recording task execution: ${result.taskId} for device ${result.deviceId}`);
      
      // 将结果添加到缓存中
      const deviceResults = this.taskResultsCache.get(result.deviceId) || [];
      deviceResults.push(result);
      this.taskResultsCache.set(result.deviceId, deviceResults);
      
      // 发出事件，通知其他模块
      this.eventEmitter.emit('task.execution.recorded', result);
      
      this.logger.debug(`Task execution recorded successfully: ${result.taskId}`);
    } catch (error) {
      this.logger.error(`Failed to record task execution: ${result.taskId}`, error);
      throw error;
    }
  }

  /**
   * 获取节点性能数据
   */
  async getNodePerformance(query: NodePerformanceQuery): Promise<NodePerformanceResponse> {
    try {
      const windows: Record<string, NodePerformanceMetrics | undefined> = {};
      
      // 如果指定了时间窗口，只查询该窗口
      const windowsToQuery = query.timeWindow ? [query.timeWindow] : this.timeWindows;
      
      for (const window of windowsToQuery) {
        const metrics = await this.repository.getNodePerformance(
          query.deviceId,
          window,
          query.startTime,
          query.endTime
        );
        windows[window] = metrics || undefined;
      }

      return {
        deviceId: query.deviceId,
        windows,
        lastUpdated: new Date()
      };
    } catch (error) {
      this.logger.error(`Failed to get node performance for device ${query.deviceId}`, error);
      throw error;
    }
  }

  /**
   * 批量获取多个节点的性能数据
   */
  async getBatchNodePerformance(query: BatchNodePerformanceQuery): Promise<NodePerformanceResponse[]> {
    try {
      const metricsMap = await this.repository.getBatchNodePerformance(
        query.deviceIds,
        query.timeWindow,
        query.startTime,
        query.endTime
      );

      return query.deviceIds.map(deviceId => ({
        deviceId,
        windows: { [query.timeWindow]: metricsMap.get(deviceId) },
        lastUpdated: new Date()
      }));
    } catch (error) {
      this.logger.error(`Failed to get batch node performance`, error);
      throw error;
    }
  }

  /**
   * 获取网络整体性能统计
   */
  async getNetworkPerformance(): Promise<NetworkPerformanceResponse> {
    try {
      const averageMetrics: Record<string, NetworkPerformanceMetrics | undefined> = {};
      const topPerformers: Record<string, Array<{
        deviceId: string;
        score: number;
        metrics: NodePerformanceMetrics;
      }>> = {};

      for (const window of this.timeWindows) {
        // 获取网络性能指标
        const networkMetrics = await this.repository.getNetworkPerformance(window);
        averageMetrics[window] = networkMetrics;

        // 暂时设置空的顶级表现者数组，后续可以实现
        topPerformers[window] = [];
      }

      // 计算总节点数和活跃节点数（使用最新的5分钟窗口数据）
      const recentMetrics = averageMetrics['5min'];
      
      return {
        totalNodes: recentMetrics?.totalNodes || 0,
        activeNodes: recentMetrics?.activeNodes || 0,
        averageMetrics,
        topPerformers
      };
    } catch (error) {
      this.logger.error('Failed to get network performance', error);
      throw error;
    }
  }

  /**
   * 更新性能统计数据
   * 由定时任务调用，处理缓存中的任务执行结果
   */
  async updatePerformanceStats(): Promise<void> {
    try {
      this.logger.debug('Starting performance stats update');
      
      const updatePromises: Promise<void>[] = [];
      
      // 处理每个设备的缓存数据
      for (const [deviceId, results] of this.taskResultsCache.entries()) {
        if (results.length === 0) continue;
        
        // 为每个时间窗口更新统计数据
        for (const timeWindow of this.timeWindows) {
          updatePromises.push(this.updateDeviceWindowStats(deviceId, timeWindow, results));
        }
      }
      
      // 等待所有更新完成
      await Promise.all(updatePromises);
      
      // 清空缓存
      this.taskResultsCache.clear();
      
      this.logger.debug('Performance stats update completed');
    } catch (error) {
      this.logger.error('Failed to update performance stats', error);
      throw error;
    }
  }

  /**
   * 清理过期的性能统计数据
   */
  async cleanupExpiredMetrics(): Promise<void> {
    try {
      const deletedCount = await this.repository.cleanupExpiredMetrics();
      if (deletedCount > 0) {
        this.logger.log(`Cleaned up ${deletedCount} expired metrics records`);
      }
    } catch (error) {
      this.logger.error('Failed to cleanup expired metrics', error);
      throw error;
    }
  }

  /**
   * 获取设备的实时性能评分
   * 用于节点选择算法
   */
  async getDevicePerformanceScore(deviceId: string, timeWindow: TimeWindow = '5min'): Promise<number> {
    try {
      const metrics = await this.repository.getNodePerformance(deviceId, timeWindow);
      if (!metrics || metrics.totalTasks === 0) {
        return 0; // 没有数据时返回0分
      }

      // 计算综合评分 (0-100)
      const successRateScore = metrics.successRate * 40; // 成功率权重40%
      const responseTimeScore = metrics.avgResponseTime ? 
        Math.max(0, 30 - (metrics.avgResponseTime / 2)) : 0; // 响应时间权重30%
      const tpsScore = metrics.tokensPerSecond ? 
        Math.min(30, metrics.tokensPerSecond) : 0; // TPS权重30%

      return Math.min(100, successRateScore + responseTimeScore + tpsScore);
    } catch (error) {
      this.logger.error(`Failed to get performance score for device ${deviceId}`, error);
      return 0;
    }
  }

  /**
   * 检查设备是否满足性能阈值
   */
  async isDeviceMeetingThresholds(
    deviceId: string,
    minSuccessRate: number = 0.9,
    maxResponseTime: number = 60,
    minTokensPerSecond: number = 10,
    timeWindow: TimeWindow = '5min'
  ): Promise<boolean> {
    try {
      const metrics = await this.repository.getNodePerformance(deviceId, timeWindow);
      if (!metrics || metrics.totalTasks === 0) {
        return true; // 没有历史数据时允许尝试
      }

      const meetsSuccessRate = metrics.successRate >= minSuccessRate;
      const meetsResponseTime = !metrics.avgResponseTime || metrics.avgResponseTime <= maxResponseTime;
      const meetsTps = !metrics.tokensPerSecond || metrics.tokensPerSecond >= minTokensPerSecond;

      return meetsSuccessRate && meetsResponseTime && meetsTps;
    } catch (error) {
      this.logger.error(`Failed to check thresholds for device ${deviceId}`, error);
      return false;
    }
  }

  /**
   * 更新指定设备在指定时间窗口的统计数据
   */
  private async updateDeviceWindowStats(
    deviceId: string,
    timeWindow: TimeWindow,
    results: TaskExecutionResult[]
  ): Promise<void> {
    try {
      const windowStart = this.calculateWindowStart(new Date(), timeWindow);
      const windowResults = this.filterResultsByWindow(results, windowStart, timeWindow);
      
      if (windowResults.length === 0) return;

      // 计算统计数据
      const stats = this.calculateStats(windowResults);
      
      // 创建更新请求
      const update: PerformanceStatsUpdate = {
        deviceId,
        timeWindow,
        windowStart,
        stats
      };

      // 更新数据库
      await this.repository.upsertPerformanceMetrics(update);
      
      this.logger.debug(`Updated ${timeWindow} stats for device ${deviceId}: ${windowResults.length} results`);
    } catch (error) {
      this.logger.error(`Failed to update ${timeWindow} stats for device ${deviceId}`, error);
    }
  }

  /**
   * 计算时间窗口的开始时间
   */
  private calculateWindowStart(now: Date, timeWindow: TimeWindow): Date {
    switch (timeWindow) {
      case '5min':
        // 对齐到5分钟边界
        const minutes = Math.floor(now.getMinutes() / 5) * 5;
        return new Date(now.getFullYear(), now.getMonth(), now.getDate(), now.getHours(), minutes, 0, 0);
      case '1h':
        // 对齐到小时边界
        return new Date(now.getFullYear(), now.getMonth(), now.getDate(), now.getHours(), 0, 0, 0);
      case '24h':
        // 对齐到天边界
        return new Date(now.getFullYear(), now.getMonth(), now.getDate(), 0, 0, 0, 0);
      case 'lifetime':
        // 生命周期从设备第一次记录开始
        return new Date(0);
      default:
        return now;
    }
  }

  /**
   * 根据时间窗口过滤任务执行结果
   */
  private filterResultsByWindow(
    results: TaskExecutionResult[],
    windowStart: Date,
    timeWindow: TimeWindow
  ): TaskExecutionResult[] {
    if (timeWindow === 'lifetime') {
      return results; // 生命周期包含所有结果
    }

    const windowEnd = this.calculateWindowEnd(windowStart, timeWindow);
    return results.filter(result => 
      result.timestamp >= windowStart && result.timestamp < windowEnd
    );
  }

  /**
   * 计算时间窗口的结束时间
   */
  private calculateWindowEnd(windowStart: Date, timeWindow: TimeWindow): Date {
    switch (timeWindow) {
      case '5min':
        return new Date(windowStart.getTime() + 5 * 60 * 1000);
      case '1h':
        return new Date(windowStart.getTime() + 60 * 60 * 1000);
      case '24h':
        return new Date(windowStart.getTime() + 24 * 60 * 60 * 1000);
      case 'lifetime':
        return new Date(Date.now() + 365 * 24 * 60 * 60 * 1000); // 一年后
      default:
        return new Date(windowStart.getTime() + 60 * 60 * 1000);
    }
  }

  /**
   * 计算任务执行结果的统计数据
   */
  private calculateStats(results: TaskExecutionResult[]) {
    const totalTasks = results.length;
    const successfulTasks = results.filter(r => r.status === 'completed').length;
    const failedTasks = totalTasks - successfulTasks;
    
    const responseTimes = results.map(r => r.responseTime);
    const avgResponseTime = responseTimes.length > 0 ? 
      responseTimes.reduce((sum, time) => sum + time, 0) / responseTimes.length : undefined;
    const minResponseTime = responseTimes.length > 0 ? Math.min(...responseTimes) : undefined;
    const maxResponseTime = responseTimes.length > 0 ? Math.max(...responseTimes) : undefined;
    
    const totalTokens = results.reduce((sum, r) => sum + r.totalTokens, 0);
    const totalDuration = results.reduce((sum, r) => sum + r.totalDuration, 0);
    const tokensPerSecond = totalDuration > 0 ? totalTokens / totalDuration : undefined;

    return {
      totalTasks,
      successfulTasks,
      failedTasks,
      avgResponseTime,
      minResponseTime,
      maxResponseTime,
      totalTokens,
      tokensPerSecond,
      totalDuration
    };
  }

  /**
   * 计算节点性能评分
   * 基于成功率、响应时间和令牌处理速度的加权评分
   * @param deviceId 设备ID
   * @param timeWindow 时间窗口
   * @returns 性能评分 (0-100)，分数越高表示性能越好
   */
  async calculatePerformanceScore(deviceId: string, timeWindow: TimeWindow): Promise<number> {
    try {
      const metrics = await this.repository.getNodePerformance(deviceId, timeWindow);

      if (!metrics || metrics.totalTasks === 0) {
        // 没有历史数据时返回中等分数，给新节点机会
        return 50;
      }

      // 权重配置
      const weights = {
        successRate: 0.4,      // 成功率权重 40%
        responseTime: 0.3,     // 响应时间权重 30%
        tokensPerSecond: 0.3   // 令牌处理速度权重 30%
      };

      // 1. 成功率评分 (0-100)
      const successRateScore = metrics.successRate * 100;

      // 2. 响应时间评分 (0-100)
      // 响应时间越短分数越高，使用反比例函数
      // 假设理想响应时间为1秒(1000ms)，超过10秒(10000ms)得分接近0
      const idealResponseTime = 1000; // 1秒
      const maxResponseTime = 10000;  // 10秒
      const avgResponseTime = metrics.avgResponseTime || maxResponseTime;
      const responseTimeScore = Math.max(0, Math.min(100,
        100 * (1 - (avgResponseTime - idealResponseTime) / (maxResponseTime - idealResponseTime))
      ));

      // 3. 令牌处理速度评分 (0-100)
      // 假设理想速度为50 tokens/second，超过100 tokens/second得满分
      const idealTokensPerSecond = 50;
      const maxTokensPerSecond = 100;
      const tokensPerSecond = metrics.tokensPerSecond || 0;
      const tokensPerSecondScore = Math.min(100,
        (tokensPerSecond / idealTokensPerSecond) * 100
      );

      // 计算加权总分
      const totalScore =
        successRateScore * weights.successRate +
        responseTimeScore * weights.responseTime +
        tokensPerSecondScore * weights.tokensPerSecond;

      // 应用任务数量调整因子
      // 任务数量越多，评分越可靠，给予轻微加成
      const taskCountFactor = Math.min(1.1, 1 + (metrics.totalTasks / 1000) * 0.1);
      const adjustedScore = Math.min(100, totalScore * taskCountFactor);

      this.logger.debug(`Performance score for ${deviceId} (${timeWindow}): ${adjustedScore.toFixed(2)} ` +
        `(success: ${successRateScore.toFixed(1)}, response: ${responseTimeScore.toFixed(1)}, ` +
        `tokens: ${tokensPerSecondScore.toFixed(1)}, tasks: ${metrics.totalTasks})`);

      return adjustedScore;
    } catch (error) {
      this.logger.error(`Failed to calculate performance score for device ${deviceId}`, error);
      // 出错时返回默认分数
      return 50;
    }
  }
}
