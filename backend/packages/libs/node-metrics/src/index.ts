// 导出模块
export { NodeMetricsModule } from './lib/node-metrics.module';

// 导出服务
export { NodeMetricsService } from './lib/node-metrics.service';
export { NodeMetricsRepository } from './lib/node-metrics.repository';
export { NodeMetricsScheduler } from './lib/node-metrics.scheduler';

// 从 models 模块重新导出相关类型，方便使用
export {
  TimeWindow,
  TaskExecutionResult,
  NodePerformanceMetrics,
  NetworkPerformanceMetrics,
  NodePerformanceQuery,
  BatchNodePerformanceQuery,
  NodePerformanceResponse,
  NetworkPerformanceResponse,
  PerformanceStatsUpdate,
  NodePerformanceMetricsDb,
  
  // 导出 Zod 模式
  TimeWindowSchema,
  TaskExecutionResultSchema,
  NodePerformanceMetricsSchema,
  NetworkPerformanceMetricsSchema,
  NodePerformanceQuerySchema,
  BatchNodePerformanceQuerySchema,
  NodePerformanceResponseSchema,
  NetworkPerformanceResponseSchema,
  PerformanceStatsUpdateSchema,
  NodePerformanceMetricsDbSchema
} from '@saito/models';
