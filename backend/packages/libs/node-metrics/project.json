{"name": "node-metrics", "$schema": "../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "packages/libs/node-metrics/src", "projectType": "library", "targets": {"build": {"executor": "@nx/js:tsc", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/packages/libs/node-metrics", "main": "packages/libs/node-metrics/src/index.ts", "tsConfig": "packages/libs/node-metrics/tsconfig.lib.json", "assets": ["packages/libs/node-metrics/*.md"]}}, "lint": {"executor": "@nx/linter:eslint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["packages/libs/node-metrics/**/*.ts"]}}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "packages/libs/node-metrics/jest.config.ts", "passWithNoTests": true}, "configurations": {"ci": {"ci": true, "coverageReporters": ["text"]}}}}, "tags": ["scope:backend", "type:lib"]}