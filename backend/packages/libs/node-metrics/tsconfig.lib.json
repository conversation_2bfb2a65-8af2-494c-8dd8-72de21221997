{"extends": "../../../tsconfig.base.json", "compilerOptions": {"module": "commonjs", "forceConsistentCasingInFileNames": true, "strict": true, "noImplicitOverride": true, "noPropertyAccessFromIndexSignature": true, "noImplicitReturns": true, "noFallthroughCasesInSwitch": true, "declaration": true, "outDir": "../../../dist/out-tsc", "experimentalDecorators": true, "emitDecoratorMetadata": true, "skipLibCheck": true}, "include": ["src/**/*"], "exclude": ["jest.config.ts", "src/**/*.spec.ts", "src/**/*.test.ts"]}