{"name": "@saito/node-metrics", "version": "0.0.1", "description": "Node performance metrics collection and analysis module", "main": "./src/index.ts", "types": "./src/index.ts", "exports": {".": {"import": "./src/index.ts", "require": "./src/index.ts"}}, "scripts": {"build": "tsc", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix"}, "dependencies": {"@nestjs/common": "^10.0.0", "@nestjs/core": "^10.0.0", "@nestjs/schedule": "^4.0.0", "@nestjs/event-emitter": "^2.0.0", "@saito/models": "*", "@saito/persistent": "*", "kysely": "^0.27.0", "zod": "^3.22.0"}, "devDependencies": {"@types/node": "^20.0.0", "typescript": "^5.0.0", "jest": "^29.0.0", "@types/jest": "^29.0.0", "ts-jest": "^29.0.0", "eslint": "^8.0.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0"}, "peerDependencies": {"reflect-metadata": "^0.1.13", "rxjs": "^7.8.0"}, "keywords": ["<PERSON><PERSON><PERSON>", "node-metrics", "performance", "statistics", "saito"], "author": "Saito Team", "license": "MIT"}