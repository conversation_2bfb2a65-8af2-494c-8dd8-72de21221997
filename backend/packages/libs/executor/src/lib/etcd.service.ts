import { Injectable, Logger, OnModuleInit, OnModuleDestroy } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Etcd3 } from 'etcd3';



@Injectable()
export class EtcdService implements OnModuleInit, OnModuleDestroy {
  private readonly logger = new Logger(EtcdService.name);
  private etcdClient!: Etcd3;

  constructor(private configService: ConfigService) {}

  async onModuleInit() {
    try {
      const etcdHost = this.configService.get<string>('ETCD_HOST', 'localhost');
      const etcdPort = this.configService.get<number>('ETCD_PORT', 2379);
      
      this.etcdClient = new Etcd3({
        hosts: [`${etcdHost}:${etcdPort}`],
      });

      // 测试连接
      await this.etcdClient.get('test').string();
      this.logger.log(`Connected to etcd at ${etcdHost}:${etcdPort}`);
    } catch (error) {
      this.logger.error('Failed to connect to etcd:', error);
      throw error;
    }
  }

  async onModuleDestroy() {
    if (this.etcdClient) {
      this.etcdClient.close();
      this.logger.log('Etcd connection closed');
    }
  }

  /**
   * Get the etcd client instance for advanced operations
   */
  getClient(): Etcd3 {
    if (!this.etcdClient) {
      throw new Error('Etcd client not initialized');
    }
    return this.etcdClient;
  }

  /**
   * Generic method to get all keys with a prefix
   */
  async getAllWithPrefix(prefix: string): Promise<Record<string, string>> {
    try {
      return await this.etcdClient.getAll().prefix(prefix);
    } catch (error) {
      this.logger.error(`Failed to get keys with prefix ${prefix}:`, error);
      throw error;
    }
  }

  /**
   * Generic method to put a key-value pair
   */
  async put(key: string, value: string): Promise<void> {
    try {
      await this.etcdClient.put(key).value(value);
    } catch (error) {
      this.logger.error(`Failed to put key ${key}:`, error);
      throw error;
    }
  }

  async set(key: string, value: string): Promise<void> {
    try {
      await this.etcdClient.put(key).value(value);
    } catch (error) {
      this.logger.error(`Failed to set key ${key}:`, error);
      throw error;
    }
  }

  /**
   * Generic method to get a value by key
   */
  async get(key: string): Promise<string | null> {
    try {
      return await this.etcdClient.get(key).string();
    } catch (error) {
      this.logger.error(`Failed to get key ${key}:`, error);
      throw error;
    }
  }

  /**
   * Generic method to delete a key
   */
  async delete(key: string): Promise<void> {
    try {
      await this.etcdClient.delete().key(key);
    } catch (error) {
      this.logger.error(`Failed to delete key ${key}:`, error);
      throw error;
    }
  }

  /**
   * Generic method to delete all keys with a prefix
   */
  async deleteWithPrefix(prefix: string): Promise<void> {
    try {
      await this.etcdClient.delete().prefix(prefix);
    } catch (error) {
      this.logger.error(`Failed to delete keys with prefix ${prefix}:`, error);
      throw error;
    }
  }




}
