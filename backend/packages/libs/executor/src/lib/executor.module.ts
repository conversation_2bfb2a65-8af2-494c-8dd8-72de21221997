import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { EtcdService } from './etcd.service';
import { PublicKeyService } from './public-key.service';
import { KeyStatusService } from './key-status.service';
import { ExecutorKeyService } from './executor-key.service';
import { PersistentModule } from '@saito/persistent';

/**
 * Executor services module
 *
 * This module contains services related to executor functionality:
 * - EtcdService: etcd client for distributed configuration
 * - PublicKeyService: manages executor public key registrations
 * - KeyStatusService: tracks API key status and health
 * - ExecutorKeyService: manages executor-specific encrypted keys
 */
@Module({
  imports: [
    PersistentModule,
    ConfigModule,
  ],
  providers: [
    EtcdService,
    PublicKeyService,
    KeyStatusService,
    ExecutorKeyService,
  ],
  exports: [
    EtcdService,
    PublicKeyService,
    KeyStatusService,
    ExecutorKeyService,
  ],
})
export class ExecutorModule {}
