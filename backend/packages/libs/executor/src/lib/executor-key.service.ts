import { Injectable, Logger } from '@nestjs/common';
import { EtcdService } from './etcd.service';

// Local interface for executor encrypted API key data
export interface ExecutorEncryptedApiKeyData {
  uuid: string;
  encryptedKey: string;
  nonce: string;
  tag: string;
  ephemeralPubKey: string;
  status: 'active' | 'inactive' | 'revoked';
  createdAt: string;
}

export interface ExecutorKeysResult {
  keys: ExecutorEncryptedApiKeyData[];
  totalCount: number;
}

@Injectable()
export class ExecutorKeyService {
  private readonly logger = new Logger(ExecutorKeyService.name);

  constructor(private etcdService: EtcdService) {}

  /**
   * Get encrypted API keys for a specific scope (provider:region)
   * According to design document section 4.2
   * Path: /api-keys/{provider}/{region}/{uuid}
   */
  async getEncryptedKeysForScope(
    provider: string,
    region: string,
    executorId: string,
  ): Promise<ExecutorKeysResult> {
    try {
      this.logger.debug(
        `Getting encrypted keys for scope ${provider}:${region}, executor: ${executorId}`,
      );

      // Build etcd path prefix according to design document
      const keyPrefix = `/api-keys/${provider}/${region}/`;

      // Get all keys for this provider/region
      const kvs = await this.etcdService.getAllWithPrefix(keyPrefix);

      const encryptedKeys: ExecutorEncryptedApiKeyData[] = [];

      for (const [fullPath, jsonValue] of Object.entries(kvs)) {
        try {
          const keyData = JSON.parse(jsonValue as string);
          
          // Extract UUID from path (last part)
          const pathParts = fullPath.split('/');
          const uuid = pathParts[pathParts.length - 1];

          // Only include active keys
          if (keyData.status !== 'active') {
            this.logger.debug(`Skipping inactive key ${uuid} with status: ${keyData.status}`);
            continue;
          }

          // Check if key is targeted for this specific executor (if keyId is specified)
          if (keyData.keyId && keyData.keyId !== executorId) {
            this.logger.debug(`Skipping key ${uuid} - targeted for different executor: ${keyData.keyId}`);
            continue;
          }

          // Validate required encryption fields
          if (!keyData.encryptedKey || !keyData.nonce || !keyData.tag || !keyData.ephemeralPubKey) {
            this.logger.warn(`Skipping key ${uuid} - missing required encryption fields`);
            continue;
          }

          const encryptedKey: ExecutorEncryptedApiKeyData = {
            uuid,
            encryptedKey: keyData.encryptedKey,
            nonce: keyData.nonce,
            tag: keyData.tag,
            ephemeralPubKey: keyData.ephemeralPubKey,
            status: keyData.status,
            createdAt: keyData.createdAt || new Date().toISOString(),
          };

          encryptedKeys.push(encryptedKey);
          this.logger.debug(`Added encrypted key ${uuid} for scope ${provider}:${region}`);
        } catch (parseError) {
          this.logger.warn(
            `Failed to parse encrypted key data for ${fullPath}: ${parseError instanceof Error ? parseError.message : String(parseError)}`,
          );
        }
      }

      this.logger.log(
        `Found ${encryptedKeys.length} encrypted keys for scope ${provider}:${region}, executor: ${executorId}`,
      );

      return {
        keys: encryptedKeys,
        totalCount: encryptedKeys.length,
      };
    } catch (error) {
      this.logger.error(
        `Failed to get encrypted keys for scope ${provider}:${region}: ${error instanceof Error ? error.message : String(error)}`,
      );
      throw error;
    }
  }



  /**
   * Validate scope format
   */
  static validateScope(scope: string): { valid: boolean; provider?: string; region?: string; error?: string } {
    if (!scope) {
      return { valid: false, error: 'Scope is required' };
    }

    const parts = scope.split(':');
    if (parts.length !== 2) {
      return { valid: false, error: 'Scope must be in format provider:region' };
    }

    const [provider, region] = parts;
    
    if (!provider || !region) {
      return { valid: false, error: 'Both provider and region must be non-empty' };
    }

    // Validate provider and region format (alphanumeric and hyphens only)
    const validFormat = /^[a-zA-Z0-9-]+$/;
    if (!validFormat.test(provider) || !validFormat.test(region)) {
      return { valid: false, error: 'Provider and region must contain only alphanumeric characters and hyphens' };
    }

    return { valid: true, provider, region };
  }
}
