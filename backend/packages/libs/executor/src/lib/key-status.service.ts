import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Etcd3 } from 'etcd3';

export interface ApiKeyStatus {
  key: string;
  status: 'active' | 'rate_limited' | 'exhausted' | 'revoked';
  lastUsedAt: number;
  lastError?: string;
  note?: string;
}

export interface KeyStatusWithId extends ApiKeyStatus {
  keyId: string;
  provider: string;
}

export interface KeyStatusStatistics {
  total: number;
  active: number;
  rateLimited: number;
  exhausted: number;
  revoked: number;
}

export interface KeyStatusResult {
  keys: KeyStatusWithId[];
  statistics: KeyStatusStatistics;
}

@Injectable()
export class KeyStatusService {
  private readonly logger = new Logger(KeyStatusService.name);
  private etcdClient!: Etcd3;

  constructor(private configService: ConfigService) {
    this.initializeEtcdClient();
  }

  private initializeEtcdClient(): void {
    const etcdHost = this.configService.get<string>('ETCD_HOST', 'localhost');
    const etcdPort = this.configService.get<number>('ETCD_PORT', 2379);

    this.etcdClient = new Etcd3({
      hosts: [`${etcdHost}:${etcdPort}`],
    });

    this.logger.log(`KeyStatusService connected to etcd at ${etcdHost}:${etcdPort}`);
  }

  /**
   * Get all API key statuses for a provider
   * Implements the Gateway's ability to view all key statuses
   */
  async getAllKeyStatuses(provider: string, statusFilter?: string): Promise<KeyStatusResult> {
    try {
      this.logger.debug(`Getting all key statuses for provider: ${provider}`);

      const keyPrefix = `/keys/${provider}/`;
      const kvs = await this.etcdClient.getAll().prefix(keyPrefix);

      const keys: KeyStatusWithId[] = [];
      const statistics: KeyStatusStatistics = {
        total: 0,
        active: 0,
        rateLimited: 0,
        exhausted: 0,
        revoked: 0,
      };

      for (const [fullPath, jsonValue] of Object.entries(kvs)) {
        try {
          const keyStatus = JSON.parse(jsonValue as string) as ApiKeyStatus;
          
          // Extract keyId from path (last part)
          const pathParts = fullPath.split('/');
          const keyId = pathParts[pathParts.length - 1];

          // Apply status filter if provided
          if (statusFilter && keyStatus.status !== statusFilter) {
            continue;
          }

          const keyWithId: KeyStatusWithId = {
            keyId,
            provider,
            ...keyStatus,
          };

          keys.push(keyWithId);

          // Update statistics
          statistics.total++;
          switch (keyStatus.status) {
            case 'active':
              statistics.active++;
              break;
            case 'rate_limited':
              statistics.rateLimited++;
              break;
            case 'exhausted':
              statistics.exhausted++;
              break;
            case 'revoked':
              statistics.revoked++;
              break;
          }
        } catch (parseError) {
          this.logger.warn(`Failed to parse key status for ${fullPath}: ${parseError}`);
        }
      }

      this.logger.log(
        `Retrieved ${keys.length} key statuses for provider ${provider} (total in etcd: ${statistics.total})`,
      );

      return {
        keys: keys.sort((a, b) => b.lastUsedAt - a.lastUsedAt), // Sort by last used, newest first
        statistics,
      };
    } catch (error) {
      this.logger.error(
        `Failed to get key statuses for provider ${provider}: ${error instanceof Error ? error.message : String(error)}`,
      );
      throw error;
    }
  }

  /**
   * Get specific API key status
   */
  async getKeyStatus(provider: string, keyId: string): Promise<KeyStatusWithId | null> {
    try {
      const keyPath = `/keys/${provider}/${keyId}`;
      const jsonValue = await this.etcdClient.get(keyPath);

      if (!jsonValue) {
        this.logger.debug(`Key ${keyId} not found for provider ${provider}`);
        return null;
      }

      const keyStatus = JSON.parse(jsonValue) as ApiKeyStatus;
      
      return {
        keyId,
        provider,
        ...keyStatus,
      };
    } catch (error) {
      this.logger.error(
        `Failed to get status for key ${keyId}: ${error instanceof Error ? error.message : String(error)}`,
      );
      throw error;
    }
  }

  /**
   * Update API key status
   * Used by Provider/Admin to manually update key status
   */
  async updateKeyStatus(
    provider: string,
    keyId: string,
    status: 'active' | 'rate_limited' | 'exhausted' | 'revoked',
    lastError?: string,
    note?: string,
  ): Promise<boolean> {
    try {
      const keyPath = `/keys/${provider}/${keyId}`;
      
      // Get existing status or create new one
      let existingStatus: ApiKeyStatus | null = null;
      try {
        const existingValue = await this.etcdClient.get(keyPath);
        if (existingValue) {
          existingStatus = JSON.parse(existingValue) as ApiKeyStatus;
        }
      } catch (error) {
        this.logger.debug(`No existing status found for key ${keyId}, creating new one`);
      }

      const updatedStatus: ApiKeyStatus = {
        key: existingStatus?.key || `sk-${keyId}`, // Placeholder if key not found
        status,
        lastUsedAt: Date.now(),
        lastError,
        note,
      };

      await this.etcdClient.put(keyPath).value(JSON.stringify(updatedStatus));
      
      this.logger.log(`Updated key ${keyId} status to ${status} for provider ${provider}`);
      return true;
    } catch (error) {
      this.logger.error(
        `Failed to update key status for ${keyId}: ${error instanceof Error ? error.message : String(error)}`,
      );
      return false;
    }
  }

  /**
   * Get key status statistics
   */
  async getKeyStatistics(provider: string): Promise<KeyStatusStatistics> {
    try {
      const result = await this.getAllKeyStatuses(provider);
      return result.statistics;
    } catch (error) {
      this.logger.error(
        `Failed to get statistics for provider ${provider}: ${error instanceof Error ? error.message : String(error)}`,
      );
      throw error;
    }
  }

  /**
   * Get keys by status
   */
  async getKeysByStatus(
    provider: string,
    status: 'active' | 'rate_limited' | 'exhausted' | 'revoked',
  ): Promise<KeyStatusWithId[]> {
    try {
      const result = await this.getAllKeyStatuses(provider, status);
      return result.keys;
    } catch (error) {
      this.logger.error(
        `Failed to get keys by status ${status} for provider ${provider}: ${error instanceof Error ? error.message : String(error)}`,
      );
      throw error;
    }
  }

  /**
   * Mark multiple keys with the same status (bulk operation)
   */
  async bulkUpdateKeyStatus(
    provider: string,
    keyIds: string[],
    status: 'active' | 'rate_limited' | 'exhausted' | 'revoked',
    reason?: string,
  ): Promise<{ success: number; failed: number }> {
    let success = 0;
    let failed = 0;

    for (const keyId of keyIds) {
      try {
        const updated = await this.updateKeyStatus(provider, keyId, status, reason);
        if (updated) {
          success++;
        } else {
          failed++;
        }
      } catch (error) {
        this.logger.error(`Failed to update key ${keyId}: ${error}`);
        failed++;
      }
    }

    this.logger.log(`Bulk update completed: ${success} success, ${failed} failed`);
    return { success, failed };
  }

  /**
   * Delete key status (remove from etcd)
   */
  async deleteKeyStatus(provider: string, keyId: string): Promise<boolean> {
    try {
      const keyPath = `/keys/${provider}/${keyId}`;
      await this.etcdClient.delete().key(keyPath);

      this.logger.log(`Deleted key status for ${keyId} in provider ${provider}`);
      return true;
    } catch (error) {
      this.logger.error(
        `Failed to delete key status for ${keyId}: ${error instanceof Error ? error.message : String(error)}`,
      );
      return false;
    }
  }

  /**
   * Get available (active) keys for a provider
   * Used by Executors to get list of usable keys
   */
  async getAvailableKeys(provider: string): Promise<KeyStatusWithId[]> {
    try {
      return await this.getKeysByStatus(provider, 'active');
    } catch (error) {
      this.logger.error(
        `Failed to get available keys for provider ${provider}: ${error instanceof Error ? error.message : String(error)}`,
      );
      throw error;
    }
  }

  /**
   * Check if a key is available for use
   */
  async isKeyAvailable(provider: string, keyId: string): Promise<boolean> {
    try {
      const status = await this.getKeyStatus(provider, keyId);
      return status?.status === 'active';
    } catch (error) {
      this.logger.error(
        `Failed to check availability for key ${keyId}: ${error instanceof Error ? error.message : String(error)}`,
      );
      return false;
    }
  }

  /**
   * Get keys that need attention (rate limited, exhausted, or have errors)
   */
  async getProblematicKeys(provider: string): Promise<{
    rateLimited: KeyStatusWithId[];
    exhausted: KeyStatusWithId[];
    revoked: KeyStatusWithId[];
  }> {
    try {
      const [rateLimited, exhausted, revoked] = await Promise.all([
        this.getKeysByStatus(provider, 'rate_limited'),
        this.getKeysByStatus(provider, 'exhausted'),
        this.getKeysByStatus(provider, 'revoked'),
      ]);

      return {
        rateLimited,
        exhausted,
        revoked,
      };
    } catch (error) {
      this.logger.error(
        `Failed to get problematic keys for provider ${provider}: ${error instanceof Error ? error.message : String(error)}`,
      );
      throw error;
    }
  }
}
