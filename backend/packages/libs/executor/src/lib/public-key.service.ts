import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Etcd3 } from 'etcd3';

export interface PublicKeyRegistration {
  keyId: string;                  // executor 公钥标识
  publicKey: string;             // base64(X25519 pub) - base64 编码公钥
  provider: 'openai' | 'claude' | 'anthropic' | 'google' | 'cohere'; // 模型提供商
  region: string;                // 区域
  executorUrl?: string;          // Executor URL (optional)
  registeredAt: string;          // 注册时间
  lastHeartbeat?: string;        // 最后心跳时间
  status: 'active' | 'inactive'; // 状态
}

export interface PublicKeyQueryResult {
  keys: PublicKeyRegistration[];
  totalCount: number;
}

@Injectable()
export class PublicKeyService {
  private readonly logger = new Logger(PublicKeyService.name);
  private etcdClient!: Etcd3;

  constructor(private configService: ConfigService) {
    this.initializeEtcdClient();
  }

  private initializeEtcdClient(): void {
    const etcdHost = this.configService.get<string>('ETCD_HOST', 'localhost');
    const etcdPort = this.configService.get<number>('ETCD_PORT', 2379);

    this.etcdClient = new Etcd3({
      hosts: [`${etcdHost}:${etcdPort}`],
    });

    this.logger.log(`PublicKeyService connected to etcd at ${etcdHost}:${etcdPort}`);
  }

  /**
   * Get public keys for a specific provider and region
   * Used by frontend to get available public keys for encryption
   * Gateway 只负责读取，不负责写入（写入由 Executor 负责）
   */
  async getPublicKeys(
    provider: string,
    region?: string,
    status: 'active' | 'inactive' | 'all' = 'active'
  ): Promise<PublicKeyQueryResult> {
    try {
      let keyPrefix: string;
      
      if (region) {
        keyPrefix = `/keys/public/${provider}/${region}/`;
      } else {
        keyPrefix = `/keys/public/${provider}/`;
      }

      this.logger.debug(`Getting public keys with prefix: ${keyPrefix}`);

      const kvs = await this.etcdClient.getAll().prefix(keyPrefix);
      const keys: PublicKeyRegistration[] = [];

      for (const [fullPath, jsonValue] of Object.entries(kvs)) {
        try {
          const keyData = JSON.parse(jsonValue as string) as PublicKeyRegistration;
          
          // Apply status filter
          if (status !== 'all' && keyData.status !== status) {
            continue;
          }

          // Check if key is still fresh (heartbeat within last 5 minutes)
          if (keyData.lastHeartbeat) {
            const lastHeartbeatTime = new Date(keyData.lastHeartbeat).getTime();
            const now = Date.now();
            const fiveMinutesAgo = now - (5 * 60 * 1000);
            
            if (lastHeartbeatTime < fiveMinutesAgo && keyData.status === 'active') {
              // Mark as inactive if heartbeat is stale
              keyData.status = 'inactive';
              this.logger.debug(`Marking key ${keyData.keyId} as inactive due to stale heartbeat`);
            }
          }

          keys.push(keyData);
        } catch (parseError) {
          this.logger.warn(`Failed to parse public key data for ${fullPath}: ${parseError}`);
        }
      }

      // Sort by registration time, newest first
      keys.sort((a, b) => new Date(b.registeredAt).getTime() - new Date(a.registeredAt).getTime());

      this.logger.log(`Found ${keys.length} public keys for ${provider}${region ? `:${region}` : ''}`);

      return {
        keys,
        totalCount: keys.length,
      };
    } catch (error) {
      this.logger.error(
        `Failed to get public keys for ${provider}:${region}: ${error instanceof Error ? error.message : String(error)}`,
      );
      throw error;
    }
  }

  /**
   * Get all available providers and regions
   */
  async getAvailableProvidersAndRegions(): Promise<{
    providers: string[];
    regions: Record<string, string[]>;
  }> {
    try {
      const keyPrefix = '/keys/public/';
      const kvs = await this.etcdClient.getAll().prefix(keyPrefix);

      const providers = new Set<string>();
      const regions: Record<string, Set<string>> = {};

      for (const [fullPath] of Object.entries(kvs)) {
        // Parse path: /keys/public/{provider}/{region}/{keyId}
        const pathParts = fullPath.split('/');
        if (pathParts.length >= 5) {
          const provider = pathParts[3];
          const region = pathParts[4];
          
          providers.add(provider);
          
          if (!regions[provider]) {
            regions[provider] = new Set();
          }
          regions[provider].add(region);
        }
      }

      // Convert Sets to Arrays
      const regionsArray: Record<string, string[]> = {};
      for (const [provider, regionSet] of Object.entries(regions)) {
        regionsArray[provider] = Array.from(regionSet).sort();
      }

      return {
        providers: Array.from(providers).sort(),
        regions: regionsArray,
      };
    } catch (error) {
      this.logger.error(
        `Failed to get available providers and regions: ${error instanceof Error ? error.message : String(error)}`,
      );
      throw error;
    }
  }

  /**
   * Get public key statistics
   */
  async getPublicKeyStatistics(): Promise<{
    totalKeys: number;
    activeKeys: number;
    inactiveKeys: number;
    keysByProvider: Record<string, number>;
    keysByRegion: Record<string, number>;
  }> {
    try {
      const keyPrefix = '/keys/public/';
      const kvs = await this.etcdClient.getAll().prefix(keyPrefix);

      let totalKeys = 0;
      let activeKeys = 0;
      let inactiveKeys = 0;
      const keysByProvider: Record<string, number> = {};
      const keysByRegion: Record<string, number> = {};

      for (const [fullPath, jsonValue] of Object.entries(kvs)) {
        try {
          const keyData = JSON.parse(jsonValue as string) as PublicKeyRegistration;
          
          totalKeys++;
          
          // Check heartbeat freshness
          let currentStatus = keyData.status;
          if (keyData.lastHeartbeat) {
            const lastHeartbeatTime = new Date(keyData.lastHeartbeat).getTime();
            const now = Date.now();
            const fiveMinutesAgo = now - (5 * 60 * 1000);
            
            if (lastHeartbeatTime < fiveMinutesAgo && keyData.status === 'active') {
              currentStatus = 'inactive';
            }
          }
          
          if (currentStatus === 'active') {
            activeKeys++;
          } else {
            inactiveKeys++;
          }

          // Count by provider
          keysByProvider[keyData.provider] = (keysByProvider[keyData.provider] || 0) + 1;
          
          // Count by region
          keysByRegion[keyData.region] = (keysByRegion[keyData.region] || 0) + 1;
        } catch (parseError) {
          this.logger.warn(`Failed to parse public key data for statistics: ${fullPath}`);
        }
      }

      return {
        totalKeys,
        activeKeys,
        inactiveKeys,
        keysByProvider,
        keysByRegion,
      };
    } catch (error) {
      this.logger.error(
        `Failed to get public key statistics: ${error instanceof Error ? error.message : String(error)}`,
      );
      throw error;
    }
  }

  /**
   * Get a specific public key by keyId (for internal use)
   */
  async getPublicKeyById(provider: string, region: string, keyId: string): Promise<PublicKeyRegistration | null> {
    try {
      const keyPath = `/keys/public/${provider}/${region}/${keyId}`;
      const jsonValue = await this.etcdClient.get(keyPath);

      if (!jsonValue) {
        this.logger.debug(`Public key not found: ${keyId}`);
        return null;
      }

      const keyData = JSON.parse(jsonValue) as PublicKeyRegistration;
      return keyData;
    } catch (error) {
      this.logger.error(
        `Failed to get public key ${keyId}: ${error instanceof Error ? error.message : String(error)}`,
      );
      return null;
    }
  }

  /**
   * Check if a specific executor is available
   * Used internally by Gateway for routing decisions
   */
  async isExecutorAvailable(provider: string, region: string, keyId: string): Promise<boolean> {
    try {
      const keyData = await this.getPublicKeyById(provider, region, keyId);
      
      if (!keyData || keyData.status !== 'active') {
        return false;
      }

      // Check heartbeat freshness
      if (keyData.lastHeartbeat) {
        const lastHeartbeatTime = new Date(keyData.lastHeartbeat).getTime();
        const now = Date.now();
        const fiveMinutesAgo = now - (5 * 60 * 1000);
        
        if (lastHeartbeatTime < fiveMinutesAgo) {
          return false;
        }
      }

      return true;
    } catch (error) {
      this.logger.error(`Failed to check executor availability for ${keyId}: ${error}`);
      return false;
    }
  }
}
