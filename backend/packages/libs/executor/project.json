{"name": "executor", "$schema": "../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "packages/libs/executor/src", "projectType": "library", "targets": {"build": {"executor": "@nx/js:tsc", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/packages/libs/executor", "main": "packages/libs/executor/src/index.ts", "tsConfig": "packages/libs/executor/tsconfig.lib.json", "assets": ["packages/libs/executor/*.md"]}}, "lint": {"executor": "@nx/linter:eslint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["packages/libs/executor/**/*.ts"]}}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "packages/libs/executor/jest.config.ts", "passWithNoTests": true}, "configurations": {"ci": {"ci": true, "coverageReporters": ["text"]}}}}, "tags": []}