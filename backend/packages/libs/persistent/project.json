{"name": "libs-persistent", "$schema": "../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "packages/libs/persistent/src", "projectType": "library", "targets": {"build": {"executor": "@nx/js:tsc", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/packages/libs/persistent", "tsConfig": "packages/libs/persistent/tsconfig.lib.json", "packageJson": "packages/libs/persistent/package.json", "main": "packages/libs/persistent/src/index.ts", "assets": ["packages/libs/persistent/*.md"]}}, "lint": {"executor": "@nx/eslint:lint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["packages/libs/persistent/**/*.ts"]}}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "packages/libs/persistent/jest.config.ts"}}}, "tags": []}