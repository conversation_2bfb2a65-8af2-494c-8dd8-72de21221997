import { Inject, Injectable, Logger } from '@nestjs/common';
import { RawDidDocument, RawDidDocumentSchema } from '@saito/models';
import { PersistentService } from '@saito/persistent';
import { sql } from 'slonik';

import * as fs from 'fs/promises';
import * as path from 'path';

@Injectable()
export class DidLocalStorage {
  private readonly logger = new Logger(DidLocalStorage.name);

  constructor(
    private readonly persistentService: PersistentService,
  ) {}

  async load(): Promise<RawDidDocument | null> {
    try {
      // TODO，带Zod parse
      const result = await this.persistentService.pgPool.query(sql.unsafe`
        SELECT document
        FROM saito_gateway.did_documents
        WHERE is_peer = false
        LIMIT 1
      `);
      if (!result.rows.length) return null;
      return result.rows[0]['document'] as RawDidDocument;
    } catch (e) {
      this.logger.error('Fail to load: ' + (e instanceof Error ? e.message : String(e)));
      return null;
    }
  }

  async persist(doc: RawDidDocument) {
    try {
      await this.persistentService.pgPool.query(sql.unsafe`
        INSERT INTO saito_gateway.did_documents (id, document, is_peer)
        VALUES (${doc.id}, ${JSON.stringify(doc, null, 2)}, false)
        ON CONFLICT (id) DO UPDATE
        SET document = EXCLUDED.document,
            is_peer = false,
            updated_at = NOW()
      `);
    } catch (e) {
      this.logger.error('Fail to persist: ' + (e instanceof Error ? e.message : String(e)));
      throw e;
    }
  }
}