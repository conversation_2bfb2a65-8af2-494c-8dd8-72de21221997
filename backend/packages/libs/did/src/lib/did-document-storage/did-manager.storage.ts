import * as fs from 'fs/promises';
import { ParsedDidDocument } from '@saito/models';
import * as path from 'path';
import { PersistentService } from '@saito/persistent';
import { sql } from 'slonik';
import { Inject, Injectable, Logger } from '@nestjs/common';

@Injectable()
export class DidManagerStorage {
  private readonly logger = new Logger(DidManagerStorage.name);

  constructor(
    private readonly persistentService: PersistentService,
  ) {}

  async load(): Promise<ParsedDidDocument[]> {
    try {
      const result = await this.persistentService.pgPool.query(sql.unsafe`
        SELECT document FROM saito_gateway.did_documents
        WHERE is_peer = true
      `);
      return result.rows.map(r => r.document as ParsedDidDocument);
    } catch (e) {
      this.logger.error('Fail to load the DID: ' + (e instanceof Error ? e.message : String(e)));
      return [];
    }
  }

  async persist(docs: ParsedDidDocument[]): Promise<void> {
    const conn = this.persistentService.pgPool;
    try {
      // 1. 先删所有 peer DID
      await conn.query(sql.unsafe`DELETE FROM saito_gateway.did_documents WHERE is_peer = true`);

      // 2. 再批量插入
      for (const doc of docs) {
        await conn.query(sql.unsafe`
          INSERT INTO saito_gateway.did_documents (id, document, is_peer)
          VALUES (${doc.id}, ${JSON.stringify(doc, null, 2)}, true)
          ON CONFLICT (id) DO UPDATE
          SET document = EXCLUDED.document,
              is_peer = true,
              updated_at = NOW()
        `);
      }
    } catch (e) {
      this.logger.error('Fail to persist all: ' + (e instanceof Error ? e.message : String(e)));
      throw e;
    }
  }

  async persistOne(doc: ParsedDidDocument): Promise<void> {
    try {
      await this.persistentService.pgPool.query(sql.unsafe`
        INSERT INTO saito_gateway.did_documents (id, document, is_peer)
        VALUES (${doc.id}, ${JSON.stringify(doc, null, 2)}, true)
        ON CONFLICT (id) DO UPDATE
        SET document = EXCLUDED.document,
            is_peer = true,
            updated_at = NOW()
      `);
    } catch (e) {
      this.logger.error('Fail to persist DID: ' + (e instanceof Error ? e.message : String(e)));
      throw e;
    }
  }

  async loadOne(id: string): Promise<ParsedDidDocument | undefined> {
    try {
      const result = await this.persistentService.pgPool.query(sql.unsafe`
        SELECT document FROM saito_gateway.did_documents
        WHERE id = ${id} AND is_peer = true
        LIMIT 1
      `);
      if (!result.rows.length) return undefined;
      return result.rows[0].document as ParsedDidDocument;
    } catch (e) {
      this.logger.error('Fail to get the DID: ' + (e instanceof Error ? e.message : String(e)));
      return undefined;
    }
  }
}