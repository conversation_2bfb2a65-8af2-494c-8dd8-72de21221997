import { Injectable, Logger } from '@nestjs/common';
import { OnEvent } from '@nestjs/event-emitter';
import { DidDocumentManagerService } from '../did-document-manager/did-document-manager.service';

// 定义事件接口，避免使用 any
interface DeviceRegistrationEvent {
  deviceId: string;
  didDocument: any | null;
  registrationData: {
    code: string;
    gatewayAddress: string;
    rewardAddress: string;
    deviceType: string | null;
    ipAddress: string | null;
  };
}

/**
 * 设备注册事件监听器
 * 监听设备注册事件，处理 DID 文档的存储
 */
@Injectable()
export class DeviceRegistrationListener {
  private readonly logger = new Logger(DeviceRegistrationListener.name);

  constructor(
    private readonly didDocumentManager: DidDocumentManagerService
  ) {}

  /**
   * 处理设备注册事件
   * 当设备注册时，如果提供了 DID 文档，则使用 DID 模块的 addDocument 方法存储
   */
  @OnEvent('device.registration')
  async handleDeviceRegistration(event: DeviceRegistrationEvent): Promise<void> {
    try {
      const { deviceId, didDocument } = event;
      
      if (!didDocument) {
        this.logger.debug(`No DID document provided for device ${deviceId}`);
        return;
      }

      this.logger.log(`Processing DID document for device ${deviceId}`);
      
      // 使用 DID 模块的 addDocument 方法存储 DID 文档
      const result = await this.didDocumentManager.addDocument(didDocument);
      
      if (result) {
        this.logger.log(`DID document successfully stored for device ${deviceId}`);
      } else {
        this.logger.warn(`DID document was not stored for device ${deviceId} (possibly not newer than existing)`);
      }
      
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      this.logger.error(`Failed to process DID document for device registration: ${errorMessage}`);
      // 不抛出错误，避免影响设备注册流程
    }
  }
}
