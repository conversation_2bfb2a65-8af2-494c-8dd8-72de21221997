import { Injectable, InternalServerErrorException, Logger, UnauthorizedException } from "@nestjs/common";
import { JwtService } from "@nestjs/jwt";
import { NonceStore } from "./nonce.store";
import { ethers } from "ethers";
import { AuthService } from "./auth.interface";
import { AuthSchema } from "@saito/models";
import { AuthRepository } from "./auth.repository";

const SIGHT_URI = "https://sightai.io";

@Injectable()
export class DefaultAuthService implements AuthService {

    private readonly logger = new Logger(AuthService.name);

    constructor(
        private readonly jwtService: JwtService,
        private readonly nonceStore: NonceStore,
        private readonly authRepository: AuthRepository,
    ) {}

    async signIn(message: string, signature: string): Promise<{ accessToken: string, userId?: string }> {
        const messageConvert = message.replace(/\\n/g, '\n');

        // Validate message format
        const result = AuthSchema.eip4361_message.safeParse(messageConvert);

        if (!result.success) {
            throw new UnauthorizedException('Invalid message format');
        }

        const { address, uri, nonce, expirationTime } = result.data;

        // Validate the URI
        if (uri !== SIGHT_URI) {
            throw new UnauthorizedException(`Invalid URI, expecting ${SIGHT_URI}`);
        }

        if (this.nonceStore.hasNonce(nonce, address)) {
            throw new UnauthorizedException('Nonce already used or expired');
        }
        const currentTime = Date.now();
        if(expirationTime) {
          const expirationTimeInMs = new Date(expirationTime).getTime();
          if (currentTime > expirationTimeInMs) {
            this.nonceStore.removeNonce(nonce, address);
            throw new UnauthorizedException('Nonce has expired');
          }
          this.nonceStore.addNonce(nonce, address, expirationTimeInMs);
        }

        // Signature verification
        const isValidSignature = await this.verifySignature(messageConvert, signature, address);

        if (!isValidSignature) {
            throw new UnauthorizedException('Invalid signature');
        }

        // 查找或创建用户
        let user = await this.authRepository.findUserByWalletAddress(address);
        
        // 如果用户不存在，则创建用户
        if (!user) {
            user = await this.authRepository.createUser({
                wallet_address: address,
                username: `user_${address.substring(0, 8)}` // 创建默认用户名
            });
        }

        // JWT Issuance 添加用户ID
        const jwtPayloadParse = AuthSchema.jwt_payload.safeParse({
            sub: address,
            iss: SIGHT_URI,
            aud: uri,
            address,
            userId: user.id // 添加用户ID到JWT
        });

        if (jwtPayloadParse.success) {
            const payload = jwtPayloadParse.data;
            const accessToken = this.jwtService.sign(payload);
            return { accessToken, userId: user.id };
        } else {
            throw new InternalServerErrorException("Unexpected jwt payload");
        }
    }

    async verifySignature(message: string, signature: string, publicKey: string): Promise<boolean> {
        try {
            const recoveredAddress = ethers.verifyMessage(message, signature);
            return publicKey.toLowerCase() === recoveredAddress.toLowerCase();
        } catch (error) {
            console.error('Signature verification failed', error);
            return false;
        }
    }

    // for non-production environment only
    async generateUnlimitedJwtToken(address: string): Promise<string> {
        // 查找或创建用户以确保有用户ID
        let user = await this.authRepository.findUserByWalletAddress(address);

        if (!user) {
            user = await this.authRepository.createUser({
                wallet_address: address,
                username: `user_${address.substring(0, 8)}`
            });
        }
        
        const payload = { sub: address, address, userId: user.id };
        return this.jwtService.sign(payload, { expiresIn: '9999 years' });
    }
}

export const AuthServiceProvider = {
  provide: AuthService,
  useClass: DefaultAuthService
}
