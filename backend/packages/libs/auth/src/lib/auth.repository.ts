import { Injectable, Logger } from "@nestjs/common";
import { PersistentService } from '@saito/persistent';
import { sql } from 'slonik';
import { UserSchema, User } from '@saito/models';

@Injectable()
export class AuthRepository {
  private readonly logger = new Logger(AuthRepository.name);

  constructor(private readonly persistentService: PersistentService) {}

  /**
   * 根据钱包地址查找用户
   */
  async findUserByWalletAddress(walletAddress: string): Promise<User | null> {
    try {
      const result = await this.persistentService.pgPool.query(
        sql.type(UserSchema)`
          SELECT * FROM saito_gateway.users
          WHERE wallet_address = ${walletAddress}
          LIMIT 1
        `
      );

      if (result.rows.length === 0) {
        return null;
      }

      return result.rows[0];
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      this.logger.error(`Error finding user by wallet address: ${errorMessage}`);
      return null;
    }
  }

  /**
   * 创建新用户
   */
  async createUser(data: { wallet_address: string, username: string }): Promise<User> {
    try {
      const result = await this.persistentService.pgPool.query(
        sql.type(UserSchema)`
          INSERT INTO saito_gateway.users (
            wallet_address,
            username,
            created_at,
            updated_at
          )
          VALUES (
            ${data.wallet_address},
            ${data.username},
            now(),
            now()
          )
          RETURNING *
        `
      );

      return result.rows[0];
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      this.logger.error(`Error creating user: ${errorMessage}`);
      throw new Error('Failed to create user');
    }
  }
}
