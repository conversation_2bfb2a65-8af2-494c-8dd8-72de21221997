import { Injectable } from '@nestjs/common';

@Injectable()
export class NonceStore {
    private nonces: Map<string, { publicKey: string, expiryTime: number }> = new Map();

    /**
     * Adds a nonce and publicKey combination to the store with an explicit expiration time.
     * @param nonce - The nonce to be stored.
     * @param publicKey - The public key that is associated with the nonce.
     * @param expirationTime - The time at which the nonce should expire.
     */
    addNonce(nonce: string, publicKey: string, expirationTime: number) {
        this.nonces.set(this.generateKey(nonce, publicKey), { publicKey, expiryTime: expirationTime });
        this.removeExpiredNonces(); // Clean up expired nonces whenever a new one is added
    }

    /**
     * Checks if a nonce-publicKey pair exists and is valid (not expired).
     * If the nonce-publicKey pair is expired, it will be removed from the store.
     * @param nonce - The nonce to be checked.
     * @param publicKey - The public key associated with the nonce.
     * @returns True if the nonce-publicKey pair exists and is valid, otherwise false.
     */
    hasNonce(nonce: string, publicKey: string): boolean {
        const key = this.generateKey(nonce, publicKey);
        const nonceInfo = this.nonces.get(key);
        if (!nonceInfo || nonceInfo.expiryTime < Date.now()) {
            this.nonces.delete(key); // Remove expired nonce-publicKey pair
            return false;
        }

        return true;
    }

    /**
     * Removes a nonce-publicKey pair from the store.
     * This should be called after the nonce has been successfully used, or if it has expired.
     * @param nonce - The nonce to be removed.
     * @param publicKey - The public key associated with the nonce.
     */
    removeNonce(nonce: string, publicKey: string) {
        const key = this.generateKey(nonce, publicKey);
        this.nonces.delete(key);
    }

    /**
     * Iterates through the store and removes expired nonce-publicKey pairs.
     * This helps in cleaning up memory by discarding stale pairs.
     */
    private removeExpiredNonces() {
        const now = Date.now();
        for (const [key, nonceInfo] of this.nonces.entries()) {
            if (nonceInfo.expiryTime < now) {
                this.nonces.delete(key);
            }
        }
    }

    /**
     * Generates a unique key based on nonce and publicKey.
     * @param nonce - The nonce to be used in the key.
     * @param publicKey - The public key to be used in the key.
     * @returns A unique string that combines the nonce and publicKey.
     */
    private generateKey(nonce: string, publicKey: string): string {
        return `${nonce}:${publicKey}`;
    }
}
