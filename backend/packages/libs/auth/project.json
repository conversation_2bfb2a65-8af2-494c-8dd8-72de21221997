{"name": "lib-auth", "$schema": "../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "packages/libs/auth/src", "projectType": "library", "targets": {"build": {"executor": "@nx/js:tsc", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/packages/libs/auth", "main": "packages/libs/auth/src/index.ts", "tsConfig": "packages/libs/auth/tsconfig.lib.json", "assets": ["packages/libs/auth/*.md"]}}, "lint": {"executor": "@nx/eslint:lint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["packages/libs/auth/**/*.ts", "packages/libs/auth/package.json"]}}}, "tags": []}