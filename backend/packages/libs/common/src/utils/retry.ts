import { Logger } from '@nestjs/common';
import { stringifyJSON } from './bitint-json';

// Simple retry implementation without p-retry to avoid type issues
export async function dbRetry<T>(
  input: (attemptCount: number) => PromiseLike<T> | T,
  options: { logger: Logger },
): Promise<T> {
  const maxRetries = 10;
  let lastError: any;

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      return await input(attempt);
    } catch (error) {
      lastError = error;
      options.logger.warn(`retrying: ${attempt}/${maxRetries} - ${error}`);

      if (attempt === maxRetries) {
        throw lastError;
      }

      // Wait before retry
      await new Promise(resolve => setTimeout(resolve, 250));
    }
  }

  throw lastError;
}

const logger = new Logger('retry');

export const fastRetry = async <T>(
  fn: () => Promise<T>,
  label = 'retry',
): Promise<T> => {
  const maxRetries = 10;
  let lastError: any;

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      return await fn().catch(e => {
        if (e instanceof Error) {
          throw e;
        } else {
          const wrapError = new Error(
            `[${label}] wrapped error: ${stringifyJSON(e)}`,
          );
          logger.error(
            `[${label}] is not error, wrapping error: ${stringifyJSON(e)}`,
          );
          throw wrapError;
        }
      });
    } catch (error) {
      lastError = error;
      logger.warn(
        `[${label}] Attempt ${attempt} failed. There are ${
          maxRetries - attempt
        } retries left., error: ${error}`,
      );

      if (attempt === maxRetries) {
        throw lastError;
      }

      // Wait before retry with exponential backoff
      await new Promise(resolve => setTimeout(resolve, 200 * attempt));
    }
  }

  throw lastError;
};

export const expoRetry = async <T>(
  fn: () => Promise<T>,
  label = 'retry',
): Promise<T> => {
  const maxRetries = 10;
  let lastError: any;

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      return await fn().catch(e => {
        if (e instanceof Error) {
          throw e;
        } else {
          const wrapError = new Error(
            `[${label}] wrapped error: ${stringifyJSON(e)}`,
          );
          logger.error(
            `[${label}] is not error, wrapping error: ${stringifyJSON(e)}`,
          );
          throw wrapError;
        }
      });
    } catch (error) {
      lastError = error;
      logger.warn(
        `[${label}] Attempt ${attempt} failed. There are ${
          maxRetries - attempt
        } retries left., error: ${error}`,
      );

      if (attempt === maxRetries) {
        throw lastError;
      }

      // Wait before retry with exponential backoff
      await new Promise(resolve => setTimeout(resolve, 500 * Math.pow(2, attempt - 1)));
    }
  }

  throw lastError;
};
