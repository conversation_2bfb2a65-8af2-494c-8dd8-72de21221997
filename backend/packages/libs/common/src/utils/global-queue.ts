import PQueue from 'p-queue';
import * as R from 'ramda';

type QueueConfig = ConstructorParameters<typeof PQueue>[0];

// Simple memoization implementation since memoizeWith might not be available
const memoCache = new Map<string, PQueue>();

export const getGlobalPQueue = (queueName: string, config?: QueueConfig | undefined) => {
  if (!memoCache.has(queueName)) {
    memoCache.set(queueName, new PQueue(config));
  }
  return memoCache.get(queueName)!;
};
