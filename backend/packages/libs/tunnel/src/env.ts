import { createEnv } from '@t3-oss/env-core';
import memoizee from 'memoizee';
import { z } from 'zod';

export const env = memoizee(() =>
  createEnv({
    server: {
      PEER_ID: z.string().default('gateway'),
      NODE_ENV: z.string().default('development'),
      BOOTSTRAP_ADDRS: z
        .string()
        .default(
          process.env['BOOTSTRAP_ADDRS']
            ? process.env['BOOTSTRAP_ADDRS']
            : '/ip4/127.0.0.1/tcp/15001/p2p/12D3KooWPjceQrSwdWXPyLLeABRXmuqt69Rg3sBYbU1Nft9HyQ6X,/ip4/127.0.0.1/tcp/15002/p2p/12D3KooWH3uVF6wv47WnArKHk5p6cvgCJEb74UTmxztmQDc298L3,/ip4/127.0.0.1/tcp/15003/p2p/12D3KooWQYhTNQdmr3ArTeUHRYzFg94BKyTkoWBDWez9kSCVe2Xo,/ip4/127.0.0.1/tcp/15004/p2p/12D3KooWLJtG8fd2hkQzTn96MrLvThmnNQjTUFZwGEsLRz5EmSzc,/ip4/127.0.0.1/tcp/15005/p2p/12D3KooWSHj3RRbBjD15g6wekV8y3mm57Pobmps2g2WJm6F67Lay,/ip4/127.0.0.1/tcp/15006/p2p/12D3KooWDMCQbZZvLgHiHntG1KwcHoqHPAxL37KvhgibWqFtpqUY,/ip4/127.0.0.1/tcp/15007/p2p/12D3KooWLnZUpcaBwbz9uD1XsyyHnbXUrJRmxnsMiRnuCmvPix67,/ip4/127.0.0.1/tcp/15008/p2p/12D3KooWQ8vrERR8bnPByEjjtqV6hTWehaf8TmK7qR1cUsyrPpfZ,/ip4/127.0.0.1/tcp/15009/p2p/12D3KooWNRk8VBuTJTYyTbnJC7Nj2UN5jij4dJMo8wtSGT2hRzRP,/ip4/127.0.0.1/tcp/1500/p2p/12D3KooWNRk8VBuTJTYyTbnJC7Nj2UN5jij4dJMo8wtSGT2hRzRP,/ip4/127.0.0.1/tcp/15011/p2p/12D3KooWHbEputWi1fJAxoYgmvvDe3yP7acTACqmXKGYwMgN2daQ,/ip4/127.0.0.1/tcp/15012/p2p/12D3KooWCxnyz1JxC9y1RniRQVFe2cLaLHsYNc2SnXbM7yq5JBbJ,/ip4/127.0.0.1/tcp/15013/p2p/12D3KooWFNisMCMFB4sxKjQ4VLoTrMYh7fUJqXr1FMwhqAwfdxPS,/ip4/127.0.0.1/tcp/15014/p2p/12D3KooW9ubkfzRCQrUvcgvSqL2Cpri5pPV9DuyoHptvshVcNE9h,/ip4/127.0.0.1/tcp/15015/p2p/12D3KooWRVJCFqFBrasjtcGHnRuuut9fQLsfcUNLfWFFqjMm2p4n,/ip4/127.0.0.1/tcp/15016/p2p/12D3KooWGtVQAq3A8GPyq5ZuwBoE4V278EkDpETijz1dm7cY4LsG,/ip4/127.0.0.1/tcp/15017/p2p/12D3KooWGjxVp88DuWx6P6cN5ZLtud51TNWK6a7K1h9cYb8qDuci,/ip4/127.0.0.1/tcp/15018/p2p/12D3KooWDWC9G1REgGwHTzVNtXL8x6okkRQzsYb7V9mw9UGKhC1H,/ip4/127.0.0.1/tcp/15019/p2p/12D3KooWE92WS4t4UBFxryqsx78hSaFaZMLaAkRwkynjsL1mdt8h',
        ),
      LIBP2P_PORT: z.coerce
        .number()
        .default(
          process.env['LIBP2P_PORT']
            ? parseInt(process.env['LIBP2P_PORT'])
            : 4012,
        ),
    },
    runtimeEnv: process.env,
  }),
);
