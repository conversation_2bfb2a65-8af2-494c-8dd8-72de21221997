import {Inject, Injectable} from "@nestjs/common";
import {OutcomeBaseMessageHandler} from "../base-message-handler";
import {ContextPingMessageSchema, ContextPongMessage, TunnelMessage} from "@saito/models";
import {TunnelService, TunnelMessageListener} from "@saito/tunnel";
import {MessageHandler} from "../message-handler.decorator";

@MessageHandler({ type: 'context-ping', direction: 'outcome' })
@Injectable()
export class OutcomeContextPingMessageHandler extends OutcomeBaseMessageHandler {

  constructor(@Inject('TunnelService') private readonly tunnel: TunnelService,
              @Inject('PEER_ID') protected override readonly peerId: string){
    super(peerId);
  }

  async handleOutcomeMessage(message: TunnelMessage): Promise<void> {
    const pingMessage = ContextPingMessageSchema.parse(message);
    // console.log('[OutcomePingHandler] Received:', message);

    this.tunnel.sendMessage(pingMessage);
  }
}