import { Inject, Injectable, Logger } from "@nestjs/common";
import { OutcomeBaseMessageHandler } from "../base-message-handler";
import {
  DeviceHeartbeatResponseMessageSchema,
  TunnelMessage
} from "@saito/models";
import { TunnelService } from "../../tunnel.interface";
import { MessageHandler } from "../message-handler.decorator";

@MessageHandler({ type: 'device_heartbeat_response', direction: 'outcome' })
@Injectable()
export class OutcomeDeviceHeartbeatResponseMessageHandler extends OutcomeBaseMessageHandler {
  private readonly logger = new Logger(OutcomeDeviceHeartbeatResponseMessageHandler.name);

  constructor(
    @Inject('TunnelService') private readonly tunnel: TunnelService,
    @Inject('PEER_ID') protected override readonly peerId: string
  ) {
    super(peerId);
  }

  async handleOutcomeMessage(message: TunnelMessage): Promise<void> {
    const responseMessage = DeviceHeartbeatResponseMessageSchema.parse(message);
    this.logger.log(`[OutcomeDeviceHeartbeatResponseHandler] Sending heartbeat response to ${message.to}`);

    // 发送消息到目标设备
    this.tunnel.sendMessage(responseMessage);
  }
}
