import {Inject, Injectable} from "@nestjs/common";
import {OutcomeBaseMessageHandler} from "../base-message-handler";
import {PingMessageSchema, PongMessageSchema, TunnelMessage} from "@saito/models";
import {TunnelService} from "@saito/tunnel";
import {MessageHandler} from "../message-handler.decorator";

@MessageHandler({ type: 'pong', direction: 'outcome' })
@Injectable()
export class OutcomePongMessageHandler extends OutcomeBaseMessageHandler {

  constructor(@Inject('TunnelService') private readonly tunnel: TunnelService,
              @Inject('PEER_ID') protected override readonly peerId: string){
    super(peerId);
  }

  async handleOutcomeMessage(message: TunnelMessage): Promise<void> {
    const pongMessage = PongMessageSchema.parse(message);
    // console.log('[OutcomePongHandler] Received:', message);

    this.tunnel.sendMessage(pongMessage);
  }
}