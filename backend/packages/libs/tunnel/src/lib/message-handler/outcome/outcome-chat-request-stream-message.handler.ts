import {Inject, Injectable, Logger} from "@nestjs/common";
import {OutcomeBaseMessageHandler} from "../base-message-handler";
import {StreamChatRequestMessageSchema, TunnelMessage} from "@saito/models";
import {TunnelService} from "@saito/tunnel";
import {MessageHandler} from "../message-handler.decorator";

@MessageHandler({ type: 'chat_request_stream', direction: 'outcome' })
@Injectable()
export class OutcomeChatRequestStreamMessageHandler extends OutcomeBaseMessageHandler {
  private readonly logger = new Logger(OutcomeChatRequestStreamMessageHandler.name);

  constructor(
    @Inject('TunnelService') private readonly tunnel: TunnelService,
    @Inject('PEER_ID') protected override readonly peerId: string
  ) {
    super(peerId);
  }

  async handleOutcomeMessage(message: TunnelMessage): Promise<void> {
    const chatRequestMessage = StreamChatRequestMessageSchema.parse(message);
    this.logger.log(`[OutcomeChatRequestStreamHandler] Sending stream chat request to ${message.to} for task ${chatRequestMessage.payload.taskId}`);

    // Send the stream chat request message to the target node
    await this.tunnel.sendMessage(chatRequestMessage);
  }
}
