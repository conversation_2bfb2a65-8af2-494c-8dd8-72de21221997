import {Inject, Injectable, Logger} from "@nestjs/common";
import {OutcomeBaseMessageHandler} from "../base-message-handler";
import {NoStreamCompletionRequestMessageSchema, TunnelMessage} from "@saito/models";
import {TunnelService} from "@saito/tunnel";
import {MessageHandler} from "../message-handler.decorator";

@MessageHandler({ type: 'completion_request_no_stream', direction: 'outcome' })
@Injectable()
export class OutcomeCompletionRequestNoStreamMessageHandler extends OutcomeBaseMessageHandler {
  private readonly logger = new Logger(OutcomeCompletionRequestNoStreamMessageHandler.name);

  constructor(
    @Inject('TunnelService') private readonly tunnel: TunnelService,
    @Inject('PEER_ID') protected override readonly peerId: string
  ) {
    super(peerId);
  }

  async handleOutcomeMessage(message: TunnelMessage): Promise<void> {
    const completionRequestMessage = NoStreamCompletionRequestMessageSchema.parse(message);
    this.logger.log(`[OutcomeCompletionRequestNoStreamHandler] Sending non-stream completion request to ${message.to} for task ${completionRequestMessage.payload.taskId}`);

    // Send the non-stream completion request message to the target node
    await this.tunnel.sendMessage(completionRequestMessage);
  }
}
