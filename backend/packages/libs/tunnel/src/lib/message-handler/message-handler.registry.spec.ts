import { Test, TestingModule } from '@nestjs/testing';
import { DiscoveryService } from '@nestjs/core';
import { MessageHandlerRegistry } from './message-handler.registry';
import {MESSAGE_HANDLER_META, MessageHandler} from './message-handler.decorator';
import { IncomeBaseMessageHandler, OutcomeBaseMessageHandler } from './base-message-handler';
import { InstanceWrapper } from '@nestjs/core/injector/instance-wrapper';
import {TunnelMessage} from "@saito/models";

// Mock message handlers
@MessageHandler({ type: 'mock', direction: 'income' })
class MockIncomeHandler extends IncomeBaseMessageHandler {
  async handleIncomeMessage(message: TunnelMessage): Promise<void> {}
}

@MessageHandler({ type: 'mock', direction: 'outcome' })
class MockOutcomeHandler extends OutcomeBaseMessageHandler {
  async handleOutcomeMessage(message: TunnelMessage): Promise<void> {}
}

describe('MessageHandlerRegistry', () => {
  let registry: MessageHandlerRegistry;
  let discoveryService: jest.Mocked<DiscoveryService>;

  beforeEach(async () => {
    // Create mock instance wrappers for our handlers
    const mockIncomeInstance = new MockIncomeHandler();
    const mockOutcomeInstance = new MockOutcomeHandler();

    // Create mock instance wrappers
    const mockWrappers: InstanceWrapper[] = [
      {
        instance: mockIncomeInstance,
        metatype: MockIncomeHandler,
      } as InstanceWrapper,
      {
        instance: mockOutcomeInstance,
        metatype: MockOutcomeHandler,
      } as InstanceWrapper,
      {
        instance: null, // Test null instance case
      } as InstanceWrapper,
      {
        instance: {}, // Test instance without metadata
      } as InstanceWrapper,
    ];

    // Create mock discovery service
    const mockDiscoveryService = {
      getProviders: jest.fn().mockReturnValue(mockWrappers),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        MessageHandlerRegistry,
        {
          provide: DiscoveryService,
          useValue: mockDiscoveryService,
        },
      ],
    }).compile();

    registry = module.get<MessageHandlerRegistry>(MessageHandlerRegistry);
    discoveryService = module.get(DiscoveryService) as jest.Mocked<DiscoveryService>;
  });

  it('should be defined', () => {
    expect(registry).toBeDefined();
  });

  describe('onModuleInit', () => {
    it('should discover and register handlers', async () => {
      await registry.onModuleInit();

      // Verify discovery service was called
      expect(discoveryService.getProviders).toHaveBeenCalled();

      // Verify handlers were registered
      expect(registry.getIncomeHandler('mock')).toBeInstanceOf(MockIncomeHandler);
      expect(registry.getOutcomeHandler('mock')).toBeInstanceOf(MockOutcomeHandler);
    });
  });

  describe('getIncomeHandler', () => {
    it('should return the correct income handler', async () => {
      await registry.onModuleInit();
      const handler = registry.getIncomeHandler('mock');
      expect(handler).toBeInstanceOf(MockIncomeHandler);
    });

    it('should return undefined for unknown handler type', async () => {
      await registry.onModuleInit();
      const handler = registry.getIncomeHandler('unknown-type');
      expect(handler).toBeUndefined();
    });
  });

  describe('getOutcomeHandler', () => {
    it('should return the correct outcome handler', async () => {
      await registry.onModuleInit();
      const handler = registry.getOutcomeHandler('mock');
      expect(handler).toBeInstanceOf(MockOutcomeHandler);
    });

    it('should return undefined for unknown handler type', async () => {
      await registry.onModuleInit();
      const handler = registry.getOutcomeHandler('unknown-type');
      expect(handler).toBeUndefined();
    });
  });

  describe('getHandlerDescriptors', () => {
    it('should return descriptors for all registered handlers', async () => {
      await registry.onModuleInit();
      const descriptors = registry.getHandlerDescriptors();

      expect(descriptors).toHaveLength(2);
      expect(descriptors).toContainEqual({
        direction: 'income',
        type: 'mock',
        className: 'MockIncomeHandler',
      });
      expect(descriptors).toContainEqual({
        direction: 'outcome',
        type: 'mock',
        className: 'MockOutcomeHandler',
      });
    });
  });
});
