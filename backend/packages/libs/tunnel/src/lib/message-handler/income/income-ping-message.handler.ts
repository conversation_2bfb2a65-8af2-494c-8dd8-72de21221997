import {Inject, Injectable} from "@nestjs/common";
import {IncomeBaseMessageHandler} from "../base-message-handler";
import {PingMessageSchema, PongMessageSchema, TunnelMessage} from "@saito/models";
import {TunnelService} from "@saito/tunnel";
import {MessageHandler} from "../message-handler.decorator";

@MessageHandler({ type: 'ping', direction: 'income' })
@Injectable()
export class IncomePingMessageHandler extends IncomeBaseMessageHandler {

  constructor(@Inject('TunnelService') private readonly tunnel: TunnelService,
              @Inject('PEER_ID') protected override readonly peerId: string){
    super(peerId);
  }

  async handleIncomeMessage(message: TunnelMessage): Promise<void> {
    const pingMessage = PingMessageSchema.parse(message);

    const pongMessage = PongMessageSchema.parse({
      type: 'pong',
      from: this.peerId,
      to: pingMessage.from,
      payload: { message: pingMessage.payload.message, timestamp: Date.now() },
    });
    // console.log('[IncomePingHandler] Received:', message);
    this.tunnel.handleMessage(pongMessage);
  }
}
