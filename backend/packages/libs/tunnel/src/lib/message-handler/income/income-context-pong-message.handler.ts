import {Inject, Injectable} from "@nestjs/common";
import {IncomeBaseMessageHandler} from "../base-message-handler";
import {ContextPingMessageSchema, ContextPongMessageSchema, TunnelMessage} from "@saito/models";
import {TunnelService} from "@saito/tunnel";
import {MessageHandler} from "../message-handler.decorator";

@MessageHandler({ type: 'context-pong', direction: 'income' })
@Injectable()
export class IncomeContextPongMessageHandler extends IncomeBaseMessageHandler {

  constructor(@Inject('TunnelService') private readonly tunnel: TunnelService,
              @Inject('PEER_ID') protected override readonly peerId: string){
    super(peerId);
  }

  async handleIncomeMessage(message: TunnelMessage): Promise<void> {
    const pongMessage = ContextPongMessageSchema.parse(message);

    // console.log('[IncomePingHandler] Received:', message);
  }
}
