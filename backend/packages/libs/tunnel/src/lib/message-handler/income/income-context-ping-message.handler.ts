import {Inject, Injectable} from "@nestjs/common";
import {IncomeBaseMessageHandler} from "../base-message-handler";
import {ContextPingMessageSchema, ContextPongMessageSchema, TunnelMessage} from "@saito/models";
import {TunnelService} from "@saito/tunnel";
import {MessageHandler} from "../message-handler.decorator";

@MessageHandler({ type: 'context-ping', direction: 'income' })
@Injectable()
export class IncomeContextPingMessageHandler extends IncomeBaseMessageHandler {

  constructor(@Inject('TunnelService') private readonly tunnel: TunnelService,
              @Inject('PEER_ID') protected override readonly peerId: string){
    super(peerId);
  }

  async handleIncomeMessage(message: TunnelMessage): Promise<void> {
    const pingMessage = ContextPingMessageSchema.parse(message);

    await new Promise((resolve) => setTimeout(resolve, 10));

    const pongMessage = ContextPongMessageSchema.parse({
      type: 'context-pong',
      from: this.peerId,
      to: pingMessage.from,
      payload: { requestId: pingMessage.payload.requestId, message: pingMessage.payload.message, timestamp: Date.now() },
    });
    // console.log('[IncomePingHandler] Received:', message);
    this.tunnel.handleMessage(pongMessage);
  }
}
