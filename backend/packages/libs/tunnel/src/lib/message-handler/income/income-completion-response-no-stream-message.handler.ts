import {Inject, Injectable, Logger} from "@nestjs/common";
import {IncomeBaseMessageHandler} from "../base-message-handler";
import {NoStreamCompletionResponseMessageSchema, TunnelMessage, TaskMetrics} from "@saito/models";
import {MessageHandler} from "../message-handler.decorator";
import {TaskExecutionService} from "@saito/task-manager";
import {ResponseAdapterService} from "../../response-adapter.service";

@MessageHandler({ type: 'completion_response', direction: 'income' })
@Injectable()
export class IncomeCompletionResponseNoStreamMessageHandler extends IncomeBaseMessageHandler {
  private readonly logger = new Logger(IncomeCompletionResponseNoStreamMessageHandler.name);

  constructor(
    @Inject('PEER_ID') protected override readonly peerId: string,
    @Inject('TaskExecutionService') private readonly taskExecutionService: TaskExecutionService,
    @Inject('ResponseAdapterService') private readonly responseAdapter: ResponseAdapterService
  ) {
    super(peerId);
  }

  async handleIncomeMessage(message: TunnelMessage): Promise<void> {
    // 解析非流式完成响应消息
    const completionResponseMessage = NoStreamCompletionResponseMessageSchema.parse(message);
    const taskId = completionResponseMessage.payload.taskId;
    const data = completionResponseMessage.payload.data;

    this.logger.debug(`[IncomeCompletionResponseNoStreamHandler] Processing non-stream message for task ${taskId}`);

    try {
      // 检查是否有服务注册了这个任务
      const hasRegisteredService = this.responseAdapter.hasRegisteredService(taskId);

      if (hasRegisteredService) {
        // 只有当有服务注册时才处理（OpenAI等旧架构）
        // 调用专门的 completion 非流式响应处理方法
        this.logger.debug(`[IncomeCompletionResponseNoStreamHandler] Calling responseAdapter.handleCompletionNonStreamResponse for task ${taskId}`);
        await this.responseAdapter.handleCompletionNonStreamResponse(taskId, data);
      } else {
        // 没有注册的服务，说明是新架构（Ollama等），跳过处理
        this.logger.debug(`[IncomeCompletionResponseNoStreamHandler] No service registered for task ${taskId}, skipping ResponseAdapter processing (handled by new architecture)`);
      }

      // 非流式响应是完整的响应，直接完成任务
      // 从响应数据中提取指标
      const metrics: TaskMetrics = {
        total_duration: data.usage?.total_tokens || 0,
        load_duration: data.usage?.completion_tokens || 0,
        prompt_eval_count: data.usage?.prompt_tokens || 0,
        prompt_eval_duration: data.usage?.prompt_tokens || 0,
        eval_count: data.usage?.completion_tokens || 0,
        eval_duration: data.usage?.completion_tokens || 0
      };

      // 完成任务并记录指标
      await this.taskExecutionService.completeTask(taskId, metrics);

      this.logger.log(`[IncomeCompletionResponseNoStreamHandler] Non-stream task ${taskId} completed successfully`);
    } catch (error) {
      this.logger.error(`[IncomeCompletionResponseNoStreamHandler] Error processing non-stream response for task ${taskId}: ${error instanceof Error ? error.message : String(error)}`);

      // 只有当有服务注册时才通知响应适配器处理错误
      if (this.responseAdapter.hasRegisteredService(taskId)) {
        await this.responseAdapter.handleErrorResponse(taskId, error);
      }

      // 标记任务失败
      await this.taskExecutionService.failTask(taskId, `Failed to process non-stream completion response: ${error instanceof Error ? error.message : String(error)}`);
    }
  }
}
