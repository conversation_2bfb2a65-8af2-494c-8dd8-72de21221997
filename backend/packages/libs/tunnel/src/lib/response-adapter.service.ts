import { Injectable, Logger } from '@nestjs/common';
import { OpenAIChatCompletionChunk } from '@saito/models';

/**
 * 响应处理器接口
 * 支持流式和非流式响应处理
 */
export interface StreamResponseHandler {
  handleStreamResponse(taskId: string, data: any): Promise<void>;
  handleNonStreamResponse?(taskId: string, data: any): Promise<void>;
  handleCompletionStreamResponse?(taskId: string, data: any): Promise<void>;
  handleCompletionNonStreamResponse?(taskId: string, data: any): Promise<void>;
  handleErrorResponse?(taskId: string, error: any): Promise<void>;
}

/**
 * 响应适配器服务
 * 职责：将 tunnel 接收到的响应分发给对应的服务处理器
 * 设计原则：
 * - 单一职责：只负责响应的路由和分发
 * - 依赖倒置：通过接口依赖具体的服务实现
 * - 开闭原则：可以轻松添加新的服务适配器
 */
@Injectable()
export class ResponseAdapterService {
  private readonly logger = new Logger(ResponseAdapterService.name);

  // 存储任务ID到服务类型的映射
  private readonly taskServiceMap = new Map<string, 'ollama' | 'openai'>();

  // 存储服务类型到处理器的映射
  private readonly serviceHandlers = new Map<'ollama' | 'openai', StreamResponseHandler>();

  constructor() {}

  /**
   * 注册服务处理器
   * @param serviceType 服务类型
   * @param handler 处理器实例
   */
  registerServiceHandler(serviceType: 'ollama' | 'openai', handler: StreamResponseHandler): void {
    if (this.serviceHandlers.has(serviceType)) {
      this.logger.warn(`[ResponseAdapter] Handler for service type ${serviceType} is already registered! This may cause duplicate processing.`);
    }
    this.serviceHandlers.set(serviceType, handler);
    this.logger.debug(`[ResponseAdapter] Registered ${serviceType} service handler. Total handlers: ${this.serviceHandlers.size}`);
  }

  /**
   * 注册任务的服务类型
   * @param taskId 任务ID
   * @param serviceType 服务类型
   */
  registerTaskService(taskId: string, serviceType: 'ollama' | 'openai'): void {
    this.taskServiceMap.set(taskId, serviceType);
    this.logger.debug(`[ResponseAdapter] Registered task ${taskId} for service ${serviceType}`);
  }

  /**
   * 处理流式响应
   * @param taskId 任务ID
   * @param data 响应数据
   */
  async handleStreamResponse(taskId: string, data: OpenAIChatCompletionChunk): Promise<void> {
    const serviceType = this.taskServiceMap.get(taskId);

    if (!serviceType) {
      this.logger.warn(`[ResponseAdapter] No service registered for task ${taskId}`);
      return;
    }

    const handler = this.serviceHandlers.get(serviceType);
    if (!handler) {
      this.logger.warn(`[ResponseAdapter] No handler registered for service type: ${serviceType}`);
      return;
    }

    this.logger.debug(`[ResponseAdapter] Processing ${serviceType} stream response for task ${taskId}`);

    try {
      await handler.handleStreamResponse(taskId, data);

      // 如果响应完成，清理映射
      if (data.choices?.[0]?.finish_reason === 'stop') {
        this.taskServiceMap.delete(taskId);
      }
    } catch (error) {
      this.logger.error(`[ResponseAdapter] Error handling stream response for task ${taskId}: ${error}`);
      this.taskServiceMap.delete(taskId);
    }
  }

  /**
   * 处理非流式响应
   * @param taskId 任务ID
   * @param data 响应数据
   */
  async handleNonStreamResponse(taskId: string, data: any): Promise<void> {
    const serviceType = this.taskServiceMap.get(taskId);

    if (!serviceType) {
      this.logger.warn(`[ResponseAdapter] No service registered for task ${taskId}`);
      return;
    }

    const handler = this.serviceHandlers.get(serviceType);
    if (!handler) {
      this.logger.warn(`[ResponseAdapter] No handler registered for service type: ${serviceType}`);
      return;
    }

    if (!handler.handleNonStreamResponse) {
      this.logger.warn(`[ResponseAdapter] Handler for ${serviceType} does not support non-stream response handling`);
      return;
    }

    this.logger.debug(`[ResponseAdapter] Processing ${serviceType} non-stream response for task ${taskId}`);

    try {
      await handler.handleNonStreamResponse(taskId, data);
      // 非流式响应处理完成后立即清理映射
      this.taskServiceMap.delete(taskId);
    } catch (error) {
      this.logger.error(`[ResponseAdapter] Error handling non-stream response for task ${taskId}: ${error}`);
      this.taskServiceMap.delete(taskId);
    }
  }

  /**
   * 处理 completion 流式响应
   * @param taskId 任务ID
   * @param data 响应数据
   */
  async handleCompletionStreamResponse(taskId: string, data: any): Promise<void> {
    const serviceType = this.taskServiceMap.get(taskId);

    if (!serviceType) {
      this.logger.warn(`[ResponseAdapter] No service registered for task ${taskId}`);
      return;
    }

    const handler = this.serviceHandlers.get(serviceType);
    if (!handler) {
      this.logger.warn(`[ResponseAdapter] No handler registered for service type: ${serviceType}`);
      return;
    }

    if (!handler.handleCompletionStreamResponse) {
      this.logger.warn(`[ResponseAdapter] Handler for ${serviceType} does not support completion stream response handling`);
      return;
    }

    this.logger.debug(`[ResponseAdapter] Processing ${serviceType} completion stream response for task ${taskId}`);

    try {
      await handler.handleCompletionStreamResponse(taskId, data);

      // 如果响应完成，清理映射
      if (data.choices?.[0]?.finish_reason === 'stop') {
        this.taskServiceMap.delete(taskId);
      }
    } catch (error) {
      this.logger.error(`[ResponseAdapter] Error handling completion stream response for task ${taskId}: ${error}`);
      this.taskServiceMap.delete(taskId);
    }
  }

  /**
   * 处理 completion 非流式响应
   * @param taskId 任务ID
   * @param data 响应数据
   */
  async handleCompletionNonStreamResponse(taskId: string, data: any): Promise<void> {
    const serviceType = this.taskServiceMap.get(taskId);

    if (!serviceType) {
      this.logger.warn(`[ResponseAdapter] No service registered for task ${taskId}`);
      return;
    }

    const handler = this.serviceHandlers.get(serviceType);
    if (!handler) {
      this.logger.warn(`[ResponseAdapter] No handler registered for service type: ${serviceType}`);
      return;
    }

    if (!handler.handleCompletionNonStreamResponse) {
      this.logger.warn(`[ResponseAdapter] Handler for ${serviceType} does not support completion non-stream response handling`);
      return;
    }

    this.logger.debug(`[ResponseAdapter] Processing ${serviceType} completion non-stream response for task ${taskId}`);

    try {
      await handler.handleCompletionNonStreamResponse(taskId, data);
      // 非流式响应处理完成后立即清理映射
      this.taskServiceMap.delete(taskId);
    } catch (error) {
      this.logger.error(`[ResponseAdapter] Error handling completion non-stream response for task ${taskId}: ${error}`);
      this.taskServiceMap.delete(taskId);
    }
  }

  /**
   * 处理错误响应
   * @param taskId 任务ID
   * @param error 错误信息
   */
  async handleErrorResponse(taskId: string, error: any): Promise<void> {
    const serviceType = this.taskServiceMap.get(taskId);

    if (!serviceType) {
      this.logger.warn(`[ResponseAdapter] No service registered for task ${taskId}`);
      return;
    }

    const handler = this.serviceHandlers.get(serviceType);
    if (!handler) {
      this.logger.warn(`[ResponseAdapter] No handler registered for service type: ${serviceType}`);
      this.taskServiceMap.delete(taskId);
      return;
    }

    try {
      if (handler.handleErrorResponse) {
        await handler.handleErrorResponse(taskId, error);
      } else {
        this.logger.warn(`[ResponseAdapter] Handler for ${serviceType} does not support error handling`);
      }
    } catch (err) {
      this.logger.error(`[ResponseAdapter] Error handling error response for task ${taskId}: ${err}`);
    } finally {
      // 清理映射
      this.taskServiceMap.delete(taskId);
    }
  }

  /**
   * 获取任务的服务类型
   * @param taskId 任务ID
   * @returns 服务类型或undefined
   */
  getTaskServiceType(taskId: string): 'ollama' | 'openai' | undefined {
    return this.taskServiceMap.get(taskId);
  }

  /**
   * 检查任务是否有注册的服务
   * @param taskId 任务ID
   * @returns 是否有注册的服务
   */
  hasRegisteredService(taskId: string): boolean {
    return this.taskServiceMap.has(taskId);
  }

  /**
   * 清理任务映射
   * @param taskId 任务ID
   */
  cleanupTask(taskId: string): void {
    this.taskServiceMap.delete(taskId);
    this.logger.debug(`[ResponseAdapter] Cleaned up task ${taskId}`);
  }
}

export const ResponseAdapterServiceProvider = {
  provide: 'ResponseAdapterService',
  useClass: ResponseAdapterService,
};
