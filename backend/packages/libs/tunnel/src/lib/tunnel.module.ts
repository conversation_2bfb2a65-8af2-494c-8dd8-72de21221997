import { Global, Module } from '@nestjs/common';
import { DiscoveryModule } from '@nestjs/core';
import { TaskManagerModule } from '@saito/task-manager';
import { env } from '../env';
import {
  MessageGatewayProvider,
  SocketMessageGatewayService,
} from './message-gateway/message-gateway.service';
import {
  IncomeChatResponseNoStreamMessageHandler,
  IncomeChatResponseStreamMessageHandler,
  IncomeCompletionResponseNoStreamMessageHandler,
  IncomeCompletionResponseStreamMessageHandler,
  IncomeContextPingMessageHandler,
  IncomeContextPongMessageHandler,
  IncomeDeviceHeartbeatReportMessageHandler,
  IncomeDeviceModelReportMessageHandler,
  IncomeDeviceRegisterRequestMessageHandler,
  IncomePongMessageHandler,
  OutcomeChatRequestNoStreamMessageHandler,
  OutcomeChatRequestStreamMessageHandler,
  OutcomeCompletionRequestNoStreamMessageHandler,
  OutcomeCompletionRequestStreamMessageHandler,
  OutcomeContextPingMessageHandler,
  OutcomeContextPongMessageHandler,
  OutcomeDeviceHeartbeatResponseMessageHandler,
  OutcomeDeviceModelReportResponseMessageHandler,
  OutcomeDeviceRegisterResponseMessageHandler,
  OutcomePongMessageHandler,
} from './message-handler';
import { IncomePingMessageHandler } from './message-handler/income/income-ping-message.handler';
import { MessageHandlerRegistry } from './message-handler/message-handler.registry';
import { OutcomePingMessageHandler } from './message-handler/outcome/outcome-ping-message.handler';
import { ResponseAdapterServiceProvider } from './response-adapter.service';
import { MessageGatewayLibp2pProvider, Libp2pMessageGatewayService } from './message-gateway';
import { TunnelServiceImpl } from './tunnel.service';
import { MultiTransportService } from './message-gateway/multi-transport.service';

export const TunnelServiceProvider = {
  provide: 'TunnelService',
  useClass: TunnelServiceImpl,
};

// MultiTransportService provider
export const MultiTransportServiceProvider = {
  provide: 'MultiTransportService',
  useClass: MultiTransportService,
};

@Global()
@Module({
  imports: [DiscoveryModule, TaskManagerModule],
  providers: [

    TunnelServiceProvider,
    MessageGatewayProvider,
    MessageHandlerRegistry,
    SocketMessageGatewayService,
    ResponseAdapterServiceProvider,
    Libp2pMessageGatewayService,
    MessageGatewayLibp2pProvider,

    // MultiTransportService provider
    MultiTransportServiceProvider,

    // Incoming Handlers
    IncomePingMessageHandler,
    IncomePongMessageHandler,
    IncomeContextPingMessageHandler,
    IncomeContextPongMessageHandler,
    IncomeChatResponseStreamMessageHandler,
    IncomeChatResponseNoStreamMessageHandler,
    IncomeCompletionResponseStreamMessageHandler,
    IncomeCompletionResponseNoStreamMessageHandler,
    IncomeDeviceRegisterRequestMessageHandler,
    IncomeDeviceHeartbeatReportMessageHandler,
    IncomeDeviceModelReportMessageHandler,
    // Outcoming Handlers
    OutcomePingMessageHandler,
    OutcomePongMessageHandler,
    OutcomeContextPingMessageHandler,
    OutcomeContextPongMessageHandler,
    OutcomeChatRequestStreamMessageHandler,
    OutcomeChatRequestNoStreamMessageHandler,
    OutcomeCompletionRequestStreamMessageHandler,
    OutcomeCompletionRequestNoStreamMessageHandler,
    OutcomeDeviceRegisterResponseMessageHandler,
    OutcomeDeviceHeartbeatResponseMessageHandler,
    OutcomeDeviceModelReportResponseMessageHandler,
    {
      provide: 'PEER_ID',
      useValue: env().PEER_ID,
    },
  ],
  exports: [
    TunnelServiceProvider,
    MessageHandlerRegistry,
    ResponseAdapterServiceProvider,

    // Export MultiTransportService
    MultiTransportServiceProvider,

    {
      provide: 'PEER_ID',
      useValue: env().PEER_ID,
    },
  ],
})
export class TunnelModule {}
