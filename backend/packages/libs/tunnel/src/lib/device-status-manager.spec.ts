import { Test, TestingModule } from '@nestjs/testing';
import { DeviceStatusManagerService } from './device-status-manager.service';
import { Logger } from '@nestjs/common';

describe('DeviceStatusManagerService', () => {
  let service: DeviceStatusManagerService;
  let mockNodeRepository: any;

  beforeEach(async () => {
    mockNodeRepository = {
      updateDeviceStatus: jest.fn()
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        DeviceStatusManagerService,
        {
          provide: 'NodeRepository',
          useValue: mockNodeRepository
        }
      ],
    }).compile();

    service = module.get<DeviceStatusManagerService>(DeviceStatusManagerService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('onDeviceDisconnected', () => {
    it('should update device status to disconnected with socket_disconnect reason', async () => {
      const deviceId = 'test-device-id';
      const mockResult = { fromStatus: 'connected', toStatus: 'disconnected' };
      mockNodeRepository.updateDeviceStatus.mockResolvedValue(mockResult);

      await service.onDeviceDisconnected(deviceId);

      expect(mockNodeRepository.updateDeviceStatus).toHaveBeenCalledWith(
        deviceId,
        'disconnected',
        'socket_disconnect'
      );
    });

    it('should handle errors gracefully', async () => {
      const deviceId = 'test-device-id';
      mockNodeRepository.updateDeviceStatus.mockRejectedValue(new Error('Database error'));

      // Should not throw error
      await expect(service.onDeviceDisconnected(deviceId)).resolves.toBeUndefined();
    });
  });

  describe('onDeviceConnected', () => {
    it('should log device connection without updating status', async () => {
      const deviceId = 'test-device-id';
      const logSpy = jest.spyOn(service['logger'], 'log');

      await service.onDeviceConnected(deviceId);

      expect(logSpy).toHaveBeenCalledWith(`Device ${deviceId} socket connected`);
      expect(mockNodeRepository.updateDeviceStatus).not.toHaveBeenCalled();
    });
  });

  describe('shouldMarkAsDisconnected', () => {
    it('should return true by default', async () => {
      const deviceId = 'test-device-id';
      const result = await service.shouldMarkAsDisconnected(deviceId);
      expect(result).toBe(true);
    });
  });
});
