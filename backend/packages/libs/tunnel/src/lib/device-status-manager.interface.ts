/**
 * 设备状态管理接口
 * 用于处理设备连接状态的实时更新
 */
export interface DeviceStatusManager {
  /**
   * 当设备Socket连接断开时调用
   * @param deviceId 设备ID
   */
  onDeviceDisconnected(deviceId: string): Promise<void>;

  /**
   * 当设备Socket连接建立时调用
   * @param deviceId 设备ID
   */
  onDeviceConnected(deviceId: string): Promise<void>;

  /**
   * 检查设备是否应该被标记为断开连接
   * @param deviceId 设备ID
   * @returns 是否应该断开连接
   */
  shouldMarkAsDisconnected(deviceId: string): Promise<boolean>;
}
