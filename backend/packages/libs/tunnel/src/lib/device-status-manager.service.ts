import { Injectable, Logger, Inject } from '@nestjs/common';
import { DeviceStatusManager } from './device-status-manager.interface';

/**
 * 设备状态管理服务实现
 * 处理设备连接状态的实时更新
 */
@Injectable()
export class DeviceStatusManagerService implements DeviceStatusManager {
  private readonly logger = new Logger(DeviceStatusManagerService.name);

  constructor(
    @Inject('NodeRepository') private readonly nodeRepository: any
  ) {}

  /**
   * 当设备Socket连接断开时调用
   * 立即将设备状态设置为disconnected
   */
  async onDeviceDisconnected(deviceId: string): Promise<void> {
    try {
      this.logger.log(`Device ${deviceId} socket disconnected, updating status to disconnected`);

      // 立即更新设备状态为disconnected，并传递断开原因
      const result = await this.nodeRepository.updateDeviceStatus(deviceId, 'disconnected', 'socket_disconnect');

      if (result) {
        this.logger.log(`Device ${deviceId} status changed from ${result.fromStatus} to ${result.toStatus} due to socket disconnection`);
      } else {
        this.logger.warn(`Failed to update status for device ${deviceId} - device may not exist in database`);
      }
    } catch (error) {
      this.logger.error(`Failed to update device status on disconnection for ${deviceId}: ${error}`);
      // 不抛出错误，避免影响Socket断开处理流程
    }
  }

  /**
   * 当设备Socket连接建立时调用
   * 注意：这里不立即设置为connected，因为设备需要通过注册或心跳来确认连接
   */
  async onDeviceConnected(deviceId: string): Promise<void> {
    try {
      this.logger.log(`Device ${deviceId} socket connected`);
      // 这里不立即更新状态，等待设备发送注册请求或心跳
      // 设备状态的connected更新应该通过正常的注册流程或心跳机制来处理
    } catch (error) {
      this.logger.error(`Error handling device connection for ${deviceId}: ${error}`);
    }
  }

  /**
   * 检查设备是否应该被标记为断开连接
   * 这个方法可以用于额外的业务逻辑检查
   */
  async shouldMarkAsDisconnected(deviceId: string): Promise<boolean> {
    try {
      // 可以在这里添加额外的检查逻辑
      // 例如检查设备是否在数据库中存在，是否处于可断开的状态等
      
      // 目前简单返回true，表示所有断开的Socket都应该更新设备状态
      return true;
    } catch (error) {
      this.logger.error(`Error checking if device ${deviceId} should be marked as disconnected: ${error}`);
      return false;
    }
  }
}

export const DeviceStatusManagerProvider = {
  provide: 'DeviceStatusManager',
  useClass: DeviceStatusManagerService,
};
