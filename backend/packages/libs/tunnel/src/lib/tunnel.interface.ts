/**
 * @interface TunnelService
 * @description Interface for tunnel service that handles communication with devices
 */
import {TunnelMessage} from '@saito/models';

export interface TunnelService {
  handleMessage(message: TunnelMessage, listener?: TunnelMessageListener): Promise<void>;
  sendMessage(message: TunnelMessage): Promise<void>;

  // 监听器管理
  addListener(listener: TunnelMessageListener): void;
  removeListener(listener: TunnelMessageListener): void;

  // 新增的方法用于任务处理
  handleSendToDevice(options: SendToDeviceOptions): Promise<void>;

  // 设备连接状态检查
  isDeviceConnected(deviceId: string): Promise<boolean>;

  // 验证设备连接（发送 ping 并等待响应）
  verifyDeviceConnection(deviceId: string, timeoutMs?: number): Promise<boolean>;
}

export type TunnelMessageListener = {
  match: (msg: TunnelMessage) => boolean;
  callback: (msg: TunnelMessage) => void;
  once?: (msg: TunnelMessage) => boolean;
};

// 新增的类型定义
export interface StreamHandlerOptions {
  taskId: string;
  targetDeviceId: string;
  onMessage: (message: unknown) => Promise<void>;
}

export interface NoStreamHandlerOptions {
  taskId: string;
  targetDeviceId: string;
  onMessage: (message: unknown) => Promise<void>;
}

export interface SendToDeviceOptions {
  deviceId: string;
  message: string | object;
}
