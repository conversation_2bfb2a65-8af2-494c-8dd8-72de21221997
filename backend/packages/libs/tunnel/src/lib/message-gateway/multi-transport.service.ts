import { Injectable, Logger, OnModuleInit, OnModuleDestroy, Inject } from '@nestjs/common';
import { TunnelMessage } from '@saito/models';
import {
  ITransportGateway,
  ISocketTransportGateway,
  ILibp2pTransportGateway,
  ConnectionStatus,
  TransportGatewayType
} from './message-gateway.interface';

/**
 * 多传输网关管理服务
 * 统一管理Socket和Libp2p传输，维护peerId -> transportType映射
 * 提供统一的消息路由和协议归因功能
 */
@Injectable()
export class MultiTransportService implements ITransportGateway, OnModuleInit, OnModuleDestroy {
  private readonly logger = new Logger(MultiTransportService.name);

  // 传输网关实例
  private transports: Map<TransportGatewayType, ITransportGateway> = new Map();

  // peerId -> transportType mapping
  private peerTransportMap: Map<string, TransportGatewayType> = new Map();

  // 回调函数
  private messageCallback: ((message: TunnelMessage) => void) | null = null;
  private errorCallback: ((error: Error) => void) | null = null;

  private isInitialized = false;

  constructor(
    @Inject('MessageGateway') private readonly socketGateway: ISocketTransportGateway,
    @Inject('MessageLibp2pGateway') private readonly libp2pGateway: ILibp2pTransportGateway,
    @Inject('PEER_ID') private readonly peerId: string
  ) {}

  async onModuleInit() {
    this.logger.log('MultiTransportService initializing...');

    // 注册传输网关
    this.transports.set('socket', this.socketGateway);
    this.transports.set('libp2p', this.libp2pGateway);

    // 设置消息回调
    this.socketGateway.onMessage((message) => this.handleIncomingMessage(message, 'socket'));
    this.libp2pGateway.onMessage((message) => this.handleIncomingMessage(message, 'libp2p'));

    // 设置错误回调
    this.socketGateway.onError!((error) => this.handleError(error, 'socket'));
    this.libp2pGateway.onError!((error) => this.handleError(error, 'libp2p'));

    this.isInitialized = true;
    this.logger.log('MultiTransportService initialized with Socket and Libp2p gateways');
  }

  async onModuleDestroy() {
    this.logger.log('MultiTransportService destroying...');
    this.peerTransportMap.clear();
    this.transports.clear();
    this.isInitialized = false;
  }

  /**
   * 处理传入消息，判断transportType
   */
  public handleIncomingMessage(message: TunnelMessage, transportType: TransportGatewayType): void {
    // 记录消息来源的transportType
    if (message.from) {
      this.setPeerTransportType(message.from, transportType);
      this.logger.debug(`[MultiTransport] Protocol attribution: ${message.from} -> ${transportType}`);
    }

    // TODO 添加schema
    // 转发消息到业务层
    if (this.messageCallback) {
      // TODO: 将来自一个通信的信息用相同的通道回复
      // 在消息中标记传输协议类型，确保回复使用相同协议
      const enrichedMessage = {
        ...message,
        _transportType: transportType,
        // _originalTransport: transportType  // 添加original传输协议标记，用于判断是否是回复消息，这部分还没实现
      };
      this.messageCallback(enrichedMessage);
    }
  }

  /**
   * 处理传输错误
   */
  private handleError(error: Error, transportType: TransportGatewayType): void {
    this.logger.error(`[MultiTransport] ${transportType} transport error:`, error);
    if (this.errorCallback) {
      this.errorCallback(error);
    }
  }

  /**
   * peerId -> transportType 的增删改查
   */
  setPeerTransportType(peerId: string, transportType: TransportGatewayType): void {
    this.peerTransportMap.set(peerId, transportType);
    this.logger.debug(`[MultiTransport] Mapped peer ${peerId} to ${transportType} transport`);
  }

  getPeerTransportType(peerId: string): TransportGatewayType | null {
    return this.peerTransportMap.get(peerId) || null;
  }

  removePeerTransportType(peerId: string): void {
    this.peerTransportMap.delete(peerId);
    this.logger.debug(`[MultiTransport] Removed transport mapping for peer ${peerId}`);
  }

  getAllPeerMappings(): Map<string, TransportGatewayType> {
    return new Map(this.peerTransportMap);
  }

  // ITransportGateway interface implementation

  /**
   * 发送消息 - 会根据 transportType 选择发送信道
   */
  async sendMessage(message: TunnelMessage): Promise<void> {
    const transportType = this.selectTransportForMessage(message);
    const transport = this.transports.get(transportType);

    if (!transport) {
      throw new Error(`No available transport for message to ${message.to}`);
    }

    try {
      await transport.sendMessage(message);
      this.logger.debug(`[MultiTransport] Message sent via ${transportType} to ${message.to}`);
    } catch (error) {
      this.logger.error(`[MultiTransport] Failed to send message via ${transportType}:`, error);

      // 已有 transportType 无法使用，尝试使用 fallback 方式
      // 如果是回复消息且有original传输协议标记，不使用fallback路径，直接失败
      // if ((message as any)._originalTransport && (message as any)._originalTransport === transportType) {
      //   this.logger.error(`[MultiTransport] Reply message must use original transport ${transportType}, not falling back`);
      //   throw error;
      // }

      // 尝试使用备用传输协议
      const fallbackType = transportType === 'socket' ? 'libp2p' : 'socket';
      const fallbackTransport = this.transports.get(fallbackType);

      if (fallbackTransport) {
        this.logger.warn(`[MultiTransport] Retrying with ${fallbackType} transport`);
        await fallbackTransport.sendMessage(message);
        // 如果成功，更新映射为成功的传输类型
        this.setPeerTransportType(message.to, fallbackType);
      } else {
        throw error;
      }
    }
  }

  /**
   * 设置消息回调
   */
  onMessage(callback: (message: TunnelMessage) => void): void {
    this.messageCallback = callback;
  }

  /**
   * 设置错误回调
   */
  onError(callback: (error: Error) => void): void {
    this.errorCallback = callback;
  }

  /**
   * 获取连接状态 - 返回任一传输协议的连接状态
   */
  getConnectionStatus(): ConnectionStatus {
    const socketStatus = this.socketGateway.getConnectionStatus();
    const libp2pStatus = this.libp2pGateway.getConnectionStatus();

    return {
      connected: socketStatus.connected || libp2pStatus.connected,
      deviceId: this.peerId,
      gatewayUrl: 'multi-transport-gateway'
    };
  }

  /**
   * 获取传输类型 - 返回'libp2p'作为默认类型
   */
  getTransportType(): 'libp2p' {
    return 'libp2p';
  }

  // 辅助方法

  /**
   * 为消息选择最佳传输协议
   */
  private selectTransportForMessage(message: TunnelMessage): TransportGatewayType {
    // 1. 如果已知peerId的传输类型，优先使用
    const knownType = this.getPeerTransportType(message.to);
    this.logger.debug(`[MultiTransport] Checking known transport for peer ${message.to}: ${knownType}`);
    this.logger.debug(`[MultiTransport] Current peer transport map:`, Array.from(this.peerTransportMap.entries()));
    if (knownType) {
      this.logger.debug(`[MultiTransport] Using known transport ${knownType} for peer ${message.to}`);
      return knownType;
    }

    // 2. 如果没有已连接的类型，检查哪个传输协议能连接到目标设备
    if ((this.socketGateway as ISocketTransportGateway).isDeviceConnected && (this.socketGateway as any).isDeviceConnected(message.to)) {
      this.logger.debug(`[MultiTransport] Device ${message.to} found via socket transport`);
      return 'socket';
    }

    // if ((this.libp2pGateway as any).isDeviceConnected && (this.libp2pGateway as any).isDeviceConnected(message.to)) {
    //   this.logger.debug(`[MultiTransport] Device ${message.to} found via libp2p transport`);
    //   return 'libp2p';
    // }

    // 3. 默认使用libp2p传输
    this.logger.debug(`[MultiTransport] Using default libp2p transport for peer ${message.to}`);
    return 'libp2p';
  }

  /**
   * 检查设备是否在任何传输协议中连接
   */
  isDeviceConnected(deviceId: string): boolean {
    const socketConnected = (this.socketGateway as ISocketTransportGateway).isDeviceConnected ? (this.socketGateway as any).isDeviceConnected(deviceId) : false;
    const libp2pConnected = (this.libp2pGateway as any).isDeviceConnected ? (this.libp2pGateway as any).isDeviceConnected(deviceId) : false;

    return socketConnected || libp2pConnected;
  }

  /**
   * 广播消息到所有传输协议
   */
  async broadcastMessage(message: TunnelMessage): Promise<void> {
    this.logger.debug(`[MultiTransport] Broadcasting message type: ${message.type}`);

    const broadcastPromises = Array.from(this.transports.values()).map(async (transport) => {
      try {
        await transport.sendMessage(message);
        this.logger.debug(`[MultiTransport] Broadcast completed via ${transport.getTransportType()} transport`);
      } catch (error) {
        this.logger.warn(`[MultiTransport] Broadcast failed via ${transport.getTransportType()} transport:`, error);
      }
    });

    await Promise.allSettled(broadcastPromises);
    this.logger.debug(`[MultiTransport] Broadcast completed to ${this.transports.size} transports`);
  }

  /**
   * 获取调试信息
   */
  getDebugInfo() {
    return {
      isInitialized: this.isInitialized,
      transportCount: this.transports.size,
      peerMappingCount: this.peerTransportMap.size,
      transports: Array.from(this.transports.keys()),
      peerMappings: Array.from(this.peerTransportMap.entries()),
      socketStatus: this.socketGateway.getConnectionStatus(),
      libp2pStatus: this.libp2pGateway.getConnectionStatus()
    };
  }
}
