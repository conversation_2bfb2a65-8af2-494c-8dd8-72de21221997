import { Test, TestingModule } from '@nestjs/testing';
import { MessageGatewayServiceImpl } from './message-gateway.service';
import { TunnelServiceImpl } from '../tunnel.service';
import { Server, Socket } from 'socket.io';
import { PingMessage } from '@saito/models';

describe('MessageGatewayServiceImpl', () => {
  let service: MessageGatewayServiceImpl;
  let tunnelService: jest.Mocked<TunnelServiceImpl>;

  const mockSocket = {
    id: 'test-socket-id',
    connected: true,
    emit: jest.fn(),
  } as unknown as Socket;

  beforeEach(async () => {
    const tunnelServiceMock = {
      handleMessage: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        MessageGatewayServiceImpl,
        {
          provide: TunnelServiceImpl,
          useValue: tunnelServiceMock,
        },
      ],
    }).compile();

    service = module.get<MessageGatewayServiceImpl>(MessageGatewayServiceImpl);
    tunnelService = module.get(TunnelServiceImpl) as jest.Mocked<TunnelServiceImpl>;

    // Mock the WebSocket server
    service.server = {
      emit: jest.fn(),
    } as unknown as Server;

    // Clear the peers map
    service['peers'] = new Map();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('handleConnection', () => {
    it('should add a socket to peers map on connection', () => {
      service.handleConnection(mockSocket);
      expect(service['peers'].get('test-socket-id')).toBe(mockSocket);
    });
  });

  describe('handleDisconnect', () => {
    it('should remove a socket from peers map on disconnection', () => {
      service['peers'].set('test-socket-id', mockSocket);
      service.handleDisconnect(mockSocket);
      expect(service['peers'].has('test-socket-id')).toBe(false);
    });
  });

  describe('handleMessage', () => {
    it('should forward message to tunnel service', async () => {
      const message: PingMessage = {
        from: 'sender-id',
        to: 'receiver-id',
        type: 'ping',
        payload: { timestamp: Date.now(), message: 'Hello world' },
      };

      await service.handleMessage(message, mockSocket);
      expect(tunnelService.handleMessage).toHaveBeenCalledWith(message);
    });
  });

  describe('sendMessage', () => {
    it('should send message to the correct peer', () => {
      service['peers'].set('receiver-id', mockSocket);

      const message: PingMessage = {
        from: 'sender-id',
        to: 'receiver-id',
        type: 'ping',
        payload: { timestamp: Date.now(), message: 'Hello world' },
      };

      service.sendMessage(message);
      expect(mockSocket.emit).toHaveBeenCalledWith('message', message);
    });

    it('should throw UnknownPeerError if peer not found', () => {
      const message: PingMessage = {
        from: 'sender-id',
        to: 'receiver-id',
        type: 'ping',
        payload: { timestamp: Date.now(), message: 'Hello world' },
      };

      expect(() => service.sendMessage(message)).toThrow();
    });
  });

  describe('checkInactivePeers', () => {
    it('should remove inactive peers', () => {
      const activeSocket = {
        id: 'test-socket-id',
        connected: true,
        emit: jest.fn(),
      } as unknown as Socket;

      const inactiveSocket = {
        id: 'inactive-id',
        connected: false,
        emit: jest.fn(),
      } as unknown as Socket;

      service['peers'].set('test-socket-id', activeSocket);
      service['peers'].set('inactive-id', inactiveSocket);

      service.checkInactivePeers();

      expect(service['peers'].has('test-socket-id')).toBe(true);
      expect(service['peers'].has('inactive-id')).toBe(false);
    });
});
});
