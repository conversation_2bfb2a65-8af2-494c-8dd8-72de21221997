import {forwardRef, Inject, Injectable, OnModuleInit, Logger} from "@nestjs/common";
import {TunnelMessage} from "@saito/models";
import {ISocketTransportGateway, ConnectionStatus} from "./message-gateway.interface";
import {MessageBody, SubscribeMessage, WebSocketGateway, WebSocketServer, ConnectedSocket} from "@nestjs/websockets";
import {Server, Socket} from "socket.io";
import {UnknownPeerError} from "../errors/unknown-peer.error";
import { TunnelService } from "../tunnel.interface";

@Injectable()
@WebSocketGateway()
export class SocketMessageGatewayService implements ISocketTransportGateway, OnModuleInit {
  private readonly logger = new Logger(SocketMessageGatewayService.name);

  constructor(
    @Inject(forwardRef(() => 'TunnelService'))
    private readonly tunnelService: TunnelService,
    @Inject('PEER_ID') private readonly peerId: string
  ) {}
  @WebSocketServer()
  server!: Server; // WebSocket server instance
  private peers: Map<string, Socket> = new Map(); // Socket ID -> Socket
  private deviceToPeer: Map<string, string> = new Map(); // Device ID -> Socket ID
  private peerToDevice: Map<string, string> = new Map(); // Socket ID -> Device ID

  // 回调函数
  private messageCallback: ((message: TunnelMessage) => void) | null = null;
  private connectionCallback: ((connected: boolean) => void) | null = null;
  private errorCallback: ((error: Error) => void) | null = null;

  onModuleInit() {
    // Periodic heartbeat check to clean up inactive connections
    setInterval(() => {
      this.checkInactivePeers();
    }, 60000); // Check every minute
  }

  checkInactivePeers() {
    this.peers.forEach((socket, peerId) => {
      if (!socket.connected) {
        this.logger.log(`Peer ${peerId} is inactive, cleaning up.`);
        this.peers.delete(peerId);

        // 清理设备映射
        const deviceId = this.peerToDevice.get(peerId);
        if (deviceId) {
          this.deviceToPeer.delete(deviceId);
          this.peerToDevice.delete(peerId);
          this.logger.log(`Cleaned up device mapping for device ${deviceId}`);

          // TODO: 实现设备状态更新
        }
      }
    });
  }

  // When a new client connects
  handleConnection(client: Socket) {
    try {
      console.log('Peer connected with ID:', client.id);
      this.peers.set(client.id, client);
    } catch (error) {
      console.error('Error handling connection:', error);
    }
  }

  // When a client disconnects
  handleDisconnect(client: Socket) {
    try {
      this.logger.log(`Peer disconnected with ID: ${client.id}`);
      this.peers.delete(client.id);

      // 清理设备映射并立即更新设备状态
      const deviceId = this.peerToDevice.get(client.id);
      if (deviceId) {
        this.deviceToPeer.delete(deviceId);
        this.peerToDevice.delete(client.id);
        this.logger.log(`Device ${deviceId} disconnected (peer ${client.id})`);

        // TODO: 实现设备状态更新
      }
    } catch (error) {
      this.logger.error(`Error handling disconnection: ${error}`);
    }
  }

  // Handling messages from the client
  @SubscribeMessage('message')
  async handleMessage(@ConnectedSocket() client: Socket, @MessageBody() message: TunnelMessage): Promise<void> {
    try {
      console.log(`[MessageGateway] Received message from client ${client.id}:`, {
        type: message.type,
        from: message.from,
        to: message.to,
        hasPayload: !!message.payload
      });

      // 检查是否是设备注册消息
      // if (message.type === 'device_register' && message.payload && typeof message.payload === 'object' && 'deviceId' in message.payload) {
      //   const deviceId = message.payload.deviceId as string;
      //   console.log(`[MessageGateway] Registering device ${deviceId} with peer ${client.id}`);

      //   // 建立设备ID和Socket ID的映射
      //   this.deviceToPeer.set(deviceId, client.id);
      //   this.peerToDevice.set(client.id, deviceId);

      //   console.log(`[MessageGateway] Device mapping created: ${deviceId} -> ${client.id}`);
      //   console.log(`[MessageGateway] Total device mappings: ${this.deviceToPeer.size}`);
      //   console.log(`[MessageGateway] All device mappings: ${Array.from(this.deviceToPeer.entries()).map(([d, p]) => `${d}->${p}`).join(', ')}`);

      //   // 发送注册确认
      //   client.emit('message', {
      //     from: 'gateway',
      //     to: deviceId,
      //     type: 'device_register_ack',
      //     payload: { success: true, deviceId },
      //     timestamp: Date.now()
      //   });

      //   console.log(`[MessageGateway] Device ${deviceId} successfully registered with peer ${client.id}`);

      //   // 自动触发设备注册请求处理，创建数据库记录
      //   // 检查payload中是否包含注册所需的信息
      //   if (message.payload && typeof message.payload === 'object') {
      //     const payload = message.payload as any;

      //     // 如果包含注册信息，自动创建device_register_request消息
      //     if (payload.code || payload.gateway_address) {
      //       console.log(`[MessageGateway] Auto-triggering device registration for ${deviceId}`);

      //       const registerRequestMessage: TunnelMessage = {
      //         type: 'device_register_request' as const,
      //         from: deviceId,
      //         to: this.peerId,
      //         payload: {
      //           code: payload.code || 'AUTO_GENERATED',
      //           device_id: deviceId,
      //           gateway_address: payload.gateway_address || 'localhost',
      //           reward_address: payload.reward_address || 'unknown',
      //           device_type: payload.device_type || payload.type || 'unknown',
      //           gpu_type: payload.gpu_type || payload.gpu_model || null,
      //           ip: payload.ip || null,
      //           local_models: payload.local_models || null,
      //           did_document: payload.did_document || null // 传递DID Document
      //         }
      //       };

      //       // 转发到tunnel服务进行处理
      //       console.log(`[MessageGateway] Forwarding auto-generated registration request for ${deviceId}`);
      //       await this.tunnelService.handleMessage(registerRequestMessage);
      //     }
      //   }

      //   return;
      // }

      // 检查是否是设备注册请求消息，如果是则建立映射关系
      if (message.type === 'device_register_request' && message.from) {
        const deviceId = message.from;
        console.log(`[MessageGateway] Processing device_register_request from ${deviceId}`);

        // 检查是否已经有映射关系
        if (!this.deviceToPeer.has(deviceId)) {
          // 建立设备ID和Socket ID的映射
          this.deviceToPeer.set(deviceId, client.id);
          this.peerToDevice.set(client.id, deviceId);

          console.log(`[MessageGateway] Auto-created device mapping for register_request: ${deviceId} -> ${client.id}`);
          console.log(`[MessageGateway] Total device mappings after auto-creation: ${this.deviceToPeer.size}`);
          console.log(`[MessageGateway] All device mappings: ${Array.from(this.deviceToPeer.entries()).map(([d, p]) => `${d}->${p}`).join(', ')}`);
        } else {
          console.log(`[MessageGateway] Device mapping already exists for ${deviceId}`);
        }
      }

      // 调用消息回调函数
      if (this.messageCallback) {
        console.log(`[MessageGateway] Calling message callback for: ${message.type}`);
        this.messageCallback(message);
      } else {
        // 向后兼容：如果没有设置回调，直接调用tunnelService
        console.log(`[MessageGateway] Forwarding message to TunnelService (fallback): ${message.type}`);
        await this.tunnelService.handleMessage(message);
      }
    } catch (error) {
      console.error('Error handling WebSocket message:', error);
      // 确保抛出的是标准 Error 对象
      if (error instanceof Error) {
        throw error;
      } else {
        throw new Error(`WebSocket message handling failed: ${String(error)}`);
      }
    }
  }

  // Method to send a message to a specific peer using their socketId
  async sendMessage(message: TunnelMessage): Promise<void> {
    try {
      console.log(`[MessageGateway] Attempting to send message to: ${message.to}, type: ${message.type}`);

      // 首先尝试直接使用 message.to 作为 Socket ID
      let socket = this.peers.get(message.to);

      // 如果没找到，尝试使用设备ID映射
      if (!socket) {
        const peerId = this.deviceToPeer.get(message.to);
        if (peerId) {
          socket = this.peers.get(peerId);
          console.log(`[MessageGateway] Using device mapping: device ${message.to} -> peer ${peerId}`);
        } else {
          console.log(`[MessageGateway] No device mapping found for: ${message.to}`);
        }
      } else {
        console.log(`[MessageGateway] Found direct peer connection for: ${message.to}`);
      }

      if (!socket) {
        console.log(`[MessageGateway] Failed to find socket for: ${message.to}`);
        console.log(`[MessageGateway] Available peers: ${Array.from(this.peers.keys()).join(', ')}`);
        console.log(`[MessageGateway] Available device mappings: ${Array.from(this.deviceToPeer.entries()).map(([d, p]) => `${d}->${p}`).join(', ')}`);
        console.log(`[MessageGateway] Total peers: ${this.peers.size}, Total mappings: ${this.deviceToPeer.size}`);
        throw new UnknownPeerError(message.to);
      }

      console.log(`[MessageGateway] Successfully sending message to ${message.to}`);
      socket.emit("message", message);
    } catch (error) {
      console.error('[MessageGateway] Error sending message:', error);
      // 确保抛出的是标准 Error 对象
      if (error instanceof Error) {
        throw error;
      } else {
        throw new Error(`Failed to send message: ${String(error)}`);
      }
    }
  }

  // 添加一个方法来检查设备是否连接（供 TunnelService 使用）
  isDeviceConnected(deviceId: string): boolean {
    // this.logger.debug(`Checking connection status for device: ${deviceId}`);

    // // 首先检查是否直接作为 peer ID 存在
    // if (this.peers.has(deviceId)) {
    //   const socket = this.peers.get(deviceId);
    //   const isConnected = socket?.connected || false;
    //   this.logger.debug(`Device ${deviceId} found as direct peer, connected: ${isConnected}`);

    //   // 如果socket存在但未连接，清理映射
    //   if (socket && !socket.connected) {
    //     this.logger.warn(`Socket for device ${deviceId} exists but is not connected, cleaning up`);
    //     this.peers.delete(deviceId);
    //     return false;
    //   }

    //   return isConnected;
    // }

    // // 然后检查设备ID映射
    // const peerId = this.deviceToPeer.get(deviceId);
    // if (peerId) {
    //   this.logger.debug(`Device ${deviceId} mapped to peer: ${peerId}`);

    //   if (this.peers.has(peerId)) {
    //     const socket = this.peers.get(peerId);
    //     const isConnected = socket?.connected || false;
    //     this.logger.debug(`Peer ${peerId} for device ${deviceId}, connected: ${isConnected}`);

    //     // 如果socket存在但未连接，清理映射
    //     if (socket && !socket.connected) {
    //       this.logger.warn(`Socket for peer ${peerId} (device ${deviceId}) exists but is not connected, cleaning up`);
    //       this.peers.delete(peerId);
    //       this.deviceToPeer.delete(deviceId);
    //       this.peerToDevice.delete(peerId);
    //       return false;
    //     }

    //     return isConnected;
    //   } else {
    //     this.logger.warn(`Device ${deviceId} mapped to peer ${peerId}, but peer not found in peers map, cleaning up mapping`);
    //     this.deviceToPeer.delete(deviceId);
    //     this.peerToDevice.delete(peerId);
    //     return false;
    //   }
    // }

    // this.logger.debug(`Device ${deviceId} not found in any mappings`);
    // return false;
    return true;
  }

  // 获取连接状态
  getConnectionStatus(): ConnectionStatus {
    return {
      connected: this.peers.size > 0,
      deviceId: this.peerId,
      gatewayUrl: 'socket-gateway' // Socket gateway doesn't have a specific URL
    };
  }

  // 调试方法：获取详细连接状态
  getDetailedConnectionStatus() {
    return {
      totalPeers: this.peers.size,
      totalDeviceMappings: this.deviceToPeer.size,
      peers: Array.from(this.peers.keys()),
      deviceMappings: Array.from(this.deviceToPeer.entries()).map(([deviceId, peerId]) => ({
        deviceId,
        peerId,
        connected: this.peers.get(peerId)?.connected || false
      }))
    };
  }

  // 手动注册设备（用于测试）
  manualRegisterDevice(deviceId: string, peerId: string) {
    if (this.peers.has(peerId)) {
      this.deviceToPeer.set(deviceId, peerId);
      this.peerToDevice.set(peerId, deviceId);
      console.log(`Manually registered device ${deviceId} with peer ${peerId}`);
      return true;
    }
    console.log(`Cannot register device ${deviceId}: peer ${peerId} not found`);
    return false;
  }

  // ISocketTransportGateway interface methods
  async connect(gatewayAddress: string, code?: string, basePath?: string): Promise<void> {
    // Socket gateway is server-side, so connect is not applicable
    // This method exists for interface compatibility
    this.logger.log('Socket gateway connect called (server-side implementation)');
  }

  async disconnect(): Promise<void> {
    // Disconnect all connected peers
    this.peers.forEach((socket) => {
      socket.disconnect();
    });
    this.peers.clear();
    this.deviceToPeer.clear();
    this.peerToDevice.clear();
    this.logger.log('All socket connections disconnected');
  }

  isConnected(): boolean {
    return this.peers.size > 0;
  }

  getDeviceId(): string | null {
    return this.peerId;
  }

  onMessage(callback: (message: TunnelMessage) => void): void {
    this.messageCallback = callback;
  }

  onConnectionChange(callback: (connected: boolean) => void): void {
    this.connectionCallback = callback;
  }

  onError(callback: (error: Error) => void): void {
    this.errorCallback = callback;
  }

  getTransportType(): 'socket' {
    return 'socket';
  }
}

export const MessageGatewayProvider = {
  provide: 'MessageGateway',
  useClass: SocketMessageGatewayService,
};
