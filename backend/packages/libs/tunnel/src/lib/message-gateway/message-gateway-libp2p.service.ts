import { Inject, Injectable, Logger, forwardRef } from '@nestjs/common';
import { TunnelMessage } from '@saito/models';
import { TunnelService } from '../tunnel.interface';
import { ILibp2pTransportGateway, ConnectionStatus } from './message-gateway.interface';
import axios from 'axios';

@Injectable()
export class Libp2pMessageGatewayService implements ILibp2pTransportGateway {
  private readonly logger = new Logger(Libp2pMessageGatewayService.name);

  constructor(
    @Inject(forwardRef(() => 'TunnelService'))
    private readonly tunnelService: TunnelService,
    @Inject('PEER_ID') private readonly peerId: string,
  ) {}

  // 回调函数
  private messageCallback: ((message: TunnelMessage) => void) | null = null;
  private errorCallback: ((error: Error) => void) | null = null;

  /**
   * Handle incoming libp2p message
   * This method is called by the libp2p controller when a message is received
   */
  async handleIncomingMessage(message: TunnelMessage): Promise<void> {
    try {
      this.logger.debug(`[Libp2pGateway] Received message:`, {
        type: message.type,
        from: message.from,
        to: message.to,
      });

      // 调用消息回调函数
      if (this.messageCallback) {
        this.logger.debug(`[Libp2pGateway] Calling message callback for: ${message.type}`);
        this.messageCallback(message);
      } else {
        // 向后兼容：如果没有设置回调，直接调用tunnelService
        this.logger.debug(`[Libp2pGateway] Forwarding message to TunnelService (fallback): ${message.type}`);
        await this.tunnelService.handleMessage(message);
      }
    } catch (error) {
      this.logger.error('Error handling libp2p message:', error);
      if (this.errorCallback) {
        this.errorCallback(error instanceof Error ? error : new Error(String(error)));
      }
      throw error instanceof Error ? error : new Error(`Libp2p message handling failed: ${String(error)}`);
    }
  }

  // Method to send a message to a specific peer
  async sendMessage(message: TunnelMessage): Promise<void> {
    try {
      const port = process.env['LIBP2P_PORT'] || 4012;
      const url = `http://localhost:${port}/libp2p/send`;
      this.logger.debug(`[Libp2pGateway] Send message to: ${url}`, message);
      await axios.post(url, message);
      // this.logger.log(`[Libp2pGateway] Message sent to libp2p REST:`, {
      //   url,
      //   to: message.to,
      //   type: message.type,
      // });
    } catch (err) {
      this.logger.error(
        '[Libp2pGateway] Failed to send message via libp2p REST',
        err,
      );
      throw err;
    }
  }

// In libp2p, devices are reachable if they're in the network
// TODO, check how is a device is connected in libp2p?
  isDeviceConnected(deviceId: string): boolean {
    this.logger.debug(`[Libp2pGateway] Checking device connectivity for: ${deviceId}`);
    return true; 
  }

  // 获取连接状态
  getConnectionStatus(): ConnectionStatus {
    return {
      connected: true, // Libp2p is always "connected" if the service is running
      deviceId: this.peerId,
      gatewayUrl: 'libp2p-gateway' // Libp2p gateway identifier
    };
  }

  // ILibp2pTransportGateway interface methods
  onMessage(callback: (message: TunnelMessage) => void): void {
    this.messageCallback = callback;
  }

  onError(callback: (error: Error) => void): void {
    this.errorCallback = callback;
  }

  getTransportType(): 'libp2p' {
    return 'libp2p';
  }
}

export const MessageGatewayLibp2pProvider = {
  provide: 'MessageLibp2pGateway',
  useClass: Libp2pMessageGatewayService,
};
