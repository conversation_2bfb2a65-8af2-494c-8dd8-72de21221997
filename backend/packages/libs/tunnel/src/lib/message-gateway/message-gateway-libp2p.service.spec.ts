import axios from 'axios';
import { MessageGatewayLibp2pService } from './message-gateway-libp2p.service';

jest.mock('axios');
const mockedAxios = axios as jest.Mocked<typeof axios>;

describe('MessageGatewayLibp2pService', () => {
  let service: MessageGatewayLibp2pService;

  const mockTunnelService = {
    handleMessage: jest.fn(),
  };
  const mockPeerId = 'peer-id-test';

  beforeEach(() => {
    service = new MessageGatewayLibp2pService(
      mockTunnelService as any,
      mockPeerId,
    );
    jest.clearAllMocks();
  });

  it('should send message successfully', async () => {
    mockedAxios.post.mockResolvedValue({ data: { status: 'ok' } });

    await expect(
      service.sendMessage({ from: 'a', to: 'b', type: 'ping', payload: { timestamp: 12323123, message: 'foo' } }),
    ).resolves.not.toThrow();

    expect(mockedAxios.post).toHaveBeenCalledWith(
      'http://localhost:4012/libp2p/send',
      { from: 'a', to: 'b', type: 'ping', payload: { timestamp: 12323123, message: 'foo' } },
      expect.objectContaining({
        headers: { 'Content-Type': 'application/json' },
      }),
    );
  });

  // 下面这些接口如果你的类没实现，就注释掉
  // it('should call onMessage callback', async () => {
  //   const callback = jest.fn();
  //   service.onMessage(callback);
  //   const msg = { from: 'x', to: 'y', type: 'ping', payload: { timestamp: 12323123, message: 'foo' } };
  //   await service.receiveMessage(msg);
  //   expect(callback).toHaveBeenCalledWith(msg);
  // });

  // it('isConnected should always return true', () => {
  //   expect(service.isConnected()).toBe(true);
  // });

  // it('getDeviceId should return null', () => {
  //   expect(service.getDeviceId()).toBeNull();
  // });
});
