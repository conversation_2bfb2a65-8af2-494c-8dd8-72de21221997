import { Test, TestingModule } from '@nestjs/testing';
import { Logger } from '@nestjs/common';
import { MultiTransportService } from './multi-transport.service';
import { ISocketTransportGateway, ILibp2pTransportGateway } from './message-gateway.interface';
import { TunnelMessage } from '@saito/models';

describe('MultiTransportService', () => {
  let service: MultiTransportService;
  let mockSocketGateway: jest.Mocked<ISocketTransportGateway>;
  let mockLibp2pGateway: jest.Mocked<ILibp2pTransportGateway>;

  beforeEach(async () => {
    // 创建mock gateways
    mockSocketGateway = {
      sendMessage: jest.fn(),
      onMessage: jest.fn(),
      onError: jest.fn(),
      getConnectionStatus: jest.fn().mockReturnValue({
        connected: true,
        deviceId: 'test-device',
        gatewayUrl: 'socket-gateway'
      }),
      getTransportType: jest.fn().mockReturnValue('socket'),
      connect: jest.fn(),
      disconnect: jest.fn(),
      isConnected: jest.fn().mockReturnValue(true),
      onConnectionChange: jest.fn(),
      getConnectedDevices: jest.fn().mockReturnValue([]),
      broadcastMessage: jest.fn()
    } as any;

    mockLibp2pGateway = {
      sendMessage: jest.fn(),
      onMessage: jest.fn(),
      onError: jest.fn(),
      getConnectionStatus: jest.fn().mockReturnValue({
        connected: true,
        deviceId: 'test-device',
        gatewayUrl: 'libp2p-gateway'
      }),
      getTransportType: jest.fn().mockReturnValue('libp2p')
    } as any;

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        MultiTransportService,
        {
          provide: 'MessageGateway',
          useValue: mockSocketGateway,
        },
        {
          provide: 'MessageLibp2pGateway',
          useValue: mockLibp2pGateway,
        },
        {
          provide: 'PEER_ID',
          useValue: 'test-peer-id',
        },
      ],
    }).compile();

    service = module.get<MultiTransportService>(MultiTransportService);
    
    // 手动初始化服务
    await service.onModuleInit();
  });

  afterEach(async () => {
    await service.onModuleDestroy();
  });

  describe('基本功能', () => {
    it('应该正确初始化', () => {
      expect(service).toBeDefined();
      expect(service.getTransportType()).toBe('socket');
    });

    it('应该返回连接状态', () => {
      const status = service.getConnectionStatus();
      expect(status.connected).toBe(true);
      expect(status.deviceId).toBe('test-peer-id');
    });
  });

  describe('peerId映射管理', () => {
    it('应该能设置和获取peerId映射', () => {
      service.setPeerTransportType('peer1', 'socket');
      service.setPeerTransportType('peer2', 'libp2p');

      expect(service.getPeerTransportType('peer1')).toBe('socket');
      expect(service.getPeerTransportType('peer2')).toBe('libp2p');
      expect(service.getPeerTransportType('peer3')).toBeNull();
    });

    it('应该能移除peerId映射', () => {
      service.setPeerTransportType('peer1', 'socket');
      expect(service.getPeerTransportType('peer1')).toBe('socket');

      service.removePeerTransportType('peer1');
      expect(service.getPeerTransportType('peer1')).toBeNull();
    });

    it('应该能获取所有映射', () => {
      service.setPeerTransportType('peer1', 'socket');
      service.setPeerTransportType('peer2', 'libp2p');

      const mappings = service.getAllPeerMappings();
      expect(mappings.size).toBe(2);
      expect(mappings.get('peer1')).toBe('socket');
      expect(mappings.get('peer2')).toBe('libp2p');
    });
  });

  describe('消息发送', () => {
    it('应该使用已知的传输类型发送消息', async () => {
      const message: TunnelMessage = {
        id: 'test-id',
        type: 'test',
        from: 'sender',
        to: 'peer1',
        data: {}
      };

      // 设置peer1使用socket传输
      service.setPeerTransportType('peer1', 'socket');

      await service.sendMessage(message);

      expect(mockSocketGateway.sendMessage).toHaveBeenCalledWith(message);
      expect(mockLibp2pGateway.sendMessage).not.toHaveBeenCalled();
    });

    it('应该在主传输失败时使用备用传输', async () => {
      const message: TunnelMessage = {
        id: 'test-id',
        type: 'test',
        from: 'sender',
        to: 'peer1',
        data: {}
      };

      // 设置peer1使用socket传输
      service.setPeerTransportType('peer1', 'socket');
      
      // 模拟socket传输失败
      mockSocketGateway.sendMessage.mockRejectedValueOnce(new Error('Socket failed'));

      await service.sendMessage(message);

      expect(mockSocketGateway.sendMessage).toHaveBeenCalledWith(message);
      expect(mockLibp2pGateway.sendMessage).toHaveBeenCalledWith(message);
      
      // 验证映射已更新为成功的传输类型
      expect(service.getPeerTransportType('peer1')).toBe('libp2p');
    });

    it('应该默认使用socket传输', async () => {
      const message: TunnelMessage = {
        id: 'test-id',
        type: 'test',
        from: 'sender',
        to: 'unknown-peer',
        data: {}
      };

      await service.sendMessage(message);

      expect(mockSocketGateway.sendMessage).toHaveBeenCalledWith(message);
    });
  });

  describe('协议归因', () => {
    it('应该在处理传入消息时设置协议归因', () => {
      const message: TunnelMessage = {
        id: 'test-id',
        type: 'test',
        from: 'sender',
        to: 'receiver',
        data: {}
      };

      // 模拟从socket传输接收消息
      const messageCallback = mockSocketGateway.onMessage.mock.calls[0][0];
      messageCallback(message);

      // 验证协议归因已设置
      expect(service.getPeerTransportType('sender')).toBe('socket');
    });
  });

  describe('广播消息', () => {
    it('应该向所有传输协议广播消息', async () => {
      const message: TunnelMessage = {
        id: 'test-id',
        type: 'broadcast',
        from: 'sender',
        to: 'all',
        data: {}
      };

      await service.broadcastMessage(message);

      expect(mockSocketGateway.sendMessage).toHaveBeenCalledWith(message);
      expect(mockLibp2pGateway.sendMessage).toHaveBeenCalledWith(message);
    });
  });

  describe('调试信息', () => {
    it('应该返回正确的调试信息', () => {
      service.setPeerTransportType('peer1', 'socket');
      service.setPeerTransportType('peer2', 'libp2p');

      const debugInfo = service.getDebugInfo();

      expect(debugInfo.isInitialized).toBe(true);
      expect(debugInfo.transportCount).toBe(2);
      expect(debugInfo.peerMappingCount).toBe(2);
      expect(debugInfo.transports).toEqual(['socket', 'libp2p']);
      expect(debugInfo.peerMappings).toEqual([['peer1', 'socket'], ['peer2', 'libp2p']]);
    });
  });
});
