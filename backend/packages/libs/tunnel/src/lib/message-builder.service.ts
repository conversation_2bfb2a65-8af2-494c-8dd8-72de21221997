import { Injectable, Inject } from '@nestjs/common';
import { TunnelMessage, StreamChatRequestMessage, PingMessage } from '@saito/models';

/**
 * 消息构造器服务
 */
@Injectable()
export class MessageBuilderService {
  constructor(
    @Inject('PEER_ID') private readonly peerId: string
  ) {}
  /**
   * 构造聊天请求消息
   * @param taskId 任务ID
   * @param data 聊天数据
   * @param to 目标设备ID
   * @returns 构造好的 StreamChatRequestMessage
   */
  buildChatRequestMessage(
    taskId: string,
    data: {
      model: string;
      messages: Array<{ role: string; content: string }>;
      stream?: boolean;
      temperature?: number;
      top_p?: number;
      n?: number;
      max_tokens?: number;
      presence_penalty?: number;
      frequency_penalty?: number;
      [key: string]: any; // 允许其他字段
    },
    to: string,
    path: string = '/v1/chat/completions' // 添加path参数，提供默认值
  ): StreamChatRequestMessage {
    return {
      from: this.peerId,
      to,
      type: 'chat_request_stream',
      payload: {
        taskId,
        path, // 添加path字段
        data: {
          // 提供默认值以满足 TunnelOpenAIChatCompletionRequest 的要求
          temperature: 0.7,
          top_p: 1.0,
          n: 1,
          presence_penalty: 0,
          frequency_penalty: 0,
          // 覆盖默认值，包括stream字段
          ...data,
          // 确保stream字段正确设置
          stream: data.stream ?? true
        }
      }
    };
  }
}
