import { Test, TestingModule } from '@nestjs/testing';
import { TunnelServiceImpl } from './tunnel.service';
import { MessageHandlerRegistry } from './message-handler/message-handler.registry';
import { ITransportGateway } from './message-gateway/message-gateway.interface';
import { TunnelMessage } from '@saito/models';
import { UnknownMessageTypeError } from './errors/unknown-message-type.error';
import {IncomeBaseMessageHandler, MessageHandler, OutcomeBaseMessageHandler} from "@saito/tunnel";

// Mock message handlers
@MessageHandler({ type: 'test', direction: 'income' })
class MockIncomeHandler extends IncomeBaseMessageHandler {
  async handleIncomeMessage(message: TunnelMessage): Promise<void> {}

  override handleMessage = jest.fn().mockResolvedValue(undefined);
}

@MessageHandler({ type: 'test', direction: 'outcome' })
class MockOutcomeHandler extends OutcomeBaseMessageHandler {
  async handleOutcomeMessage(message: TunnelMessage): Promise<void> {}
  override handleMessage = jest.fn().mockResolvedValue(undefined);
}

describe('TunnelServiceImpl', () => {
  let service: TunnelServiceImpl;
  let handlerRegistry: jest.Mocked<MessageHandlerRegistry>;
  let messageSender: jest.Mocked<ITransportGateway>;
  const peerId = 'test-peer-id';

  beforeEach(async () => {
    const handlerRegistryMock = {
      getIncomeHandler: jest.fn(),
      getOutcomeHandler: jest.fn(),
    };

    const messageSenderMock = {
      sendMessage: jest.fn(),
      getConnectionStatus: jest.fn().mockReturnValue({ connected: true }),
      onMessage: jest.fn(),
      onError: jest.fn(),
      getTransportType: jest.fn().mockReturnValue('socket'),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        TunnelServiceImpl,
        {
          provide: MessageHandlerRegistry,
          useValue: handlerRegistryMock,
        },
        {
          provide: 'MessageGateway',
          useValue: messageSenderMock,
        },
        {
          provide: 'PEER_ID',
          useValue: peerId,
        },
      ],
    }).compile();

    service = module.get<TunnelServiceImpl>(TunnelServiceImpl);
    handlerRegistry = module.get(MessageHandlerRegistry) as jest.Mocked<MessageHandlerRegistry>;
    messageSender = module.get('MessageGateway') as jest.Mocked<ITransportGateway>;
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('handleMessage', () => {
    it('should ignore messages where from equals to', async () => {
      const message: TunnelMessage = {
        from: 'same-id',
        to: 'same-id',
        type: 'test',
        payload: { timestamp: Date.now(), message: 'hello world'},
      };

      await service.handleMessage(message);
      expect(handlerRegistry.getIncomeHandler).not.toHaveBeenCalled();
      expect(handlerRegistry.getOutcomeHandler).not.toHaveBeenCalled();
    });

    it('should handle incoming messages', async () => {
      const mockHandler = new MockIncomeHandler();
      jest.spyOn(mockHandler, 'handleMessage');
      handlerRegistry.getIncomeHandler.mockReturnValue(mockHandler);

      const message: TunnelMessage = {
        from: 'sender-id',
        to: peerId,
        type: 'test',
        payload: { timestamp: Date.now(), message: 'hello world'},
      };

      await service.handleMessage(message);
      expect(handlerRegistry.getIncomeHandler).toHaveBeenCalledWith('test');
      expect(mockHandler.handleMessage).toHaveBeenCalledWith(message);
    });

    it('should handle outgoing messages', async () => {
      const mockHandler = new MockOutcomeHandler();
      jest.spyOn(mockHandler, 'handleMessage');
      handlerRegistry.getOutcomeHandler.mockReturnValue(mockHandler);

      const message: TunnelMessage = {
        from: peerId,
        to: 'receiver-id',
        type: 'test',
        payload: { timestamp: Date.now(), message: 'hello world'},
      };

      await service.handleMessage(message);
      expect(handlerRegistry.getOutcomeHandler).toHaveBeenCalledWith('test');
      expect(mockHandler.handleMessage).toHaveBeenCalledWith(message);
    });

    it('should ignore messages not related to this peer', async () => {
      const message: TunnelMessage = {
        from: 'sender-id',
        to: 'receiver-id',
        type: 'test',
        payload: { timestamp: Date.now(), message: 'hello world'},
      };

      await service.handleMessage(message);
      expect(handlerRegistry.getIncomeHandler).not.toHaveBeenCalled();
      expect(handlerRegistry.getOutcomeHandler).not.toHaveBeenCalled();
    });

    it('should throw UnknownMessageTypeError if no income handler found', async () => {
      handlerRegistry.getIncomeHandler.mockReturnValue(undefined);

      const message: TunnelMessage = {
        from: 'sender-id',
        to: peerId,
        type: 'test',
        payload: { timestamp: Date.now(), message: 'hello world'},
      };

      await expect(service.handleMessage(message)).rejects.toThrow(UnknownMessageTypeError);
    });

    it('should throw UnknownMessageTypeError if no outcome handler found', async () => {
      handlerRegistry.getOutcomeHandler.mockReturnValue(undefined);

      const message: TunnelMessage = {
        from: peerId,
        to: 'receiver-id',
        type: 'test',
        payload: { timestamp: Date.now(), message: 'hello world'},
      };

      await expect(service.handleMessage(message)).rejects.toThrow(UnknownMessageTypeError);
    });
  });

  describe('sendMessage', () => {
    it('should forward message to messageSender', async () => {
      const message: TunnelMessage = {
        from: peerId,
        to: 'receiver-id',
        type: 'test',
        payload: { timestamp: Date.now(), message: 'hello world'},
      };

      await service.sendMessage(message);
      expect(messageSender.sendMessage).toHaveBeenCalledWith(message);
    });
  });

  describe('triggerListener', () => {
    it('should call matching listeners', async () => {
      const message: TunnelMessage = {
        from: 'sender-id',
        to: peerId,
        type: 'test',
        payload: { timestamp: Date.now(), message: 'hello world'},
      };

      const matchingListener = {
        match: jest.fn().mockReturnValue(true),
        callback: jest.fn(),
      };

      const nonMatchingListener = {
        match: jest.fn().mockReturnValue(false),
        callback: jest.fn(),
      };

      // Add listeners to the service
      service['listeners'] = [matchingListener, nonMatchingListener];

      await service['triggerListener'](message);

      expect(matchingListener.match).toHaveBeenCalledWith(message);
      expect(matchingListener.callback).toHaveBeenCalledWith(message);
      expect(nonMatchingListener.match).toHaveBeenCalledWith(message);
      expect(nonMatchingListener.callback).not.toHaveBeenCalled();
    });

    it('should remove one-time listeners after they match', async () => {
      const message: TunnelMessage = {
        from: 'sender-id',
        to: peerId,
        type: 'test',
        payload: { timestamp: Date.now(), message: 'hello world'},
      };

      const oneTimeListener = {
        match: jest.fn().mockReturnValue(true),
        callback: jest.fn(),
        once: jest.fn().mockReturnValue(true),
      };

      const persistentListener = {
        match: jest.fn().mockReturnValue(true),
        callback: jest.fn(),
        once: jest.fn().mockReturnValue(false),
      };

      // Add listeners to the service
      service['listeners'] = [oneTimeListener, persistentListener];

      await service['triggerListener'](message);

      // Check that only the persistent listener remains
      expect(service['listeners']).toHaveLength(1);
      expect(service['listeners'][0]).toBe(persistentListener);
    });
  });
});
