import { Injectable, Logger, HttpException, HttpStatus } from '@nestjs/common';
import { Response } from 'express';
import { Task, InferenceError } from '@saito/models';
import { OllamaResponseManager } from './ollama-response.manager';

/**
 * Ollama 错误处理器
 * 职责：统一的错误处理逻辑
 * 设计原则：单一职责原则 - 只负责错误的处理和响应
 */
@Injectable()
export class OllamaErrorHandler {
  private readonly logger = new Logger(OllamaErrorHandler.name);

  constructor(
    private readonly responseManager: OllamaResponseManager
  ) {}

  /**
   * 处理请求错误 - 单一职责原则
   */
  async handleRequestError(error: unknown, task: Task | null, res: Response): Promise<void> {
    this.logger.error(`[OllamaErrorHandler] Error handling request: ${error}`);

    // 清理资源
    if (task?.id) {
      this.responseManager.cleanupResponse(task.id);
    }

    // 发送错误响应
    if (!res.headersSent) {
      const errorResponse = this.createOllamaErrorResponse(error);
      const statusCode = error instanceof HttpException ? error.getStatus() : HttpStatus.INTERNAL_SERVER_ERROR;
      res.status(statusCode).json(errorResponse);
    }
  }

  /**
   * 处理流式错误 - 单一职责原则
   */
  async handleStreamError(taskId: string, error: unknown, res: Response): Promise<void> {
    this.logger.error(`[OllamaErrorHandler] Stream error for task ${taskId}: ${error}`);

    // 对于数据格式错误，不关闭流
    if (error instanceof TypeError) {
      this.logger.warn(`[OllamaErrorHandler] Data format error for task ${taskId}, continuing stream`);
      return;
    }

    // 关闭流并发送错误响应
    this.responseManager.cleanupResponse(taskId);
    if (!res.headersSent) {
      const errorResponse = this.createOllamaErrorResponse(error);
      res.status(HttpStatus.INTERNAL_SERVER_ERROR).json(errorResponse);
    }
  }

  /**
   * 处理非流式错误 - 单一职责原则
   */
  async handleNonStreamError(taskId: string, error: unknown, res: Response): Promise<void> {
    this.logger.error(`[OllamaErrorHandler] Non-stream error for task ${taskId}: ${error}`);

    // 清理存储的响应对象
    this.responseManager.cleanupResponse(taskId);

    if (!res.headersSent) {
      const errorResponse = this.createOllamaErrorResponse(error);
      res.status(HttpStatus.INTERNAL_SERVER_ERROR).json(errorResponse);
    }
  }

  /**
   * 处理错误响应 - 单一职责原则
   * 使用强类型替代any
   */
  async handleErrorResponse(taskId: string, error: InferenceError): Promise<void> {
    const res = this.responseManager.getStreamResponse(taskId);
    if (!res) {
      this.logger.debug(`[OllamaErrorHandler] No active stream for error response, task ${taskId}`);
      return;
    }

    this.logger.error(`[OllamaErrorHandler] Error response for task ${taskId}: ${error.error.message}`);
    this.responseManager.cleanupResponse(taskId);

    if (!res.headersSent) {
      const errorResponse = this.createOllamaErrorResponse(error);
      res.status(HttpStatus.INTERNAL_SERVER_ERROR).json(errorResponse);
    }
  }

  /**
   * 创建Ollama格式的错误响应 - 工厂模式
   */
  private createOllamaErrorResponse(error: unknown): any {
    if (error instanceof HttpException) {
      return {
        error: error.message
      };
    }

    if (error && typeof error === 'object' && 'error' in error) {
      const inferenceError = error as InferenceError;
      return {
        error: inferenceError.error.message || 'Inference error'
      };
    }

    return {
      error: 'Internal server error'
    };
  }

  /**
   * 创建未实现错误响应 - 工厂模式
   */
  createNotImplementedError(message: string): any {
    return {
      error: message
    };
  }
}
