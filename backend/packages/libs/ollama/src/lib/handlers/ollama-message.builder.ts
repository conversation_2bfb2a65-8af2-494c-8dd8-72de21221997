import { Injectable, Logger, Inject } from '@nestjs/common';
import {
  OpenAIChatCompletionRequest,
  OpenAICompletionRequest,
  StreamChatRequestMessage,
  NoStreamChatRequestMessage,
  StreamCompletionRequestMessage,
  NoStreamCompletionRequestMessage
} from '@saito/models';
import { TunnelService } from '@saito/tunnel';

/**
 * Ollama 消息构建器
 * 职责：构建各种tunnel消息，专门为Ollama服务
 * 设计原则：建造者模式 - 专门负责消息的构建和发送
 */
@Injectable()
export class OllamaMessageBuilder {
  private readonly logger = new Logger(OllamaMessageBuilder.name);

  constructor(
    @Inject('TunnelService') private readonly tunnelService: TunnelService,
    @Inject('PEER_ID') private readonly peerId: string
  ) {}

  /**
   * 发送Chat Tunnel消息 - 建造者模式
   */
  async sendChatTunnelMessage(
    taskId: string,
    targetDeviceId: string,
    openaiRequest: OpenAIChatCompletionRequest,
    isStreamRequest: boolean
  ): Promise<void> {
    if (isStreamRequest) {
      await this.sendStreamChatTunnelMessage(taskId, targetDeviceId, openaiRequest);
    } else {
      await this.sendNonStreamChatTunnelMessage(taskId, targetDeviceId, openaiRequest);
    }
  }

  /**
   * 发送Completion Tunnel消息 - 建造者模式
   */
  async sendCompletionTunnelMessage(
    taskId: string,
    targetDeviceId: string,
    openaiRequest: OpenAICompletionRequest,
    isStreamRequest: boolean
  ): Promise<void> {
    if (isStreamRequest) {
      await this.sendStreamCompletionTunnelMessage(taskId, targetDeviceId, openaiRequest);
    } else {
      await this.sendNonStreamCompletionTunnelMessage(taskId, targetDeviceId, openaiRequest);
    }
  }

  /**
   * 发送流式Chat Tunnel消息
   */
  private async sendStreamChatTunnelMessage(
    taskId: string,
    targetDeviceId: string,
    openaiRequest: OpenAIChatCompletionRequest
  ): Promise<void> {
    const tunnelMessage: StreamChatRequestMessage = {
      from: this.peerId,
      to: targetDeviceId,
      type: 'chat_request_stream',
      payload: {
        taskId,
        path: '/openai/chat/completions',
        data: openaiRequest
      }
    };

    await this.tunnelService.handleMessage(tunnelMessage);
  }

  /**
   * 发送非流式Chat Tunnel消息
   */
  private async sendNonStreamChatTunnelMessage(
    taskId: string,
    targetDeviceId: string,
    openaiRequest: OpenAIChatCompletionRequest
  ): Promise<void> {
    const tunnelMessage: NoStreamChatRequestMessage = {
      from: this.peerId,
      to: targetDeviceId,
      type: 'chat_request_no_stream',
      payload: {
        taskId,
        data: openaiRequest
      }
    };

    await this.tunnelService.handleMessage(tunnelMessage);
  }

  /**
   * 发送流式Completion Tunnel消息
   */
  private async sendStreamCompletionTunnelMessage(
    taskId: string,
    targetDeviceId: string,
    openaiRequest: OpenAICompletionRequest
  ): Promise<void> {
    const tunnelMessage: StreamCompletionRequestMessage = {
      from: this.peerId,
      to: targetDeviceId,
      type: 'completion_request_stream',
      payload: {
        taskId,
        path: '/openai/completions',
        data: openaiRequest
      }
    };

    await this.tunnelService.handleMessage(tunnelMessage);
  }

  /**
   * 发送非流式Completion Tunnel消息
   */
  private async sendNonStreamCompletionTunnelMessage(
    taskId: string,
    targetDeviceId: string,
    openaiRequest: OpenAICompletionRequest
  ): Promise<void> {
    const tunnelMessage: NoStreamCompletionRequestMessage = {
      from: this.peerId,
      to: targetDeviceId,
      type: 'completion_request_no_stream',
      payload: {
        taskId,
        data: openaiRequest
      }
    };

    await this.tunnelService.handleMessage(tunnelMessage);
  }
}
