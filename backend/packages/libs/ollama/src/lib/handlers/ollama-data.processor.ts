import { Injectable, Logger } from '@nestjs/common';
import { Response } from 'express';
import { OpenAIChatCompletionChunk } from '@saito/models';
import { OllamaFormatConverter } from './ollama-format.converter';
import { OllamaResponseManager } from './ollama-response.manager';
import { OllamaErrorHandler } from './ollama-error.handler';

/**
 * Ollama 数据处理器
 * 职责：处理来自设备的OpenAI格式响应数据，转换为Ollama格式
 * 设计原则：单一职责原则 - 只负责数据的提取、验证、转换和处理
 */
@Injectable()
export class OllamaDataProcessor {
  private readonly logger = new Logger(OllamaDataProcessor.name);

  constructor(
    private readonly formatConverter: OllamaFormatConverter,
    private readonly responseManager: OllamaResponseManager,
    private readonly errorHandler: OllamaErrorHandler
  ) {}

  /**
   * 处理来自设备的流式响应
   * 应用单一职责原则：接收OpenAI格式，转换为Ollama格式
   */
  async handleStreamResponse(taskId: string, payload: unknown): Promise<void> {
    this.logger.debug(`[OllamaDataProcessor] Processing stream response for task ${taskId}`);

    const res = this.responseManager.getStreamResponse(taskId);
    if (!res) {
      this.logger.debug(`[OllamaDataProcessor] No active stream found for task ${taskId}`);
      return;
    }

    try {
      // 1. 数据提取和验证 - 策略模式
      const openaiChunkData = this.extractOpenAIChunkData(payload);
      if (!openaiChunkData) {
        this.logger.warn(`[OllamaDataProcessor] Invalid OpenAI chunk data for task ${taskId}`);
        return;
      }

      // 2. 格式检测和转换 - OpenAI → Ollama
      const responseFormat = this.formatConverter.detectResponseFormat(openaiChunkData);
      let ollamaResponse: any;

      if (responseFormat === 'chat') {
        const isComplete = this.isOpenAIChatStreamCompleted(openaiChunkData as OpenAIChatCompletionChunk);
        ollamaResponse = this.formatConverter.convertOpenAIChatChunkToOllama(
          openaiChunkData as OpenAIChatCompletionChunk,
          isComplete
        );
      } else if (responseFormat === 'completion') {
        const isComplete = this.isOpenAICompletionStreamCompleted(openaiChunkData as any);
        ollamaResponse = this.formatConverter.convertOpenAICompletionChunkToOllama(
          openaiChunkData as any,
          isComplete
        );
      } else {
        this.logger.warn(`[OllamaDataProcessor] Unknown response format for task ${taskId}`);
        return;
      }

      // 3. 过滤内容
      if (ollamaResponse.message?.content) {
        ollamaResponse.message.content = this.formatConverter.filterThinkTags(ollamaResponse.message.content);
      }
      if (ollamaResponse.response) {
        ollamaResponse.response = this.formatConverter.filterThinkTags(ollamaResponse.response);
      }

      // 4. 数据处理 - 模板方法模式
      await this.processOllamaStreamChunk(taskId, ollamaResponse, res);

    } catch (error) {
      await this.errorHandler.handleStreamError(taskId, error, res);
    }
  }

  /**
   * 处理来自设备的非流式响应
   * 应用单一职责原则：接收OpenAI格式，转换为Ollama格式
   */
  async handleNonStreamResponse(taskId: string, payload: unknown): Promise<void> {
    this.logger.debug(`[OllamaDataProcessor] Processing non-stream response for task ${taskId}`);

    const res = this.responseManager.getNonStreamResponse(taskId);
    if (!res) {
      this.logger.debug(`[OllamaDataProcessor] No active non-stream found for task ${taskId}`);
      return;
    }

    try {
      // 提取和验证OpenAI数据
      const openaiResponseData = this.extractOpenAIResponseData(payload);
      if (!openaiResponseData) {
        this.logger.warn(`[OllamaDataProcessor] Invalid OpenAI response data for task ${taskId}`);
        return;
      }

      // 格式检测和转换 - OpenAI → Ollama
      const responseFormat = this.formatConverter.detectResponseFormat(openaiResponseData);
      let ollamaResponse: any;

      if (responseFormat === 'chat') {
        ollamaResponse = this.formatConverter.convertOpenAIChatResponseToOllama(openaiResponseData as any);
      } else if (responseFormat === 'completion') {
        ollamaResponse = this.formatConverter.convertOpenAICompletionResponseToOllama(openaiResponseData as any);
      } else {
        this.logger.warn(`[OllamaDataProcessor] Unknown response format for task ${taskId}`);
        return;
      }

      // 过滤内容
      if (ollamaResponse.message?.content) {
        ollamaResponse.message.content = this.formatConverter.filterThinkTags(ollamaResponse.message.content);
      }
      if (ollamaResponse.response) {
        ollamaResponse.response = this.formatConverter.filterThinkTags(ollamaResponse.response);
      }

      // 处理非流式响应数据
      await this.responseManager.processNonStreamResponse(taskId, ollamaResponse, res);

    } catch (error) {
      await this.errorHandler.handleNonStreamError(taskId, error, res);
    }
  }

  /**
   * 处理来自设备的流式 Completion 响应
   * 专门处理 completion 格式的数据
   */
  async handleCompletionStreamResponse(taskId: string, payload: unknown): Promise<void> {
    // 复用通用的流式响应处理逻辑
    await this.handleStreamResponse(taskId, payload);
  }

  /**
   * 处理来自设备的非流式 Completion 响应
   * 专门处理 completion 格式的数据
   */
  async handleCompletionNonStreamResponse(taskId: string, payload: unknown): Promise<void> {
    // 复用通用的非流式响应处理逻辑
    await this.handleNonStreamResponse(taskId, payload);
  }

  /**
   * 提取OpenAI chunk数据 - 策略模式
   */
  private extractOpenAIChunkData(payload: unknown): OpenAIChatCompletionChunk | any | null {
    try {
      // 支持不同的payload结构
      let data = payload;
      if (payload && typeof payload === 'object' && 'data' in payload) {
        data = (payload as { data: unknown }).data;
      }

      // 基本验证
      if (!data || typeof data !== 'object') {
        return null;
      }

      // 这里可以添加更严格的Zod验证
      return data as OpenAIChatCompletionChunk | any;
    } catch {
      return null;
    }
  }

  /**
   * 提取OpenAI响应数据 - 策略模式
   */
  private extractOpenAIResponseData(payload: unknown): any | null {
    try {
      // 支持不同的payload结构
      let data = payload;
      if (payload && typeof payload === 'object' && 'data' in payload) {
        data = (payload as { data: unknown }).data;
      }

      // 基本验证
      if (!data || typeof data !== 'object') {
        return null;
      }

      return data;
    } catch {
      return null;
    }
  }

  /**
   * 处理Ollama流式数据块 - 模板方法模式
   */
  private async processOllamaStreamChunk(
    taskId: string,
    ollamaResponse: any,
    res: Response
  ): Promise<void> {
    const isCompleted = ollamaResponse.done;

    if (isCompleted) {
      // 写入最后一个数据块
      await this.responseManager.writeOllamaStreamChunk(ollamaResponse, res);
      // 完成流式响应
      await this.responseManager.completeStream(taskId, res);
    } else {
      // 写入数据块
      await this.responseManager.writeOllamaStreamChunk(ollamaResponse, res);
    }
  }

  /**
   * 判断OpenAI Chat流是否完成 - 策略模式
   */
  private isOpenAIChatStreamCompleted(chunkData: OpenAIChatCompletionChunk): boolean {
    return chunkData.choices?.[0]?.finish_reason === 'stop';
  }

  /**
   * 判断OpenAI Completion流是否完成 - 策略模式
   */
  private isOpenAICompletionStreamCompleted(chunkData: any): boolean {
    return chunkData.choices?.[0]?.finish_reason === 'stop';
  }
}
