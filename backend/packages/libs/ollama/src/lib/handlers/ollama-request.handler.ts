import { Injectable, Logger, Inject, HttpException, HttpStatus } from '@nestjs/common';
import { Response } from 'express';
import {
  OllamaChatRequest,
  OllamaGenerateRequest,
  Task,
  CreateTaskRequest
} from '@saito/models';
import { TaskManager } from '@saito/task-manager';
import { NodeService } from '@saito/node';

/**
 * Ollama 请求处理器
 * 职责：处理HTTP请求的协调和转发
 * 设计原则：单一职责原则 - 只负责请求流程的协调
 */
@Injectable()
export class OllamaRequestHandler {
  private readonly logger = new Logger(OllamaRequestHandler.name);

  constructor(
    @Inject('TaskManager') private readonly taskManager: TaskManager,
    private readonly nodeService: NodeService
  ) {}

  /**
   * 处理聊天请求
   * 应用单一职责原则：只负责请求的协调和转发
   */
  async handleChat(
    _path: string,
    body: OllamaChatRequest,
    res: Response,
    formatConverter: any,
    messageBuilder: any,
    responseManager: any,
    errorHandler: any,
    req?: any
  ): Promise<void> {
    let task: Task | null = null;

    try {
      this.logger.log(`[OllamaRequestHandler] Handling chat request for model: ${body.model}`);

      // 1. 设备选择 - 委托给NodeService
      const targetDevice = await this.selectAvailableDevice(body.model);

      // 2. 任务创建 - 委托给TaskManager（传递API_KEY信息）
      task = await this.createInferenceTask(targetDevice.device_id, body.model, req?.apiKeyInfo);

      // 3. 格式转换 - Ollama → OpenAI
      const openaiRequest = formatConverter.convertChatRequestToOpenAI(body);

      // 4. 响应设置 - 应用策略模式
      const isStreamRequest = this.isStreamingRequest(body.stream);
      if (isStreamRequest) {
        responseManager.setupStreamingResponse(task.id, res, 'ollama');
      } else {
        responseManager.setupNonStreamingResponse(task.id, res, 'ollama');
      }

      // 5. 消息构建和发送 - 应用建造者模式
      await messageBuilder.sendChatTunnelMessage(task.id, targetDevice.device_id, openaiRequest, isStreamRequest);

      this.logger.log(`[OllamaRequestHandler] Chat request sent for task ${task.id}`);

    } catch (error) {
      await errorHandler.handleRequestError(error, task, res);
    }
  }

  /**
   * 处理生成请求
   * 应用单一职责原则：只负责请求的协调和转发
   */
  async handleGenerate(
    _path: string,
    body: OllamaGenerateRequest,
    res: Response,
    formatConverter: any,
    messageBuilder: any,
    responseManager: any,
    errorHandler: any,
    req?: any
  ): Promise<void> {
    let task: Task | null = null;

    try {
      this.logger.log(`[OllamaRequestHandler] Handling generate request for model: ${body.model}`);

      // 1. 设备选择 - 委托给NodeService
      const targetDevice = await this.selectAvailableDevice(body.model);

      // 2. 任务创建 - 委托给TaskManager（传递API_KEY信息）
      task = await this.createInferenceTask(targetDevice.device_id, body.model, req?.apiKeyInfo);

      // 3. 格式转换 - Ollama → OpenAI
      const openaiRequest = formatConverter.convertGenerateRequestToOpenAI(body);

      // 4. 响应设置 - 应用策略模式
      const isStreamRequest = this.isStreamingRequest(body.stream);
      if (isStreamRequest) {
        responseManager.setupStreamingResponse(task.id, res, 'ollama');
      } else {
        responseManager.setupNonStreamingResponse(task.id, res, 'ollama');
      }

      // 5. 消息构建和发送 - 应用建造者模式
      await messageBuilder.sendCompletionTunnelMessage(task.id, targetDevice.device_id, openaiRequest, isStreamRequest);

      this.logger.log(`[OllamaRequestHandler] Generate request sent for task ${task.id}`);

    } catch (error) {
      await errorHandler.handleRequestError(error, task, res);
    }
  }

  /**
   * 选择可用设备 - 单一职责原则
   */
  private async selectAvailableDevice(model: string) {
    const availableDevices = await this.nodeService.findAvailableNodes(model);
    if (availableDevices.length === 0) {
      throw new HttpException('No available devices', HttpStatus.SERVICE_UNAVAILABLE);
    }
    return availableDevices[0]; // 可以扩展为负载均衡策略
  }

  /**
   * 创建推理任务 - 单一职责原则
   */
  private async createInferenceTask(deviceId: string, model: string, apiKeyInfo?: any): Promise<Task> {
    const taskRequest: CreateTaskRequest = {
      device_id: deviceId,
      model: model,
      user_id: apiKeyInfo?.userId, // 使用API_KEY关联的用户ID
      api_key_info: apiKeyInfo // 传递API_KEY信息用于任务分层
    };

    const task = await this.taskManager.createTask(taskRequest);
    this.logger.log(`[OllamaRequestHandler] Created task ${task.id} for device ${deviceId}${apiKeyInfo ? ' with API key' : ' (anonymous)'}`);
    return task;
  }

  /**
   * 判断是否为流式请求 - 策略模式
   */
  private isStreamingRequest(streamFlag?: boolean): boolean {
    // Ollama默认为流式
    return streamFlag !== false;
  }
}
