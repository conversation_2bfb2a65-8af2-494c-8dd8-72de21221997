import { Injectable, Logger, Inject } from '@nestjs/common';
import { Response } from 'express';
import { ResponseAdapterService } from '@saito/tunnel';

/**
 * Ollama 响应管理器
 * 职责：管理HTTP响应的设置和生命周期
 * 设计原则：单一职责原则 - 只负责响应的管理
 */
@Injectable()
export class OllamaResponseManager {
  private readonly logger = new Logger(OllamaResponseManager.name);

  // 存储活跃的流式响应
  private readonly activeStreams = new Map<string, Response>();

  // 存储活跃的非流式响应
  private readonly activeNonStreams = new Map<string, Response>();

  constructor(
    @Inject('ResponseAdapterService') private readonly responseAdapter: ResponseAdapterService
  ) {}

  /**
   * 设置流式响应 - 单一职责原则
   */
  setupStreamingResponse(taskId: string, res: Response, serviceType: 'ollama' = 'ollama'): void {
    this.activeStreams.set(taskId, res);
    this.responseAdapter.registerTaskService(taskId, serviceType);

    // 设置Ollama流式响应头（NDJSON格式）
    res.setHeader('Content-Type', 'application/x-ndjson');
    res.setHeader('Cache-Control', 'no-cache');
    res.setHeader('Connection', 'keep-alive');
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Headers', 'Cache-Control');

    // 处理客户端断开连接
    res.on('close', () => {
      this.logger.log(`[OllamaResponseManager] Client disconnected for task ${taskId}`);
      this.activeStreams.delete(taskId);
    });

    res.on('error', (error) => {
      this.logger.error(`[OllamaResponseManager] Response error for task ${taskId}: ${error}`);
      this.activeStreams.delete(taskId);
    });

    this.logger.debug(`[OllamaResponseManager] Configured streaming response for task ${taskId}`);
  }

  /**
   * 设置非流式响应 - 单一职责原则
   */
  setupNonStreamingResponse(taskId: string, res: Response, serviceType: 'ollama' = 'ollama'): void {
    this.activeNonStreams.set(taskId, res);
    this.responseAdapter.registerTaskService(taskId, serviceType);

    // 设置JSON响应头
    res.setHeader('Content-Type', 'application/json');
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Headers', 'Cache-Control');

    this.logger.debug(`[OllamaResponseManager] Configured non-streaming response for task ${taskId}`);
  }

  /**
   * 获取流式响应对象
   */
  getStreamResponse(taskId: string): Response | undefined {
    return this.activeStreams.get(taskId);
  }

  /**
   * 获取非流式响应对象
   */
  getNonStreamResponse(taskId: string): Response | undefined {
    return this.activeNonStreams.get(taskId);
  }

  /**
   * 写入Ollama流式数据块 - 单一职责原则
   */
  async writeOllamaStreamChunk(chunkData: any, res: Response): Promise<void> {
    const jsonData = JSON.stringify(chunkData) + '\n';
    
    this.logger.debug(`[OllamaResponseManager] Writing Ollama stream chunk: ${jsonData.substring(0, 100)}...`);

    if (!res.writable) {
      this.logger.error(`[OllamaResponseManager] Response is not writable for Ollama stream chunk`);
      return;
    }

    try {
      res.write(jsonData);
      this.logger.debug(`[OllamaResponseManager] Successfully wrote Ollama stream chunk`);
    } catch (error) {
      this.logger.error(`[OllamaResponseManager] Error writing Ollama stream chunk: ${error}`);
    }
  }

  /**
   * 完成流式响应 - 单一职责原则
   */
  async completeStream(taskId: string, res: Response): Promise<void> {
    this.logger.log(`[OllamaResponseManager] Stream completed for task ${taskId}`);

    // Ollama流式响应结束时不需要特殊标记，直接结束即可
    res.end();
    this.activeStreams.delete(taskId);

    this.logger.debug(`[OllamaResponseManager] Stream response ended and cleaned up for task ${taskId}`);
  }

  /**
   * 处理非流式响应数据 - 模板方法模式
   */
  async processNonStreamResponse(taskId: string, responseData: any, res: Response): Promise<void> {
    this.logger.debug(`[OllamaResponseManager] Processing complete non-stream response for task ${taskId}`);

    // 发送完整的JSON响应
    if (!res.headersSent) {
      res.json(responseData);
    }

    // 清理存储的响应对象
    this.activeNonStreams.delete(taskId);

    this.logger.log(`[OllamaResponseManager] Non-stream response sent for task ${taskId}`);
  }

  /**
   * 清理响应资源
   */
  cleanupResponse(taskId: string): void {
    this.activeStreams.delete(taskId);
    this.activeNonStreams.delete(taskId);
  }

  /**
   * 检查是否有活跃的流式响应
   */
  hasActiveStream(taskId: string): boolean {
    return this.activeStreams.has(taskId);
  }

  /**
   * 检查是否有活跃的非流式响应
   */
  hasActiveNonStream(taskId: string): boolean {
    return this.activeNonStreams.has(taskId);
  }
}
