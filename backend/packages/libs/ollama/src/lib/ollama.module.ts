import { Module } from '@nestjs/common';
import { OllamaServiceProvider } from './ollama.service';
import { OllamaRequestHandler } from './handlers/ollama-request.handler';
import { OllamaFormatConverter } from './handlers/ollama-format.converter';
import { OllamaMessageBuilder } from './handlers/ollama-message.builder';
import { OllamaResponseManager } from './handlers/ollama-response.manager';
import { OllamaDataProcessor } from './handlers/ollama-data.processor';
import { OllamaErrorHandler } from './handlers/ollama-error.handler';
import { TunnelModule } from '@saito/tunnel';
import { TaskManagerModule } from '@saito/task-manager';
import { NodeModule } from '@saito/node';

/**
 * Ollama 模块 - 重构后的版本
 * 职责：提供 Ollama API 服务的依赖注入配置
 * 设计原则：模块化设计，清晰的依赖关系，单一职责原则
 */
@Module({
  imports: [
    TunnelModule,
    TaskManagerModule,
    NodeModule
  ],
  providers: [
    OllamaServiceProvider,
    OllamaRequestHandler,
    OllamaFormatConverter,
    OllamaMessageBuilder,
    OllamaResponseManager,
    OllamaDataProcessor,
    OllamaErrorHandler
  ],
  exports: [
    OllamaServiceProvider
  ],
})
export class OllamaModule {}
