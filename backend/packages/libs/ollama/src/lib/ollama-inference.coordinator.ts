import { Injectable, Logger, Inject } from '@nestjs/common';
import { Response } from 'express';
import {
  InferenceRequest,
  InferenceResponse,
  InferenceError,
  StreamChatRequestMessage,
  TunnelMessage,
  Task,
  CreateTaskRequest
} from '@saito/models';
import { TunnelService, TunnelMessageListener } from '@saito/tunnel';
import { TaskManager } from '@saito/task-manager';
import { NodeService } from '@saito/node';

/**
 * 推理回调接口 - 接口隔离原则
 */
interface InferenceCallbacks {
  onResponse: (data: InferenceResponse) => Promise<void>;
  onError: (error: InferenceError) => Promise<void>;
  onComplete: () => Promise<void>;
}

/**
 * 会话信息接口 - 接口隔离原则
 */
interface SessionInfo {
  response: Response;
  onResponse: (data: InferenceResponse) => Promise<void>;
  onError: (error: InferenceError) => Promise<void>;
  onComplete: () => Promise<void>;
  deviceId: string;
}

/**
 * Ollama 推理协调器
 * 职责：协调 Ollama 推理请求的处理
 * 设计原则：
 * - 单一职责：只负责 Ollama 相关的推理协调
 * - 依赖倒置：依赖抽象接口
 * - 内聚性：与 OllamaService 紧密配合
 */
@Injectable()
export class OllamaInferenceCoordinator {
  private readonly logger = new Logger(OllamaInferenceCoordinator.name);
  
  // 存储活跃的推理会话 - 使用强类型接口
  private readonly activeSessions = new Map<string, SessionInfo>();

  // 消息去重：存储已处理的消息ID
  private readonly processedMessages = new Set<string>();

  constructor(
    @Inject('TunnelService') private readonly tunnelService: TunnelService,
    @Inject('TaskManager') private readonly taskManager: TaskManager,
    private readonly nodeService: NodeService,
    @Inject('PEER_ID') private readonly peerId: string
  ) {}

  /**
   * 处理推理请求 - 应用单一职责原则
   * 将复杂的处理流程分解为多个职责明确的方法
   */
  async handleInferenceRequest(
    request: InferenceRequest,
    response: Response,
    callbacks: InferenceCallbacks
  ): Promise<void> {
    let task: Task | null = null;

    try {
      this.logger.log(`[OllamaInferenceCoordinator] Handling inference request for model ${request.model}`);

      // 1. 设备选择 - 委托给NodeService
      const targetDevice = await this.selectAvailableDevice(request.model);

      // 2. 任务创建 - 委托给TaskManager
      task = await this.createInferenceTask(targetDevice.device_id, request.model);

      // 3. 会话管理 - 应用策略模式
      this.registerSession(task.id, response, callbacks, targetDevice.device_id);

      // 4. 流式响应设置 - 单一职责
      if (request.stream) {
        this.setupStreamingResponse(task.id, response);
      }

      // 5. 响应监听器注册 - 观察者模式
      this.registerResponseListener(task.id, targetDevice.device_id);

      // 6. 消息发送 - 建造者模式
      await this.sendTunnelMessage(task.id, targetDevice.device_id, request);

      this.logger.log(`[OllamaInferenceCoordinator] Inference request sent for task ${task.id}`);

    } catch (error) {
      await this.handleRequestError(error, task, callbacks);
    }
  }



  /**
   * 选择可用设备 - 单一职责原则
   */
  private async selectAvailableDevice(model: string) {
    const availableDevices = await this.nodeService.findAvailableNodes(model);
    if (availableDevices.length === 0) {
      throw new Error('No available devices');
    }
    return availableDevices[0]; // 可以扩展为负载均衡策略
  }

  /**
   * 创建推理任务 - 单一职责原则
   */
  private async createInferenceTask(deviceId: string, model: string): Promise<Task> {
    const taskRequest: CreateTaskRequest = {
      device_id: deviceId,
      model: model,
      user_id: undefined // 匿名用户
    };

    const task = await this.taskManager.createTask(taskRequest);
    this.logger.log(`[OllamaInferenceCoordinator] Created task ${task.id} for device ${deviceId}`);
    return task;
  }

  /**
   * 注册会话 - 单一职责原则
   */
  private registerSession(
    taskId: string,
    response: Response,
    callbacks: InferenceCallbacks,
    deviceId: string
  ): void {
    this.activeSessions.set(taskId, {
      response,
      onResponse: callbacks.onResponse,
      onError: callbacks.onError,
      onComplete: callbacks.onComplete,
      deviceId
    });
  }

  /**
   * 设置流式响应 - 单一职责原则
   */
  private setupStreamingResponse(taskId: string, response: Response): void {
    response.setHeader('Content-Type', 'application/x-ndjson');
    response.setHeader('Cache-Control', 'no-cache');
    response.setHeader('Connection', 'keep-alive');

    // 处理客户端断开连接
    response.on('close', () => {
      this.logger.log(`[OllamaInferenceCoordinator] Client disconnected for task ${taskId}`);
      this.activeSessions.delete(taskId);
    });

    response.on('error', (error) => {
      this.logger.error(`[OllamaInferenceCoordinator] Response error for task ${taskId}: ${error}`);
      this.activeSessions.delete(taskId);
    });
  }

  /**
   * 发送Tunnel消息 - 建造者模式
   */
  private async sendTunnelMessage(
    taskId: string,
    targetDeviceId: string,
    request: InferenceRequest
  ): Promise<void> {
    const tunnelMessage: StreamChatRequestMessage = {
      from: this.peerId,
      to: targetDeviceId,
      type: 'chat_request_stream',
      payload: {
        taskId,
        path: '/ollama/api/chat',
        data: {
          model: request.model,
          messages: request.messages,
          stream: request.stream ?? true,
          // 提供默认值以满足 TunnelOpenAIChatCompletionRequest 的要求
          temperature: request.temperature ?? 0.7,
          top_p: request.top_p ?? 1.0,
          // OpenAI特有字段，提供默认值
          n: 1,
          presence_penalty: 0,
          frequency_penalty: 0,
          // 可选的Ollama字段
          max_tokens: request.max_tokens,
          stop: request.stop
        }
      }
    };

    await this.tunnelService.handleMessage(tunnelMessage);
  }

  /**
   * 处理请求错误 - 单一职责原则
   */
  private async handleRequestError(
    error: unknown,
    task: Task | null,
    callbacks: InferenceCallbacks
  ): Promise<void> {
    this.logger.error(`[OllamaInferenceCoordinator] Error handling inference request: ${error}`);

    // 清理会话
    if (task?.id) {
      this.activeSessions.delete(task.id);
    }

    // 通知错误回调
    if (task?.id) {
      const inferenceError: InferenceError = {
        taskId: task.id,
        error: {
          message: error instanceof Error ? error.message : 'Unknown error',
          type: 'inference_error'
        }
      };
      await callbacks.onError(inferenceError);
    }

    throw error;
  }

  /**
   * 注册响应监听器
   * @param taskId 任务ID
   * @param deviceId 设备ID
   */
  private registerResponseListener(taskId: string, deviceId: string): void {
    const responseListener: TunnelMessageListener = {
      match: (msg: TunnelMessage) => {
        return msg.from === deviceId &&
               msg.type === 'chat_response_stream' &&
               msg.payload &&
               typeof msg.payload === 'object' &&
               'taskId' in msg.payload &&
               msg.payload.taskId === taskId;
      },
      callback: async (msg: TunnelMessage) => {
        this.logger.log(`[OllamaInferenceCoordinator] Listener triggered for task ${taskId}, calling handleInferenceResponse`);
        await this.handleInferenceResponse(msg);
        this.logger.debug(`[OllamaInferenceCoordinator] Finished processing response for task ${taskId}`);
      },
      once: (msg: TunnelMessage) => {
        // 如果消息标记为完成，则只处理一次
        return this.isResponseComplete(msg);
      }
    };

    // 注册监听器到 tunnel 服务
    this.tunnelService.addListener(responseListener);
  }

  /**
   * 处理推理响应 - 应用单一职责原则
   * 使用强类型替代any，提高类型安全性
   */
  private async handleInferenceResponse(message: TunnelMessage): Promise<void> {
    try {
      // 1. 数据提取和验证 - 策略模式
      const { taskId, responseData } = this.extractResponseData(message);
      if (!taskId || !responseData) {
        this.logger.warn(`[OllamaInferenceCoordinator] Invalid response data`);
        return;
      }

      // 2. 重复消息检测 - 防御性编程
      if (this.isDuplicateMessage(message, taskId)) {
        return;
      }

      // 3. 会话查找 - 单一职责
      const session = this.activeSessions.get(taskId);
      if (!session) {
        this.logger.warn(`[OllamaInferenceCoordinator] No active session found for task ${taskId}`);
        return;
      }

      // 4. 响应处理 - 模板方法模式
      await this.processInferenceResponse(taskId, responseData, session);

    } catch (error) {
      this.logger.error(`[OllamaInferenceCoordinator] Error handling inference response: ${error}`);
    }
  }

  /**
   * 提取响应数据 - 策略模式
   */
  private extractResponseData(message: TunnelMessage): { taskId: string | null, responseData: unknown } {
    if (!message.payload || typeof message.payload !== 'object') {
      return { taskId: null, responseData: null };
    }

    const payload = message.payload as Record<string, unknown>;
    const taskId = typeof payload['taskId'] === 'string' ? payload['taskId'] : null;

    // 支持不同的响应数据结构
    let responseData: unknown;
    if (payload['chunk']) {
      responseData = payload['chunk'];
    } else if (payload['data']) {
      responseData = payload['data'];
    } else {
      responseData = { done: payload['done'] || true };
    }

    return { taskId, responseData };
  }

  /**
   * 检测重复消息 - 防御性编程
   */
  private isDuplicateMessage(message: TunnelMessage, taskId: string): boolean {
    const payloadStr = JSON.stringify(message.payload);
    const messageId = `${message.from}-${taskId}-${payloadStr}`;

    if (this.processedMessages.has(messageId)) {
      this.logger.debug(`[OllamaInferenceCoordinator] Duplicate message detected for task ${taskId}`);
      return true;
    }

    // 标记消息为已处理
    this.processedMessages.add(messageId);

    // 清理旧的消息ID（保留最近1000条）
    if (this.processedMessages.size > 1000) {
      const oldestIds = Array.from(this.processedMessages).slice(0, 500);
      oldestIds.forEach(id => this.processedMessages.delete(id));
    }

    return false;
  }

  /**
   * 处理推理响应 - 模板方法模式
   */
  private async processInferenceResponse(
    taskId: string,
    responseData: unknown,
    session: SessionInfo
  ): Promise<void> {
    // 构造推理响应
    const inferenceResponse = this.buildInferenceResponse(taskId, responseData);

    // 通知响应回调
    await session.onResponse(inferenceResponse);

    // 检查是否完成
    if (this.isResponseComplete(responseData)) {
      await session.onComplete();
      this.activeSessions.delete(taskId);
    }
  }

  /**
   * 构建推理响应 - 建造者模式
   */
  private buildInferenceResponse(taskId: string, responseData: unknown): InferenceResponse {
    const data = responseData as Record<string, unknown>;

    return {
      taskId,
      model: typeof data['model'] === 'string' ? data['model'] : '',
      message: data['message'] as { role: string; content: string; } | undefined,
      done: Boolean(data['done']),
      created_at: typeof data['created_at'] === 'string' ? data['created_at'] : undefined,
      total_duration: typeof data['total_duration'] === 'number' ? data['total_duration'] : undefined,
      load_duration: typeof data['load_duration'] === 'number' ? data['load_duration'] : undefined,
      prompt_eval_count: typeof data['prompt_eval_count'] === 'number' ? data['prompt_eval_count'] : undefined,
      prompt_eval_duration: typeof data['prompt_eval_duration'] === 'number' ? data['prompt_eval_duration'] : undefined,
      eval_count: typeof data['eval_count'] === 'number' ? data['eval_count'] : undefined,
      eval_duration: typeof data['eval_duration'] === 'number' ? data['eval_duration'] : undefined
    };
  }

  /**
   * 判断响应是否完成 - 策略模式
   */
  private isResponseComplete(responseData: unknown): boolean {
    const data = responseData as Record<string, unknown>;
    return Boolean(data['done']);
  }



  /**
   * 获取活跃会话数量
   * @returns number
   */
  getActiveSessionCount(): number {
    return this.activeSessions.size;
  }

  /**
   * 清理指定任务的会话
   * @param taskId 任务ID
   */
  cleanupSession(taskId: string): void {
    this.activeSessions.delete(taskId);
    this.logger.debug(`[OllamaInferenceCoordinator] Cleaned up session for task ${taskId}`);
  }
}
