# Ollama Module

Ollama API 服务模块，负责处理 Ollama 协议的 HTTP 请求并通过 tunnel 模块转发到设备端。

## 功能特性

- **聊天请求处理**: 支持流式和非流式聊天请求
- **生成请求处理**: 支持文本生成请求
- **Tunnel 集成**: 通过 tunnel 模块与设备端通信
- **任务管理**: 集成任务管理系统，跟踪请求状态
- **响应适配**: 自动适配 Ollama 格式的响应

## 架构设计

### 设计原则

- **单一职责**: 只负责 Ollama 协议的适配和转换
- **依赖倒置**: 依赖于抽象的 TunnelService 和 TaskManager
- **接口隔离**: 只实现必要的 Ollama 功能

### 核心组件

- `OllamaService`: 核心服务实现
- `OllamaModule`: NestJS 模块配置
- `OllamaInterface`: 服务接口定义

## 使用方法

```typescript
import { OllamaModule } from '@saito/ollama';

@Module({
  imports: [OllamaModule],
})
export class AppModule {}
```

## API 支持

- `POST /ollama/api/chat` - 聊天请求
- `POST /ollama/api/generate` - 生成请求
- `GET /ollama/api/tags` - 模型列表

## 依赖模块

- `@saito/tunnel` - 消息通信
- `@saito/task-manager` - 任务管理
- `@saito/node` - 节点管理
- `@saito/models` - 数据模型
