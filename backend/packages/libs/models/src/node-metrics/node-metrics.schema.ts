import { z } from 'zod';

/**
 * 时间窗口枚举
 */
export const TimeWindowSchema = z.enum(['5min', '1h', '24h', 'lifetime']);
export type TimeWindow = z.infer<typeof TimeWindowSchema>;

/**
 * 任务执行结果 - 用于记录任务执行的性能数据
 */
export const TaskExecutionResultSchema = z.object({
  taskId: z.string().uuid().describe('任务ID'),
  deviceId: z.string().describe('设备ID'),
  model: z.string().describe('使用的模型'),
  status: z.enum(['completed', 'failed']).describe('任务状态'),
  responseTime: z.number().positive().describe('响应时间（秒）'),
  totalTokens: z.number().int().nonnegative().default(0).describe('总token数'),
  promptTokens: z.number().int().nonnegative().default(0).describe('提示token数'),
  completionTokens: z.number().int().nonnegative().default(0).describe('完成token数'),
  tokensPerSecond: z.number().nonnegative().default(0).describe('每秒token数'),
  totalDuration: z.number().nonnegative().describe('总执行时间（秒）'),
  failureReason: z.string().optional().describe('失败原因'),
  timestamp: z.date().describe('执行时间戳')
});

export type TaskExecutionResult = z.infer<typeof TaskExecutionResultSchema>;

/**
 * 节点性能指标 - 数据库表对应的模型
 */
export const NodePerformanceMetricsSchema = z.object({
  id: z.string().uuid().describe('记录ID'),
  deviceId: z.string().describe('设备ID'),
  timeWindow: TimeWindowSchema.describe('时间窗口'),
  windowStart: z.date().describe('窗口开始时间'),
  windowEnd: z.date().optional().describe('窗口结束时间'),
  
  // 任务统计
  totalTasks: z.number().int().nonnegative().default(0).describe('总任务数'),
  successfulTasks: z.number().int().nonnegative().default(0).describe('成功任务数'),
  failedTasks: z.number().int().nonnegative().default(0).describe('失败任务数'),
  successRate: z.number().min(0).max(1).describe('成功率'),
  
  // 响应时间统计
  avgResponseTime: z.number().nonnegative().optional().describe('平均响应时间（秒）'),
  minResponseTime: z.number().nonnegative().optional().describe('最小响应时间（秒）'),
  maxResponseTime: z.number().nonnegative().optional().describe('最大响应时间（秒）'),
  
  // Token统计
  totalTokens: z.number().int().nonnegative().default(0).describe('总token数'),
  tokensPerSecond: z.number().nonnegative().optional().describe('每秒token数'),
  
  // 总执行时间
  totalDuration: z.number().nonnegative().optional().describe('总执行时间（秒）'),
  
  // 元数据
  createdAt: z.date().describe('创建时间'),
  updatedAt: z.date().describe('更新时间')
});

export type NodePerformanceMetrics = z.infer<typeof NodePerformanceMetricsSchema>;

/**
 * 网络平均性能指标
 */
export const NetworkPerformanceMetricsSchema = z.object({
  timeWindow: TimeWindowSchema.describe('时间窗口'),
  totalNodes: z.number().int().nonnegative().describe('总节点数'),
  activeNodes: z.number().int().nonnegative().describe('活跃节点数'),
  
  // 平均指标
  avgSuccessRate: z.number().min(0).max(1).describe('平均成功率'),
  avgResponseTime: z.number().nonnegative().describe('平均响应时间（秒）'),
  avgTokensPerSecond: z.number().nonnegative().describe('平均TPS'),
  
  // 总计指标
  totalTasks: z.number().int().nonnegative().describe('总任务数'),
  totalSuccessfulTasks: z.number().int().nonnegative().describe('总成功任务数'),
  totalFailedTasks: z.number().int().nonnegative().describe('总失败任务数'),
  
  // 时间戳
  calculatedAt: z.date().describe('计算时间')
});

export type NetworkPerformanceMetrics = z.infer<typeof NetworkPerformanceMetricsSchema>;

/**
 * 节点性能查询请求
 */
export const NodePerformanceQuerySchema = z.object({
  deviceId: z.string().describe('设备ID'),
  timeWindow: TimeWindowSchema.describe('时间窗口'),
  startTime: z.date().optional().describe('开始时间'),
  endTime: z.date().optional().describe('结束时间')
});

export type NodePerformanceQuery = z.infer<typeof NodePerformanceQuerySchema>;

/**
 * 批量节点性能查询请求
 */
export const BatchNodePerformanceQuerySchema = z.object({
  deviceIds: z.array(z.string()).describe('设备ID列表'),
  timeWindow: TimeWindowSchema.describe('时间窗口'),
  startTime: z.date().optional().describe('开始时间'),
  endTime: z.date().optional().describe('结束时间')
});

export type BatchNodePerformanceQuery = z.infer<typeof BatchNodePerformanceQuerySchema>;

/**
 * 节点性能响应
 */
export const NodePerformanceResponseSchema = z.object({
  deviceId: z.string().describe('设备ID'),
  windows: z.record(TimeWindowSchema, NodePerformanceMetricsSchema.optional()).describe('各时间窗口的性能数据'),
  lastUpdated: z.date().describe('最后更新时间')
});

export type NodePerformanceResponse = z.infer<typeof NodePerformanceResponseSchema>;

/**
 * 网络性能响应
 */
export const NetworkPerformanceResponseSchema = z.object({
  totalNodes: z.number().int().nonnegative().describe('总节点数'),
  activeNodes: z.number().int().nonnegative().describe('活跃节点数'),
  averageMetrics: z.record(TimeWindowSchema, NetworkPerformanceMetricsSchema.optional()).describe('各时间窗口的平均指标'),
  topPerformers: z.record(TimeWindowSchema, z.array(z.object({
    deviceId: z.string().describe('设备ID'),
    score: z.number().describe('性能评分'),
    metrics: NodePerformanceMetricsSchema.describe('性能指标')
  }))).describe('各时间窗口的顶级表现者')
});

export type NetworkPerformanceResponse = z.infer<typeof NetworkPerformanceResponseSchema>;

/**
 * 节点筛选条件
 */
export const SelectionCriteriaSchema = z.object({
  // 性能阈值条件
  minSuccessRate: z.number().min(0).max(1).optional().describe('最小成功率'),
  maxResponseTime: z.number().positive().optional().describe('最大响应时间（毫秒）'),
  minTokensPerSecond: z.number().nonnegative().optional().describe('最小令牌处理速度'),

  // 相对性能条件
  performancePercentile: z.number().min(0).max(100).optional().describe('性能百分位要求'),
  relativeToAverage: z.number().optional().describe('相对于平均水平的倍数'),

  // 模型和容量条件
  model: z.string().optional().describe('指定模型'),
  maxConcurrentTasks: z.number().int().positive().optional().describe('最大并发任务数'),

  // 时间窗口
  timeWindow: TimeWindowSchema.default('5min').describe('评估时间窗口'),

  // 筛选策略
  strategy: z.enum(['threshold', 'relative', 'composite']).default('composite').describe('筛选策略'),

  // 结果限制
  maxResults: z.number().int().positive().optional().describe('最大返回节点数'),

  // 权重配置（用于综合评分策略）
  weights: z.object({
    successRate: z.number().min(0).max(1).default(0.4),
    responseTime: z.number().min(0).max(1).default(0.3),
    tokensPerSecond: z.number().min(0).max(1).default(0.3)
  }).optional().describe('各指标权重配置')
});

export type SelectionCriteria = z.infer<typeof SelectionCriteriaSchema>;

/**
 * 节点筛选结果
 */
export const NodeSelectionResultSchema = z.object({
  node: z.object({
    id: z.string(),
    device_id: z.string()
  }),
  score: z.number().describe('节点评分'),
  metrics: NodePerformanceMetricsSchema.optional().describe('节点性能指标'),
  reason: z.string().optional().describe('选择原因')
});

export type NodeSelectionResult = z.infer<typeof NodeSelectionResultSchema>;

/**
 * 节点容量状态枚举
 */
export const NodeCapacityStatusSchema = z.enum(['idle', 'busy', 'offline', 'overloaded']);
export type NodeCapacityStatus = z.infer<typeof NodeCapacityStatusSchema>;

/**
 * 节点容量信息
 */
export const NodeCapacityInfoSchema = z.object({
  deviceId: z.string().describe('设备ID'),
  status: NodeCapacityStatusSchema.describe('节点状态'),
  currentTasks: z.number().int().nonnegative().describe('当前任务数量'),
  maxConcurrentTasks: z.number().int().positive().default(1).describe('最大并发任务数'),
  lastTaskStartTime: z.date().optional().describe('最后任务开始时间'),
  estimatedAvailableTime: z.date().optional().describe('预计可用时间'),
  activeTasks: z.array(z.string()).default([]).describe('活跃任务ID列表')
});

export type NodeCapacityInfo = z.infer<typeof NodeCapacityInfoSchema>;

/**
 * 任务分配请求
 */
export const TaskAllocationRequestSchema = z.object({
  deviceId: z.string().describe('设备ID'),
  taskId: z.string().describe('任务ID'),
  estimatedDuration: z.number().positive().optional().describe('预计执行时间（毫秒）'),
  priority: z.number().int().min(0).max(10).default(5).describe('任务优先级')
});

export type TaskAllocationRequest = z.infer<typeof TaskAllocationRequestSchema>;

/**
 * 任务分配结果
 */
export const TaskAllocationResultSchema = z.object({
  success: z.boolean().describe('分配是否成功'),
  deviceId: z.string().describe('设备ID'),
  taskId: z.string().describe('任务ID'),
  reason: z.string().optional().describe('分配失败原因'),
  estimatedStartTime: z.date().optional().describe('预计开始时间')
});

export type TaskAllocationResult = z.infer<typeof TaskAllocationResultSchema>;

/**
 * 性能统计更新请求
 */
export const PerformanceStatsUpdateSchema = z.object({
  deviceId: z.string().describe('设备ID'),
  timeWindow: TimeWindowSchema.describe('时间窗口'),
  windowStart: z.date().describe('窗口开始时间'),
  stats: z.object({
    totalTasks: z.number().int().nonnegative().describe('总任务数'),
    successfulTasks: z.number().int().nonnegative().describe('成功任务数'),
    failedTasks: z.number().int().nonnegative().describe('失败任务数'),
    avgResponseTime: z.number().nonnegative().optional().describe('平均响应时间'),
    minResponseTime: z.number().nonnegative().optional().describe('最小响应时间'),
    maxResponseTime: z.number().nonnegative().optional().describe('最大响应时间'),
    totalTokens: z.number().int().nonnegative().describe('总token数'),
    tokensPerSecond: z.number().nonnegative().optional().describe('TPS'),
    totalDuration: z.number().nonnegative().optional().describe('总执行时间')
  }).describe('统计数据')
});

export type PerformanceStatsUpdate = z.infer<typeof PerformanceStatsUpdateSchema>;

/**
 * 数据库查询结果模型 - 用于Repository层
 */
export const NodePerformanceMetricsDbSchema = z.object({
  id: z.string().uuid(),
  device_id: z.string(),
  time_window: z.string(),
  window_start: z.union([
    z.string().transform(str => new Date(str)),
    z.number().transform(num => new Date(num))
  ]),
  window_end: z.union([
    z.string().transform(str => new Date(str)),
    z.number().transform(num => new Date(num)),
    z.null().transform(() => undefined)
  ]).optional(),
  total_tasks: z.number().int(),
  successful_tasks: z.number().int(),
  failed_tasks: z.number().int(),
  success_rate: z.number(),
  avg_response_time: z.number().optional(),
  min_response_time: z.number().optional(),
  max_response_time: z.number().optional(),
  total_tokens: z.number().int(),
  tokens_per_second: z.number().optional(),
  total_duration: z.number().optional(),
  created_at: z.union([
    z.string().transform(str => new Date(str)),
    z.number().transform(num => new Date(num))
  ]),
  updated_at: z.union([
    z.string().transform(str => new Date(str)),
    z.number().transform(num => new Date(num))
  ])
});

export type NodePerformanceMetricsDb = z.infer<typeof NodePerformanceMetricsDbSchema>;
