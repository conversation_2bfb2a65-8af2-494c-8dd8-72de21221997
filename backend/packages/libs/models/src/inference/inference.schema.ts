import { z } from 'zod';

/**
 * 推理请求基础模式
 */
export const InferenceRequestSchema = z.object({
  model: z.string().describe('模型名称'),
  messages: z.array(
    z.object({
      role: z.string().describe('消息角色'),
      content: z.string().describe('消息内容')
    })
  ).describe('对话消息列表'),
  stream: z.boolean().optional().default(true).describe('是否使用流式响应'),
  temperature: z.number().optional().describe('采样温度'),
  top_p: z.number().optional().describe('核采样参数'),
  top_k: z.number().optional().describe('限制每一步考虑的标记数量'),
  max_tokens: z.number().optional().describe('最大生成标记数'),
  stop: z.union([z.string(), z.array(z.string())]).optional().describe('停止序列')
});

export type InferenceRequest = z.infer<typeof InferenceRequestSchema>;

/**
 * 推理响应基础模式
 */
export const InferenceResponseSchema = z.object({
  taskId: z.string().describe('任务ID'),
  model: z.string().describe('模型名称'),
  message: z.object({
    role: z.string().describe('消息角色'),
    content: z.string().describe('消息内容')
  }).optional().describe('响应消息'),
  done: z.boolean().describe('是否完成'),
  created_at: z.string().optional().describe('创建时间'),
  total_duration: z.number().optional().describe('总耗时'),
  load_duration: z.number().optional().describe('加载耗时'),
  prompt_eval_count: z.number().optional().describe('提示评估标记数'),
  prompt_eval_duration: z.number().optional().describe('提示评估耗时'),
  eval_count: z.number().optional().describe('评估标记数'),
  eval_duration: z.number().optional().describe('评估耗时')
});

export type InferenceResponse = z.infer<typeof InferenceResponseSchema>;

/**
 * 推理错误模式
 */
export const InferenceErrorSchema = z.object({
  taskId: z.string().describe('任务ID'),
  error: z.object({
    message: z.string().describe('错误消息'),
    type: z.string().optional().describe('错误类型'),
    code: z.string().optional().describe('错误代码')
  }).describe('错误信息')
});

export type InferenceError = z.infer<typeof InferenceErrorSchema>;

/**
 * 推理事件类型
 */
export const InferenceEventTypeSchema = z.enum([
  'response',
  'error',
  'complete'
]);

export type InferenceEventType = z.infer<typeof InferenceEventTypeSchema>;

/**
 * 推理事件模式
 */
export const InferenceEventSchema = z.object({
  type: InferenceEventTypeSchema,
  taskId: z.string().describe('任务ID'),
  data: z.union([
    InferenceResponseSchema,
    InferenceErrorSchema,
    z.object({ done: z.literal(true) })
  ]).describe('事件数据')
});

export type InferenceEvent = z.infer<typeof InferenceEventSchema>;

/**
 * 推理事件监听器接口
 */
export interface InferenceEventListener {
  /**
   * 处理推理响应
   * @param taskId 任务ID
   * @param data 响应数据
   */
  onInferenceResponse(taskId: string, data: InferenceResponse): Promise<void>;

  /**
   * 处理推理错误
   * @param taskId 任务ID
   * @param error 错误信息
   */
  onInferenceError(taskId: string, error: any): Promise<void>;

  /**
   * 处理推理完成
   * @param taskId 任务ID
   */
  onInferenceComplete(taskId: string): Promise<void>;
}
