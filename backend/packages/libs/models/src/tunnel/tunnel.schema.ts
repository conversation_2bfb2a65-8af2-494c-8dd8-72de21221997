import { z } from 'zod';
import { TaskMetricsSchema } from '../task/task.schema';
import { ChatTask } from '../earnings';

/**
 * 设备注册请求模型
 */
export const RegisterDeviceRequestSchema = z.object({
  deviceId: z.string().uuid().describe('Device ID')
});

export type RegisterDeviceRequest = z.infer<typeof RegisterDeviceRequestSchema>;

/**
 * 设备注册响应模型
 */
export const RegisterDeviceResponseSchema = z.object({
  success: z.boolean().describe('Whether the registration was successful'),
  deviceId: z.string().uuid().describe('Device ID'),
  error: z.string().optional().describe('Error message if registration failed')
});

export type RegisterDeviceResponse = z.infer<typeof RegisterDeviceResponseSchema>;

/**
 * 任务请求模型
 */
export const TaskRequestSchema = z.object({
  message: z.string().describe('Task message')
});

export type TaskRequest = z.infer<typeof TaskRequestSchema>;

/**
 * 基础任务消息模型
 */
export const BaseTaskMessageSchema = z.object({
  id: z.string().describe('Message ID'),
  done: z.boolean().optional().describe('Whether the task is done'),
  error: z.string().optional().describe('Error message if task failed'),
  message: z.any().optional().describe('Message content'),
  toString: z.function().returns(z.string()).optional().describe('Convert to string method')
}).merge(TaskMetricsSchema.partial());

/**
 * TunnelChatTaskSchema模型 - 为了避免命名冲突，我们创建一个新的schema
 * 但保持与ChatTask类型兼容
 */
export const TunnelChatTaskSchema = z.custom<ChatTask>((val) => {
  return val !== null && val !== undefined;
});

/**
 * 任务响应消息模型
 */
export const TaskResponseMessageSchema = z.object({
  taskId: z.string().describe('Task ID'),
  message: z.any().describe('Task response message')
});

export type TaskResponseMessage = z.infer<typeof TaskResponseMessageSchema>;

/**
 * 任务流消息模型
 */
export const TaskStreamMessageSchema = z.object({
  taskId: z.string().describe('Task ID'),
  message: z.any().describe('Task stream message')
});

export type TaskStreamMessage = z.infer<typeof TaskStreamMessageSchema>;

/**
 * 任务错误消息模型
 */
export const TaskErrorMessageSchema = z.object({
  taskId: z.string().describe('Task ID'),
  error: z.any().describe('Task error')
});

export type TaskErrorMessage = z.infer<typeof TaskErrorMessageSchema>;

/**
 * 任务处理器参数模型 - 流式
 */
export const TaskStreamHandlerParamsSchema = z.object({
  taskId: z.string().describe('Task ID'),
  targetDeviceId: z.string().uuid().describe('Target device ID'),
  onMessage: z.function()
    .args(z.any())
    .returns(z.promise(z.void()))
    .describe('Message handler function')
});

export type TaskStreamHandlerParams = z.infer<typeof TaskStreamHandlerParamsSchema>;

/**
 * 任务处理器参数模型 - 非流式
 */
export const TaskNoStreamHandlerParamsSchema = z.object({
  taskId: z.string().describe('Task ID'),
  targetDeviceId: z.string().uuid().describe('Target device ID'),
  onMessage: z.function()
    .args(z.any())
    .returns(z.promise(z.any()))
    .describe('Message handler function')
});

export type TaskNoStreamHandlerParams = z.infer<typeof TaskNoStreamHandlerParamsSchema>;

/**
 * 发送消息到设备参数模型
 */
export const SendToDeviceParamsSchema = z.object({
  deviceId: z.string().uuid().describe('Device ID'),
  message: z.string().describe('Message to send')
});

export type SendToDeviceParams = z.infer<typeof SendToDeviceParamsSchema>;

/**
 * 任务处理器内部存储模型
 */
export const TaskHandlerSchema = z.object({
  targetDeviceId: z.string().uuid().describe('Target device ID'),
  onMessage: z.function()
    .args(z.any())
    .returns(z.promise(z.any()))
    .describe('Message handler function'),
  isStream: z.boolean().describe('Whether this is a stream handler')
});

export type TaskHandler = z.infer<typeof TaskHandlerSchema>;

/**
 * 导出所有模型
 */
export const TunnelSchemas = {
  RegisterDeviceRequest: RegisterDeviceRequestSchema,
  RegisterDeviceResponse: RegisterDeviceResponseSchema,
  TaskRequest: TaskRequestSchema,
  BaseTaskMessage: BaseTaskMessageSchema,
  ChatTask: TunnelChatTaskSchema,
  TaskResponseMessage: TaskResponseMessageSchema,
  TaskStreamMessage: TaskStreamMessageSchema,
  TaskErrorMessage: TaskErrorMessageSchema,
  TaskStreamHandlerParams: TaskStreamHandlerParamsSchema,
  TaskNoStreamHandlerParams: TaskNoStreamHandlerParamsSchema,
  SendToDeviceParams: SendToDeviceParamsSchema,
  TaskHandler: TaskHandlerSchema
};
