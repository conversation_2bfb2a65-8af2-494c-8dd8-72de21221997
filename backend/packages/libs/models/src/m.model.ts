/* eslint-disable @typescript-eslint/no-explicit-any */
import { z } from 'zod';

import {TaskSchemas} from './task';

const Models = {
  database: {
    tasks: TaskSchemas,
  },
} as const;

export type ModelOfDatabase<
  S extends keyof typeof Models.database,
  T extends keyof (typeof Models.database)[S],
> = (typeof Models.database)[S][T] extends z.ZodType<infer O> ? O : never;

export const m = {
  database<
    S extends keyof typeof Models.database,
    T extends keyof (typeof Models.database)[S],
  >(schema: S, table: T) {
    return Models.database[schema][table];
  },
};
