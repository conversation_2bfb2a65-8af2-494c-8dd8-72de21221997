import { EIP4361MessagePreprocessSchema } from "./eip4361-message.schema";
import { JwtPayloadSchema } from './jwt-payload.schema';
import { UserSchema, AuthRepositorySchemas } from './auth.repository.schema';

export const AuthSchema = {
  eip4361_message: EIP4361MessagePreprocessSchema,
  jwt_payload: JwtPayloadSchema
};

export {
  UserSchema,
  User,
  AuthRepositorySchemas
} from './auth.repository.schema';

export { JwtPayloadSchema } from './jwt-payload.schema';
