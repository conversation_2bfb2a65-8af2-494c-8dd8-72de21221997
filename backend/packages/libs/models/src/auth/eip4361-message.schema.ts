import { z } from "zod";

export const EIP4361MessageSchema = z.object({
    domain: z.string().min(1, "Domain is required"),
    address: z.string().regex(/^0x[a-fA-F0-9]{40}$/, "Invalid Ethereum address"),
    statement: z.string().optional(),
    uri: z.string().url(),
    version: z.literal('1'),
    chainId: z.string(),
    nonce: z.string().min(1, "Nonce is required"),
    issuedAt: z.string().refine((val) => !isNaN(Date.parse(val)), {
        message: "Invalid issuedAt date format",
    }),
    expirationTime: z.string().optional().refine((val) => val === undefined || val === '' || !isNaN(Date.parse(val)), {
        message: "Invalid expirationTime date format",
    }),
});

export type EIP4361MessageType = z.infer<typeof EIP4361MessageSchema>;

export const EIP4361MessagePreprocessSchema = z.preprocess((input) => {
    if (typeof input !== 'string') {
        throw new Error('Input must be a string');
    }

    const lines = input.split('\n');

    const domainAndAddress = lines[0].split(' wants you to sign in with your Ethereum account:');
    const domain = domainAndAddress[0].trim();
    const address = domainAndAddress[1].trim();

    const statement = lines[2]?.trim() || '';

    const uri = lines.find(line => line.startsWith('URI:'))?.split('URI: ')[1]?.trim() || '';
    const version = lines.find(line => line.startsWith('Version:'))?.split('Version: ')[1]?.trim() || '1';
    const chainId = lines.find(line => line.startsWith('Chain ID:'))?.split('Chain ID: ')[1]?.trim() || '1';
    const nonce = lines.find(line => line.startsWith('Nonce:'))?.split('Nonce: ')[1]?.trim() || '';
    const issuedAt = lines.find(line => line.startsWith('Issued At:'))?.split('Issued At: ')[1]?.trim() || '';
    const expirationTime = lines.find(line => line.startsWith('Expiration Time:'))?.split('Expiration Time: ')[1]?.trim() || '';

    return {
        domain,
        address,
        statement,
        uri,
        version,
        chainId,
        nonce,
        issuedAt,
        expirationTime,
    };
}, EIP4361MessageSchema);
