import { z } from 'zod';
import {coerce} from "zod/lib/types";

export const SaitoSessionContextSchema = z.object({
  userId: z.coerce.string(),
  chatId: z.coerce.string(),
  sessionId: z.coerce.string(),
  isDialogActive: z.coerce.boolean(),
  agentId: z.coerce.string().optional()
});

export type SaitoSessionContext = z.infer<typeof SaitoSessionContextSchema>;

export function makeSaitoSessionContext(
  userId: string,
  chatId: string,
  sessionId: string,
  isDialogActive: boolean,
  agentId?: string,
): SaitoSessionContext {
  return { userId, sessionId, chatId, isDialogActive, agentId };
}

export function makeDefaultSaitoSessionContext(): SaitoSessionContext {
  return makeSaitoSessionContext('', '', '', false);
}
