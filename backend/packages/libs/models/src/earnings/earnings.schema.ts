import { z } from 'zod';

// Schema for earnings item
export const EarningsItemSchema = z.object({
  id: z.string().describe('Earnings ID'),
  block_rewards: z.number().describe('Block rewards'),
  job_rewards: z.number().describe('Job rewards'),
  total_rewards: z.number().describe('Total rewards'),
  date: z.string().describe('Earnings date'),
  created_at: z.string().describe('Creation timestamp'),
  device_name: z.string().nullable().describe('Device name'),
  model: z.string().nullable().describe('AI model used'),
  task_id: z.string().nullable().describe('Task ID'),
  device_id: z.string().describe('Device ID'),
  updated_at: z.string().describe('Update timestamp'),
});

export type EarningsItem = z.infer<typeof EarningsItemSchema>;

// Schema for server status
export const ServerStatusSchema = z.object({
  server: z.string().describe('Server name or identifier'),
  apiLoad: z.number().describe('API load percentage'),
  wsStatus: z.string().describe('WebSocket connection status'),
  taskTime: z.number().describe('Average task processing time in ms'),
  health: z.string().describe('Overall server health status'),
});

export type ServerStatus = z.infer<typeof ServerStatusSchema>;

// Schema for task queue
export const TaskQueueItemSchema = z.object({
  id: z.string().describe('Task ID'),
  model: z.string().describe('AI model used'),
  status: z.string().describe('Task status (Pending, Processing, Completed, Failed)'),
  queue: z.union([z.number(), z.string()]).describe('Time in queue (ms)'),
  device: z.union([z.string(), z.null()]).describe('Device ID processing the task'),
  ptime: z.union([z.number(), z.string()]).describe('Processing time (ms)'),
  earn: z.union([z.number(), z.string()]).describe('Earnings for this task'),
  tokens: z.number().optional().describe('Number of tokens processed'),
  created_at: z.string().optional().describe('Task creation timestamp'),
  updated_at: z.string().optional().describe('Task last update timestamp'),
});

export type TaskQueueItem = z.infer<typeof TaskQueueItemSchema>;

// Schema for node performance
export const NodePerformanceSchema = z.object({
  id: z.string().describe('Device ID'),
  type: z.string().describe('Device type (GPU, CPU, etc.)'),
  tasks: z.number().describe('Current number of tasks'),
  mem: z.number().describe('Memory usage percentage'),
  rate: z.number().describe('Task completion rate percentage'),
  earn: z.number().describe('Total earnings'),
  status: z.string().optional().describe('Device status'),
  last_ping: z.string().optional().describe('Last heartbeat timestamp'),
  name: z.string().optional().describe('Device name'),
});

export type NodePerformance = z.infer<typeof NodePerformanceSchema>;

// Request schemas
export const GetServerStatusRequestSchema = z.object({
  timeRange: z.string().optional().describe('Time range for data (e.g., "7", "30", "90", "365")'),
});

export type GetServerStatusRequest = z.infer<typeof GetServerStatusRequestSchema>;

export const GetTaskQueueRequestSchema = z.object({
  page: z.number().default(1).describe('Page number'),
  pageSize: z.number().default(10).describe('Page size'),
  status: z.string().optional().describe('Filter by status'),
  search: z.string().optional().describe('Search term'),
  timeRange: z.string().optional().describe('Time range for data'),
});

export type GetTaskQueueRequest = z.infer<typeof GetTaskQueueRequestSchema>;

export const GetNodePerformanceRequestSchema = z.object({
  page: z.number().default(1).describe('Page number'),
  pageSize: z.number().default(10).describe('Page size'),
  status: z.string().optional().describe('Filter by status'),
  search: z.string().optional().describe('Search term'),
  timeRange: z.string().optional().describe('Time range for data'),
});

export type GetNodePerformanceRequest = z.infer<typeof GetNodePerformanceRequestSchema>;

// Response schemas
export const GetServerStatusResponseSchema = z.object({
  data: z.array(ServerStatusSchema),
});

export type GetServerStatusResponse = z.infer<typeof GetServerStatusResponseSchema>;

export const GetTaskQueueResponseSchema = z.object({
  data: z.array(TaskQueueItemSchema),
  total: z.number(),
  page: z.number(),
  pageSize: z.number(),
});

export type GetTaskQueueResponse = z.infer<typeof GetTaskQueueResponseSchema>;

export const GetNodePerformanceResponseSchema = z.object({
  data: z.array(NodePerformanceSchema),
  total: z.number(),
  page: z.number(),
  pageSize: z.number(),
});

export type GetNodePerformanceResponse = z.infer<typeof GetNodePerformanceResponseSchema>;

// Stats schema
export const EarningsStatsSchema = z.object({
  earnings: z.number().describe('Total earnings'),
  tasks: z.number().describe('Total tasks'),
  nodes: z.number().describe('Total active nodes'),
});

export type EarningsStats = z.infer<typeof EarningsStatsSchema>;

export const GetEarningsStatsRequestSchema = z.object({
  timeRange: z.string().optional().describe('Time range for data'),
});

export type GetEarningsStatsRequest = z.infer<typeof GetEarningsStatsRequestSchema>;

export const GetEarningsStatsResponseSchema = z.object({
  data: EarningsStatsSchema,
});

export type GetEarningsStatsResponse = z.infer<typeof GetEarningsStatsResponseSchema>;

// Get earnings request schema
export const GetEarningsRequestSchema = z.object({
  page: z.number().default(1).describe('Page number'),
  pageSize: z.number().default(10).describe('Page size'),
  timeRange: z.string().optional().describe('Time range for data'),
});

export type GetEarningsRequest = z.infer<typeof GetEarningsRequestSchema>;

// Get earnings response schema
export const GetEarningsResponseSchema = z.object({
  data: z.array(EarningsItemSchema),
  total: z.number(),
  page: z.number(),
  pageSize: z.number(),
});

export type GetEarningsResponse = z.infer<typeof GetEarningsResponseSchema>;

// Get device earnings request schema
export const GetDeviceEarningsRequestSchema = z.object({
  page: z.number().default(1).describe('Page number'),
  pageSize: z.number().default(10).describe('Page size'),
  timeRange: z.string().optional().describe('Time range for data'),
  status: z.string().optional().describe('Filter by status'),
});

export type GetDeviceEarningsRequest = z.infer<typeof GetDeviceEarningsRequestSchema>;

// Get device earnings response schema
export const GetDeviceEarningsResponseSchema = z.object({
  data: z.array(EarningsItemSchema),
  total: z.number(),
  page: z.number(),
  pageSize: z.number(),
});

export type GetDeviceEarningsResponse = z.infer<typeof GetDeviceEarningsResponseSchema>;

/**
 * 用于服务器状态查询的结果schema
 */
export const ServerStatusResultSchema = z.object({
  server: z.string().describe('Server name or identifier'),
  apiLoad: z.number().describe('API load percentage'),
  wsStatus: z.string().describe('WebSocket connection status'),
  taskTime: z.number().describe('Average task processing time in ms'),
  health: z.string().describe('Overall server health status')
});

export type ServerStatusResult = z.infer<typeof ServerStatusResultSchema>;

/**
 * 用于任务队列计数查询的结果schema
 */
export const TaskQueueCountResultSchema = z.object({
  total: z.string().or(z.number()).describe('Total count of tasks')
});

export type TaskQueueCountResult = z.infer<typeof TaskQueueCountResultSchema>;

/**
 * 用于任务队列查询的结果schema
 */
export const TaskQueueResultSchema = z.object({
  id: z.string().describe('Task ID'),
  model: z.string().describe('AI model used'),
  status: z.string().describe('Task status'),
  queue_time: z.number().nullable().describe('Time in queue (ms)'),
  device_id: z.string().nullable().describe('Device ID processing the task'),
  processing_time: z.number().nullable().describe('Processing time (ms)'),
  earnings: z.number().nullable().describe('Earnings for this task'),
  tokens: z.number().nullable().describe('Number of tokens processed'),
  created_at: z.string().describe('Task creation timestamp'),
  updated_at: z.string().describe('Task last update timestamp')
});

export type TaskQueueResult = z.infer<typeof TaskQueueResultSchema>;

/**
 * 用于节点性能计数查询的结果schema
 */
export const NodePerformanceCountResultSchema = z.object({
  total: z.string().or(z.number()).describe('Total count of nodes')
});

export type NodePerformanceCountResult = z.infer<typeof NodePerformanceCountResultSchema>;

/**
 * 用于节点性能查询的结果schema
 */
export const NodePerformanceResultSchema = z.object({
  id: z.string().describe('Device ID'),
  device_type: z.string().describe('Device type (GPU, CPU, etc.)'),
  current_tasks: z.number().nullable().describe('Current number of tasks'),
  memory_usage: z.number().nullable().describe('Memory usage percentage'),
  completion_rate: z.number().nullable().describe('Task completion rate percentage'),
  total_earnings: z.number().nullable().describe('Total earnings'),
  status: z.string().describe('Device status'),
  last_ping: z.union([z.string(), z.number(), z.null()]).describe('Last heartbeat timestamp')
});

export type NodePerformanceResult = z.infer<typeof NodePerformanceResultSchema>;

/**
 * 用于收益统计查询的结果schema
 */
export const EarningsStatsResultSchema = z.object({
  total_earnings: z.string().or(z.number()).nullable().describe('Total earnings'),
  total_tasks: z.string().or(z.number()).nullable().describe('Total tasks'),
  total_nodes: z.string().or(z.number()).nullable().describe('Total active nodes')
});

export type EarningsStatsResult = z.infer<typeof EarningsStatsResultSchema>;

/**
 * 用于收益计数查询的结果schema
 */
export const EarningsCountResultSchema = z.object({
  total: z.string().or(z.number()).describe('Total count of earnings')
});

export type EarningsCountResult = z.infer<typeof EarningsCountResultSchema>;

/**
 * 用于收益查询的结果schema
 */
export const EarningsResultSchema = z.object({
  id: z.string().describe('Earnings ID'),
  block_rewards: z.string().or(z.number()).describe('Block rewards'),
  job_rewards: z.string().or(z.number()).describe('Job rewards'),
  total_rewards: z.string().or(z.number()).describe('Total rewards'),
  date: z.string().describe('Earnings date'),
  created_at: z.string().describe('Creation timestamp'),
  device_name: z.string().nullable().describe('Device name'),
  model: z.string().nullable().describe('AI model used'),
  task_id: z.string().nullable().describe('Task ID'),
  device_id: z.string().describe('Device ID'),
  updated_at: z.string().describe('Update timestamp')
});

export type EarningsResult = z.infer<typeof EarningsResultSchema>;

/**
 * 用于设备检查查询的结果schema
 */
export const DeviceCheckResultSchema = z.object({
  id: z.string().describe('Device ID')
});

export type DeviceCheckResult = z.infer<typeof DeviceCheckResultSchema>;

/**
 * 用户收益记录项模型
 */
export const UserEarningItemSchema = z.object({
  id: z.string().uuid().describe('Earning ID'),
  block_rewards: z.number().describe('Block rewards'),
  job_rewards: z.number().describe('Job rewards'),
  total_rewards: z.number().nullable().describe('Total rewards'),
  date: z.string().describe('Earning date'),
  created_at: z.string().describe('Creation timestamp'),
  device_name: z.string().nullable().describe('Device name'),
  model: z.string().nullable().describe('AI model used')
});

export type UserEarningItem = z.infer<typeof UserEarningItemSchema>;


export const ChatTaskSchema = z.object({
  id: z.string().uuid().describe('Task ID'),
  message: z.string().optional().describe('Task message'),
  done: z.boolean().optional().describe('Whether the task is done'),
  total_duration: z.number().optional().describe('Total task duration in milliseconds'),
  load_duration: z.number().optional().describe('Model load duration in milliseconds'),
  prompt_eval_count: z.number().optional().describe('Number of prompt evaluations'),
  prompt_eval_duration: z.number().optional().describe('Prompt evaluation duration in milliseconds'),
  eval_count: z.number().optional().describe('Number of evaluations'),
  eval_duration: z.number().optional().describe('Evaluation duration in milliseconds'),
  toString: z.function().returns(z.string()).optional().describe('Convert to string method')
});

export type ChatTask = z.infer<typeof ChatTaskSchema>;


/**
 * 用户任务列表项模型
 */
export const UserTaskItemSchema = z.object({
  id: z.string().uuid().describe('Task ID'),
  model: z.string().describe('AI model used'),
  status: z.string().describe('Task status'),
  created_at: z.string().describe('Task creation timestamp'),
  updated_at: z.string().describe('Task update timestamp'),
  total_duration: z.number().nullable().describe('Total task duration in milliseconds'),
  eval_count: z.number().nullable().describe('Number of evaluations'),
  device_name: z.string().describe('Device name'),
  device_type: z.string().describe('Device type')
});

export type UserTaskItem = z.infer<typeof UserTaskItemSchema>;

/**
 * 任务详情模型
 */
export const TaskDetailSchema = z.object({
  id: z.string().uuid().describe('Task ID'),
  device_id: z.string().uuid().describe('Device ID'),
  user_id: z.string().uuid().describe('User ID'),
  model: z.string().describe('AI model used'),
  status: z.string().describe('Task status'),
  created_at: z.string().describe('Task creation timestamp'),
  updated_at: z.string().describe('Task update timestamp'),
  total_duration: z.number().nullable().describe('Total task duration in milliseconds'),
  load_duration: z.number().nullable().describe('Model load duration in milliseconds'),
  prompt_eval_count: z.number().nullable().describe('Number of prompt evaluations'),
  prompt_eval_duration: z.number().nullable().describe('Prompt evaluation duration in milliseconds'),
  eval_count: z.number().nullable().describe('Number of evaluations'),
  eval_duration: z.number().nullable().describe('Evaluation duration in milliseconds'),
  device_name: z.string().describe('Device name'),
  device_type: z.string().describe('Device type'),
  gpu_type: z.string().nullable().describe('GPU type')
});

export type TaskDetail = z.infer<typeof TaskDetailSchema>;

/**
 * Dashboard Chart Data Schemas
 */

/**
 * 用于收益图表数据查询的结果schema
 */
export const EarningsChartDataResultSchema = z.object({
  date: z.string().describe('Date in YYYY-MM-DD format'),
  value: z.number().describe('Earnings value for the date')
});

export type EarningsChartDataResult = z.infer<typeof EarningsChartDataResultSchema>;

/**
 * 用于任务数量图表数据查询的结果schema
 */
export const TaskCountChartDataResultSchema = z.object({
  date: z.string().describe('Date in YYYY-MM-DD format'),
  value: z.number().describe('Task count for the date')
});

export type TaskCountChartDataResult = z.infer<typeof TaskCountChartDataResultSchema>;

/**
 * 用于聊天热力图数据查询的结果schema
 */
export const ChatHeatmapDataResultSchema = z.object({
  date: z.string().describe('Date in YYYY-MM-DD format'),
  count: z.number().describe('Chat count for the date')
});

export type ChatHeatmapDataResult = z.infer<typeof ChatHeatmapDataResultSchema>;

/**
 * 用于创建收益记录查询的结果schema
 */
export const CreateEarningsResultSchema = z.object({
  id: z.string().describe('Created earnings record ID')
});

export type CreateEarningsResult = z.infer<typeof CreateEarningsResultSchema>;