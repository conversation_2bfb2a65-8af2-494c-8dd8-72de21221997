import { z } from 'zod';

// API Key
export const ApiKeySchema = z.object({
  id: z.string(),
  userId: z.string(),
  categoryId: z.string(),
  keyHash: z.string(),
  keyPrefix: z.string(),
  keyMask: z.string(),
  name: z.string().nullable(),
  description: z.string().nullable().optional(),
  status: z.enum(['active', 'inactive', 'revoked']),
  createdAt: z.string(),
  updatedAt: z.string(),
  deletedAt: z.string().nullable(),
  expirationDate: z.string().nullable().optional(),
  lastUsed: z.string().nullable().optional()
});

export type ApiKey = z.infer<typeof ApiKeySchema>;

// API Category
export const ApiCategorySchema = z.object({
  id: z.string(),
  name: z.string(),
  category: z.string(),
  provider: z.string(),
  iconUrl: z.string().nullable(),
  description: z.string().nullable(),
  authType: z.string(),
  authHeader: z.string(),
  authPrefix: z.string().nullable(),
  keyPrefix: z.string(),
  keyPattern: z.string(),
  isActive: z.boolean(),
  createdAt: z.string(),
  updatedAt: z.string()
});

export type ApiCategory = z.infer<typeof ApiCategorySchema>;

// API Key Stats
export const ApiKeyStatsSchema = z.object({
  requests: z.number(),
  requestsChange: z.number(),
  tokens: z.number(),
  tokensChange: z.number(),
  cost: z.number(),
  lastCharge: z.string()
});

export type ApiKeyStats = z.infer<typeof ApiKeyStatsSchema>;

// API Key Detail
export const ApiKeyDetailSchema = z.object({
  id: z.string(),
  name: z.string(),
  description: z.string().nullable().optional(),
  key: z.string(),
  category: z.string(),
  provider: z.string(),
  status: z.enum(['active', 'inactive', 'revoked']),
  createdAt: z.string(),
  updatedAt: z.string().optional(),
  lastUsed: z.string().nullable().optional(),
  expirationDate: z.string().nullable().optional(),
  stats: ApiKeyStatsSchema
});

export type ApiKeyDetail = z.infer<typeof ApiKeyDetailSchema>;

// API Key Usage
export const ApiKeyUsageSchema = z.object({
  dailyRequests: z.array(
    z.object({
      date: z.string(),
      count: z.number()
    })
  ),
  totalRequests: z.number(),
  totalTokens: z.number(),
  avgResponseTime: z.number(),
  costThisMonth: z.number(),
  usageByModel: z.array(
    z.object({
      model: z.string(),
      requests: z.number(),
      tokens: z.number(),
      cost: z.number()
    })
  )
});

export type ApiKeyUsage = z.infer<typeof ApiKeyUsageSchema>;

// Channel Usage
export const ChannelUsageSchema = z.object({
  provider: z.string(),
  model: z.string(),
  requests: z.number(),
  tokens: z.number(),
  cost: z.number(),
  avgResponseTime: z.number()
});

export type ChannelUsage = z.infer<typeof ChannelUsageSchema>;

// Create API Key Request
export const CreateApiKeyRequestSchema = z.object({
  name: z.string(),
  categoryId: z.string(),
  description: z.string().optional()
});

export type CreateApiKeyRequest = z.infer<typeof CreateApiKeyRequestSchema>;

// Create API Key Response
export const CreateApiKeyResponseSchema = z.object({
  id: z.string(),
  key: z.string(),
  name: z.string(),
  category: z.string(),
  provider: z.string(),
  createdAt: z.string()
});

export type CreateApiKeyResponse = z.infer<typeof CreateApiKeyResponseSchema>;

// Update API Key Request
export const UpdateApiKeyRequestSchema = z.object({
  name: z.string().optional(),
  status: z.enum(['active', 'inactive']).optional(),
  description: z.string().optional(),
  expirationDate: z.string().optional()
});

export type UpdateApiKeyRequest = z.infer<typeof UpdateApiKeyRequestSchema>;

// Get API Keys Request
export const GetApiKeysRequestSchema = z.object({
  page: z.number().default(1),
  pageSize: z.number().default(10),
  status: z.enum(['active', 'inactive', 'revoked']).optional(),
  categoryId: z.string().optional(),
  search: z.string().optional()
});

export type GetApiKeysRequest = z.infer<typeof GetApiKeysRequestSchema>;

// Get API Keys Response
export const GetApiKeysResponseSchema = z.object({
  data: z.array(ApiKeySchema),
  total: z.number(),
  page: z.number(),
  pageSize: z.number()
});

export type GetApiKeysResponse = z.infer<typeof GetApiKeysResponseSchema>;

// Get API Categories Request
export const GetApiCategoriesRequestSchema = z.object({
  onlyActive: z.boolean().default(true)
});

export type GetApiCategoriesRequest = z.infer<typeof GetApiCategoriesRequestSchema>;

// Get API Categories Response
export const GetApiCategoriesResponseSchema = z.object({
  data: z.array(ApiCategorySchema)
});

export type GetApiCategoriesResponse = z.infer<typeof GetApiCategoriesResponseSchema>;

// Get API Key Detail Response
export const GetApiKeyDetailResponseSchema = z.object({
  data: ApiKeyDetailSchema
});

export type GetApiKeyDetailResponse = z.infer<typeof GetApiKeyDetailResponseSchema>;

// Get API Key Usage Request
export const GetApiKeyUsageRequestSchema = z.object({
  keyId: z.string(),
  timeRange: z.string().optional()
});

export type GetApiKeyUsageRequest = z.infer<typeof GetApiKeyUsageRequestSchema>;

// Get API Key Usage Response
export const GetApiKeyUsageResponseSchema = z.object({
  data: ApiKeyUsageSchema
});

export type GetApiKeyUsageResponse = z.infer<typeof GetApiKeyUsageResponseSchema>;

// Get Channel Usage Request
export const GetChannelUsageRequestSchema = z.object({
  timeRange: z.string().optional()
});

export type GetChannelUsageRequest = z.infer<typeof GetChannelUsageRequestSchema>;

// Get Channel Usage Response
export const GetChannelUsageResponseSchema = z.object({
  data: z.array(ChannelUsageSchema)
});

export type GetChannelUsageResponse = z.infer<typeof GetChannelUsageResponseSchema>;
