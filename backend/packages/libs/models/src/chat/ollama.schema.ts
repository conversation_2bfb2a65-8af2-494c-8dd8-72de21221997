import { z } from 'zod';

/**
 * Ollama 消息模式
 */
export const OllamaMessageSchema = z.object({
  role: z.string().describe('消息的角色，例如 "user" 或 "assistant"'),
  content: z.string().describe('消息的内容')
});

export type OllamaMessage = z.infer<typeof OllamaMessageSchema>;

/**
 * Ollama 聊天请求模式
 */
export const OllamaChatRequestSchema = z.object({
  model: z.string().describe('要使用的模型名称'),
  messages: z.array(OllamaMessageSchema).describe('聊天消息数组'),
  stream: z.boolean().optional().default(true).describe('是否使用流式响应'),
  temperature: z.number().optional().default(0.7).describe('采样温度，控制输出的随机性'),
  top_p: z.number().optional().default(1.0).describe('核采样参数'),
  top_k: z.number().optional().describe('限制每一步考虑的标记数量'),
  keep_alive: z.number().optional().describe('模型在内存中保持活跃的时间（秒）'),
  format: z.enum(['json']).optional().describe('指定响应格式'),
  userId: z.string().optional().describe('用户ID')
}).catchall(z.any());

export type OllamaChatRequest = z.infer<typeof OllamaChatRequestSchema>;

/**
 * Ollama 聊天响应消息模式
 */
export const OllamaChatResponseMessageSchema = z.object({
  role: z.string().describe('响应消息的角色，通常是 "assistant"'),
  content: z.string().describe('响应消息的内容')
});

/**
 * Ollama 聊天响应模式
 */
export const OllamaChatResponseSchema = z.object({
  model: z.string().describe('使用的模型名称'),
  created_at: z.string().describe('响应创建时间'),
  message: OllamaChatResponseMessageSchema.describe('响应消息'),
  done: z.boolean().describe('是否完成'),
  done_reason: z.string().optional().describe('完成原因'),
  total_duration: z.number().optional().describe('总持续时间（毫秒）'),
  load_duration: z.number().optional().describe('加载持续时间（毫秒）'),
  prompt_eval_count: z.number().optional().describe('提示评估计数'),
  prompt_eval_duration: z.number().optional().describe('提示评估持续时间（毫秒）'),
  eval_count: z.number().optional().describe('评估计数'),
  eval_duration: z.number().optional().describe('评估持续时间（毫秒）')
});

export type OllamaChatResponse = z.infer<typeof OllamaChatResponseSchema>;

/**
 * Ollama 生成请求模式
 */
export const OllamaGenerateRequestSchema = z.object({
  model: z.string().describe('要使用的模型名称'),
  prompt: z.string().describe('生成文本的提示'),
  stream: z.boolean().optional().default(true).describe('是否使用流式响应'),
  temperature: z.number().optional().default(0.7).describe('采样温度，控制输出的随机性'),
  top_p: z.number().optional().default(1.0).describe('核采样参数'),
  top_k: z.number().optional().describe('限制每一步考虑的标记数量'),
  keep_alive: z.number().optional().describe('模型在内存中保持活跃的时间（秒）'),
  format: z.enum(['json']).optional().describe('指定响应格式'),
  userId: z.string().optional().describe('用户ID')
}).catchall(z.any());

export type OllamaGenerateRequest = z.infer<typeof OllamaGenerateRequestSchema>;

/**
 * Ollama 生成响应模式
 */
export const OllamaGenerateResponseSchema = z.object({
  model: z.string().describe('使用的模型名称'),
  created_at: z.string().describe('响应创建时间'),
  response: z.string().describe('生成的响应文本'),
  done: z.boolean().describe('是否完成'),
  done_reason: z.string().optional().describe('完成原因'),
  total_duration: z.number().optional().describe('总持续时间（毫秒）'),
  load_duration: z.number().optional().describe('加载持续时间（毫秒）'),
  prompt_eval_count: z.number().optional().describe('提示评估计数'),
  prompt_eval_duration: z.number().optional().describe('提示评估持续时间（毫秒）'),
  eval_count: z.number().optional().describe('评估计数'),
  eval_duration: z.number().optional().describe('评估持续时间（毫秒）')
});

export type OllamaGenerateResponse = z.infer<typeof OllamaGenerateResponseSchema>;

/**
 * Ollama 嵌入请求模式
 */
export const OllamaEmbedRequestSchema = z.object({
  model: z.string().describe('要使用的模型名称'),
  prompt: z.string().describe('要嵌入的文本'),
  keep_alive: z.number().optional().describe('模型在内存中保持活跃的时间（秒）')
});

export type OllamaEmbedRequest = z.infer<typeof OllamaEmbedRequestSchema>;

/**
 * Ollama 嵌入响应模式
 */
export const OllamaEmbedResponseSchema = z.object({
  embedding: z.array(z.number()).describe('嵌入向量')
});

export type OllamaEmbedResponse = z.infer<typeof OllamaEmbedResponseSchema>;

/**
 * Ollama 多输入嵌入请求模式
 */
export const OllamaEmbedMultiRequestSchema = z.object({
  model: z.string().describe('要使用的模型名称'),
  input: z.array(z.string()).describe('要嵌入的文本数组'),
  keep_alive: z.number().optional().describe('模型在内存中保持活跃的时间（秒）')
});

export type OllamaEmbedMultiRequest = z.infer<typeof OllamaEmbedMultiRequestSchema>;

/**
 * Ollama 多输入嵌入响应模式
 */
export const OllamaEmbedMultiResponseSchema = z.object({
  model: z.string().describe('使用的模型名称'),
  embeddings: z.array(z.array(z.number())).describe('嵌入向量数组')
});

export type OllamaEmbedMultiResponse = z.infer<typeof OllamaEmbedMultiResponseSchema>;

/**
 * Ollama 模型详情模式
 */
export const OllamaModelDetailsSchema = z.object({
  parent_model: z.string().optional().describe('父模型名称'),
  format: z.string().describe('模型格式，例如 "gguf"'),
  family: z.string().describe('模型家族，例如 "llama"'),
  families: z.array(z.string()).nullable().describe('模型家族数组'),
  parameter_size: z.string().describe('参数大小，例如 "7B"'),
  quantization_level: z.string().describe('量化级别，例如 "Q4_0"')
});

export type OllamaModelDetails = z.infer<typeof OllamaModelDetailsSchema>;

/**
 * Ollama 模型列表响应模式
 */
export const OllamaModelListResponseSchema = z.object({
  models: z.array(
    z.object({
      name: z.string().describe('模型名称'),
      modified_at: z.string().describe('修改时间'),
      size: z.number().describe('模型大小（字节）'),
      digest: z.string().describe('模型摘要'),
      details: OllamaModelDetailsSchema.describe('模型详情')
    })
  ).describe('模型列表')
});

export type OllamaModelListResponse = z.infer<typeof OllamaModelListResponseSchema>;

/**
 * Ollama 运行中模型列表响应模式
 */
export const OllamaRunningModelListResponseSchema = z.object({
  models: z.array(
    z.object({
      name: z.string().describe('模型名称'),
      model: z.string().describe('模型标识符'),
      size: z.number().describe('模型大小（字节）'),
      digest: z.string().describe('模型摘要'),
      details: OllamaModelDetailsSchema.describe('模型详情'),
      expires_at: z.string().describe('过期时间'),
      size_vram: z.number().describe('显存使用大小（字节）')
    })
  ).describe('运行中的模型列表')
});

export type OllamaRunningModelListResponse = z.infer<typeof OllamaRunningModelListResponseSchema>;
