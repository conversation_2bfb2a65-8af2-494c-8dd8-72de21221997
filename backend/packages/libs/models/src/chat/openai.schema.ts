import { z } from 'zod';

/**
 * OpenAI 消息模式
 */
export const OpenAIMessageSchema = z.object({
  role: z.string().describe('消息的角色，例如 "system", "user" 或 "assistant"'),
  content: z.string().describe('消息的内容')
});

export type OpenAIMessage = z.infer<typeof OpenAIMessageSchema>;

/**
 * OpenAI 聊天请求模式
 */
export const OpenAIChatCompletionRequestSchema = z.object({
  model: z.string().describe('要使用的模型的 ID'),
  messages: z.array(OpenAIMessageSchema).describe('至今为止对话所包含的消息列表'),
  temperature: z.number().optional().default(0.7).describe('使用什么采样温度，介于 0 和 2 之间'),
  top_p: z.number().optional().default(1.0).describe('核采样参数'),
  n: z.number().optional().default(1).describe('为每个输入消息生成多少个聊天补全选择'),
  stream: z.boolean().optional().default(false).describe('是否使用流式响应'),
  stop: z.union([z.string(), z.array(z.string())]).optional().describe('最多 4 个序列，API 将停止进一步生成标记'),
  max_tokens: z.number().optional().describe('在聊天补全中生成的最大标记数'),
  presence_penalty: z.number().optional().default(0).describe('-2.0 和 2.0 之间的数字，正值会根据到目前为止是否出现在文本中来惩罚新标记'),
  frequency_penalty: z.number().optional().default(0).describe('-2.0 到 2.0 之间的数字，正值根据文本目前的存在频率惩罚新标记'),
  logit_bias: z.record(z.string(), z.number()).optional().describe('修改指定标记出现在补全中的可能性'),
  user: z.string().optional().describe('代表您的最终用户的唯一标识符'),
  response_format: z.object({
    type: z.enum(['json_object', 'text']).optional()
  }).optional().describe('指定模型必须输出的格式的对象')
}).catchall(z.any());

export type OpenAIChatCompletionRequest = z.infer<typeof OpenAIChatCompletionRequestSchema>;

/**
 * OpenAI 聊天响应选择模式
 */
export const OpenAIChatCompletionChoiceSchema = z.object({
  index: z.number().describe('选择的索引'),
  message: OpenAIMessageSchema.describe('响应消息'),
  finish_reason: z.string().describe('完成原因')
});

/**
 * OpenAI 聊天响应使用情况模式
 */
export const OpenAIChatCompletionUsageSchema = z.object({
  prompt_tokens: z.number().describe('提示标记数'),
  completion_tokens: z.number().describe('补全标记数'),
  total_tokens: z.number().describe('总标记数')
});

/**
 * OpenAI 聊天响应模式
 */
export const OpenAIChatCompletionResponseSchema = z.object({
  id: z.string().describe('响应 ID'),
  object: z.string().describe('对象类型'),
  created: z.number().describe('创建时间戳'),
  choices: z.array(OpenAIChatCompletionChoiceSchema).describe('补全选择'),
  usage: OpenAIChatCompletionUsageSchema.describe('使用情况统计')
});

export type OpenAIChatCompletionResponse = z.infer<typeof OpenAIChatCompletionResponseSchema>;

/**
 * OpenAI 流式聊天响应选择模式
 */
export const OpenAIChatCompletionChunkChoiceSchema = z.object({
  index: z.number().describe('选择的索引'),
  delta: z.object({
    content: z.string().optional().describe('增量内容')
  }).describe('增量消息内容'),
  finish_reason: z.string().nullable().describe('完成原因')
});

/**
 * OpenAI 流式聊天响应模式
 */
export const OpenAIChatCompletionChunkSchema = z.object({
  id: z.string().describe('响应 ID'),
  object: z.string().describe('对象类型'),
  created: z.number().describe('创建时间戳'),
  model: z.string().describe('使用的模型'),
  choices: z.array(OpenAIChatCompletionChunkChoiceSchema).describe('补全选择'),
  usage: OpenAIChatCompletionUsageSchema.optional().describe('使用情况统计（仅在最后一个chunk中提供）')
});

export type OpenAIChatCompletionChunk = z.infer<typeof OpenAIChatCompletionChunkSchema>;

/**
 * OpenAI 补全请求模式
 */
export const OpenAICompletionRequestSchema = z.object({
  model: z.string().describe('要使用的模型的 ID'),
  prompt: z.string().describe('生成完成的提示'),
  suffix: z.string().optional().describe('在插入文本的补全之后出现的后缀'),
  max_tokens: z.number().optional().default(16).describe('在补全中生成的最大令牌数'),
  temperature: z.number().optional().default(1).describe('要使用的采样温度，介于 0 和 2 之间'),
  top_p: z.number().optional().default(1).describe('核采样参数'),
  n: z.number().optional().default(1).describe('为每个提示生成的补全数量'),
  stream: z.boolean().optional().default(false).describe('是否流回部分进度'),
  logprobs: z.number().nullable().optional().describe('包括 logprobs 个最可能令牌的日志概率'),
  echo: z.boolean().optional().default(false).describe('除了补全之外，还回显提示'),
  stop: z.union([z.string(), z.array(z.string())]).optional().describe('最多 4 个序列，API 将停止在其中生成更多令牌'),
  presence_penalty: z.number().optional().default(0).describe('-2.0 和 2.0 之间的数字，正值根据它们是否出现在目前的文本中来惩罚新令牌'),
  frequency_penalty: z.number().optional().default(0).describe('-2.0 和 2.0 之间的数字，正值根据文本目前的现有频率处罚新令牌'),
  best_of: z.number().optional().default(1).describe('在服务器端生成 best_of 个补全，并返回"最佳"补全'),
  logit_bias: z.record(z.string(), z.number()).optional().describe('修改完成中指定令牌出现的可能性'),
  user: z.string().optional().describe('代表您的最终用户的唯一标识符')
}).catchall(z.any());

export type OpenAICompletionRequest = z.infer<typeof OpenAICompletionRequestSchema>;

/**
 * OpenAI 补全响应选择模式
 */
export const OpenAICompletionChoiceSchema = z.object({
  text: z.string().describe('生成的文本'),
  index: z.number().describe('选择的索引'),
  logprobs: z.any().nullable().describe('日志概率'),
  finish_reason: z.string().describe('完成原因')
});

/**
 * OpenAI 补全响应模式
 */
export const OpenAICompletionResponseSchema = z.object({
  id: z.string().describe('响应 ID'),
  object: z.string().describe('对象类型'),
  created: z.number().describe('创建时间戳'),
  model: z.string().describe('使用的模型'),
  choices: z.array(OpenAICompletionChoiceSchema).describe('补全选择'),
  usage: OpenAIChatCompletionUsageSchema.describe('使用情况统计')
});

export type OpenAICompletionResponse = z.infer<typeof OpenAICompletionResponseSchema>;

/**
 * OpenAI 嵌入请求模式
 */
export const OpenAIEmbeddingRequestSchema = z.object({
  model: z.string().describe('要使用的模型的 ID'),
  input: z.union([z.string(), z.array(z.string())]).describe('输入文本以获取嵌入')
});

export type OpenAIEmbeddingRequest = z.infer<typeof OpenAIEmbeddingRequestSchema>;

/**
 * OpenAI 嵌入响应数据项模式
 */
export const OpenAIEmbeddingResponseDataItemSchema = z.object({
  object: z.string().describe('对象类型'),
  embedding: z.array(z.number()).describe('嵌入向量'),
  index: z.number().describe('索引')
});

/**
 * OpenAI 嵌入响应模式
 */
export const OpenAIEmbeddingResponseSchema = z.object({
  object: z.string().describe('对象类型'),
  data: z.array(OpenAIEmbeddingResponseDataItemSchema).describe('嵌入数据'),
  model: z.string().describe('使用的模型'),
  usage: z.object({
    prompt_tokens: z.number().describe('提示标记数'),
    total_tokens: z.number().describe('总标记数')
  }).describe('使用情况统计')
});

export type OpenAIEmbeddingResponse = z.infer<typeof OpenAIEmbeddingResponseSchema>;

/**
 * OpenAI 错误响应模式
 */
export const OpenAIErrorResponseSchema = z.object({
  error: z.object({
    message: z.string().describe('错误消息'),
    type: z.string().describe('错误类型'),
    param: z.string().nullable().describe('错误参数'),
    code: z.string().nullable().describe('错误代码')
  }).describe('错误详情')
});

export type OpenAIErrorResponse = z.infer<typeof OpenAIErrorResponseSchema>;

/**
 * OpenAI 模型信息模式
 */
export const OpenAIModelSchema = z.object({
  id: z.string().describe('模型的唯一标识符'),
  object: z.string().default('model').describe('对象类型，始终为 "model"'),
  created: z.number().describe('模型创建的时间戳'),
  owned_by: z.string().describe('模型的所有者，例如 "openai" 或 "system"')
});

export type OpenAIModel = z.infer<typeof OpenAIModelSchema>;

/**
 * OpenAI 模型列表响应模式
 */
export const OpenAIModelListResponseSchema = z.object({
  object: z.string().default('list').describe('对象类型，始终为 "list"'),
  data: z.array(OpenAIModelSchema).describe('模型列表')
});

export type OpenAIModelListResponse = z.infer<typeof OpenAIModelListResponseSchema>;
