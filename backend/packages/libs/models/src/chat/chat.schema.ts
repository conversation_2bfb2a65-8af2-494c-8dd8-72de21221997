import { z } from 'zod';

/**
 * Chat body schema - Compatible with OpenAI's ChatCompletionRequest
 */
export const ChatBodySchema = z.object({
  model: z.string(),
  messages: z.array(
    z.object({
      role: z.string(),
      content: z.string(),
    })
  ),
  temperature: z.number().optional().default(0.7),
  top_p: z.number().optional().default(1.0),
  n: z.number().optional().default(1),
  stream: z.boolean().optional().default(true),
  stop: z.union([z.string(), z.array(z.string())]).optional(),
  max_tokens: z.number().optional(),
  presence_penalty: z.number().optional(),
  frequency_penalty: z.number().optional(),
  logit_bias: z.record(z.number()).optional(),
  user: z.string().optional(),
  response_format: z.object({
    type: z.string()
  }).optional(),
  tools: z.array(z.any()).optional(),
  tool_choice: z.any().optional(),
  // 添加自定义字段
  userId: z.string().optional(),
});

/**
 * Generate body schema
 */
export const GenerateBodySchema = z.object({
  model: z.string(),
  prompt: z.string(),
  stream: z.boolean().optional().default(true),
  temperature: z.number().optional().default(0.7),
  top_p: z.number().optional().default(1.0),
  userId: z.string().optional(),
});

/**
 * 用户使用统计项模型
 */
export const UserUsageStatsItemSchema = z.object({
  day: z.string().describe('Day of usage'),
  task_count: z.string().describe('Number of tasks'),
  total_duration: z.number().nullable().describe('Total duration in milliseconds'),
  total_eval_count: z.number().nullable().describe('Total number of evaluations'),
  total_prompt_eval_count: z.number().nullable().describe('Total number of prompt evaluations')
});

export type UserUsageStatsItem = z.infer<typeof UserUsageStatsItemSchema>;


export const ChatRequestBodySchema = z.object({
  model: z.string(),
  messages: z.array(
    z.object({
      role: z.string(),
      content: z.string(),
    })
  ),
  temperature: z.number().optional(),
  top_p: z.number().optional(),
  n: z.number().optional(),
  stream: z.boolean().optional(),
  stop: z.union([z.string(), z.array(z.string())]).optional(),
  max_tokens: z.number().optional(),
  presence_penalty: z.number().optional(),
  frequency_penalty: z.number().optional(),
  logit_bias: z.record(z.number()).optional(),
  user: z.string().optional(),
  response_format: z.object({
    type: z.string()
  }).optional(),
  tools: z.array(z.any()).optional(),
  tool_choice: z.any().optional(),
  // 添加自定义字段
  userId: z.string().optional(),
}).catchall(z.any());

export type ChatRequestBody = z.infer<typeof ChatRequestBodySchema>;
export const GenerateRequestBodySchema = z.object({
  model: z.string(),
  prompt: z.string(),
  stream: z.boolean().optional(),
  temperature: z.number().optional(),
  top_p: z.number().optional(),
  userId: z.string().optional(),
}).catchall(z.any());

export type GenerateRequestBody = z.infer<typeof GenerateRequestBodySchema>;


/**
 * OpenAI Chat Message
 */
export const ChatMessageSchema = z.object({
  role: z.string(),
  content: z.string(),
});

export type ChatMessage = z.infer<typeof ChatMessageSchema>;

/**
 * OpenAI Chat Completion Request
 */
export const ChatCompletionRequestSchema = z.object({
  model: z.string(),
  messages: z.array(ChatMessageSchema),
  temperature: z.number().optional(),
  top_p: z.number().optional(),
  n: z.number().optional(),
  stream: z.boolean().optional(),
  stop: z.union([z.string(), z.array(z.string())]).optional(),
  max_tokens: z.number().optional(),
  presence_penalty: z.number().optional(),
  frequency_penalty: z.number().optional(),
  logit_bias: z.record(z.number()).optional(),
  user: z.string().optional(),
  response_format: z.object({
    type: z.string()
  }).optional(),
  tools: z.array(z.any()).optional(),
  tool_choice: z.any().optional(),
  userId: z.string().optional(),
});

export type ChatCompletionRequest = z.infer<typeof ChatCompletionRequestSchema>;

/**
 * OpenAI Chat Completion Response Choice
 */
export const ChatCompletionResponseChoiceSchema = z.object({
  index: z.number(),
  message: ChatMessageSchema,
  finish_reason: z.string(),
});

export type ChatCompletionResponseChoice = z.infer<typeof ChatCompletionResponseChoiceSchema>;

/**
 * OpenAI Chat Completion Response
 */
export const ChatCompletionResponseSchema = z.object({
  id: z.string(),
  object: z.string(),
  created: z.number(),
  choices: z.array(ChatCompletionResponseChoiceSchema),
  usage: z.object({
    prompt_tokens: z.number(),
    completion_tokens: z.number(),
    total_tokens: z.number(),
  }),
});

export type ChatCompletionResponse = z.infer<typeof ChatCompletionResponseSchema>;


/**
 * 导出所有模型
 */
export const ChatRepositorySchemas = {
  UserUsageStatsItem: UserUsageStatsItemSchema,
};
