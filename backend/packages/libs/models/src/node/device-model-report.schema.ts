import { z } from 'zod';
import { OllamaModel } from './device-status.schema';

/**
 * 设备模型上报请求模式
 */
export const DeviceModelReportRequestSchema = z.object({
  device_id: z.string().describe('Device ID'),
  models: z.array(OllamaModel).describe('List of models supported by the device')
});

export type DeviceModelReportRequest = z.infer<typeof DeviceModelReportRequestSchema>;

/**
 * 设备模型上报响应模式
 */
export const DeviceModelReportResponseSchema = z.object({
  success: z.boolean().describe('Whether the report was successful'),
  message: z.string().optional().describe('Message about the operation')
});

export type DeviceModelReportResponse = z.infer<typeof DeviceModelReportResponseSchema>;
