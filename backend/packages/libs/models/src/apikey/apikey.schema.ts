import { z } from 'zod';



// API Key Schema
export const ApiKeySchema = z.object({
  id: z.string().uuid().describe('API key ID'),
  userId: z.string().uuid().describe('User ID'),
  keyHash: z.string().describe('Hashed API key'),
  keyPrefix: z.string().describe('Key prefix'),
  keyMask: z.string().describe('Masked key for display'),
  description: z.string().nullable().optional(),
  name: z.string().nullable().describe('Key name'),
  status: z.enum(['active', 'inactive', 'revoked']).describe('Key status'),
  keyType: z.enum(['platform', 'third_party']).describe('Key type: platform or third_party'),
  provider: z.string().nullable().optional().describe('Provider for third-party keys (openai, anthropic, google, cohere)'),
  encrypted_key_data: z.string().nullable().optional().describe('KMS encrypted data containing client-side encrypted API key information'),
  totalRequests: z.number().default(0).describe('Total number of requests'),
  totalTokens: z.number().default(0).describe('Total number of tokens consumed'),
  totalCost: z.number().default(0).describe('Total cost incurred (USD)'),
  createdAt: z.string().datetime().describe('Creation timestamp'),
  updatedAt: z.string().datetime().describe('Update timestamp'),
  deletedAt: z.string().datetime().nullable().describe('Deletion timestamp'),
  expirationDate: z.string().nullable().optional(),
  lastUsed: z.string().nullable().optional(),
  region: z.string().nullable().optional().describe('Region for third-party keys (us, eu, apac, etc.)')
});

export type ApiKey = z.infer<typeof ApiKeySchema>;

// API Usage Schema
export const ApiUsageSchema = z.object({
  id: z.string().uuid().describe('API usage ID'),
  apiKeyId: z.string().uuid().describe('API key ID'),
  userId: z.string().uuid().describe('User ID'),
  taskId: z.string().uuid().nullable().describe('Task ID'),
  endpoint: z.string().describe('API endpoint'),
  method: z.string().describe('HTTP method'),
  statusCode: z.number().nullable().describe('HTTP status code'),
  timestamp: z.string().datetime().describe('Usage timestamp'),
  date: z.string().describe('Usage date'),
});

export type ApiUsage = z.infer<typeof ApiUsageSchema>;

// API Key Stats Schema
export const ApiKeyStatsSchema = z.object({
  requests: z.number().describe('Total number of requests'),
  requestsChange: z.number().describe('Percentage change in requests'),
  tokens: z.number().describe('Total number of tokens'),
  tokensChange: z.number().describe('Percentage change in tokens'),
  cost: z.number().describe('Total cost'),
  lastCharge: z.string().describe('Last charge date'),
});

export type ApiKeyStats = z.infer<typeof ApiKeyStatsSchema>;

// API Key Detail Schema
export const ApiKeyDetailSchema = z.object({
  id: z.string().uuid().describe('API key ID'),
  name: z.string().describe('Key name'),
  key: z.string().describe('Masked key'),
  description: z.string().nullable().describe('Key description'),
  provider: z.string().describe('API provider').optional(),
  status: z.enum(['active', 'inactive', 'revoked']).describe('Key status'),
  expirationDate: z.string().datetime().nullable().describe('Expiration date'),
  createdAt: z.string().datetime().describe('Creation timestamp'),
  updatedAt: z.string().datetime().describe('Creation timestamp'),
  lastUsed: z.string().datetime().nullable().describe('Last usage timestamp'),
  stats: ApiKeyStatsSchema,
});

export type ApiKeyDetail = z.infer<typeof ApiKeyDetailSchema>;

// Request Schemas
export const CreateApiKeyRequestSchema = z.object({
  name: z.string().min(1).describe('Key name'),
  description: z.string().optional().describe('Key description'),
});

export type CreateApiKeyRequest = z.infer<typeof CreateApiKeyRequestSchema>;

export const UpdateApiKeyRequestSchema = z.object({
  name: z.string().min(1).optional().describe('Key name'),
  status: z.enum(['active', 'inactive']).optional().describe('Key status'),
  description: z.string().optional(),
  expirationDate: z.string().optional()
});

export type UpdateApiKeyRequest = z.infer<typeof UpdateApiKeyRequestSchema>;

export const GetApiKeysRequestSchema = z.object({
  page: z.number().default(1).describe('Page number'),
  pageSize: z.number().default(10).describe('Page size'),
  status: z.enum(['active', 'inactive', 'revoked']).optional().describe('Filter by status'),
  search: z.string().optional().describe('Search term'),
});

export type GetApiKeysRequest = z.infer<typeof GetApiKeysRequestSchema>;

export const GetApiCategoriesRequestSchema = z.object({
  onlyActive: z.boolean().default(true).describe('Only return active categories'),
});

export type GetApiCategoriesRequest = z.infer<typeof GetApiCategoriesRequestSchema>;

export const GetApiKeyUsageRequestSchema = z.object({
  keyId: z.string().uuid().describe('API key ID'),
  timeRange: z.string().optional().describe('Time range in days'),
});

export type GetApiKeyUsageRequest = z.infer<typeof GetApiKeyUsageRequestSchema>;

// Response Schemas
export const CreateApiKeyResponseSchema = z.object({
  id: z.string().uuid().describe('API key ID'),
  key: z.string().describe('Full API key (only returned once)'),
  name: z.string().describe('Key name'),
  category: z.string().describe('API category'),
  provider: z.string().describe('API provider'),
  createdAt: z.string().datetime().describe('Creation timestamp'),
});

export type CreateApiKeyResponse = z.infer<typeof CreateApiKeyResponseSchema>;

export const GetApiKeysResponseSchema = z.object({
  data: z.array(ApiKeySchema),
  total: z.number(),
  page: z.number(),
  pageSize: z.number(),
});

export type GetApiKeysResponse = z.infer<typeof GetApiKeysResponseSchema>;



export const GetApiKeyDetailResponseSchema = z.object({
  data: ApiKeyDetailSchema,
});

export type GetApiKeyDetailResponse = z.infer<typeof GetApiKeyDetailResponseSchema>;

export const GetApiKeyUsageResponseSchema = z.object({
  data: z.object({
    dailyRequests: z.array(z.object({
      date: z.string().describe('Date'),
      count: z.number().describe('Request count'),
    })),
    totalRequests: z.number().describe('Total requests'),
    totalTokens: z.number().describe('Total tokens'),
    avgResponseTime: z.number().describe('Average response time in ms'),
    costThisMonth: z.number().describe('Cost this month'),
    usageByModel: z.array(z.object({
      model: z.string().describe('Model name'),
      requests: z.number().describe('Request count'),
      tokens: z.number().describe('Token count'),
      cost: z.number().describe('Cost'),
    })),
  }),
});

export type GetApiKeyUsageResponse = z.infer<typeof GetApiKeyUsageResponseSchema>;

// API Key Usage Summary Schema
export const ApiKeyUsageSummarySchema = z.object({
  totalRequests: z.string().describe('Total requests formatted'),
  totalTokens: z.string().describe('Total tokens formatted'),
  avgResponseTime: z.string().describe('Average response time formatted'),
  costThisMonth: z.string().describe('Cost this month formatted'),
});

export type ApiKeyUsageSummary = z.infer<typeof ApiKeyUsageSummarySchema>;

// Channel Usage Schema
export const ChannelUsageSchema = z.object({
  provider: z.string().describe('Provider name'),
  model: z.string().describe('Model name'),
  requests: z.number().describe('Request count'),
  tokens: z.number().describe('Token count'),
  cost: z.number().describe('Cost'),
  avgResponseTime: z.number().describe('Average response time in ms'),
});

export type ChannelUsage = z.infer<typeof ChannelUsageSchema>;

// Get Channel Usage Request Schema
export const GetChannelUsageRequestSchema = z.object({
  timeRange: z.string().optional().describe('Time range in days'),
});

export type GetChannelUsageRequest = z.infer<typeof GetChannelUsageRequestSchema>;

// Get Channel Usage Response Schema
export const GetChannelUsageResponseSchema = z.object({
  data: z.array(ChannelUsageSchema),
});

export type GetChannelUsageResponse = z.infer<typeof GetChannelUsageResponseSchema>;

// API Key Usage
export const ApiKeyUsageSchema = z.object({
  dailyRequests: z.array(
    z.object({
      date: z.string(),
      count: z.number()
    })
  ),
  totalRequests: z.number(),
  totalTokens: z.number(),
  avgResponseTime: z.number(),
  costThisMonth: z.number(),
  usageByModel: z.array(
    z.object({
      model: z.string(),
      requests: z.number(),
      tokens: z.number(),
      cost: z.number()
    })
  )
});

export type ApiKeyUsage = z.infer<typeof ApiKeyUsageSchema>;

/**
 * API Key 验证结果模型
 */
export const ApiKeyValidationResultSchema = z.object({
  id: z.string().uuid(),
  userId: z.string().uuid(),
  status: z.string()
});

export type ApiKeyValidationResult = z.infer<typeof ApiKeyValidationResultSchema>;

/**
 * API 使用日志参数模型
 */
export const ApiUsageLogParamsSchema = z.object({
  apiKeyId: z.string().uuid(),
  userId: z.string().uuid().optional(),
  endpoint: z.string(),
  method: z.string(),
  model: z.string().optional(),
  requestSize: z.number(),
  statusCode: z.number()
});

export type ApiUsageLogParams = z.infer<typeof ApiUsageLogParamsSchema>;

/**
 * API 使用日志更新参数模型
 */
export const ApiUsageLogUpdateParamsSchema = z.object({
  apiKeyId: z.string().uuid(),
  responseTime: z.number(),
  statusCode: z.number(),
  responseSize: z.number().optional()
});

export type ApiUsageLogUpdateParams = z.infer<typeof ApiUsageLogUpdateParamsSchema>;

/**
 * 使用摘要模型
 */
export const UsageSummarySchema = z.object({
  totalRequests: z.number(),
  totalTokens: z.number(),
  avgResponseTime: z.number(),
  costThisMonth: z.number()
});

export type UsageSummary = z.infer<typeof UsageSummarySchema>;

/**
 * 整体使用数据模型
 */
export const OverallUsageSchema = z.object({
  dailyData: z.array(z.object({
    date: z.string(),
    requests: z.number(),
    tokens: z.number(),
    cost: z.number()
  })),
  monthlyData: z.array(z.object({
    month: z.string(),
    year: z.number(),
    requests: z.number(),
    tokens: z.number(),
    cost: z.number()
  }))
});

export type OverallUsage = z.infer<typeof OverallUsageSchema>;

/**
 * 数据库行类型定义 - 用于类型安全的数据库操作
 */
export const ApiKeyRowSchema = z.object({
  id: z.string(),
  userId: z.string(),
  keyHash: z.string(),
  keyPrefix: z.string(),
  keyMask: z.string(),
  name: z.string().nullable(),
  status: z.enum(['active', 'inactive', 'revoked']),
  keyType: z.enum(['platform', 'third_party']),
  kmsKeyId: z.string().nullable().optional(),
  providerKeyId: z.string().nullable().optional(),
  encryptedKeyData: z.string().nullable().optional(),
  totalRequests: z.number().default(0),
  totalTokens: z.number().default(0),
  totalCost: z.number().default(0),
  createdAt: z.string(),
  updatedAt: z.string(),
  deletedAt: z.string().nullable(),
  description: z.string().nullable().optional(),
  expirationDate: z.string().nullable().optional(),
  lastUsed: z.string().nullable().optional(),
  createdBy: z.string().nullable().optional()
});

export type ApiKeyRow = z.infer<typeof ApiKeyRowSchema>;



export const DailyRequestRowSchema = z.object({
  date: z.string(),
  count: z.string()
});

export type DailyRequestRow = z.infer<typeof DailyRequestRowSchema>;

export const ModelUsageRowSchema = z.object({
  model: z.string(),
  requests: z.string(),
  tokens: z.string(),
  cost: z.string()
});

export type ModelUsageRow = z.infer<typeof ModelUsageRowSchema>;

export const ChannelUsageRowSchema = z.object({
  provider: z.string(),
  model: z.string(),
  requests: z.string(),
  tokens: z.string(),
  cost: z.string(),
  avg_response_time: z.string()
});

export type ChannelUsageRow = z.infer<typeof ChannelUsageRowSchema>;

/**
 * 导出所有ApiKey Repository相关的Schema
 */
// API Key Info Schema - 用于请求中的API密钥信息
export const ApiKeyInfoSchema = z.object({
  keyId: z.string().uuid().optional().describe('API key ID'),
  userId: z.string().uuid().optional().describe('User ID'),
  category: z.string().describe('API category name'),
  provider: z.string().describe('API provider'),
  model: z.string().optional().describe('Model name'),
  // Key routing information
  actualApiKey: z.string().optional().describe('The actual API key to use for requests'),
  thirdPartyKeyId: z.string().uuid().optional().describe('Third-party key ID used'),
  keyType: z.enum(['platform', 'third_party']).optional().describe('Type of key resolution'),
  platformKeyId: z.string().uuid().optional().describe('Original platform key ID')
});

export type ApiKeyInfo = z.infer<typeof ApiKeyInfoSchema>;



// Third-party schemas have been moved to @saito/third-party module



export const ApiKeyRepositorySchemas = {
  ApiKeyValidationResult: ApiKeyValidationResultSchema,
  ApiUsageLogParams: ApiUsageLogParamsSchema,
  ApiUsageLogUpdateParams: ApiUsageLogUpdateParamsSchema,
  UsageSummary: UsageSummarySchema,
  OverallUsage: OverallUsageSchema,
  ApiKeyRow: ApiKeyRowSchema,
  DailyRequestRow: DailyRequestRowSchema,
  ModelUsageRow: ModelUsageRowSchema,
  ChannelUsageRow: ChannelUsageRowSchema,
  ApiKeyInfo: ApiKeyInfoSchema,
  // Third-party schemas moved to @saito/third-party module
};
