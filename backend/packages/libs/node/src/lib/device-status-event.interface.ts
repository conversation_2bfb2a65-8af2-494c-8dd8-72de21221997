/**
 * 设备状态变更事件接口
 */
export interface DeviceStatusChangeEvent {
  deviceId: string;
  fromStatus: string;
  toStatus: string;
  timestamp: Date;
  reason?: string; // 状态变更原因（如：socket_disconnect, heartbeat_timeout, manual等）
}

/**
 * 设备状态事件监听器接口
 */
export interface DeviceStatusEventListener {
  /**
   * 处理设备状态变更事件
   * @param event 状态变更事件
   */
  onDeviceStatusChanged(event: DeviceStatusChangeEvent): Promise<void>;
}

/**
 * 设备状态事件管理器接口
 */
export interface DeviceStatusEventManager {
  /**
   * 注册状态变更监听器
   * @param listener 监听器
   */
  addListener(listener: DeviceStatusEventListener): void;

  /**
   * 移除状态变更监听器
   * @param listener 监听器
   */
  removeListener(listener: DeviceStatusEventListener): void;

  /**
   * 发布设备状态变更事件
   * @param event 状态变更事件
   */
  publishStatusChange(event: DeviceStatusChangeEvent): Promise<void>;
}
