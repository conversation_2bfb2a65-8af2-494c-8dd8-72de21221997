import {
  Device,
  ConnectTask,
  CreateConnectTaskRequest,
  CreateConnectTaskResponse,
  DeviceHeartbeatRequest,
  DeviceListRequest,
  DeviceListResponse,
  DeviceConnectionRequest,
  DeviceConnectionResponse,
  DeviceStatusChange,
  Task,
  DeviceRegisterRequest,
  DeviceRegisterResponse,
  NewDeviceHeartbeatRequest,
  DeviceModel,
  ChatDeviceMetricsSchema,
  ChatNode,
  DeviceModelReportRequest,
  DeviceModelReportResponse
} from '@saito/models';
import { z } from 'zod';
import { HttpStatus } from '@nestjs/common';

/**
 * @class NodeService
 * @abstract
 * @classdesc Abstract class for node services, defining abstract methods for handling node operations.
 */
export abstract class NodeService {
  /**
   * @function createConnectTask
   * @abstract
   * @description Creates a new connect task for a device.
   * @param {CreateConnectTaskRequest} request - The request containing task information.
   * @param {string} userId - The ID of the user creating the task.
   * @param {string} walletAddress - The wallet address of the user.
   * @returns {Promise<CreateConnectTaskResponse>} - The response containing the task ID and one-time code.
   */
  abstract createConnectTask(request: CreateConnectTaskRequest, userId: string, walletAddress: string): Promise<CreateConnectTaskResponse>;
  abstract updateConnectTask(deviceId:string):Promise<CreateConnectTaskResponse>;
  /**
   * @function getConnectTasks
   * @abstract
   * @description Gets a list of connect tasks for a user.
   * @param {DeviceListRequest} request - The request containing pagination and filter information.
   * @param {string} userId - The ID of the user.
   * @returns {Promise<DeviceListResponse>} - The response containing the list of connect tasks.
   */
  abstract getConnectTasks(request: DeviceListRequest, userId: string): Promise<DeviceListResponse>;

  /**
   * @function connectDevice
   * @abstract
   * @description Connects a device using a one-time code.
   * @param {DeviceConnectionRequest} request - The request containing the one-time code and device information.
   * @returns {Promise<DeviceConnectionResponse>} - The response containing the node ID and status.
   */
  abstract connectDevice(request: DeviceConnectionRequest): Promise<DeviceConnectionResponse>;

  /**
   * @function updateDeviceHeartbeat
   * @abstract
   * @description Updates the heartbeat of a device.
   * @param {DeviceHeartbeatRequest} request - The request containing the node ID and status information.
   * @returns {Promise<void>} - A promise that resolves when the heartbeat is updated.
   */
  abstract updateDeviceHeartbeat(request: DeviceHeartbeatRequest): Promise<void>;

  /**
   * @function getDevices
   * @abstract
   * @description Gets a list of devices for a user.
   * @param {DeviceListRequest} request - The request containing pagination and filter information.
   * @param {string} userId - The ID of the user.
   * @returns {Promise<DeviceListResponse>} - The response containing the list of devices.
   */
  abstract getDevices(request: DeviceListRequest, userId: string): Promise<DeviceListResponse>;

  /**
   * @function getDevice
   * @abstract
   * @description Gets a device by ID.
   * @param {string} deviceId - The ID of the device.
   * @param {string} userId - The ID of the user.
   * @returns {Promise<Device | null>} - The device or null if not found.
   */
  abstract getDevice(deviceId: string, userId: string): Promise<Device | null>;

  /**
   * @function updateDeviceStatus
   * @abstract
   * @description Updates the status of a device.
   * @param {string} deviceId - The ID of the device.
   * @param {string} status - The new status for the device.
   * @returns {Promise<any>} - The result of the update operation.
   */
  abstract updateDeviceStatus(deviceId: string, status: string): Promise<any>;

  /**
   * @function findIdleNodes
   * @abstract
   * @description Finds idle nodes that can be used for tasks.
   * @param {string} [model] - Optional model name to filter nodes by supported models.
   * @returns {Promise<Array<{device_id: string, id: string}>>} - A list of idle nodes.
   */
  abstract findIdleNodes(model?: string): Promise<Array<{device_id: string, id: string}>>;

  /**
   * @function findAvailableNodes
   * @abstract
   * @description Finds nodes that are not busy with tasks.
   * @param {string} [model] - Optional model name to filter nodes by supported models.
   * @returns {Promise<Array<{device_id: string, id: string}>>} - A list of nodes that are not busy.
   */
  abstract findAvailableNodes(model?: string): Promise<Array<{device_id: string, id: string}>>;

  /**
   * @function findNodesByStatus
   * @abstract
   * @description Finds nodes with a specific status.
   * @param {string} status - The status to filter by.
   * @param {string} [model] - Optional model name to filter nodes by supported models.
   * @returns {Promise<Array<{device_id: string, id: string}>>} - A list of nodes with the specified status.
   */
  abstract findNodesByStatus(status: string, model?: string): Promise<Array<{device_id: string, id: string}>>;

  /**
   * @function checkDevicesStatus
   * @abstract
   * @description Checks the status of all devices and updates if necessary.
   * @returns {Promise<void>} - A promise that resolves when the check is complete.
   */
  abstract checkDevicesStatus(): Promise<void>;

  /**
   * @function getDeviceStatusHistory
   * @abstract
   * @description Gets the status history of a device.
   * @param {string} deviceId - The ID of the device.
   * @param {string} userId - The ID of the user.
   * @param {number} days - The number of days to look back.
   * @returns {Promise<DeviceStatusChange[]>} - The status history.
   */
  abstract getDeviceStatusHistory(deviceId: string, userId: string, days: number): Promise<DeviceStatusChange[]>;

  /**
   * @function getDeviceTasks
   * @abstract
   * @description Gets the tasks of a device.
   * @param {string} deviceId - The ID of the device.
   * @param {string} userId - The ID of the user.
   * @param {DeviceListRequest} request - The request containing pagination and filter information.
   * @returns {Promise<{data: Task[], total: number, page: number, pageSize: number}>} - The tasks.
   */
  abstract getDeviceTasks(deviceId: string, userId: string, request: DeviceListRequest): Promise<{data: Task[], total: number, page: number, pageSize: number}>;

  /**
   * @function registerDevice
   * @abstract
   * @description Registers a device using a one-time code.
   * @param {DeviceRegisterRequest} request - The request containing the registration information.
   * @returns {Promise<DeviceRegisterResponse>} - The response containing the node ID and status.
   */
  abstract registerDevice(request: DeviceRegisterRequest): Promise<DeviceRegisterResponse>;

  /**
   * @function updateDeviceHeartbeatNew
   * @abstract
   * @description Updates the heartbeat of a device using the new format.
   * @param {NewDeviceHeartbeatRequest} request - The request containing the heartbeat information.
   * @returns {Promise<void>} - A promise that resolves when the heartbeat is updated.
   */
  abstract updateDeviceHeartbeatNew(request: NewDeviceHeartbeatRequest): Promise<void>;

  /**
   * @function getConnectTaskStatus
   * @abstract
   * @description Gets the status of a connect task.
   * @param {string} taskId - The ID of the connect task.
   * @param {string} userId - The ID of the user.
   * @returns {Promise<{task_id: string, status: string, device_id: string | null, created_at: string, updated_at: string} | null>} - The task status or null if not found.
   */
  abstract getConnectTaskStatus(taskId: string, userId: string): Promise<{task_id: string, status: string, device_id: string | null, created_at: string, updated_at: string} | null>;

  /**
   * @function getDeviceModels
   * @abstract
   * @description Gets the models supported by a device.
   * @param {string} deviceId - The ID of the device.
   * @param {string} userId - The ID of the user.
   * @returns {Promise<DeviceModel[]>} - The list of models supported by the device.
   */
  abstract getDeviceModels(deviceId: string, userId: string): Promise<DeviceModel[]>;

  /**
   * @function getModels
   * @abstract
   * @description Gets all models.
   * @returns {Promise<DeviceModel[]>} - The list of all models.
   */
  abstract getModels(): Promise<DeviceModel[]>;

  /**
   * @function getTargetNode
   * @abstract
   * @description Gets the best available node for processing requests based on performance metrics.
   * @param {string} [model] - Optional model name to filter nodes by supported models.
   * @returns {Promise<ChatNode | { error: { message: string; type: string; param: string | null; code: string | null; }; status: number; }>} - The best available node or an error object.
   */
  abstract getTargetNode(model?: string): Promise<ChatNode | { error: { message: string; type: string; param: string | null; code: string | null; }; status: number; }>;

  /**
   * @function getOnlineDeviceModels
   * @abstract
   * @description Gets all models from online devices.
   * @param {string} format - The format of the response, either 'ollama' or 'openai'.
   * @returns {Promise<any>} - The list of models from online devices in the specified format.
   */
  abstract getOnlineDeviceModels(format?: string): Promise<any>;

  /**
   * @function reportDeviceModels
   * @abstract
   * @description Updates the models supported by a device.
   * @param {DeviceModelReportRequest} request - The request containing the device ID and models.
   * @returns {Promise<DeviceModelReportResponse>} - The response indicating success or failure.
   */
  abstract reportDeviceModels(request: DeviceModelReportRequest): Promise<DeviceModelReportResponse>;

  /**
   * @function getDeviceDidDocument
   * @abstract
   * @description Gets the DID Document for a device.
   * @param {string} deviceId - The ID of the device (DID format).
   * @returns {Promise<any | null>} - The DID Document or null if not found.
   */
  abstract getDeviceDidDocument(deviceId: string): Promise<any | null>;
}
