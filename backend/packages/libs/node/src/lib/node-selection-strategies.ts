import { Injectable, Logger } from '@nestjs/common';
import { ChatNode, SelectionCriteria, NodeSelectionResult, NodePerformanceMetrics } from '@saito/models';
import { NodeMetricsService } from '@saito/node-metrics';

/**
 * 节点筛选策略接口
 */
export interface NodeSelectionStrategy {
  selectNodes(
    availableNodes: Array<{ id: string, device_id: string }>,
    criteria: SelectionCriteria
  ): Promise<NodeSelectionResult[]>;
}

/**
 * 性能阈值策略 - 基于硬编码阈值筛选节点
 */
@Injectable()
export class PerformanceThresholdStrategy implements NodeSelectionStrategy {
  private readonly logger = new Logger(PerformanceThresholdStrategy.name);

  constructor(private readonly nodeMetricsService: NodeMetricsService) {}

  async selectNodes(
    availableNodes: Array<{ id: string, device_id: string }>,
    criteria: SelectionCriteria
  ): Promise<NodeSelectionResult[]> {
    this.logger.debug(`Applying threshold strategy with criteria: ${JSON.stringify(criteria)}`);

    const results: NodeSelectionResult[] = [];

    for (const node of availableNodes) {
      try {
        // 获取节点性能指标
        const performanceResponse = await this.nodeMetricsService.getNodePerformance({
          deviceId: node.device_id,
          timeWindow: criteria.timeWindow || '5min'
        });
        const metrics = performanceResponse.windows[criteria.timeWindow || '5min'];

        if (!metrics) {
          // 没有历史数据的节点给予中等评分
          results.push({
            node,
            score: 50,
            reason: 'No historical data available'
          });
          continue;
        }

        // 检查是否满足阈值条件
        let passed = true;
        let reason = 'Meets all threshold requirements';

        if (criteria.minSuccessRate !== undefined && metrics.successRate < criteria.minSuccessRate) {
          passed = false;
          reason = `Success rate ${(metrics.successRate * 100).toFixed(1)}% below minimum ${(criteria.minSuccessRate * 100).toFixed(1)}%`;
        }

        if (criteria.maxResponseTime !== undefined && metrics.avgResponseTime && metrics.avgResponseTime > criteria.maxResponseTime) {
          passed = false;
          reason = `Response time ${metrics.avgResponseTime.toFixed(0)}ms above maximum ${criteria.maxResponseTime}ms`;
        }

        if (criteria.minTokensPerSecond !== undefined && metrics.tokensPerSecond && metrics.tokensPerSecond < criteria.minTokensPerSecond) {
          passed = false;
          reason = `Tokens per second ${metrics.tokensPerSecond.toFixed(2)} below minimum ${criteria.minTokensPerSecond}`;
        }

        if (passed) {
          // 计算基于阈值的评分
          const score = this.calculateThresholdScore(metrics, criteria);
          results.push({
            node,
            score,
            metrics,
            reason
          });
        } else {
          this.logger.debug(`Node ${node.device_id} filtered out: ${reason}`);
        }
      } catch (error) {
        this.logger.warn(`Failed to evaluate node ${node.device_id}: ${error}`);
        // 出错的节点给予低分但不完全排除
        results.push({
          node,
          score: 10,
          reason: 'Error evaluating performance metrics'
        });
      }
    }

    // 按分数排序
    results.sort((a, b) => b.score - a.score);

    // 限制结果数量
    if (criteria.maxResults) {
      return results.slice(0, criteria.maxResults);
    }

    this.logger.debug(`Threshold strategy selected ${results.length} nodes from ${availableNodes.length} candidates`);
    return results;
  }

  private calculateThresholdScore(metrics: NodePerformanceMetrics, criteria: SelectionCriteria): number {
    let score = 100; // 基础分数

    // 根据成功率调整分数
    if (criteria.minSuccessRate !== undefined) {
      const successRateBonus = Math.max(0, (metrics.successRate - criteria.minSuccessRate) * 100);
      score += successRateBonus;
    }

    // 根据响应时间调整分数
    if (criteria.maxResponseTime !== undefined && metrics.avgResponseTime) {
      const responseTimeBonus = Math.max(0, (criteria.maxResponseTime - metrics.avgResponseTime) / criteria.maxResponseTime * 50);
      score += responseTimeBonus;
    }

    // 根据令牌处理速度调整分数
    if (criteria.minTokensPerSecond !== undefined && metrics.tokensPerSecond) {
      const tpsBonus = Math.max(0, (metrics.tokensPerSecond - criteria.minTokensPerSecond) * 10);
      score += tpsBonus;
    }

    return Math.min(200, Math.max(0, score)); // 限制分数范围 0-200
  }
}

/**
 * 相对性能策略 - 基于全网平均水平筛选节点
 */
@Injectable()
export class RelativePerformanceStrategy implements NodeSelectionStrategy {
  private readonly logger = new Logger(RelativePerformanceStrategy.name);

  constructor(private readonly nodeMetricsService: NodeMetricsService) {}

  async selectNodes(
    availableNodes: Array<{ id: string, device_id: string }>,
    criteria: SelectionCriteria
  ): Promise<NodeSelectionResult[]> {
    this.logger.debug(`Applying relative performance strategy with criteria: ${JSON.stringify(criteria)}`);

    try {
      // 获取网络平均性能指标
      const networkResponse = await this.nodeMetricsService.getNetworkPerformance();
      const networkMetrics = networkResponse.averageMetrics[criteria.timeWindow || '5min'];

      if (!networkMetrics) {
        this.logger.warn('No network performance metrics available, falling back to threshold strategy');
        // 如果没有网络指标，回退到基础评分
        return availableNodes.map(node => ({
          node,
          score: 50,
          reason: 'No network metrics available for comparison'
        }));
      }

      const results: NodeSelectionResult[] = [];

      for (const node of availableNodes) {
        try {
          const performanceResponse = await this.nodeMetricsService.getNodePerformance({
            deviceId: node.device_id,
            timeWindow: criteria.timeWindow || '5min'
          });
          const metrics = performanceResponse.windows[criteria.timeWindow || '5min'];

          if (!metrics) {
            results.push({
              node,
              score: 50,
              reason: 'No historical data available'
            });
            continue;
          }

          // 计算相对于网络平均水平的性能
          const relativeScore = this.calculateRelativeScore(metrics, networkMetrics, criteria);
          
          // 检查是否满足相对性能要求
          let passed = true;
          let reason = 'Meets relative performance requirements';

          if (criteria.performancePercentile !== undefined) {
            // 这里需要更复杂的百分位计算，暂时简化处理
            if (relativeScore < criteria.performancePercentile) {
              passed = false;
              reason = `Performance score ${relativeScore.toFixed(1)} below ${criteria.performancePercentile}th percentile`;
            }
          }

          if (criteria.relativeToAverage !== undefined) {
            const avgScore = 50; // 假设平均分数为50
            if (relativeScore < avgScore * criteria.relativeToAverage) {
              passed = false;
              reason = `Performance ${relativeScore.toFixed(1)} below ${criteria.relativeToAverage}x average`;
            }
          }

          if (passed) {
            results.push({
              node,
              score: relativeScore,
              metrics,
              reason
            });
          } else {
            this.logger.debug(`Node ${node.device_id} filtered out: ${reason}`);
          }
        } catch (error) {
          this.logger.warn(`Failed to evaluate node ${node.device_id}: ${error}`);
          results.push({
            node,
            score: 10,
            reason: 'Error evaluating relative performance'
          });
        }
      }

      // 按分数排序
      results.sort((a, b) => b.score - a.score);

      // 限制结果数量
      if (criteria.maxResults) {
        return results.slice(0, criteria.maxResults);
      }

      this.logger.debug(`Relative strategy selected ${results.length} nodes from ${availableNodes.length} candidates`);
      return results;
    } catch (error) {
      this.logger.error(`Failed to apply relative performance strategy: ${error}`);
      // 出错时回退到基础评分
      return availableNodes.map(node => ({
        node,
        score: 50,
        reason: 'Error in relative performance evaluation'
      }));
    }
  }

  private calculateRelativeScore(
    nodeMetrics: NodePerformanceMetrics,
    networkMetrics: { avgSuccessRate?: number; avgResponseTime?: number; avgTokensPerSecond?: number } | null,
    criteria: SelectionCriteria
  ): number {
    let score = 50; // 基础分数

    if (!networkMetrics) {
      return score; // 没有网络指标时返回基础分数
    }

    // 相对成功率评分
    if (networkMetrics.avgSuccessRate && nodeMetrics.successRate) {
      const successRateRatio = nodeMetrics.successRate / networkMetrics.avgSuccessRate;
      score += (successRateRatio - 1) * 50; // 高于平均+分，低于平均-分
    }

    // 相对响应时间评分（响应时间越低越好）
    if (networkMetrics.avgResponseTime && nodeMetrics.avgResponseTime) {
      const responseTimeRatio = networkMetrics.avgResponseTime / nodeMetrics.avgResponseTime;
      score += (responseTimeRatio - 1) * 30; // 比平均快+分，比平均慢-分
    }

    // 相对令牌处理速度评分
    if (networkMetrics.avgTokensPerSecond && nodeMetrics.tokensPerSecond) {
      const tpsRatio = nodeMetrics.tokensPerSecond / networkMetrics.avgTokensPerSecond;
      score += (tpsRatio - 1) * 20; // 高于平均+分，低于平均-分
    }

    return Math.min(100, Math.max(0, score)); // 限制分数范围 0-100
  }
}

/**
 * 综合评分策略 - 综合响应时间、成功率、TPS等指标计算综合评分
 */
@Injectable()
export class CompositeScoreStrategy implements NodeSelectionStrategy {
  private readonly logger = new Logger(CompositeScoreStrategy.name);

  constructor(private readonly nodeMetricsService: NodeMetricsService) {}

  async selectNodes(
    availableNodes: Array<{ id: string, device_id: string }>,
    criteria: SelectionCriteria
  ): Promise<NodeSelectionResult[]> {
    this.logger.debug(`Applying composite score strategy with criteria: ${JSON.stringify(criteria)}`);

    const results: NodeSelectionResult[] = [];
    const weights = criteria.weights || {
      successRate: 0.4,
      responseTime: 0.3,
      tokensPerSecond: 0.3
    };

    // 收集所有节点的指标用于归一化
    const allMetrics: Array<{ node: { id: string, device_id: string }, metrics: NodePerformanceMetrics | null }> = [];

    for (const node of availableNodes) {
      try {
        const performanceResponse = await this.nodeMetricsService.getNodePerformance({
          deviceId: node.device_id,
          timeWindow: criteria.timeWindow || '5min'
        });
        const metrics = performanceResponse.windows[criteria.timeWindow || '5min'] || null;
        allMetrics.push({ node, metrics });
      } catch (error) {
        this.logger.warn(`Failed to get metrics for node ${node.device_id}: ${error}`);
        allMetrics.push({ node, metrics: null });
      }
    }

    // 计算归一化参数
    const normalizationParams = this.calculateNormalizationParams(
      allMetrics.map(item => item.metrics).filter(m => m !== null) as NodePerformanceMetrics[]
    );

    // 为每个节点计算综合评分
    for (const { node, metrics } of allMetrics) {
      try {
        if (!metrics) {
          // 没有历史数据的节点给予中等评分
          results.push({
            node,
            score: 50,
            reason: 'No historical data available'
          });
          continue;
        }

        // 计算综合评分
        const compositeScore = this.calculateCompositeScore(metrics, weights, normalizationParams);

        results.push({
          node,
          score: compositeScore,
          metrics,
          reason: `Composite score based on weighted metrics (SR:${weights.successRate}, RT:${weights.responseTime}, TPS:${weights.tokensPerSecond})`
        });
      } catch (error) {
        this.logger.warn(`Failed to calculate composite score for node ${node.device_id}: ${error}`);
        results.push({
          node,
          score: 10,
          reason: 'Error calculating composite score'
        });
      }
    }

    // 按分数排序
    results.sort((a, b) => b.score - a.score);

    // 限制结果数量
    if (criteria.maxResults) {
      return results.slice(0, criteria.maxResults);
    }

    this.logger.debug(`Composite strategy selected ${results.length} nodes from ${availableNodes.length} candidates`);
    return results;
  }

  private calculateNormalizationParams(allMetrics: NodePerformanceMetrics[]): {
    maxSuccessRate: number;
    minResponseTime: number;
    maxResponseTime: number;
    maxTokensPerSecond: number;
  } {
    if (allMetrics.length === 0) {
      return {
        maxSuccessRate: 1,
        minResponseTime: 0,
        maxResponseTime: 60000, // 60秒
        maxTokensPerSecond: 100
      };
    }

    const successRates = allMetrics.map(m => m.successRate);
    const responseTimes = allMetrics.map(m => m.avgResponseTime || 0).filter(rt => rt > 0);
    const tokenRates = allMetrics.map(m => m.tokensPerSecond || 0).filter(tps => tps > 0);

    return {
      maxSuccessRate: Math.max(...successRates, 1),
      minResponseTime: responseTimes.length > 0 ? Math.min(...responseTimes) : 0,
      maxResponseTime: responseTimes.length > 0 ? Math.max(...responseTimes) : 60000,
      maxTokensPerSecond: tokenRates.length > 0 ? Math.max(...tokenRates) : 100
    };
  }

  private calculateCompositeScore(
    metrics: NodePerformanceMetrics,
    weights: { successRate: number; responseTime: number; tokensPerSecond: number },
    normParams: { maxSuccessRate: number; minResponseTime: number; maxResponseTime: number; maxTokensPerSecond: number }
  ): number {
    // 归一化成功率 (0-100)
    const normalizedSuccessRate = (metrics.successRate / normParams.maxSuccessRate) * 100;

    // 归一化响应时间 (响应时间越低分数越高, 0-100)
    let normalizedResponseTime = 50; // 默认中等分数
    if (metrics.avgResponseTime && normParams.maxResponseTime > normParams.minResponseTime) {
      // 使用反向归一化，响应时间越低分数越高
      const responseTimeRatio = (normParams.maxResponseTime - metrics.avgResponseTime) /
                               (normParams.maxResponseTime - normParams.minResponseTime);
      normalizedResponseTime = Math.max(0, Math.min(100, responseTimeRatio * 100));
    }

    // 归一化令牌处理速度 (0-100)
    let normalizedTokensPerSecond = 50; // 默认中等分数
    if (metrics.tokensPerSecond && normParams.maxTokensPerSecond > 0) {
      normalizedTokensPerSecond = (metrics.tokensPerSecond / normParams.maxTokensPerSecond) * 100;
    }

    // 计算加权综合评分
    const compositeScore =
      normalizedSuccessRate * weights.successRate +
      normalizedResponseTime * weights.responseTime +
      normalizedTokensPerSecond * weights.tokensPerSecond;

    this.logger.debug(`Node ${metrics.deviceId} scores: SR=${normalizedSuccessRate.toFixed(1)}, RT=${normalizedResponseTime.toFixed(1)}, TPS=${normalizedTokensPerSecond.toFixed(1)}, Composite=${compositeScore.toFixed(1)}`);

    return Math.min(100, Math.max(0, compositeScore));
  }
}

/**
 * 策略工厂类 - 根据条件创建相应的筛选策略
 */
@Injectable()
export class NodeSelectionStrategyFactory {
  private readonly logger = new Logger(NodeSelectionStrategyFactory.name);

  constructor(
    private readonly thresholdStrategy: PerformanceThresholdStrategy,
    private readonly relativeStrategy: RelativePerformanceStrategy,
    private readonly compositeStrategy: CompositeScoreStrategy
  ) {}

  createStrategy(criteria: SelectionCriteria): NodeSelectionStrategy {
    switch (criteria.strategy) {
      case 'threshold':
        this.logger.debug('Using threshold strategy');
        return this.thresholdStrategy;
      case 'relative':
        this.logger.debug('Using relative performance strategy');
        return this.relativeStrategy;
      case 'composite':
      default:
        this.logger.debug('Using composite score strategy');
        return this.compositeStrategy;
    }
  }
}
