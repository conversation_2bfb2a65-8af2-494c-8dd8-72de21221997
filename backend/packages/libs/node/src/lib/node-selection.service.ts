import { Injectable, Inject, HttpException, HttpStatus, Logger } from '@nestjs/common';
import { NodeRepository } from "./node.repository";
import { ChatNode, SelectionCriteria, NodeSelectionResult } from '@saito/models';
import { NodeMetricsService } from '@saito/node-metrics';
import { NodeSelectionStrategyFactory } from './node-selection-strategies';
import * as R from 'ramda';

/**
 * 节点选择服务 - 处理节点选择和负载均衡
 */
@Injectable()
export class NodeSelectionService {
  private readonly logger = new Logger(NodeSelectionService.name);

  constructor(
    @Inject(NodeRepository)
    private readonly nodeRepository: NodeRepository,
    @Inject('TunnelService')
    private readonly tunnelService: any,
    private readonly nodeMetricsService: NodeMetricsService,
    private readonly strategyFactory: NodeSelectionStrategyFactory
  ) {}

  /**
   * Find idle nodes
   * @param model Optional model name to filter nodes by supported models
   */
  async findIdleNodes(model?: string): Promise<Array<{device_id: string, id: string}>> {
    try {
      const result = await this.nodeRepository.findIdleNodes(model);
      return [...result];
    } catch (error) {
      this.logger.error(`Failed to find idle nodes: ${error}`);
      throw new HttpException('Failed to find idle nodes', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * Find nodes that are not busy with tasks
   * @param model Optional model name to filter nodes by supported models
   * @returns Array of nodes that are not busy, or if none found, any online nodes that support the model
   */
  async findAvailableNodes(model?: string): Promise<Array<{device_id: string, id: string}>> {
    try {
      const result = await this.nodeRepository.findAvailableNodes(model);
      return [...result];
    } catch (error) {
      this.logger.error(`Failed to find available nodes: ${error}`);
      throw new HttpException('Failed to find available nodes', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * Find nodes by status
   * @param status Status to filter by
   * @param model Optional model name to filter nodes by supported models
   * @returns Array of nodes with the specified status
   */
  async findNodesByStatus(status: string, model?: string): Promise<Array<{device_id: string, id: string}>> {
    try {
      // For now, if status is 'connected', use findIdleNodes
      if (status === 'connected') {
        const nodes = await this.nodeRepository.findIdleNodes(model);

        // 如果指定了模型但没有找到支持该模型的节点，记录更详细的日志
        if (model && nodes.length === 0) {
          // 获取所有在线节点，看看有哪些节点是在线的
          const allConnectedNodes = await this.nodeRepository.findIdleNodes();
          if (allConnectedNodes.length > 0) {
            // 获取所有可用的模型列表
            try {
              const availableModels = await this.nodeRepository.getModels();
              if (availableModels.length > 0) {
                this.logger.log(`Available models: ${availableModels.map(m => m.model_name).join(', ')}`);
              } else {
                this.logger.log('No available models found');
              }
            } catch (modelError) {
              this.logger.error(`Failed to get available models: ${modelError}`);
            }
          }
        }

        return [...nodes];
      }

      // Otherwise return empty array
      return [];
    } catch (error) {
      this.logger.error(`Failed to find nodes by status: ${error}`);
      throw new HttpException('Failed to find nodes by status', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * Get target node for task distribution
   * Implements a node selection algorithm that:
   * 1. Retrieves all online nodes
   * 2. Filters nodes to only include those that support the requested model (if specified)
   * 3. Identifies nodes without running tasks and randomly selects one
   * 4. If all nodes have running tasks, randomly selects any online node that supports the model
   *
   * @param model Optional model name to filter nodes by supported models
   * @returns Target node or error object
   */
  public async getTargetNode(model?: string): Promise<ChatNode | { error: { message: string; type: string; param: string | null; code: string | null; }; status: number; }> {
    try {
      // Get available nodes using the repository method that already implements our selection strategy
      const availableNodes = await this.nodeRepository.findAvailableNodes(model);

      // Check if we found any nodes
      if (R.isEmpty(availableNodes)) {
        // Return appropriate error based on whether a model was specified
        if (model) {
          // Use OpenAI format error response
          return {
            error: {
              message: `The requested model '${model}' does not exist.`,
              type: "invalid_request_error",
              param: "model",
              code: "model_not_found"
            },
            status: HttpStatus.NOT_FOUND
          };
        } else {
          return {
            error: {
              message: "No available connected nodes. Please try again later.",
              type: "server_error",
              param: null,
              code: "no_nodes_available"
            },
            status: HttpStatus.SERVICE_UNAVAILABLE
          };
        }
      }

      // Filter nodes to only include those connected in the tunnel
      this.logger.log(`Checking tunnel connection status for ${availableNodes.length} available nodes`);
      const tunnelConnectedNodes = await Promise.all(
        availableNodes.map(async (node: { id: string, device_id: string }) => {
          this.logger.debug(`Checking tunnel connection for node ${node.id} (device_id: ${node.device_id})`);
          const isConnectedInTunnel = await this.tunnelService.isDeviceConnected(node.id);
          this.logger.debug(`Node ${node.id} tunnel connection result: ${isConnectedInTunnel}`);
          if (isConnectedInTunnel) {
            this.logger.log(`Node ${node.id} is connected in tunnel`);
            return node;
          } else {
            this.logger.log(`Node ${node.id} is NOT connected in tunnel (database shows connected but WebSocket is disconnected)`);
          }
          return null;
        })
      );

      // Filter out null values
      const filteredNodes = tunnelConnectedNodes.filter((node: { id: string, device_id: string } | null) => node !== null) as Array<{ id: string, device_id: string }>;

      // If no nodes are connected in the tunnel, return error
      if (R.isEmpty(filteredNodes)) {
        const nodeInfo = availableNodes.map(n => `${n.id} (${n.device_id})`).join(', ');
        this.logger.warn(`No nodes connected in tunnel. Available nodes in database: ${nodeInfo}`);

        return {
          error: {
            message: `No devices are currently connected via WebSocket. Found ${availableNodes.length} device(s) in database but none are actively connected. Please ensure your devices are running and connected to the network.`,
            type: "server_error",
            param: null,
            code: "no_nodes_available_in_tunnel"
          },
          status: HttpStatus.SERVICE_UNAVAILABLE
        };
      }

      // Use strategy-based selection instead of random selection
      const selectedNode = await this.getBestNode(filteredNodes, {
        model,
        strategy: 'composite',
        timeWindow: '5min'
      });

      this.logger.log(`Selected node ${selectedNode.id} for task distribution${model ? ` with model ${model}` : ''} using strategy-based selection`);

      return selectedNode;
    } catch (error) {
      // Provide detailed error information
      if (error instanceof HttpException) {
        const response = error.getResponse();
        if (typeof response === 'object' && response !== null && 'error' in response) {
          const errorObj = response as { error: { message: string, type: string, param: string, code: string } };
          this.logger.error(`Failed to get target node: ${errorObj.error.message}`, error);
        } else {
          this.logger.error(`Failed to get target node: ${error.message}`, error);
        }
      } else {
        this.logger.error(`Failed to get target node: ${error instanceof Error ? error.message : String(error)}`, error);
      }
      throw error;
    }
  }

  /**
   * 使用策略模式选择节点
   * 根据筛选条件选择合适的策略进行节点筛选和排序
   */
  async selectNodesWithStrategy(
    availableNodes: Array<{ id: string, device_id: string }>,
    criteria: SelectionCriteria
  ): Promise<NodeSelectionResult[]> {
    try {
      this.logger.debug(`Selecting nodes with strategy: ${criteria.strategy}, criteria: ${JSON.stringify(criteria)}`);

      // 获取策略实例
      const strategy = this.strategyFactory.createStrategy(criteria);

      // 使用策略进行节点选择
      const results = await strategy.selectNodes(availableNodes, criteria);

      this.logger.log(`Strategy ${criteria.strategy} selected ${results.length} nodes from ${availableNodes.length} candidates`);

      return results;
    } catch (error) {
      this.logger.error(`Failed to select nodes with strategy: ${error}`);

      // 出错时回退到基础选择
      return availableNodes.map(node => ({
        node,
        score: 50,
        reason: 'Fallback due to strategy error'
      }));
    }
  }

  /**
   * 获取最佳节点（使用策略模式的简化接口）
   * 返回单个最佳节点，兼容现有接口
   */
  async getBestNode(
    availableNodes: Array<{ id: string, device_id: string }>,
    criteria?: Partial<SelectionCriteria>
  ): Promise<ChatNode> {
    const fullCriteria: SelectionCriteria = {
      strategy: 'composite',
      timeWindow: '5min',
      maxResults: 1,
      ...criteria
    };

    const results = await this.selectNodesWithStrategy(availableNodes, fullCriteria);

    if (results.length === 0) {
      throw new Error('No suitable nodes found');
    }

    return results[0].node as ChatNode;
  }

  /**
   * 基于性能指标选择最佳节点
   * 使用加权评分算法，考虑成功率、响应时间和令牌处理速度
   * @deprecated 使用 getBestNode 或 selectNodesWithStrategy 替代
   */
  private async selectBestPerformingNode(
    availableNodes: Array<{ id: string, device_id: string }>,
    model?: string
  ): Promise<ChatNode> {
    try {
      // 获取所有节点的性能评分
      const nodeScores = await Promise.all(
        availableNodes.map(async (node) => {
          try {
            const score = await this.nodeMetricsService.calculatePerformanceScore(node.device_id, '5min');
            return {
              node,
              score: score || 0 // 如果没有历史数据，使用默认分数 0
            };
          } catch (error) {
            this.logger.warn(`Failed to get performance score for node ${node.device_id}: ${error}`);
            return {
              node,
              score: 0 // 出错时使用默认分数
            };
          }
        })
      );

      // 按性能评分排序（降序）
      nodeScores.sort((a, b) => b.score - a.score);

      // 记录选择过程
      this.logger.debug(`Node performance scores: ${nodeScores.map(ns => `${ns.node.device_id}:${ns.score.toFixed(2)}`).join(', ')}`);

      // 使用加权随机选择，给高分节点更高的选择概率
      const selectedNodeInfo = this.weightedRandomSelection(nodeScores);

      this.logger.log(`Selected node ${selectedNodeInfo.node.device_id} with performance score ${selectedNodeInfo.score.toFixed(2)}`);

      return selectedNodeInfo.node as ChatNode;
    } catch (error) {
      this.logger.error(`Failed to select node based on performance, falling back to random selection: ${error}`);

      // 如果性能选择失败，回退到随机选择
      const randomIndex = Math.floor(Math.random() * availableNodes.length);
      return availableNodes[randomIndex] as ChatNode;
    }
  }

  /**
   * 加权随机选择算法
   * 给性能更好的节点更高的选择概率
   */
  private weightedRandomSelection(nodeScores: Array<{ node: { id: string, device_id: string }, score: number }>): { node: { id: string, device_id: string }, score: number } {
    // 如果所有节点分数都是 0 或负数，使用均等概率
    const maxScore = Math.max(...nodeScores.map(ns => ns.score));
    if (maxScore <= 0) {
      const randomIndex = Math.floor(Math.random() * nodeScores.length);
      return nodeScores[randomIndex];
    }

    // 计算权重（分数越高权重越大）
    const weights = nodeScores.map(ns => Math.max(0.1, ns.score)); // 最小权重 0.1，避免完全排除低分节点
    const totalWeight = weights.reduce((sum, weight) => sum + weight, 0);

    // 生成随机数进行选择
    const random = Math.random() * totalWeight;
    let currentWeight = 0;

    for (let i = 0; i < nodeScores.length; i++) {
      currentWeight += weights[i];
      if (random <= currentWeight) {
        return nodeScores[i];
      }
    }

    // 兜底返回最后一个节点
    return nodeScores[nodeScores.length - 1];
  }
}
