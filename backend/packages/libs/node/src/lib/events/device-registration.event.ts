import { RawDidDocument } from '@saito/models';

/**
 * 设备注册事件
 * 当设备成功注册时发布此事件，DID 模块可以监听此事件来处理 DID 文档存储
 */
export class DeviceRegistrationEvent {
  constructor(
    public readonly deviceId: string,
    public readonly didDocument: RawDidDocument | null,
    public readonly registrationData: {
      code: string;
      gatewayAddress: string;
      rewardAddress: string;
      deviceType: string | null;
      ipAddress: string | null;
    }
  ) {}
}

/**
 * 设备注册事件类型常量
 */
export const DEVICE_REGISTRATION_EVENT = 'device.registration';
