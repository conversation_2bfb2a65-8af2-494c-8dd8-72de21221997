import { Injectable, Inject, HttpException, HttpStatus, Logger } from '@nestjs/common';
import { NodeRepository } from "./node.repository";
import {
  Device,
  DeviceListRequest,
  DeviceListResponse,
  DeviceStatusChange,
  Task,
  DeviceRegisterRequest,
  DeviceRegisterResponse,
  DeviceModel
} from '@saito/models';

/**
 * 设备服务 - 处理设备相关操作
 */
@Injectable()
export class DeviceService {
  private readonly logger = new Logger(DeviceService.name);

  constructor(
    @Inject(NodeRepository)
    private readonly nodeRepository: NodeRepository
  ) {}

  /**
   * Create a user if it doesn't exist
   * @param userId User ID to create
   * @param walletAddress Wallet address of the user
   * @returns True if user was created or already exists, false if creation failed
   */
  async createUserIfNotExists(userId: string, walletAddress: string): Promise<boolean> {
    try {
      return await this.nodeRepository.createUserIfNotExists(userId, walletAddress);
    } catch (error) {
      this.logger.error(`Error creating user: ${error}`);
      return false;
    }
  }

  /**
   * Get devices for a user
   */
  async getDevices(
    request: DeviceListRequest,
    userId: string
  ): Promise<DeviceListResponse> {
    try {
      const result = await this.nodeRepository.getDevices(
        userId,
        request.page,
        request.pageSize,
        request.status,
        request.search
      );

      return {
        data: [...result.data],
        total: result.total,
        page: result.page,
        pageSize: result.pageSize
      };
    } catch (error) {
      this.logger.error(`Failed to get devices: ${error}`);
      throw new HttpException('Failed to get devices', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * Get device by ID
   */
  async getDevice(
    deviceId: string,
    userId: string
  ): Promise<Device | null> {
    try {
      return await this.nodeRepository.getDevice(deviceId, userId);
    } catch (error) {
      this.logger.error(`Failed to get device: ${error}`);
      throw new HttpException('Failed to get device', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * Update device status
   */
  async updateDeviceStatus(deviceId: string, status: string): Promise<any> {
    try {
      return await this.nodeRepository.updateDeviceStatus(deviceId, status as any);
    } catch (error) {
      this.logger.error(`Failed to update device status: ${error}`);
      throw new HttpException('Failed to update device status', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * Get device status history
   */
  async getDeviceStatusHistory(deviceId: string, userId: string, days: number): Promise<DeviceStatusChange[]> {
    try {
      const result = await this.nodeRepository.getDeviceStatusHistory(deviceId, userId, days);
      return [...result] as DeviceStatusChange[];
    } catch (error) {
      this.logger.error(`Failed to get device status history: ${error}`);
      throw new HttpException('Failed to get device status history', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * Get device tasks
   */
  async getDeviceTasks(deviceId: string, userId: string, request: DeviceListRequest): Promise<{data: Task[], total: number, page: number, pageSize: number}> {
    try {
      const result = await this.nodeRepository.getDeviceTasks(
        deviceId,
        userId,
        request.page,
        request.pageSize,
        request.status
      );

      return {
        data: [...result.data] as Task[],
        total: result.total,
        page: result.page,
        pageSize: result.pageSize
      };
    } catch (error) {
      this.logger.error(`Failed to get device tasks: ${error}`);
      throw new HttpException('Failed to get device tasks', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * Register device
   */
  async registerDevice(request: DeviceRegisterRequest): Promise<DeviceRegisterResponse> {
    try {
      // Get device info before registration
      const deviceBeforeReg = await this.nodeRepository.getDeviceByCode(request.code);
      // Log the device's current status before registration
      if (deviceBeforeReg) {
        this.logger.log(`Device status before registration: ${deviceBeforeReg.status}`);
      }

      // Check if local_models is provided in the request
      const localModels = (request as any).local_models || null;
      if (localModels) {
        this.logger.log(`Local models provided: ${JSON.stringify(localModels)}`);
      }

      const result = await this.nodeRepository.registerDevice(
        request.code,
        request.device_id, // 使用设备携带的DID
        request.gateway_address,
        request.reward_address,
        request.device_type || null,
        request.gpu_type || null,
        request.ip || null,
        localModels,
        request.did_document // 传递DID Document
      );

      // Create response
      const response: DeviceRegisterResponse = {
        device_id: result.device_id,
        status: result.status as 'connected',
        device_type: result.device_type || undefined,
        reward_address: result.reward_address
      };

      // Add local_models to response if provided
      if (localModels) {
        (response as any).local_models = localModels;
      }

      return response;
    } catch (error) {
      this.logger.error(`Failed to register device: ${error}`);
      throw new HttpException('Failed to register device', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * Get device models
   */
  async getDeviceModels(deviceId: string, userId: string): Promise<DeviceModel[]> {
    try {
      // First verify that the device belongs to the user
      const device = await this.nodeRepository.getDevice(deviceId, userId);
      if (!device) {
        throw new HttpException('Device not found or does not belong to user', HttpStatus.NOT_FOUND);
      }

      // Get the device models
      const models = await this.nodeRepository.getDeviceModels(deviceId);
      return [...models]; // Convert readonly array to mutable array
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }
      this.logger.error(`Failed to get device models: ${error}`);
      throw new HttpException('Failed to get device models', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * Get device DID Document
   */
  async getDeviceDidDocument(deviceId: string): Promise<any | null> {
    try {
      return await this.nodeRepository.getDeviceDidDocument(deviceId);
    } catch (error) {
      this.logger.error(`Failed to get device DID Document: ${error}`);
      throw new HttpException('Failed to get device DID Document', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }
}
