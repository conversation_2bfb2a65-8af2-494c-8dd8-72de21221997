import { Injectable, Logger } from '@nestjs/common';
import { 
  DeviceStatusChangeEvent, 
  DeviceStatusEventListener, 
  DeviceStatusEventManager 
} from './device-status-event.interface';

/**
 * 设备状态事件管理器实现
 * 处理设备状态变更事件的发布和订阅
 */
@Injectable()
export class DeviceStatusEventService implements DeviceStatusEventManager {
  private readonly logger = new Logger(DeviceStatusEventService.name);
  private listeners: DeviceStatusEventListener[] = [];

  /**
   * 注册状态变更监听器
   */
  addListener(listener: DeviceStatusEventListener): void {
    this.listeners.push(listener);
    this.logger.debug(`Added device status event listener. Total listeners: ${this.listeners.length}`);
  }

  /**
   * 移除状态变更监听器
   */
  removeListener(listener: DeviceStatusEventListener): void {
    const index = this.listeners.indexOf(listener);
    if (index > -1) {
      this.listeners.splice(index, 1);
      this.logger.debug(`Removed device status event listener. Total listeners: ${this.listeners.length}`);
    }
  }

  /**
   * 发布设备状态变更事件
   */
  async publishStatusChange(event: DeviceStatusChangeEvent): Promise<void> {
    this.logger.log(`Publishing device status change event: ${event.deviceId} ${event.fromStatus} -> ${event.toStatus} (${event.reason || 'unknown'})`);
    
    // 并行通知所有监听器
    const promises = this.listeners.map(async (listener) => {
      try {
        await listener.onDeviceStatusChanged(event);
      } catch (error) {
        this.logger.error(`Error in device status event listener: ${error}`);
        // 不抛出错误，避免影响其他监听器
      }
    });

    await Promise.allSettled(promises);
    this.logger.debug(`Notified ${this.listeners.length} listeners about device status change for ${event.deviceId}`);
  }
}

export const DeviceStatusEventProvider = {
  provide: 'DeviceStatusEventManager',
  useClass: DeviceStatusEventService,
};
