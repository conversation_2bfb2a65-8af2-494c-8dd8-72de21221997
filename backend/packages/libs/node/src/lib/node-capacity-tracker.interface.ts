import { 
  NodeCapacityInfo, 
  TaskAllocationRequest, 
  TaskAllocationResult 
} from '@saito/models';

/**
 * 节点容量追踪器接口
 * 定义节点容量管理的核心方法
 */
export interface NodeCapacityTracker {
  /**
   * 分配任务给节点
   * @param request 任务分配请求
   * @returns 分配结果
   */
  allocateTask(request: TaskAllocationRequest): Promise<TaskAllocationResult>;

  /**
   * 释放节点任务
   * @param deviceId 设备ID
   * @param taskId 任务ID
   */
  releaseTask(deviceId: string, taskId: string): Promise<void>;

  /**
   * 获取节点容量状态
   * @param deviceId 设备ID
   * @returns 节点容量信息
   */
  getNodeCapacity(deviceId: string): Promise<NodeCapacityInfo>;

  /**
   * 获取空闲节点列表
   * @param modelFilter 可选的模型过滤条件
   * @returns 空闲节点设备ID列表
   */
  getIdleNodes(modelFilter?: string): Promise<string[]>;

  /**
   * 设置节点为离线状态
   * @param deviceId 设备ID
   */
  setNodeOffline(deviceId: string): Promise<void>;

  /**
   * 设置节点为在线状态
   * @param deviceId 设备ID
   * @param maxConcurrentTasks 最大并发任务数
   */
  setNodeOnline(deviceId: string, maxConcurrentTasks?: number): Promise<void>;
}

/**
 * 节点容量事件类型
 */
export interface NodeCapacityEvents {
  'task.allocated': {
    deviceId: string;
    taskId: string;
    timestamp: Date;
    priority?: number;
  };

  'task.released': {
    deviceId: string;
    taskId: string;
    timestamp: Date;
    remainingTasks: number;
  };

  'node.online': {
    deviceId: string;
    maxConcurrentTasks: number;
    timestamp: Date;
  };

  'node.offline': {
    deviceId: string;
    timestamp: Date;
  };

  'node.overloaded': {
    deviceId: string;
    currentTasks: number;
    maxConcurrentTasks: number;
    timestamp: Date;
  };
}

/**
 * 容量追踪器配置
 */
export interface NodeCapacityTrackerConfig {
  /**
   * 任务超时时间（毫秒）
   */
  taskTimeoutMs: number;

  /**
   * 清理间隔时间（毫秒）
   */
  cleanupIntervalMs: number;

  /**
   * 默认最大并发任务数
   */
  defaultMaxConcurrentTasks: number;

  /**
   * Redis缓存过期时间（秒）
   */
  cacheExpirationSeconds: number;
}

/**
 * 批量容量查询请求
 */
export interface BatchCapacityQuery {
  deviceIds: string[];
  includeOffline?: boolean;
}

/**
 * 批量容量查询结果
 */
export interface BatchCapacityResult {
  capacities: Record<string, NodeCapacityInfo>;
  errors: Record<string, string>;
}
