import { Module, Global } from '@nestjs/common';
import { EventEmitterModule } from '@nestjs/event-emitter';
import { NodeServiceProvider } from "./node.service";
import { NodeRepository } from "./node.repository";
import { PersistentModule } from '@saito/persistent';
import { RedisModule } from '@saito/redis';
import { NodeMetricsModule } from '@saito/node-metrics';
import { DeviceService } from './device.service';
import { ConnectTaskService } from './connect-task.service';
import { DeviceHeartbeatService } from './device-heartbeat.service';
import { DeviceModelService } from './device-model.service';
import { NodeSelectionService } from './node-selection.service';
import { DeviceStatusEventProvider } from './device-status-event.service';
import {
  PerformanceThresholdStrategy,
  RelativePerformanceStrategy,
  CompositeScoreStrategy,
  NodeSelectionStrategyFactory
} from './node-selection-strategies';
import { NodeCapacityTrackerService } from './node-capacity-tracker.service';

@Global()
@Module({
  imports: [
    PersistentModule,
    RedisModule,
    NodeMetricsModule,
    EventEmitterModule.forRoot(),
  ],
  providers: [
    NodeServiceProvider,
    NodeRepository,
    {
      provide: 'NodeRepository',
      useExisting: NodeRepository
    },
    DeviceService,
    ConnectTaskService,
    DeviceHeartbeatService,
    DeviceModelService,
    NodeSelectionService,
    DeviceStatusEventProvider,
    // 节点选择策略
    PerformanceThresholdStrategy,
    RelativePerformanceStrategy,
    CompositeScoreStrategy,
    NodeSelectionStrategyFactory,
    // 节点容量追踪器
    NodeCapacityTrackerService
  ],
  exports: [
    NodeServiceProvider,
    NodeRepository,
    NodeSelectionService,
    NodeCapacityTrackerService
  ]
})
export class NodeModule {}
