import { Injectable, Logger } from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';
import {
  NodeCapacityInfo,
  NodeCapacityStatus,
  TaskAllocationRequest,
  TaskAllocationResult
} from '@saito/models';
import { RedisService } from '@saito/redis';
import {
  NodeCapacityTracker,
  NodeCapacityTrackerConfig,
  BatchCapacityQuery,
  BatchCapacityResult
} from './node-capacity-tracker.interface';
import { NodeService } from './node.interface';

/**
 * 节点容量状态追踪器
 * 负责跟踪节点的任务分配状态，避免节点过载
 */
@Injectable()
export class NodeCapacityTrackerService implements NodeCapacityTracker {
  private readonly logger = new Logger(NodeCapacityTrackerService.name);
  private readonly CAPACITY_KEY_PREFIX = 'node_capacity:';
  private readonly config: NodeCapacityTrackerConfig;

  constructor(
    private readonly redisService: RedisService,
    private readonly eventEmitter: EventEmitter2,
    private readonly nodeService: NodeService
  ) {
    // 默认配置
    this.config = {
      taskTimeoutMs: 60000, // 60秒任务超时
      cleanupIntervalMs: 30000, // 30秒清理间隔
      defaultMaxConcurrentTasks: 1,
      cacheExpirationSeconds: 3600 // 1小时缓存过期
    };

    // 启动定期清理超时任务的定时器
    this.startTimeoutCleanup();
  }

  /**
   * 分配任务给节点
   */
  async allocateTask(request: TaskAllocationRequest): Promise<TaskAllocationResult> {
    const { deviceId, taskId, estimatedDuration, priority } = request;
    
    try {
      this.logger.debug(`Attempting to allocate task ${taskId} to device ${deviceId}`);

      // 获取节点当前容量状态
      const capacityInfo = await this.getNodeCapacity(deviceId);
      
      // 检查节点是否可以接受新任务
      if (!this.canAcceptTask(capacityInfo)) {
        const reason = this.getUnavailableReason(capacityInfo);
        this.logger.debug(`Cannot allocate task ${taskId} to device ${deviceId}: ${reason}`);
        
        return {
          success: false,
          deviceId,
          taskId,
          reason
        };
      }

      // 执行任务分配
      const success = await this.performTaskAllocation(deviceId, taskId, estimatedDuration);
      
      if (success) {
        this.logger.log(`Successfully allocated task ${taskId} to device ${deviceId}`);
        
        // 发送任务分配事件
        this.eventEmitter.emit('task.allocated', {
          deviceId,
          taskId,
          timestamp: new Date(),
          priority
        });

        return {
          success: true,
          deviceId,
          taskId,
          estimatedStartTime: new Date()
        };
      } else {
        return {
          success: false,
          deviceId,
          taskId,
          reason: 'Failed to update node capacity state'
        };
      }
    } catch (error) {
      this.logger.error(`Error allocating task ${taskId} to device ${deviceId}: ${error}`);
      return {
        success: false,
        deviceId,
        taskId,
        reason: `Allocation error: ${error instanceof Error ? error.message : String(error)}`
      };
    }
  }

  /**
   * 释放节点任务
   */
  async releaseTask(deviceId: string, taskId: string): Promise<void> {
    try {
      this.logger.debug(`Releasing task ${taskId} from device ${deviceId}`);

      const capacityKey = this.getCapacityKey(deviceId);
      const capacityInfo = await this.getNodeCapacity(deviceId);

      if (capacityInfo.activeTasks.includes(taskId)) {
        // 从活跃任务列表中移除
        const updatedTasks = capacityInfo.activeTasks.filter(id => id !== taskId);
        const updatedInfo: NodeCapacityInfo = {
          ...capacityInfo,
          activeTasks: updatedTasks,
          currentTasks: updatedTasks.length,
          status: updatedTasks.length === 0 ? 'idle' : 
                  updatedTasks.length >= capacityInfo.maxConcurrentTasks ? 'busy' : 'idle'
        };

        await this.redisService.set(capacityKey, JSON.stringify(updatedInfo), 3600);
        
        this.logger.log(`Released task ${taskId} from device ${deviceId}, current tasks: ${updatedInfo.currentTasks}`);
        
        // 发送任务释放事件
        this.eventEmitter.emit('task.released', {
          deviceId,
          taskId,
          timestamp: new Date(),
          remainingTasks: updatedInfo.currentTasks
        });
      } else {
        this.logger.warn(`Task ${taskId} not found in active tasks for device ${deviceId}`);
      }
    } catch (error) {
      this.logger.error(`Error releasing task ${taskId} from device ${deviceId}: ${error}`);
    }
  }

  /**
   * 获取节点容量状态
   */
  async getNodeCapacity(deviceId: string): Promise<NodeCapacityInfo> {
    try {
      const capacityKey = this.getCapacityKey(deviceId);
      const capacityData = await this.redisService.get(capacityKey);

      if (capacityData) {
        const parsed = JSON.parse(capacityData);
        return {
          deviceId,
          status: parsed.status || 'idle',
          currentTasks: parsed.currentTasks || 0,
          maxConcurrentTasks: parsed.maxConcurrentTasks || 1,
          lastTaskStartTime: parsed.lastTaskStartTime ? new Date(parsed.lastTaskStartTime) : undefined,
          estimatedAvailableTime: parsed.estimatedAvailableTime ? new Date(parsed.estimatedAvailableTime) : undefined,
          activeTasks: parsed.activeTasks || []
        };
      } else {
        // 返回默认容量信息
        const defaultInfo: NodeCapacityInfo = {
          deviceId,
          status: 'idle',
          currentTasks: 0,
          maxConcurrentTasks: 1,
          activeTasks: []
        };
        
        // 缓存默认信息
        await this.redisService.set(capacityKey, JSON.stringify(defaultInfo), 3600);
        return defaultInfo;
      }
    } catch (error) {
      this.logger.error(`Error getting node capacity for device ${deviceId}: ${error}`);
      // 返回安全的默认值
      return {
        deviceId,
        status: 'offline',
        currentTasks: 0,
        maxConcurrentTasks: 1,
        activeTasks: []
      };
    }
  }

  /**
   * 获取空闲节点列表
   */
  async getIdleNodes(modelFilter?: string): Promise<string[]> {
    try {
      // 这里需要与节点服务集成，获取所有在线节点
      // 暂时返回空数组，后续需要实现
      const allNodes = await this.getAllOnlineNodes(modelFilter);
      const idleNodes: string[] = [];

      for (const deviceId of allNodes) {
        const capacity = await this.getNodeCapacity(deviceId);
        if (capacity.status === 'idle') {
          idleNodes.push(deviceId);
        }
      }

      this.logger.debug(`Found ${idleNodes.length} idle nodes${modelFilter ? ` for model ${modelFilter}` : ''}`);
      return idleNodes;
    } catch (error) {
      this.logger.error(`Error getting idle nodes: ${error}`);
      return [];
    }
  }

  /**
   * 设置节点为离线状态
   */
  async setNodeOffline(deviceId: string): Promise<void> {
    try {
      const capacityInfo = await this.getNodeCapacity(deviceId);
      const updatedInfo: NodeCapacityInfo = {
        ...capacityInfo,
        status: 'offline',
        currentTasks: 0,
        activeTasks: []
      };

      const capacityKey = this.getCapacityKey(deviceId);
      await this.redisService.set(capacityKey, JSON.stringify(updatedInfo), 3600);
      
      this.logger.log(`Set device ${deviceId} to offline status`);
      
      // 发送节点离线事件
      this.eventEmitter.emit('node.offline', {
        deviceId,
        timestamp: new Date()
      });
    } catch (error) {
      this.logger.error(`Error setting device ${deviceId} offline: ${error}`);
    }
  }

  /**
   * 设置节点为在线状态
   */
  async setNodeOnline(deviceId: string, maxConcurrentTasks: number = 1): Promise<void> {
    try {
      const capacityInfo = await this.getNodeCapacity(deviceId);
      const updatedInfo: NodeCapacityInfo = {
        ...capacityInfo,
        status: capacityInfo.currentTasks === 0 ? 'idle' : 'busy',
        maxConcurrentTasks
      };

      const capacityKey = this.getCapacityKey(deviceId);
      await this.redisService.set(capacityKey, JSON.stringify(updatedInfo), 3600);
      
      this.logger.log(`Set device ${deviceId} to online status with max concurrent tasks: ${maxConcurrentTasks}`);
      
      // 发送节点上线事件
      this.eventEmitter.emit('node.online', {
        deviceId,
        maxConcurrentTasks,
        timestamp: new Date()
      });
    } catch (error) {
      this.logger.error(`Error setting device ${deviceId} online: ${error}`);
    }
  }

  /**
   * 检查节点是否可以接受新任务
   */
  private canAcceptTask(capacityInfo: NodeCapacityInfo): boolean {
    if (capacityInfo.status === 'offline') {
      return false;
    }
    
    if (capacityInfo.status === 'overloaded') {
      return false;
    }

    return capacityInfo.currentTasks < capacityInfo.maxConcurrentTasks;
  }

  /**
   * 获取节点不可用的原因
   */
  private getUnavailableReason(capacityInfo: NodeCapacityInfo): string {
    switch (capacityInfo.status) {
      case 'offline':
        return 'Node is offline';
      case 'overloaded':
        return 'Node is overloaded';
      case 'busy':
        return `Node is at capacity (${capacityInfo.currentTasks}/${capacityInfo.maxConcurrentTasks})`;
      default:
        return 'Node is unavailable';
    }
  }

  /**
   * 执行任务分配
   */
  private async performTaskAllocation(
    deviceId: string, 
    taskId: string, 
    estimatedDuration?: number
  ): Promise<boolean> {
    try {
      const capacityKey = this.getCapacityKey(deviceId);
      const capacityInfo = await this.getNodeCapacity(deviceId);

      const updatedTasks = [...capacityInfo.activeTasks, taskId];
      const now = new Date();
      const estimatedAvailableTime = estimatedDuration ? 
        new Date(now.getTime() + estimatedDuration) : 
        new Date(now.getTime() + this.config.taskTimeoutMs);

      const updatedInfo: NodeCapacityInfo = {
        ...capacityInfo,
        activeTasks: updatedTasks,
        currentTasks: updatedTasks.length,
        status: updatedTasks.length >= capacityInfo.maxConcurrentTasks ? 'busy' : 'idle',
        lastTaskStartTime: now,
        estimatedAvailableTime
      };

      await this.redisService.set(capacityKey, JSON.stringify(updatedInfo), 3600);
      return true;
    } catch (error) {
      this.logger.error(`Error performing task allocation: ${error}`);
      return false;
    }
  }

  /**
   * 获取容量缓存键
   */
  private getCapacityKey(deviceId: string): string {
    return `${this.CAPACITY_KEY_PREFIX}${deviceId}`;
  }

  /**
   * 获取所有在线节点（与节点服务集成）
   */
  private async getAllOnlineNodes(modelFilter?: string): Promise<string[]> {
    try {
      // 使用 NodeService 获取可用节点
      const availableNodes = await this.nodeService.findAvailableNodes(modelFilter);
      const deviceIds = availableNodes.map((node: { device_id: string }) => node.device_id);

      this.logger.debug(`Found ${deviceIds.length} online nodes${modelFilter ? ` for model ${modelFilter}` : ''}: ${deviceIds.join(', ')}`);
      return deviceIds;
    } catch (error) {
      this.logger.error(`Error getting online nodes: ${error}`);
      return [];
    }
  }

  /**
   * 启动超时任务清理定时器
   */
  private startTimeoutCleanup(): void {
    setInterval(async () => {
      await this.cleanupTimeoutTasks();
    }, 30000); // 每30秒检查一次
  }

  /**
   * 清理超时任务
   */
  private async cleanupTimeoutTasks(): Promise<void> {
    try {
      this.logger.debug('Cleaning up timeout tasks...');

      // 获取所有容量键
      const pattern = `${this.CAPACITY_KEY_PREFIX}*`;
      const keys = await this.redisService.keys(pattern);

      const now = new Date();
      let cleanedCount = 0;

      for (const key of keys) {
        try {
          const capacityData = await this.redisService.get(key);
          if (!capacityData) continue;

          const capacityInfo: NodeCapacityInfo = JSON.parse(capacityData);

          // 检查是否有超时任务
          if (capacityInfo.estimatedAvailableTime &&
              capacityInfo.estimatedAvailableTime < now &&
              capacityInfo.activeTasks.length > 0) {

            // 清理超时任务
            const deviceId = capacityInfo.deviceId;
            this.logger.warn(`Cleaning up timeout tasks for device ${deviceId}: ${capacityInfo.activeTasks.join(', ')}`);

            const updatedInfo: NodeCapacityInfo = {
              ...capacityInfo,
              activeTasks: [],
              currentTasks: 0,
              status: 'idle',
              estimatedAvailableTime: undefined
            };

            await this.redisService.set(key, JSON.stringify(updatedInfo), this.config.cacheExpirationSeconds);

            // 发送超时清理事件
            for (const taskId of capacityInfo.activeTasks) {
              this.eventEmitter.emit('task.timeout', {
                deviceId,
                taskId,
                timestamp: now
              });
            }

            cleanedCount++;
          }
        } catch (error) {
          this.logger.error(`Error processing capacity key ${key}: ${error}`);
        }
      }

      if (cleanedCount > 0) {
        this.logger.log(`Cleaned up timeout tasks for ${cleanedCount} devices`);
      }
    } catch (error) {
      this.logger.error(`Error cleaning up timeout tasks: ${error}`);
    }
  }

  /**
   * 批量获取节点容量信息
   */
  async getBatchCapacity(query: BatchCapacityQuery): Promise<BatchCapacityResult> {
    const result: BatchCapacityResult = {
      capacities: {},
      errors: {}
    };

    try {
      const promises = query.deviceIds.map(async (deviceId) => {
        try {
          const capacity = await this.getNodeCapacity(deviceId);

          // 如果不包含离线节点且节点离线，则跳过
          if (!query.includeOffline && capacity.status === 'offline') {
            return;
          }

          result.capacities[deviceId] = capacity;
        } catch (error) {
          result.errors[deviceId] = error instanceof Error ? error.message : String(error);
        }
      });

      await Promise.all(promises);
    } catch (error) {
      this.logger.error(`Error in batch capacity query: ${error}`);
    }

    return result;
  }

  /**
   * 获取容量统计信息
   */
  async getCapacityStats(): Promise<{
    totalNodes: number;
    idleNodes: number;
    busyNodes: number;
    offlineNodes: number;
    overloadedNodes: number;
    totalActiveTasks: number;
  }> {
    try {
      const pattern = `${this.CAPACITY_KEY_PREFIX}*`;
      const keys = await this.redisService.keys(pattern);

      let idleNodes = 0;
      let busyNodes = 0;
      let offlineNodes = 0;
      let overloadedNodes = 0;
      let totalActiveTasks = 0;

      for (const key of keys) {
        try {
          const capacityData = await this.redisService.get(key);
          if (!capacityData) continue;

          const capacityInfo: NodeCapacityInfo = JSON.parse(capacityData);
          totalActiveTasks += capacityInfo.currentTasks;

          switch (capacityInfo.status) {
            case 'idle':
              idleNodes++;
              break;
            case 'busy':
              busyNodes++;
              break;
            case 'offline':
              offlineNodes++;
              break;
            case 'overloaded':
              overloadedNodes++;
              break;
          }
        } catch (error) {
          this.logger.error(`Error processing capacity stats for key ${key}: ${error}`);
        }
      }

      return {
        totalNodes: keys.length,
        idleNodes,
        busyNodes,
        offlineNodes,
        overloadedNodes,
        totalActiveTasks
      };
    } catch (error) {
      this.logger.error(`Error getting capacity stats: ${error}`);
      return {
        totalNodes: 0,
        idleNodes: 0,
        busyNodes: 0,
        offlineNodes: 0,
        overloadedNodes: 0,
        totalActiveTasks: 0
      };
    }
  }

  /**
   * 强制释放节点所有任务（紧急情况使用）
   */
  async forceReleaseAllTasks(deviceId: string, reason: string = 'Force release'): Promise<void> {
    try {
      const capacityInfo = await this.getNodeCapacity(deviceId);

      if (capacityInfo.activeTasks.length > 0) {
        this.logger.warn(`Force releasing ${capacityInfo.activeTasks.length} tasks from device ${deviceId}: ${reason}`);

        // 发送强制释放事件
        for (const taskId of capacityInfo.activeTasks) {
          this.eventEmitter.emit('task.force_released', {
            deviceId,
            taskId,
            reason,
            timestamp: new Date()
          });
        }

        // 重置节点状态
        const updatedInfo: NodeCapacityInfo = {
          ...capacityInfo,
          activeTasks: [],
          currentTasks: 0,
          status: 'idle',
          estimatedAvailableTime: undefined
        };

        const capacityKey = this.getCapacityKey(deviceId);
        await this.redisService.set(capacityKey, JSON.stringify(updatedInfo), this.config.cacheExpirationSeconds);

        this.logger.log(`Force released all tasks from device ${deviceId}`);
      }
    } catch (error) {
      this.logger.error(`Error force releasing tasks from device ${deviceId}: ${error}`);
    }
  }
}
