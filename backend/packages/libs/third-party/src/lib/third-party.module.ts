import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { ModuleRef } from '@nestjs/core';
import { ThirdPartyKeyService } from './third-party-key.service';
import { ThirdPartyRepository } from './third-party.repository';
import { EncryptedApiKeyService } from './encrypted-api-key.service';
import { EtcdSyncService } from './etcd-sync.service';
import { PersistentModule } from '@saito/persistent';
import { ExecutorModule } from '@saito/executor';
import {
  API_KEY_REPOSITORY_TOKEN,
  THIRD_PARTY_REPOSITORY_TOKEN
} from './repository.interfaces';

/**
 * Third-party API key management module
 * Provides services for managing third-party API keys
 * Extracted from @saito/apikey to create a dedicated module
 */
@Module({
  imports: [
    PersistentModule,
    ConfigModule,
    ExecutorModule, // Import ExecutorModule for PublicKeyService and EtcdService
  ],
  providers: [
    ThirdPartyKeyService,
    ThirdPartyRepository,
    EncryptedApiKeyService,
    EtcdSyncService,
    // Provide repository implementations via dependency injection
    {
      provide: API_KEY_REPOSITORY_TOKEN,
      useFactory: async (moduleRef: ModuleRef) => {
        const { ApiKeyRepository } = await import('@saito/apikey');
        return moduleRef.get(ApiKeyRepository, { strict: false });
      },
      inject: [ModuleRef],
    },
    {
      provide: THIRD_PARTY_REPOSITORY_TOKEN,
      useExisting: ThirdPartyRepository,
    },
  ],
  exports: [
    ThirdPartyKeyService,
    ThirdPartyRepository,
    EncryptedApiKeyService,
    EtcdSyncService,
  ],
})
export class ThirdPartyModule {}
