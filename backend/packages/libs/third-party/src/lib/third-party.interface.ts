import { <PERSON><PERSON><PERSON><PERSON> } from '@saito/models';
import { 
  CreateThirdPartyKeyRequest, 
  UpdateThirdPartyKeyRequest,
  ThirdPartyKeyInfo 
} from '@saito/models';

/**
 * Third-party key service interface
 * Defines the contract for third-party API key management
 */
export interface ThirdPartyKeyServiceInterface {
  /**
   * Get third-party key information (metadata only)
   */
  getThirdPartyKeyInfo(keyId: string): Promise<{ keyType: string }>;

  /**
   * Get third-party keys for a user
   */
  getThirdPartyKeys(userId: string, options?: {
    provider?: string;
    status?: string;
    page?: number;
    pageSize?: number;
  }): Promise<{
    items: ApiKey[];
    total: number;
    page: number;
    pageSize: number;
  }>;

  /**
   * Get third-party key by ID
   */
  getThirdPartyKeyById(keyId: string, userId: string): Promise<ApiKey>;

  /**
   * Update third-party key
   */
  updateThirdPartyKey(keyId: string, request: UpdateThirdPartyKeyRequest, userId: string): Promise<ApiKey>;

  /**
   * Remove a third-party key
   */
  removeThirdPartyKey(keyId: string, userId: string): Promise<void>;
}

/**
 * Third-party repository interface
 * Defines the contract for third-party key data access
 */
export interface ThirdPartyRepositoryInterface {
  /**
   * Create a new third-party API key
   */
  createThirdPartyKey(params: {
    userId: string;
    name: string;
    description?: string;
    provider: string;
    keyHash: string;
    region?: string;
    encryptedKeyData?: string;
  }): Promise<ApiKey>;

  /**
   * Get third-party keys for a user
   */
  getThirdPartyKeys(userId: string, options?: {
    provider?: string;
    status?: string;
    page?: number;
    pageSize?: number;
  }): Promise<{
    items: ApiKey[];
    total: number;
    page: number;
    pageSize: number;
  }>;

  /**
   * Update third-party key
   */
  updateThirdPartyKey(keyId: string, updates: UpdateThirdPartyKeyRequest | any): Promise<ApiKey>;

  /**
   * Get third-party key information for decryption
   */
  getThirdPartyKeyInfo(keyId: string): Promise<{ keyType: string } | null>;
}
