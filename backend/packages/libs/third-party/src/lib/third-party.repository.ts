import { Injectable, Logger } from '@nestjs/common';
import { PersistentService } from '@saito/persistent';
import { SQL as sql } from '@saito/common';
import { Api<PERSON>ey } from '@saito/models';
import { UpdateThirdPartyKeyRequest } from '@saito/models';
import { ThirdPartyRepositoryInterface } from './third-party.interface';

/**
 * Third-party API key repository
 * Handles database operations for third-party API keys
 * Extracted from ApiKeyRepository to create a dedicated third-party module
 */
@Injectable()
export class ThirdPartyRepository implements ThirdPartyRepositoryInterface {
  private readonly logger = new Logger(ThirdPartyRepository.name);

  constructor(private readonly persistentService: PersistentService) {}

  /**
   * Create a new third-party API key
   */
  async createThirdPartyKey(params: {
    userId: string;
    name: string;
    description?: string;
    provider: string;
    region?: string;
    encryptedKeyData?: string;
  }): Promise<ApiKey> {
    this.logger.debug('Creating third-party key with params:', params);
    console.log(`
        INSERT INTO saito_gateway.api_keys (
          user_id,
          name,
          description,
          status,
          key_type,
          provider,
          key_prefix,
          key_mask,
          encrypted_key_data,
          region,
          key_hash
        )
        VALUES (
          ${params.userId},
          ${params.name},
          ${params.description || null},
          'active',
          'third_party',
          '${params.provider}',
          'tp-',
          'tp-***simple***',
          '${params.encryptedKeyData || null}',
          '${params.region || null}',
          'key_hash'
        )
        RETURNING
          id,
          user_id as "userId",
          name,
          description,
          status,
          key_type as "keyType",
          provider as "providerKeyId",
          encrypted_key_data as "encryptedKeyData",
          key_prefix as "keyPrefix",
          key_mask as "keyMask",
          key_hash as "keyHash",
          region,
          created_at as "createdAt",
          updated_at as "updatedAt",
          deleted_at as "deletedAt"
      `)
    try {
      const result = await this.persistentService.pgPool.query(sql.unsafe`
        INSERT INTO saito_gateway.api_keys (
          user_id,
          name,
          description,
          status,
          key_type,
          provider,
          key_prefix,
          key_mask,
          encrypted_key_data,
          region,
          key_hash
        )
        VALUES (
          ${params.userId},
          ${params.name},
          ${params.description || null},
          'active',
          'third_party',
          ${params.provider},
          'tp-',
          'tp-***simple***',
          ${params.encryptedKeyData || null},
          ${params.region || null},
          'key_hash'
        )
        RETURNING
          id,
          user_id as "userId",
          name,
          description,
          status,
          key_type as "keyType",
          provider as "providerKeyId",
          encrypted_key_data as "encryptedKeyData",
          key_prefix as "keyPrefix",
          key_mask as "keyMask",
          key_hash as "keyHash",
          region,
          created_at as "createdAt",
          updated_at as "updatedAt",
          deleted_at as "deletedAt"
      `);

      if (result.rows.length === 0) {
        throw new Error('Failed to create third-party API key');
      }

      this.logger.log('Third-party key created successfully:', result.rows[0]);
      return result.rows[0] as ApiKey;
    } catch (error) {
      this.logger.error(`Error creating third-party API key: ${error}`);
      throw error;
    }
  }

  /**
   * Get third-party keys for a user
   */
  async getThirdPartyKeys(userId: string, options?: {
    provider?: string;
    status?: string;
    page?: number;
    pageSize?: number;
  }): Promise<{
    items: ApiKey[];
    total: number;
    page: number;
    pageSize: number;
  }> {
    try {
      const page = options?.page || 1;
      const pageSize = options?.pageSize || 10;
      const offset = (page - 1) * pageSize;

      // Build WHERE clause
      let whereClause = sql.fragment`WHERE user_id = ${userId} AND key_type = 'third_party' AND deleted_at IS NULL`;

      if (options?.provider) {
        whereClause = sql.fragment`${whereClause} AND provider = ${options.provider}`;
      }

      if (options?.status) {
        whereClause = sql.fragment`${whereClause} AND status = ${options.status}`;
      }

      // Get total count
      const countResult = await this.persistentService.pgPool.query(sql.unsafe`
        SELECT COUNT(*) as total
        FROM saito_gateway.api_keys
        ${whereClause}
      `);

      const total = parseInt(countResult.rows[0].total);

      // Get paginated results
      const result = await this.persistentService.pgPool.query(sql.unsafe`
        SELECT
          id,
          user_id as "userId",
          name,
          description,
          status,
          key_type as "keyType",
          provider,
          key_prefix as "keyPrefix",
          key_mask as "keyMask",
          total_requests as "totalRequests",
          region,
          total_tokens as "totalTokens",
          total_cost as "totalCost",
          created_at as "createdAt",
          updated_at as "updatedAt",
          last_used as "lastUsed"
        FROM saito_gateway.api_keys
        ${whereClause}
        ORDER BY created_at DESC
        LIMIT ${pageSize} OFFSET ${offset}
      `);

      return {
        items: result.rows as ApiKey[],
        total,
        page,
        pageSize
      };
    } catch (error) {
      this.logger.error(`Error getting third-party keys: ${error}`);
      throw error;
    }
  }

  /**
   * Update third-party key
   */
  async updateThirdPartyKey(keyId: string, updates: UpdateThirdPartyKeyRequest | { etcdKey: string } | { encrypted_key_data: string } | any): Promise<ApiKey> {
    try {
      let query = sql.unsafe`UPDATE saito_gateway.api_keys SET updated_at = NOW()`;

      if ('name' in updates && updates.name !== undefined) {
        query = sql.unsafe`${query}, name = ${updates.name}`;
      }

      if ('description' in updates && updates.description !== undefined) {
        query = sql.unsafe`${query}, description = ${updates.description}`;
      }

      if ('status' in updates && updates.status !== undefined) {
        query = sql.unsafe`${query}, status = ${updates.status}`;
      }

      if ('etcdKey' in updates && updates.etcdKey !== undefined) {
        query = sql.unsafe`${query}, etcd_key = ${updates.etcdKey}`;
      }

      if ('encrypted_key_data' in updates && updates.encrypted_key_data !== undefined) {
        query = sql.unsafe`${query}, encrypted_key_data = ${updates.encrypted_key_data}`;
      }

      query = sql.unsafe`${query} WHERE id = ${keyId} AND deleted_at IS NULL
        RETURNING
          id,
          user_id as "userId",
          name,
          description,
          status,
          key_type as "keyType",
          provider,
          key_prefix as "keyPrefix",
          key_mask as "keyMask",
          total_requests as "totalRequests",
          total_tokens as "totalTokens",
          total_cost as "totalCost",
          created_at as "createdAt",
          updated_at as "updatedAt",
          last_used as "lastUsed"`;

      const result = await this.persistentService.pgPool.query(query);

      if (result.rows.length === 0) {
        throw new Error('Third-party key not found or already deleted');
      }

      return result.rows[0];
    } catch (error) {
      this.logger.error(`Error updating third-party key: ${error}`);
      throw error;
    }
  }

  /**
   * Get third-party key by hash
   */
  async getThirdPartyKeyById(id: string): Promise<ApiKey | null> {
    try {
      const result = await this.persistentService.pgPool.query(sql.unsafe`
        SELECT
          id,
          user_id as "userId",
          name,
          description,
          status,
          key_type as "keyType",
          provider,
          key_prefix as "keyPrefix",
          key_mask as "keyMask",
          encrypted_key_data,
          region,
          total_requests as "totalRequests",
          total_tokens as "totalTokens",
          total_cost as "totalCost",
          created_at as "createdAt",
          updated_at as "updatedAt",
          deleted_at as "deletedAt",
          last_used as "lastUsed",
          expiration_date as "expirationDate"
        FROM saito_gateway.api_keys
        WHERE id = ${id}
        AND key_type = 'third_party'
        LIMIT 1
      `);

      if (result.rows.length === 0) {
        return null;
      }

      return result.rows[0] as ApiKey;
    } catch (error) {
      this.logger.error(`Failed to get third-party key by hash ${id}: ${error}`);
      return null;
    }
  }
  /**
   * Get third-party key information for decryption
   */
  async getThirdPartyKeyInfo(keyId: string): Promise<{
    keyType: string;
  } | null> {
    try {
      const result = await this.persistentService.pgPool.query(sql.unsafe`
        SELECT
          key_type as "keyType"
        FROM saito_gateway.api_keys
        WHERE id = ${keyId}
          AND status = 'active'
          AND deleted_at IS NULL
      `);

      if (result.rows.length === 0) {
        return null;
      }

      return result.rows[0];
    } catch (error) {
      this.logger.error(`Error getting third-party key info: ${error}`);
      throw error;
    }
  }

  /**
   * Get all third-party keys for etcd sync
   * Returns all active third-party keys with encrypted_key_data
   */
  async getAllThirdPartyKeysForSync(): Promise<Array<{
    id: string;
    provider: string;
    region: string;
    encryptedKeyData: string;
  }>> {
    try {
      const result = await this.persistentService.pgPool.query(sql.unsafe`
        SELECT
          id,
          provider,
          region,
          encrypted_key_data as "encryptedKeyData"
        FROM saito_gateway.api_keys
        WHERE key_type = 'third_party'
          AND status = 'active'
          AND deleted_at IS NULL
          AND encrypted_key_data IS NOT NULL
          AND provider IS NOT NULL
          AND region IS NOT NULL
        ORDER BY created_at ASC
      `);

      return result.rows as Array<{
        id: string;
        provider: string;
        region: string;
        encryptedKeyData: string;
      }>;
    } catch (error) {
      this.logger.error(`Error getting all third-party keys for sync: ${error}`);
      throw error;
    }
  }
}
