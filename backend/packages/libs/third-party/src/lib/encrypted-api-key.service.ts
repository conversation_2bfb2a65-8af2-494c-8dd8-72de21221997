import { Injectable, Logger, HttpException, HttpStatus, Inject } from '@nestjs/common';
import { EtcdService } from '@saito/executor';
import { StoredEncryptedApiKeyData } from './shared-interfaces';
import { ThirdPartyRepository } from './third-party.repository';
import { ApiKey } from '@saito/models';

export interface CreateEncryptedApiKeyRequest {
  name: string;                  // 密钥名称
  provider: string;
  region: string;
  keyId: string;                  // 目标 Executor 的 keyId
  encryptedKey: string;          // base64(ciphertext) 加密后的Provider API KEY
  nonce: string;                 // base64(nonce) 随机数，ChaCha20-Poly1305要求解密用
  tag: string;                   // base64(tag) ChaCha20-Poly1305要求解密用
  ephemeralPubKey: string;       // base64(X25519 公钥)
}

export type EncryptedApiKeyData = StoredEncryptedApiKeyData;

export interface CreateEncryptedApiKeyResult {
  provider: string;
  region: string;
  keyId: string;
  status: string;
  createdAt: string;
}

export interface UpdateEncryptedApiKeyRequest {
  status: 'active' | 'revoked' | 'waiting-to-verify' | 'inactive';
}

@Injectable()
export class EncryptedApiKeyService {
  private readonly logger = new Logger(EncryptedApiKeyService.name);

  constructor(
    private thirdPartyRepository: ThirdPartyRepository,
    private etcdService: EtcdService,
  ) { }

  /**
   * Create and store encrypted API key in etcd
   * Path: /api-keys/{provider}/{region}/{uuid}
   */
  async createEncryptedApiKey(
    request: CreateEncryptedApiKeyRequest,
    userId: string,
  ): Promise<CreateEncryptedApiKeyResult> {
    try {
      this.validateEncryptionData(request);

      const encryptedKeyData = this.buildEncryptedKeyData(request, userId);
      let {id} =  await this.createSharedKeyInDatabase(request, userId);
      await this.storeEncryptedKey(request, id, encryptedKeyData);

      this.logger.log(
        `Created encrypted API key: ${id} for provider: ${request.provider}, region: ${request.region}, executor: ${request.keyId}`,
      );

      return this.buildCreateResult(request, encryptedKeyData);
    } catch (error) {
      return this.handleCreateError(error);
    }
  }

  /**
   * Validate encryption data format
   */
  private validateEncryptionData(request: CreateEncryptedApiKeyRequest): void {
    const isValidEncryption = [
      request.encryptedKey,
      request.nonce,
      request.tag,
      request.ephemeralPubKey
    ].every(data => this.isValidBase64(data));

    if (!isValidEncryption) {
      throw new HttpException(
        {
          success: false,
          error: {
            message: 'Invalid base64 encoding in encryption data',
            code: 'INVALID_ENCRYPTION_DATA',
          },
        },
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  /**
   * Build encrypted key data object
   */
  private buildEncryptedKeyData(
    request: CreateEncryptedApiKeyRequest,
    userId: string
  ): StoredEncryptedApiKeyData {
    return {
      keyId: request.keyId,
      encryptedKey: request.encryptedKey,
      nonce: request.nonce,
      tag: request.tag,
      ephemeralPubKey: request.ephemeralPubKey,
      status: 'waiting-to-verify',
      createdAt: new Date().toISOString(),
      createdBy: userId,
    };
  }

  /**
   * Store encrypted key in etcd
   */
  private async storeEncryptedKey(
    request: CreateEncryptedApiKeyRequest,
    uuid: string,
    encryptedKeyData: StoredEncryptedApiKeyData
  ): Promise<void> {
    const keyPath = `/api-keys/${request.provider}/${request.region}/${uuid}`;
    await this.etcdService.put(keyPath, JSON.stringify(encryptedKeyData));
  }

  /**
   * Build create result object
   */
  private buildCreateResult(
    request: CreateEncryptedApiKeyRequest,
    encryptedKeyData: StoredEncryptedApiKeyData
  ): CreateEncryptedApiKeyResult {
    return {
      provider: request.provider,
      region: request.region,
      keyId: request.keyId,
      status: encryptedKeyData.status,
      createdAt: encryptedKeyData.createdAt,
    };
  }

  /**
   * Handle errors during key creation
   */
  private handleCreateError(error: unknown): never {
    if (error instanceof HttpException) {
      throw error;
    }

    this.logger.error(
      `Failed to create encrypted API key: ${error instanceof Error ? error.message : String(error)}`,
    );
    throw new HttpException(
      {
        success: false,
        error: {
          message: 'Internal server error while creating encrypted API key',
          code: 'INTERNAL_ERROR',
        },
      },
      HttpStatus.INTERNAL_SERVER_ERROR,
    );
  }

  /**
   * Get encrypted API key by UUID
   */
  async getEncryptedApiKey(
    provider: string,
    region: string,
    uuid: string,
  ): Promise<EncryptedApiKeyData | null> {
    try {
      const keyPath = `/api-keys/${provider}/${region}/${uuid}`;
      const jsonValue = await this.etcdService.get(keyPath);

      if (!jsonValue) {
        return null;
      }

      return JSON.parse(jsonValue) as EncryptedApiKeyData;
    } catch (error) {
      this.logger.error(
        `Failed to get encrypted API key ${uuid}: ${error instanceof Error ? error.message : String(error)}`,
      );
      return null;
    }
  }

  /**
   * Update encrypted API key status
   */
  async updateEncryptedApiKeyStatus(
    provider: string,
    region: string,
    uuid: string,
    status: 'active' | 'revoked' | 'waiting-to-verify',
  ): Promise<boolean> {
    try {
      const keyPath = `/api-keys/${provider}/${region}/${uuid}`;

      // Get existing data
      const existingData = await this.getEncryptedApiKey(provider, region, uuid);
      if (!existingData) {
        this.logger.warn(`Cannot update non-existent encrypted API key: ${uuid}`);
        return false;
      }

      // Update status
      const updatedData = {
        ...existingData,
        status,
      };

      await this.etcdService.put(keyPath, JSON.stringify(updatedData));

      // 同步更新数据库状态
      await this.updateSharedKeyStatusInDatabase(uuid, status);

      this.logger.log(`Updated encrypted API key ${uuid} status to: ${status}`);
      return true;
    } catch (error) {
      this.logger.error(
        `Failed to update encrypted API key status for ${uuid}: ${error instanceof Error ? error.message : String(error)}`,
      );
      return false;
    }
  }

  /**
   * List encrypted API keys for a provider/region
   */
  async listEncryptedApiKeys(
    provider: string,
    region?: string,
    status?: 'active' | 'revoked' | 'waiting-to-verify',
  ): Promise<{
    keys: Array<EncryptedApiKeyData & { uuid: string }>;
    totalCount: number;
  }> {
    try {
      let keyPrefix: string;

      if (region) {
        keyPrefix = `/api-keys/${provider}/${region}/`;
      } else {
        keyPrefix = `/api-keys/${provider}/`;
      }

      const kvs = await this.etcdService.getAllWithPrefix(keyPrefix);
      const keys: Array<EncryptedApiKeyData & { uuid: string }> = [];

      for (const [fullPath, jsonValue] of Object.entries(kvs)) {
        try {
          const keyData = JSON.parse(jsonValue as string) as EncryptedApiKeyData;

          // Apply status filter if provided
          if (status && keyData.status !== status) {
            continue;
          }

          // Extract UUID from path
          const pathParts = fullPath.split('/');
          const uuid = pathParts[pathParts.length - 1];

          keys.push({
            ...keyData,
            uuid,
          });
        } catch (parseError) {
          this.logger.warn(`Failed to parse encrypted API key data for ${fullPath}: ${parseError}`);
        }
      }

      // Sort by creation time, newest first
      keys.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());

      return {
        keys,
        totalCount: keys.length,
      };
    } catch (error) {
      this.logger.error(
        `Failed to list encrypted API keys: ${error instanceof Error ? error.message : String(error)}`,
      );
      throw error;
    }
  }

  /**
   * Validate base64 encoding
   */
  private isValidBase64(str: string): boolean {
    try {
      return Buffer.from(str, 'base64').toString('base64') === str;
    } catch {
      return false;
    }
  }

  /**
   * Create shared key record in database
   */
  private async createSharedKeyInDatabase(
    request: CreateEncryptedApiKeyRequest,
    userId: string,
  ): Promise<ApiKey> {
    try {
      // 为共享密钥创建数据库记录
      // 使用 encrypted_key_data 字段存储加密信息和元数据的 JSON
      const encryptedKeyData = JSON.stringify({
        encryptedKey: request.encryptedKey,
        nonce: request.nonce,
        tag: request.tag,
        ephemeralPubKey: request.ephemeralPubKey,
        keyId: request.keyId,
        region: request.region, // 添加 region 信息
      });

      return await this.thirdPartyRepository.createThirdPartyKey({
        userId,
        name: request.name,
        provider: request.provider,
        region: request.region, // 添加 region 信息
        encryptedKeyData, // 存储加密信息
      });

    } catch (error) {
      this.logger.error(`Failed to create shared key in database: ${error instanceof Error ? error.message : String(error)}`);
      throw error;
    }
  }

  /**
   * Update shared key status in database
   */
  private async updateSharedKeyStatusInDatabase(
    uuid: string,
    status: 'active' | 'revoked' | 'waiting-to-verify',
  ): Promise<void> {
    try {
      // 映射状态到数据库状态 (只支持 active/inactive)
      let dbStatus: 'active' | 'inactive';
      if (status === 'active') {
        dbStatus = 'active';
      } else {
        // revoked 和 waiting-to-verify 都映射为 inactive
        dbStatus = 'inactive';
      }

      await this.thirdPartyRepository.updateThirdPartyKey(uuid, {
        status: dbStatus,
      });

      this.logger.log(`Updated shared key status in database: ${uuid} -> ${dbStatus}`);
    } catch (error) {
      this.logger.error(`Failed to update shared key status in database: ${error instanceof Error ? error.message : String(error)}`);
      // 不抛出错误，因为 etcd 操作已经成功
    }
  }

  /**
   * Update shared key in database
   */
  private async updateSharedKeyInDatabase(
    uuid: string,
    updateRequest: UpdateEncryptedApiKeyRequest,
  ): Promise<void> {
    try {
      if (!updateRequest || Object.keys(updateRequest).length === 0) {
        this.logger.debug(`No updates to apply for key: ${uuid}`);
        return;
      }

      this.logger.debug(`Updating shared key in database: ${uuid}`, updateRequest);

      await this.thirdPartyRepository.updateThirdPartyKey(uuid, updateRequest);
      this.logger.log(`Successfully updated shared key in database: ${uuid}`);

    } catch (error) {
      this.logger.error(`Failed to update shared key in database for ${uuid}:`, {
        error: error instanceof Error ? error.message : String(error),
        updateRequest,
        stack: error instanceof Error ? error.stack : undefined
      });
    }
  }

  /**
   * Delete shared key from database
   */
  private async deleteSharedKeyFromDatabase(uuid: string): Promise<void> {
    try {
      // 软删除数据库记录 (设置为 inactive)
      await this.thirdPartyRepository.updateThirdPartyKey(uuid, {
        status: 'inactive',
      });

      this.logger.log(`Deleted shared key from database: ${uuid}`);
    } catch (error) {
      this.logger.error(`Failed to delete shared key from database: ${error instanceof Error ? error.message : String(error)}`);
      // 不抛出错误，因为 etcd 操作已经成功
    }
  }

  /**
   * Update encrypted API key
   */
  async updateEncryptedApiKey(
    provider: string,
    region: string,
    uuid: string,
    updateRequest: UpdateEncryptedApiKeyRequest,
  ): Promise<boolean> {
    try {
      this.logger.debug(`Updating encrypted API key: ${uuid}`);
      // Get key from database
      const key = await this.thirdPartyRepository.getThirdPartyKeyById(uuid);
      this.logger.debug(`Retrieved key from database:`, key);

      if (!key) {
        this.logger.warn(`API key not found in database: ${uuid}`);
        return false;
      }
      // Validate required fields - use the UUID as keyHash if keyHash is missing
      const provider = key.provider
      const region = key.region

      const keyPath = `/api-keys/${provider}/${region}/${uuid}`;
      this.logger.debug(`Using etcd key path: ${keyPath}`);

      // Get existing data from etcd
      const existingData = await this.getEncryptedApiKey(provider, region, uuid);
      if (!existingData) {
        this.logger.warn(`Cannot update non-existent encrypted API key in etcd: ${uuid}`);
        return false;
      }

      // Update data
      const updatedData = {
        ...existingData,
        status: updateRequest.status,
      };

      // Update in etcd
      await this.etcdService.put(keyPath, JSON.stringify(updatedData));
      this.logger.debug(`Updated key in etcd: ${keyPath}`);

      // Update database if status changed
      if (updateRequest.status) {
        await this.updateSharedKeyInDatabase(uuid, updateRequest);
      }

      this.logger.log(`Successfully updated encrypted API key: ${uuid}`);
      return true;
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }

      this.logger.error(
        `Failed to update encrypted API key ${uuid}: ${error instanceof Error ? error.message : String(error)}`,
      );
      return false;
    }
  }

  /**
   * Get statistics about encrypted API keys
   */
  async getEncryptedApiKeyStatistics(): Promise<{
    totalKeys: number;
    keysByStatus: Record<string, number>;
    keysByProvider: Record<string, number>;
    keysByRegion: Record<string, number>;
  }> {
    try {
      const keyPrefix = '/api-keys/';
      const kvs = await this.etcdService.getAllWithPrefix(keyPrefix);

      let totalKeys = 0;
      const keysByStatus: Record<string, number> = {};
      const keysByProvider: Record<string, number> = {};
      const keysByRegion: Record<string, number> = {};

      for (const [fullPath, jsonValue] of Object.entries(kvs)) {
        try {
          const keyData = JSON.parse(jsonValue as string) as EncryptedApiKeyData;

          totalKeys++;

          // Count by status
          keysByStatus[keyData.status] = (keysByStatus[keyData.status] || 0) + 1;

          // Extract provider and region from path
          const pathParts = fullPath.split('/');
          if (pathParts.length >= 4) {
            const provider = pathParts[2];
            const region = pathParts[3];

            keysByProvider[provider] = (keysByProvider[provider] || 0) + 1;
            keysByRegion[region] = (keysByRegion[region] || 0) + 1;
          }
        } catch (parseError) {
          this.logger.warn(`Failed to parse encrypted API key data for statistics: ${fullPath}`);
        }
      }

      return {
        totalKeys,
        keysByStatus,
        keysByProvider,
        keysByRegion,
      };
    } catch (error) {
      this.logger.error(
        `Failed to get encrypted API key statistics: ${error instanceof Error ? error.message : String(error)}`,
      );
      throw error;
    }
  }
}
