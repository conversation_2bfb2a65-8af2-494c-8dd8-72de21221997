import { z } from 'zod';

/**
 * Third-party API key management schemas
 * Extracted from @saito/models to create a dedicated third-party module
 */

// Provider enum for third-party services
export const ThirdPartyProviderSchema = z.enum(['openai', 'anthropic', 'google', 'cohere']);
export type ThirdPartyProvider = z.infer<typeof ThirdPartyProviderSchema>;

// Key status enum
export const ThirdPartyKeyStatusSchema = z.enum(['active', 'inactive', 'revoked', 'in_use']);
export type ThirdPartyKeyStatus = z.infer<typeof ThirdPartyKeyStatusSchema>;

// Create third-party key request schema
export const CreateThirdPartyKeyRequestSchema = z.object({
  name: z.string().min(1).max(50).describe('密钥名称'),
  description: z.string().max(200).optional().describe('密钥描述'),
  region: z.string().describe('区域'),
  provider: ThirdPartyProviderSchema.describe('提供商'),
  apiKey: z.string().min(1).describe('第三方 API 密钥'),
});

export type CreateThirdPartyKeyRequest = z.infer<typeof CreateThirdPartyKeyRequestSchema>;

// Update third-party key request schema
export const UpdateThirdPartyKeyRequestSchema = z.object({
  name: z.string().min(1).max(50).optional().describe('密钥名称'),
  description: z.string().max(200).optional().describe('密钥描述'),
  status: z.enum(['active', 'inactive']).optional().describe('密钥状态')
});

export type UpdateThirdPartyKeyRequest = z.infer<typeof UpdateThirdPartyKeyRequestSchema>;

// Third-party key info interface (for backward compatibility with executor)
export interface ThirdPartyKeyInfo {
  keyId: string;
  userId: string;
  provider: string;
  encryptedApiKey?: string;  // API 密钥 (简化版本，假设为明文)
  status: 'active' | 'inactive' | 'revoked' | 'in_use';
  createdAt: number;
  lastUsedAt?: number;
  lockedAt?: number;
  lockTimeout?: number;
  note?: string;
  keyType?: string;  // 密钥类型
}

// Third-party key with etcd path
export interface ThirdPartyKeyWithPath extends ThirdPartyKeyInfo {
  etcdPath: string;
}

// Cached API key interface
export interface CachedApiKey {
  keyId: string;
  decryptedKey: string;
  cachedAt: number;
}

// Export all schemas for validation
export const ThirdPartySchemas = {
  CreateThirdPartyKeyRequest: CreateThirdPartyKeyRequestSchema,
  UpdateThirdPartyKeyRequest: UpdateThirdPartyKeyRequestSchema,
  ThirdPartyProvider: ThirdPartyProviderSchema,
  ThirdPartyKeyStatus: ThirdPartyKeyStatusSchema,
};
