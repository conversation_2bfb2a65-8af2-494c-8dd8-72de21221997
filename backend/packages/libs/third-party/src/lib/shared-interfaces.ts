/**
 * Shared interfaces for API key management
 * These interfaces are used across different modules for API key operations
 */

// Base encrypted API key data structure
export interface BaseEncryptedApiKeyData {
  encryptedKey: string;          // Base64 encoded encrypted API key
  nonce: string;                 // Base64 encoded nonce for decryption
  tag: string;                   // Base64 encoded authentication tag
  ephemeralPubKey: string;       // Base64 encoded ephemeral public key
  createdAt: string;             // ISO timestamp
}

// Encrypted API key data for storage (used by EncryptedApiKeyService)
export interface StoredEncryptedApiKeyData extends BaseEncryptedApiKeyData {
  keyId: string;                  // 目标 Executor 的 keyId
  status: 'active' | 'revoked' | 'waiting-to-verify' | 'inactive';
  createdBy: string;             // User ID who created this key
}

// Encrypted API key data for executor consumption (used by ExecutorKeyService)
export interface ExecutorEncryptedApiKeyData extends BaseEncryptedApiKeyData {
  status: 'active' | 'inactive' | 'revoked';
}

// Note: CreateEncryptedApiKeyRequest and UpdateEncryptedApiKeyRequest
// are defined in encrypted-api-key.service.ts to avoid circular exports

// Response interface for encrypted API key operations
export interface EncryptedApiKeyResponse {
  success: boolean;
  data?: StoredEncryptedApiKeyData;
  error?: {
    message: string;
    code: string;
  };
}
