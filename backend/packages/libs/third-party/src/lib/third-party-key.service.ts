import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { ThirdPartyRepository } from './third-party.repository';
import { Api<PERSON><PERSON> } from '@saito/models';
import {
  CreateThirdPartyKeyRequest,
  UpdateThirdPartyKeyRequest,
} from '@saito/models';
import { ThirdPartyKeyServiceInterface } from './third-party.interface';
import * as crypto from 'crypto';
import { EtcdService } from '@saito/executor';

/**
 * Third-Party Key Management Service
 * Handles creation, management, and storage of third-party API keys
 * Stores metadata in database and indexes in etcd
 * Extracted from @saito/apikey to create a dedicated third-party module
 */
@Injectable()
export class ThirdPartyKeyService implements ThirdPartyKeyServiceInterface {
  private readonly logger = new Logger(ThirdPartyKeyService.name);

  constructor(
    private readonly thirdPartyRepository: ThirdPartyRepository,
    private readonly etcdService: EtcdService
  ) {}

  /**
   * Create a new third-party API key (simplified version without encryption)
   */
  // async createThirdPartyKey(
  //   request: CreateThirdPartyKeyRequest,
  //   userId: string
  // ): Promise<ApiKey> {
  //   try {
  //     this.logger.debug(`Creating third-party key for user ${userId}`);

  //     // Generate etcd key for indexing
  //     const etcdKey = `/api-keys/${request.provider}/${request.region}`;

  //     // Generate a hash for the actual API key
  //     const keyHash = crypto.createHash('sha256').update(request.apiKey).digest('hex');

  //     // Create the API key record in database (simplified)
  //     const apiKey = await this.thirdPartyRepository.createThirdPartyKey({
  //       userId,
  //       name: request.name,
  //       description: request.description,
  //       provider: request.provider,
  //       keyHash
  //     });

  //     // Store key information in etcd for Executor access (simplified)
  //     const dynamicEtcdKey = `${etcdKey}/${apiKey.id}`;
  //     const etcdData = {
  //       keyId: apiKey.id,
  //       userId: apiKey.userId,
  //       provider: request.provider,
  //       status: apiKey.status,
  //       keyType: 'third_party' as const,
  //       createdAt: new Date(apiKey.createdAt).toISOString(),
  //       encryptionType: 'none'
  //     };

  //     await this.storeKeyInEtcd(dynamicEtcdKey, etcdData);

  //     this.logger.log(`Created third-party key ${apiKey.id} for user ${userId}`);
  //     return apiKey;
  //   } catch (error) {
  //     this.logger.error(`Failed to create third-party key: ${error}`);
  //     throw error;
  //   }
  // }

  /**
   * Get third-party key information (metadata only)
   */
  async getThirdPartyKeyInfo(keyId: string): Promise<{
    keyType: string;
  }> {
    try {
      this.logger.debug(`Retrieving third-party key info ${keyId}`);

      // Get key information from database
      const keyInfo = await this.thirdPartyRepository.getThirdPartyKeyInfo(keyId);
      if (!keyInfo) {
        throw new NotFoundException(`Third-party key ${keyId} not found`);
      }

      if (keyInfo.keyType !== 'third_party') {
        throw new Error(`Key ${keyId} is not a third-party key`);
      }

      this.logger.debug(`Successfully retrieved third-party key info ${keyId}`);
      return { keyType: keyInfo.keyType };
    } catch (error) {
      this.logger.error(`Failed to get third-party key info ${keyId}: ${error}`);
      throw error;
    }
  }

  /**
   * Get third-party keys for a user
   */
  async getThirdPartyKeys(userId: string, options?: {
    provider?: string;
    status?: string;
    page?: number;
    pageSize?: number;
  }): Promise<{
    items: ApiKey[];
    total: number;
    page: number;
    pageSize: number;
  }> {
    try {
      this.logger.debug(`Getting third-party keys for user ${userId}`);
      return await this.thirdPartyRepository.getThirdPartyKeys(userId, options);
    } catch (error) {
      this.logger.error(`Failed to get third-party keys for user ${userId}: ${error}`);
      throw error;
    }
  }

  /**
   * Get third-party key by ID
   */
  async getThirdPartyKeyById(keyId: string, userId: string): Promise<ApiKey> {
    try {
      // For now, we'll use a simple approach and get the key from repository
      // In the future, this could be enhanced to use the general ApiKeyRepository
      const keys = await this.thirdPartyRepository.getThirdPartyKeys(userId, { page: 1, pageSize: 1000 });
      const key = keys.items.find(k => k.id === keyId);

      if (!key || key.userId !== userId || key.keyType !== 'third_party') {
        throw new NotFoundException('Third-party key not found or access denied');
      }

      return key;
    } catch (error) {
      this.logger.error(`Failed to get third-party key ${keyId}: ${error}`);
      throw error;
    }
  }

  /**
   * Update third-party key
   */
  async updateThirdPartyKey(keyId: string, request: UpdateThirdPartyKeyRequest, userId: string): Promise<ApiKey> {
    try {
      // Verify ownership
      const existingKey = await this.getThirdPartyKeyById(keyId, userId);

      // Update the key
      const updatedKey = await this.thirdPartyRepository.updateThirdPartyKey(keyId, request);

      if (request.status && request.status !== existingKey.status && existingKey.provider) {
        const etcdKeyPath = `/api-keys/${existingKey.provider}/${existingKey.region}/${existingKey.keyHash}`;
        await this.updateEtcdKeyStatus(etcdKeyPath, request.status);
      }

      this.logger.log(`Updated third-party key ${keyId} for user ${userId}`);
      return updatedKey;
    } catch (error) {
      this.logger.error(`Failed to update third-party key ${keyId}: ${error}`);
      throw error;
    }
  }

  /**
   * Update key status in etcd
   */
  private async updateEtcdKeyStatus(etcdKey: string, status: string): Promise<void> {
    try {
      const existingData = await this.etcdService.get(etcdKey);
      if (existingData) {
        const keyInfo = JSON.parse(existingData);
        keyInfo.status = status;
        await this.etcdService.set(etcdKey, JSON.stringify(keyInfo));
      }
    } catch (error) {
      this.logger.warn(`Failed to update etcd key status: ${error}`);
    }
  }

  /**
   * Remove a third-party key
   */
  async removeThirdPartyKey(keyId: string, userId: string): Promise<void> {
    try {
      this.logger.debug(`Removing third-party key ${keyId}`);

      // Verify ownership - get the key first
      const keys = await this.thirdPartyRepository.getThirdPartyKeys(userId, { page: 1, pageSize: 1000 });
      const key = keys.items.find(k => k.id === keyId);

      if (!key || key.userId !== userId) {
        throw new NotFoundException('Third-party key not found or access denied');
      }

      if (key.keyType !== 'third_party') {
        throw new Error('Key is not a third-party key');
      }

      await this.thirdPartyRepository.updateThirdPartyKey(keyId, { status: 'revoked' });
      console.log(key)
      if (key.provider && key.keyType === 'third_party') {
        const etcdKeyPath = `/api-keys/${key?.provider}/${key?.region}/${key?.keyHash}`;
        await this.etcdService.delete(etcdKeyPath);
      }

      this.logger.log(`Removed third-party key ${keyId}`);
    } catch (error) {
      this.logger.error(`Failed to remove third-party key ${keyId}: ${error}`);
      throw error;
    }
  }

  // TODO: Implement etcd storage when EtcdService is available
  // /**
  //  * Store key information in etcd
  //  */
  // private async storeKeyInEtcd(keyId: string, keyInfo: {
  //   keyId: string;
  //   userId: string;
  //   provider: string;
  //   status: string;
  //   keyType: string;
  //   createdAt: string;
  //   encryptionType: string;
  // }): Promise<void> {
  //   try {
  //     await this.etcdService.set(keyId, JSON.stringify(keyInfo));
  //   } catch (error) {
  //     this.logger.warn(`Failed to store key ${keyId} in etcd: ${error}`);
  //     // Don't throw - etcd is for caching, not critical
  //   }
  // }
}
