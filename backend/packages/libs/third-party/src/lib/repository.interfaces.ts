/**
 * Repository interfaces for EncryptedApiKeyService
 * These interfaces help break circular dependencies by providing abstractions
 */

import { ApiKey } from '@saito/models';

// Abstract interface for ApiKeyRepository operations needed by EncryptedApiKeyService
export interface IApiKeyRepository {
  getApiKeyByHash(keyHash: string): Promise<ApiKey | null>;
  getApiKeyById(id: string): Promise<ApiKey | null>;
  updateApiKey(id: string, updates: Partial<ApiKey>): Promise<ApiKey>;
}

// Abstract interface for ThirdPartyRepository operations needed by EncryptedApiKeyService
export interface IThirdPartyRepository {
  createThirdPartyKey(data: {
    userId: string;
    name: string;
    provider: string;
    region?: string;
    keyHash: string;
    encryptedKeyData?: string;
    status?: string;
    keyType?: string;
  }): Promise<ApiKey>;
  
  getThirdPartyKeyByHash(keyHash: string): Promise<ApiKey | null>;
  
  updateThirdPartyKey(keyHash: string, updates: {
    status?: string;
    name?: string;
    encryptedKeyData?: string;
  }): Promise<ApiKey>;
  
  deleteThirdPartyKey(keyHash: string): Promise<boolean>;
}

// Injection tokens for dependency injection
export const API_KEY_REPOSITORY_TOKEN = 'API_KEY_REPOSITORY';
export const THIRD_PARTY_REPOSITORY_TOKEN = 'THIRD_PARTY_REPOSITORY';
