import { Injectable, Logger, OnApplicationBootstrap } from '@nestjs/common';
import { EtcdService } from '@saito/executor';
import { ThirdPartyRepository } from './third-party.repository';

/**
 * EtcdSyncService
 * 
 * Responsible for syncing third-party shared keys from database to etcd on application startup.
 * This ensures that all executors have access to the latest shared keys.
 * 
 * Key path format: /api-keys/{provider}/{region}/{uuid}
 * Example: /api-keys/openai/asia/9b940beb-7d17-4471-8edc-8ca65c8e1c41
 */
@Injectable()
export class EtcdSyncService implements OnApplicationBootstrap {
  private readonly logger = new Logger(EtcdSyncService.name);

  constructor(
    private readonly etcdService: EtcdService,
    private readonly thirdPartyRepository: ThirdPartyRepository,
  ) {}

  /**
   * Called when the application starts
   * Triggers the sync process
   */
  async onApplicationBootstrap(): Promise<void> {
    this.logger.log('Starting etcd sync process...');
    
    try {
      await this.syncThirdPartyKeysToEtcd();
      this.logger.log('✅ Etcd sync process completed successfully');
    } catch (error) {
      this.logger.error(`❌ Etcd sync process failed: ${error}`);
      // Don't throw error to prevent app startup failure
      // Log the error and continue
    }
  }

  /**
   * Main sync method - Dynamic comparison approach
   * 1. Load all third-party keys from database
   * 2. Load existing keys from etcd
   * 3. Compare and identify differences
   * 4. Add new keys, update changed keys, remove obsolete keys
   */
  async syncThirdPartyKeysToEtcd(): Promise<void> {
    this.logger.log('📥 Loading third-party keys from database...');
    const databaseKeys = await this.thirdPartyRepository.getAllThirdPartyKeysForSync();

    this.logger.log('📥 Loading existing keys from etcd...');
    const etcdKeys = await this.etcdService.getAllWithPrefix('/api-keys/');

    this.logger.log(`📊 Found ${databaseKeys.length} keys in database, ${Object.keys(etcdKeys).length} keys in etcd`);

    if (databaseKeys.length === 0) {
      this.logger.log('No third-party keys found in database');
      // If no database keys but etcd has keys, optionally clean up etcd
      if (Object.keys(etcdKeys).length > 0) {
        this.logger.log('🧹 Cleaning up orphaned etcd keys...');
        await this.cleanupOrphanedEtcdKeys(etcdKeys);
      }
      return;
    }

    const syncResult = await this.performDynamicSync(databaseKeys, etcdKeys);

    this.logger.log(`✅ Dynamic sync completed: ${syncResult.added} added, ${syncResult.updated} updated, ${syncResult.removed} removed, ${syncResult.errors} errors`);
  }

  /**
   * Perform dynamic sync by comparing database and etcd data
   */
  private async performDynamicSync(
    databaseKeys: Array<{
      id: string;
      provider: string;
      region: string;
      encryptedKeyData: string;
    }>,
    etcdKeys: Record<string, string>
  ): Promise<{
    added: number;
    updated: number;
    removed: number;
    errors: number;
  }> {
    let added = 0;
    let updated = 0;
    let removed = 0;
    let errors = 0;

    // Create a map of database keys for quick lookup
    const databaseKeyMap = new Map<string, any>();
    for (const key of databaseKeys) {
      const etcdPath = `/api-keys/${key.provider}/${key.region}/${key.id}`;
      databaseKeyMap.set(etcdPath, key);
    }

    // Process database keys - add new or update existing
    for (const [etcdPath, databaseKey] of databaseKeyMap) {
      try {
        const existingEtcdData = etcdKeys[etcdPath];

        if (!existingEtcdData) {
          // Key doesn't exist in etcd - add it
          await this.syncSingleKeyToEtcd(databaseKey);
          added++;
          this.logger.debug(`➕ Added new key: ${etcdPath}`);
        } else {
          // Key exists - check if it needs updating
          const needsUpdate = await this.keyNeedsUpdate(databaseKey, existingEtcdData);
          if (needsUpdate) {
            await this.syncSingleKeyToEtcd(databaseKey);
            updated++;
            this.logger.debug(`🔄 Updated key: ${etcdPath}`);
          } else {
            this.logger.debug(`✅ Key up to date: ${etcdPath}`);
          }
        }
      } catch (error) {
        errors++;
        this.logger.error(`Failed to sync key ${databaseKey.id}: ${error}`);
      }
    }

    // Remove keys that exist in etcd but not in database
    for (const etcdPath of Object.keys(etcdKeys)) {
      if (!databaseKeyMap.has(etcdPath)) {
        try {
          await this.etcdService.delete(etcdPath);
          removed++;
          this.logger.debug(`🗑️ Removed orphaned key: ${etcdPath}`);
        } catch (error) {
          errors++;
          this.logger.error(`Failed to remove orphaned key ${etcdPath}: ${error}`);
        }
      }
    }

    return { added, updated, removed, errors };
  }

  /**
   * Check if a key needs updating by comparing database and etcd data
   */
  private async keyNeedsUpdate(
    databaseKey: {
      keyHash: string;
      provider: string;
      region: string;
      encryptedKeyData: string;
    },
    etcdDataString: string
  ): Promise<boolean> {
    try {
      const etcdData = JSON.parse(etcdDataString);
      const databaseEncryptedData = JSON.parse(databaseKey.encryptedKeyData);

      // Compare critical fields that would indicate the key data has changed
      const criticalFields = [
        'encryptedKey',
        'nonce',
        'tag',
        'ephemeralPubKey',
        'keyId'
      ];

      for (const field of criticalFields) {
        if (etcdData[field] !== databaseEncryptedData[field]) {
          this.logger.debug(`Key ${databaseKey.keyHash} needs update: ${field} changed`);
          return true;
        }
      }

      // Check if status needs to be updated to active (in case it was inactive)
      if (etcdData.status !== 'active') {
        this.logger.debug(`Key ${databaseKey.keyHash} needs update: status should be active`);
        return true;
      }

      return false;
    } catch (error) {
      this.logger.warn(`Failed to compare key data for ${databaseKey.keyHash}, will update: ${error}`);
      return true; // If we can't compare, err on the side of updating
    }
  }

  /**
   * Clean up orphaned keys in etcd when no database keys exist
   */
  private async cleanupOrphanedEtcdKeys(etcdKeys: Record<string, string>): Promise<void> {
    let cleanedCount = 0;
    let errorCount = 0;

    for (const etcdPath of Object.keys(etcdKeys)) {
      try {
        await this.etcdService.delete(etcdPath);
        cleanedCount++;
        this.logger.debug(`🗑️ Cleaned up orphaned key: ${etcdPath}`);
      } catch (error) {
        errorCount++;
        this.logger.error(`Failed to clean up orphaned key ${etcdPath}: ${error}`);
      }
    }

    this.logger.log(`🧹 Cleanup completed: ${cleanedCount} keys removed, ${errorCount} errors`);
  }

  /**
   * Sync a single third-party key to etcd
   */
  private async syncSingleKeyToEtcd(key: {
    keyHash: string;
    provider: string;
    region: string;
    encryptedKeyData: string;
  }): Promise<void> {
    try {
      // Parse the encrypted key data from database
      const encryptedData = JSON.parse(key.encryptedKeyData);
      
      // Build the etcd key path: /api-keys/{provider}/{region}/{uuid}
      const etcdKey = `/api-keys/${key.provider}/${key.region}/${key.keyHash}`;
      
      // Prepare the value to store in etcd
      const etcdValue = {
        uuid: key.keyHash,
        keyId: encryptedData.keyId,
        encryptedKey: encryptedData.encryptedKey,
        nonce: encryptedData.nonce,
        tag: encryptedData.tag,
        ephemeralPubKey: encryptedData.ephemeralPubKey,
        status: 'active',
        createdAt: new Date().toISOString(),
        createdBy: 'system-sync'
      };

      // Store in etcd
      await this.etcdService.put(etcdKey, JSON.stringify(etcdValue));
      
      this.logger.debug(`✅ Synced key to etcd: ${etcdKey}`);
    } catch (error) {
      this.logger.error(`Failed to sync key ${key.keyHash} to etcd: ${error}`);
      throw error;
    }
  }

  /**
   * Manual sync trigger (for testing or admin operations)
   */
  async triggerManualSync(): Promise<{
    success: boolean;
    message: string;
    stats?: {
      added: number;
      updated: number;
      removed: number;
      errors: number;
    }
  }> {
    try {
      this.logger.log('🔄 Manual sync triggered');

      const databaseKeys = await this.thirdPartyRepository.getAllThirdPartyKeysForSync();
      const etcdKeys = await this.etcdService.getAllWithPrefix('/api-keys/');

      const syncResult = await this.performDynamicSync(databaseKeys, etcdKeys);

      return {
        success: true,
        message: 'Manual sync completed successfully',
        stats: syncResult
      };
    } catch (error) {
      this.logger.error(`Manual sync failed: ${error}`);
      return {
        success: false,
        message: `Manual sync failed: ${error instanceof Error ? error.message : String(error)}`
      };
    }
  }
}
