import { Test, TestingModule } from '@nestjs/testing';
import { Logger } from '@nestjs/common';
import { EtcdSyncService } from './etcd-sync.service';
import { EtcdService } from '@saito/executor';
import { ThirdPartyRepository } from './third-party.repository';

describe('EtcdSyncService', () => {
  let service: EtcdSyncService;
  let mockEtcdService: jest.Mocked<EtcdService>;
  let mockThirdPartyRepository: jest.Mocked<ThirdPartyRepository>;

  beforeEach(async () => {
    // Create mock services
    mockEtcdService = {
      getAllWithPrefix: jest.fn(),
      put: jest.fn(),
      delete: jest.fn(),
      deleteWithPrefix: jest.fn(),
    } as any;

    mockThirdPartyRepository = {
      getAllThirdPartyKeysForSync: jest.fn(),
    } as any;

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        EtcdSyncService,
        {
          provide: EtcdService,
          useValue: mockEtcdService,
        },
        {
          provide: ThirdPartyRepository,
          useValue: mockThirdPartyRepository,
        },
      ],
    }).compile();

    service = module.get<EtcdSyncService>(EtcdSyncService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('dynamic sync', () => {
    it('should add new keys that exist in database but not in etcd', async () => {
      // Mock database keys
      const databaseKeys = [
        {
          keyHash: 'key1',
          provider: 'openai',
          region: 'us-east-1',
          encryptedKeyData: JSON.stringify({
            keyId: 'test-key-1',
            encryptedKey: 'encrypted1',
            nonce: 'nonce1',
            tag: 'tag1',
            ephemeralPubKey: 'pubkey1'
          })
        }
      ];

      // Mock empty etcd
      const etcdKeys = {};

      mockThirdPartyRepository.getAllThirdPartyKeysForSync.mockResolvedValue(databaseKeys);
      mockEtcdService.getAllWithPrefix.mockResolvedValue(etcdKeys);
      mockEtcdService.put.mockResolvedValue();

      await service.syncThirdPartyKeysToEtcd();

      expect(mockEtcdService.put).toHaveBeenCalledWith(
        '/api-keys/openai/us-east-1/key1',
        expect.stringContaining('"keyId":"test-key-1"')
      );
    });

    it('should remove orphaned keys that exist in etcd but not in database', async () => {
      // Mock empty database
      const databaseKeys = [];

      // Mock etcd with orphaned key
      const etcdKeys = {
        '/api-keys/openai/us-east-1/orphaned-key': JSON.stringify({
          uuid: 'orphaned-key',
          keyId: 'old-key',
          status: 'active'
        })
      };

      mockThirdPartyRepository.getAllThirdPartyKeysForSync.mockResolvedValue(databaseKeys);
      mockEtcdService.getAllWithPrefix.mockResolvedValue(etcdKeys);
      mockEtcdService.delete.mockResolvedValue();

      await service.syncThirdPartyKeysToEtcd();

      expect(mockEtcdService.delete).toHaveBeenCalledWith('/api-keys/openai/us-east-1/orphaned-key');
    });

    it('should update keys when encryption data has changed', async () => {
      const databaseKeys = [
        {
          keyHash: 'key1',
          provider: 'openai',
          region: 'us-east-1',
          encryptedKeyData: JSON.stringify({
            keyId: 'test-key-1',
            encryptedKey: 'new-encrypted-data',
            nonce: 'new-nonce',
            tag: 'new-tag',
            ephemeralPubKey: 'new-pubkey'
          })
        }
      ];

      const etcdKeys = {
        '/api-keys/openai/us-east-1/key1': JSON.stringify({
          uuid: 'key1',
          keyId: 'test-key-1',
          encryptedKey: 'old-encrypted-data',
          nonce: 'old-nonce',
          tag: 'old-tag',
          ephemeralPubKey: 'old-pubkey',
          status: 'active'
        })
      };

      mockThirdPartyRepository.getAllThirdPartyKeysForSync.mockResolvedValue(databaseKeys);
      mockEtcdService.getAllWithPrefix.mockResolvedValue(etcdKeys);
      mockEtcdService.put.mockResolvedValue();

      await service.syncThirdPartyKeysToEtcd();

      expect(mockEtcdService.put).toHaveBeenCalledWith(
        '/api-keys/openai/us-east-1/key1',
        expect.stringContaining('"encryptedKey":"new-encrypted-data"')
      );
    });

    it('should not update keys when data is identical', async () => {
      const encryptedData = {
        keyId: 'test-key-1',
        encryptedKey: 'same-encrypted-data',
        nonce: 'same-nonce',
        tag: 'same-tag',
        ephemeralPubKey: 'same-pubkey'
      };

      const databaseKeys = [
        {
          keyHash: 'key1',
          provider: 'openai',
          region: 'us-east-1',
          encryptedKeyData: JSON.stringify(encryptedData)
        }
      ];

      const etcdKeys = {
        '/api-keys/openai/us-east-1/key1': JSON.stringify({
          uuid: 'key1',
          ...encryptedData,
          status: 'active',
          createdAt: '2023-01-01T00:00:00.000Z',
          createdBy: 'system-sync'
        })
      };

      mockThirdPartyRepository.getAllThirdPartyKeysForSync.mockResolvedValue(databaseKeys);
      mockEtcdService.getAllWithPrefix.mockResolvedValue(etcdKeys);

      await service.syncThirdPartyKeysToEtcd();

      // Should not call put since data is identical
      expect(mockEtcdService.put).not.toHaveBeenCalled();
    });
  });

  describe('getSyncStatus', () => {
    it('should return detailed sync status', async () => {
      const databaseKeys = [
        {
          keyHash: 'key1',
          provider: 'openai',
          region: 'us-east-1',
          encryptedKeyData: '{}'
        },
        {
          keyHash: 'key2',
          provider: 'openai',
          region: 'us-west-1',
          encryptedKeyData: '{}'
        }
      ];

      const etcdKeys = {
        '/api-keys/openai/us-east-1/key1': '{}',
        '/api-keys/openai/us-west-1/orphaned-key': '{}'
      };

      mockThirdPartyRepository.getAllThirdPartyKeysForSync.mockResolvedValue(databaseKeys);
      mockEtcdService.getAllWithPrefix.mockResolvedValue(etcdKeys);

      const status = await service.getSyncStatus();

      expect(status).toEqual({
        etcdKeysCount: 2,
        databaseKeysCount: 2,
        inSync: false,
        missingInEtcd: ['/api-keys/openai/us-west-1/key2'],
        orphanedInEtcd: ['/api-keys/openai/us-west-1/orphaned-key'],
        lastChecked: expect.any(String)
      });
    });
  });
});
