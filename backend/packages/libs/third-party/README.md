# @saito/third-party

Third-party API key management module for the Saito platform.

## Features

- Third-party API key creation and management
- Integration with etcd for key storage and discovery
- Support for multiple providers (OpenAI, Anthropic, Google, Cohere)
- Key status tracking and lifecycle management

## Usage

```typescript
import { ThirdPartyModule, ThirdPartyKeyService } from '@saito/third-party';

// In your module
@Module({
  imports: [ThirdPartyModule],
  // ...
})
export class YourModule {}

// In your service
constructor(private thirdPartyKeyService: ThirdPartyKeyService) {}
```
