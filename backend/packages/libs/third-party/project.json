{"name": "lib-third-party", "$schema": "../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "packages/libs/third-party/src", "projectType": "library", "targets": {"build": {"executor": "@nx/js:tsc", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/packages/libs/third-party", "main": "packages/libs/third-party/src/index.ts", "tsConfig": "packages/libs/third-party/tsconfig.lib.json", "assets": ["packages/libs/third-party/*.md"]}}, "lint": {"executor": "@nx/eslint:lint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["packages/libs/third-party/**/*.ts", "packages/libs/third-party/package.json"]}}}, "tags": []}