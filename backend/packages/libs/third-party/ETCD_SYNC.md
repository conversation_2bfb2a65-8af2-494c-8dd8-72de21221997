# Etcd Sync Service - Dynamic Comparison Approach

## Overview

The `EtcdSyncService` has been refactored to use a **dynamic comparison approach** instead of the previous "clear and rebuild" strategy. This new approach is more efficient, safer, and provides better data consistency.

## Key Improvements

### 🔄 **Dynamic Sync vs Full Rebuild**

| Aspect | Old Approach (Full Rebuild) | New Approach (Dynamic Sync) |
|--------|------------------------------|------------------------------|
| **Data Safety** | ❌ Clears all data first | ✅ Preserves existing data |
| **Efficiency** | ❌ Rebuilds everything | ✅ Only updates what changed |
| **Downtime** | ❌ Brief data unavailability | ✅ Zero downtime |
| **Network Usage** | ❌ High (full rebuild) | ✅ Minimal (only changes) |
| **Error Recovery** | ❌ Data loss on failure | ✅ Partial updates preserved |

### 🎯 **Smart Comparison Logic**

The service now intelligently compares database and etcd data:

1. **Loads both datasets** - Database keys and existing etcd keys
2. **Identifies differences** - Missing, orphaned, or changed keys
3. **Applies minimal changes** - Only updates what's necessary
4. **Preserves existing data** - No unnecessary deletions or recreations

## Core Methods

### `syncThirdPartyKeysToEtcd()`
Main sync method that performs dynamic comparison and updates.

```typescript
// Loads data from both sources
const databaseKeys = await this.thirdPartyRepository.getAllThirdPartyKeysForSync();
const etcdKeys = await this.etcdService.getAllWithPrefix('/api-keys/');

// Performs intelligent comparison and updates
const result = await this.performDynamicSync(databaseKeys, etcdKeys);
```

### `performDynamicSync()`
Core comparison logic that:
- ➕ **Adds** new keys from database to etcd
- 🔄 **Updates** changed keys in etcd
- 🗑️ **Removes** orphaned keys from etcd

### `keyNeedsUpdate()`
Smart comparison that checks if a key needs updating by comparing:
- `encryptedKey` - The actual encrypted API key data
- `nonce` - Encryption nonce
- `tag` - Authentication tag
- `ephemeralPubKey` - Ephemeral public key
- `keyId` - Original key identifier
- `status` - Key status (should be 'active')

## API Endpoints

### GET `/third-party/sync/status`
Returns detailed sync status:

```json
{
  "success": true,
  "data": {
    "etcdKeysCount": 15,
    "databaseKeysCount": 16,
    "inSync": false,
    "missingInEtcd": ["/api-keys/openai/us-west-1/abc123"],
    "orphanedInEtcd": ["/api-keys/openai/eu-west-1/old456"],
    "lastChecked": "2024-01-15T10:30:00.000Z"
  }
}
```

### POST `/third-party/sync/manual`
Triggers dynamic sync:

```json
{
  "success": true,
  "message": "Manual sync completed successfully",
  "stats": {
    "added": 1,
    "updated": 2,
    "removed": 1,
    "errors": 0
  }
}
```

### POST `/third-party/sync/force-full`
⚠️ **Use with caution** - Clears etcd and rebuilds (legacy behavior):

```json
{
  "success": true,
  "message": "Force full resync completed successfully",
  "stats": {
    "synced": 15,
    "errors": 0
  }
}
```

### POST `/third-party/sync/key/:keyHash`
Syncs a specific key by hash:

```json
{
  "success": true,
  "message": "Key abc123 synced successfully"
}
```

## Usage Examples

### Application Startup
The service automatically runs dynamic sync on application bootstrap:

```typescript
// Automatically triggered on app start
async onApplicationBootstrap(): Promise<void> {
  await this.syncThirdPartyKeysToEtcd();
}
```

### Manual Sync Trigger
```bash
# Trigger dynamic sync via API
curl -X POST http://localhost:3000/third-party/sync/manual \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### Check Sync Status
```bash
# Check if database and etcd are in sync
curl -X GET http://localhost:3000/third-party/sync/status \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### Sync Specific Key
```bash
# Sync a specific key that might be out of sync
curl -X POST http://localhost:3000/third-party/sync/key/abc123 \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

## Benefits

### 🚀 **Performance**
- Only processes changed data
- Reduces network traffic
- Faster sync operations
- Lower resource usage

### 🛡️ **Safety**
- No data loss during sync
- Preserves existing keys
- Graceful error handling
- Partial sync recovery

### 🔍 **Observability**
- Detailed sync statistics
- Clear status reporting
- Granular error tracking
- Audit trail of changes

### 🔧 **Flexibility**
- Multiple sync strategies
- Specific key sync
- Status monitoring
- Manual intervention options

## Migration Notes

The new dynamic sync approach is **backward compatible**. Existing deployments will automatically benefit from the improved sync logic without any configuration changes.

For emergency situations, the `forceFullResync()` method preserves the old "clear and rebuild" behavior if needed.

## Testing

Comprehensive test suite covers:
- ✅ Adding new keys
- ✅ Updating changed keys  
- ✅ Removing orphaned keys
- ✅ Preserving unchanged keys
- ✅ Error handling scenarios
- ✅ Status reporting accuracy

Run tests:
```bash
npm test -- etcd-sync.service.spec.ts
```
