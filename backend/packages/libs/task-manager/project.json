{"name": "task-manager", "$schema": "../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "packages/libs/task-manager/src", "projectType": "library", "targets": {"build": {"executor": "@nx/js:tsc", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/packages/libs/task-manager", "main": "packages/libs/task-manager/src/index.ts", "tsConfig": "packages/libs/task-manager/tsconfig.lib.json", "assets": ["packages/libs/task-manager/*.md"]}}, "lint": {"executor": "@nx/eslint:lint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["packages/libs/task-manager/**/*.ts", "packages/libs/task-manager/package.json"]}}}, "tags": []}