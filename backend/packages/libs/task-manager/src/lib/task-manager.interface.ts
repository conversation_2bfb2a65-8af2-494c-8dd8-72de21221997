import {
  Task,
  CreateTaskRequest,
  ChatCompletionResponse,
  TaskMetrics,
} from '@saito/models';

/**
 * Task event listeners for streaming and completion
 */
export interface NonStreamTaskEventListeners {
  onData?: () => void;
  onComplete?: (response: ChatCompletionResponse) => void;
  onError?: (error: Error | string) => void;
}

export interface StreamTaskEventListeners {
  onData?: (chunk: ChatCompletionResponse) => void;
  onComplete?: (final: string) => void;
  onError?: (error: Error | string) => void;
}

export type TaskEventListeners = NonStreamTaskEventListeners | StreamTaskEventListeners;

/**
 * @interface TaskManager
 * @description Interface for task manager service that handles task creation and external interactions
 * TaskManager专注于与外部模块交互的逻辑，不负责Task的执行调度和内部状态管理
 */
export interface TaskManager {
  /**
   * Create a new task (不启动执行，由TaskExecutionService决定何时执行)
   * @param taskData Task data
   * @param listeners Optional event listeners
   * @returns Created task
   */
  createTask(taskData: CreateTaskRequest, listeners?: TaskEventListeners): Promise<Task>;

  /**
   * Create and start a task (创建并立即启动任务)
   * @param deviceId Device ID
   * @param model Model name
   * @returns Created task
   */
  createAndStartTask(deviceId: string, model: string): Promise<Task>;

  /**
   * Complete a task with metrics (完成任务并记录指标)
   * @param taskId Task ID
   * @param metrics Task metrics
   */
  completeTaskWithMetrics(taskId: string, metrics: TaskMetrics): Promise<void>;

  /**
   * Get a task by ID (供controller->前端使用)
   * @param taskId Task ID
   * @returns Task
   */
  getTask(taskId: string): Promise<Task | null>;

  /**
   * Get task details by ID (供controller->前端使用)
   * @param taskId Task ID
   * @returns Task details as a record or null if not found
   */
  getTaskDetails(taskId: string): Promise<Record<string, unknown> | null>;

  /**
   * Fail a task (仅在外部驱动的场景下执行，如REST API超时)
   * TaskManager直接操作TaskRepository进行状态更新
   * @param taskId Task ID
   * @param errorMessage Optional error message
   * @returns Failed task
   */
  failTask(taskId: string, errorMessage?: string): Promise<Task>;

}
