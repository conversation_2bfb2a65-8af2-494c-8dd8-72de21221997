import { Module } from '@nestjs/common';
import { TaskManagerProvider } from './task-manager.service';
import { TaskRepository } from './task-manager.repository';
import { PersistentModule } from '@saito/persistent';
import { EarningsModule } from '@saito/earnings';
import { TaskQueueModule } from './queue/task-queue.module';
import { NodeModule } from '@saito/node';
import { NodeMetricsModule } from '@saito/node-metrics';
import { TaskCoreService } from './task-core.service';
import { TaskExecutionService, TaskExecutionServiceProvider } from './task-execution.service';
import { TierAwareDispatcherService } from './tier-aware-dispatcher.service';
import { PrimaryTaskDispatchStrategy } from './strategies/primary-task-dispatch.strategy';
import { LowRankTaskDispatchStrategy } from './strategies/low-rank-task-dispatch.strategy';

/**
 * TaskManager 模块 - 简化版本
 *
 * 架构设计：
 * 1. 核心服务层: TaskCoreService (统一处理所有任务逻辑)
 * 2. 接口层: TaskManager, TaskExecutionService (对外接口)
 * 3. 队列层: TaskQueueModule (独立的队列功能)
 *
 * 设计原则：
 * - 简单直接：减少抽象层次，提高可理解性
 * - 单一核心：TaskCoreService 处理所有核心逻辑
 * - 保持兼容：外部接口保持不变
 */
@Module({
  imports: [
    PersistentModule,
    EarningsModule,
    TaskQueueModule,
    NodeModule,
    NodeMetricsModule
  ],
  providers: [
    // 主要服务
    TaskManagerProvider,
    TaskRepository,
    TaskCoreService,
    TaskExecutionServiceProvider,

    // 分层任务调度器
    TierAwareDispatcherService,
    PrimaryTaskDispatchStrategy,
    LowRankTaskDispatchStrategy,
  ],
  exports: [
    // 对外暴露的主要接口
    TaskManagerProvider,
    TaskQueueModule,
    TaskCoreService,
    TaskExecutionServiceProvider,

    // 分层任务调度器
    TierAwareDispatcherService,
    PrimaryTaskDispatchStrategy,
    LowRankTaskDispatchStrategy,
  ],
})
export class TaskManagerModule {}
