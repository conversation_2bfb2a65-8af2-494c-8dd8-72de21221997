import { Injectable, Logger } from '@nestjs/common';
import { DatabaseTransactionConnection, sql } from 'slonik';
import {
  TaskMetrics,
  TaskStatus,
  TaskIdResultSchema,
  TaskRowSchema
} from '@saito/models';
// 使用node模块中的Task类型
import { Task } from '@saito/models';
import { PersistentService } from '@saito/persistent';
import { z } from 'zod';

@Injectable()
export class TaskRepository {
  private readonly logger = new Logger(TaskRepository.name);

  constructor(private readonly persistentService: PersistentService) {}

  /**
   * Create a new task
   * @param conn Database connection
   * @param deviceId Device ID
   * @param model Model name
   * @param userId Optional user ID
   * @returns Created task
   */
  async createTask(
    conn: DatabaseTransactionConnection,
    deviceId: string,
    model: string,
    userId?: string
  ): Promise<Task> {
    try {
      

      // Build the SQL query
      let query = sql.unsafe`
        INSERT INTO saito_gateway.tasks (
          device_id,
          model,
          status,
          created_at,
          updated_at
        `;

      // Add user_id column if provided
      if (userId) {
        query = sql.unsafe`${query}, user_id`;
      }

      query = sql.unsafe`${query}) VALUES (
        ${deviceId},
        ${model},
        'pending',
        NOW(),
        NOW()
      `;

      // Add user_id value if provided
      if (userId) {
        query = sql.unsafe`${query}, ${userId}`;
      }

      query = sql.unsafe`${query}) RETURNING id`;

      // 使用zod类型校验
      const result = await conn.query(
        sql.type(TaskIdResultSchema)`${query}`
      );

      const taskId = result.rows[0].id;

      // Get the complete task object
      return await this.getTask(conn, taskId);
    } catch (error) {
      this.logger.error(`Error creating task: ${error}`);
      throw error;
    }
  }

  /**
   * Update task status to running
   * @param conn Database connection
   * @param taskId Task ID
   * @returns Updated task ID
   */
  async startTask(
    conn: DatabaseTransactionConnection,
    taskId: string
  ): Promise<string> {
    try {
      

      // Update the task status with zod type validation for the returning id
      await conn.query(
        sql.type(TaskIdResultSchema)`
          UPDATE saito_gateway.tasks
          SET status = 'running', updated_at = NOW()
          WHERE id = ${taskId}
          RETURNING id
        `
      );

      return taskId;
    } catch (error) {
      this.logger.error(`Error starting task: ${error}`);
      throw error;
    }
  }

  /**
   * Update task status to completed and save metrics
   * @param conn Database connection
   * @param taskId Task ID
   * @param metrics Task metrics
   * @returns Updated task ID
   */
  async completeTask(
    conn: DatabaseTransactionConnection,
    taskId: string,
    metrics: TaskMetrics
  ): Promise<string> {
    try {
      

      // Update the task status and metrics with zod type validation
      await conn.query(
        sql.type(TaskIdResultSchema)`
          UPDATE saito_gateway.tasks
          SET
            status = 'completed',
            total_duration = ${metrics.total_duration || 0},
            load_duration = ${metrics.load_duration || 0},
            prompt_eval_count = ${metrics.prompt_eval_count || 0},
            prompt_eval_duration = ${metrics.prompt_eval_duration || 0},
            eval_count = ${metrics.eval_count || 0},
            eval_duration = ${metrics.eval_duration || 0},
            updated_at = NOW()
          WHERE id = ${taskId}
          RETURNING id
        `
      );

      return taskId;
    } catch (error) {
      this.logger.error(`Error completing task: ${error}`);
      throw error;
    }
  }

  /**
   * Update task status (simplified for task execution service)
   * @param taskId Task ID
   * @param status New status
   * @returns Updated task ID
   */
  async updateTask(taskId: string, status: string): Promise<string> {
    try {
      this.logger.log(`Task status update requested: ${taskId} -> ${status}`);

      // For now, this is just a placeholder that logs the status change
      // The actual database update should be handled by TaskManager methods
      // that have proper database connections

      this.logger.log(`Task ${taskId} status update logged (actual DB update should be handled by TaskManager)`);
      return taskId;
    } catch (error) {
      this.logger.error(`Error logging task status update ${taskId} to ${status}: ${error}`);
      throw error;
    }
  }

  /**
   * Update task status to failed
   * @param conn Database connection
   * @param taskId Task ID
   * @returns Updated task
   */
  async failTask(
    conn: DatabaseTransactionConnection,
    taskId: string
  ): Promise<Task> {
    try {
      // Update the task status with zod type validation
      await conn.query(
        sql.type(TaskIdResultSchema)`
          UPDATE saito_gateway.tasks
          SET status = 'failed', updated_at = NOW()
          WHERE id = ${taskId}
          RETURNING id
        `
      );

      // Get the complete task object
      return await this.getTask(conn, taskId);
    } catch (error) {
      this.logger.error(`Error failing task: ${error}`);
      throw error;
    }
  }

  /**
   * Update task status to cancelled
   * @param conn Database connection
   * @param taskId Task ID
   * @returns Updated task ID
   */
  async cancelTask(
    conn: DatabaseTransactionConnection,
    taskId: string
  ): Promise<string> {
    try {
      

      // Update the task status with zod type validation
      await conn.query(
        sql.type(TaskIdResultSchema)`
          UPDATE saito_gateway.tasks
          SET status = 'cancelled', updated_at = NOW()
          WHERE id = ${taskId}
          RETURNING id
        `
      );

      return taskId;
    } catch (error) {
      this.logger.error(`Error cancelling task: ${error}`);
      throw error;
    }
  }

  /**
   * Get a task by ID
   * @param conn Database connection
   * @param taskId Task ID
   * @returns Task data or null if not found
   */
  async getTask(
    conn: DatabaseTransactionConnection,
    taskId: string
  ): Promise<Task> {
    try {
      

      // Get the task with zod type validation
      const result = await conn.query(
        sql.type(TaskRowSchema)`
          SELECT * FROM saito_gateway.tasks
          WHERE id = ${taskId}
        `
      );

      if (result.rows.length === 0) {
        return {
          id: '',
          device_id: '',
          model: '',
          status: 'pending',
          created_at: '',
          updated_at: ''
        };
      }

      // 将数据库行转换为Task类型
      const taskRow = result.rows[0];

      // 构建符合Task类型的对象
      const task: Task = {
        id: taskRow.id,
        device_id: taskRow.device_id,
        model: taskRow.model,
        status: taskRow.status as TaskStatus,
        created_at: taskRow.created_at,
        updated_at: taskRow.updated_at
      };

      if (taskRow.user_id) {
        (task as Record<string, unknown>)['user_id'] = taskRow.user_id;
      }

      if (taskRow.total_duration !== null) {
        task.total_duration = taskRow.total_duration;
      }

      if (taskRow.load_duration !== null) {
        task.load_duration = taskRow.load_duration;
      }

      if (taskRow.prompt_eval_count !== null) {
        task.prompt_eval_count = taskRow.prompt_eval_count;
      }

      if (taskRow.prompt_eval_duration !== null) {
        task.prompt_eval_duration = taskRow.prompt_eval_duration;
      }

      if (taskRow.eval_count !== null) {
        task.eval_count = taskRow.eval_count;
      }

      if (taskRow.eval_duration !== null) {
        task.eval_duration = taskRow.eval_duration;
      }

      return task;
    } catch (error) {
      this.logger.error(`Error getting task: ${error}`);
      throw error;
    }
  }

  /**
   * Get task details by ID
   * @param conn Database connection
   * @param taskId Task ID
   * @returns Task details as a record or null if not found
   */
  async getTaskDetailsById(
    conn: DatabaseTransactionConnection,
    taskId: string
  ): Promise<Record<string, unknown> | null> {
    try {
      

      // Validate taskId to prevent "Cannot read properties of undefined (reading 'trim')" error
      if (!taskId || typeof taskId !== 'string') {
        this.logger.error(`Invalid taskId: ${taskId}`);
        return null;
      }

      const result = await conn.maybeOne(sql.type(z.record(z.string(), z.unknown()))`
        SELECT * FROM saito_gateway.tasks WHERE id = ${taskId}
      `);

      return result;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      this.logger.error(`获取任务详情失败: ${errorMessage}`);
      return null;
    }
  }

  /**
   * Find tasks by filters
   */
  async findTasksByFilters(conn: DatabaseTransactionConnection, filters: any): Promise<Task[]> {
    // 简化实现，返回空数组
    return [];
  }

  /**
   * Delete task
   */
  async deleteTask(conn: DatabaseTransactionConnection, taskId: string): Promise<void> {
    // 简化实现
  }

  /**
   * Get task statistics
   */
  async getTaskStats(conn: DatabaseTransactionConnection): Promise<any> {
    // 简化实现
    return {
      totalTasks: 0,
      pendingTasks: 0,
      runningTasks: 0,
      completedTasks: 0,
      failedTasks: 0,
      cancelledTasks: 0
    };
  }

  /**
   * Cleanup old tasks
   */
  async cleanupOldTasks(conn: DatabaseTransactionConnection, daysOld: number): Promise<number> {
    // 简化实现
    return 0;
  }

  /**
   * Get task by ID (alternative method)
   */
  async getTaskById(taskId: string): Promise<Task | null> {
    try {
      return await this.persistentService.pgPool.transaction(async (conn) => {
        return await this.getTask(conn, taskId);
      });
    } catch (error) {
      return null;
    }
  }
}

export const TaskRepositoryProvider = {
  provide: 'TaskRepository',
  useClass: TaskRepository,
};