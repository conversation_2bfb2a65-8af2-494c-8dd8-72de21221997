import { Injectable, Logger } from '@nestjs/common';
import { NodeCapacityTrackerService, NodeSelectionService } from '@saito/node';
import {
  TaskDispatchStrategy,
  TaskTier,
  TaskDispatchContext,
  DispatchResult,
  DispatchStatus,
  NodeSelectionResult
} from '../tier-aware-dispatcher.interface';

/**
 * Low Rank 任务调度策略
 * 
 * 专门处理普通任务（无 API_KEY）的调度逻辑：
 * 1. 获取所有空闲节点
 * 2. 选择响应时间最近的节点
 * 3. 分配任务
 */
@Injectable()
export class LowRankTaskDispatchStrategy implements TaskDispatchStrategy {
  private readonly logger = new Logger(LowRankTaskDispatchStrategy.name);

  readonly name = 'LowRankTaskDispatchStrategy';
  readonly supportedTiers = [TaskTier.LOW_RANK];

  constructor(
    private readonly nodeCapacityTracker: NodeCapacityTrackerService,
    private readonly nodeSelectionService: NodeSelectionService
  ) {}

  /**
   * 执行 Low Rank 任务调度
   */
  async dispatch(task: any, context: TaskDispatchContext): Promise<DispatchResult> {
    const startTime = Date.now();
    
    try {
      this.logger.debug(`开始 Low Rank 任务调度: ${context.taskId}, 模型: ${context.model}`);

      // 1. 获取所有空闲节点
      const idleNodes = await this.getIdleNodes(context.model);
      
      if (idleNodes.length === 0) {
        this.logger.warn(`没有找到空闲节点，任务: ${context.taskId}`);
        return this.createFailureResult(
          DispatchStatus.NO_AVAILABLE_NODES,
          '没有找到可用的空闲节点',
          context,
          Date.now() - startTime
        );
      }

      // 2. 选择最适合的节点（优先选择最近响应的节点）
      const selectedNode = await this.selectBestIdleNode(idleNodes, context);
      
      if (!selectedNode) {
        return this.createFailureResult(
          DispatchStatus.NO_AVAILABLE_NODES,
          '无法从空闲节点中选择合适的节点',
          context,
          Date.now() - startTime
        );
      }

      // 3. 分配节点容量
      const capacityResult = await this.allocateNodeCapacity(selectedNode.deviceId, context);
      
      if (!capacityResult.success) {
        this.logger.warn(`节点容量分配失败: ${selectedNode.deviceId}, 原因: ${capacityResult.reason}`);
        
        // 尝试其他空闲节点
        return this.tryAlternativeNodes(idleNodes, selectedNode.deviceId, context, startTime);
      }

      // 4. 调度成功
      this.logger.log(`Low Rank 任务调度成功: ${context.taskId} -> ${selectedNode.deviceId}`);
      
      return {
        status: DispatchStatus.SUCCESS,
        deviceId: selectedNode.deviceId,
        message: `任务成功分配到空闲节点: ${selectedNode.deviceId}`,
        metadata: {
          tier: context.tier,
          attemptCount: context.attemptCount,
          selectedStrategy: this.name,
          nodeSelectionTime: Date.now() - startTime - 50, // 估算节点选择时间
          totalDispatchTime: Date.now() - startTime
        }
      };

    } catch (error) {
      this.logger.error(`Low Rank 任务调度失败: ${context.taskId}`, error);
      
      return this.createFailureResult(
        DispatchStatus.FAILED,
        `调度过程中发生错误: ${error instanceof Error ? error.message : String(error)}`,
        context,
        Date.now() - startTime,
        error instanceof Error ? error : new Error(String(error))
      );
    }
  }

  /**
   * 检查策略是否可用
   */
  async isAvailable(context: TaskDispatchContext): Promise<boolean> {
    try {
      // 检查是否为 Low Rank 任务
      if (context.tier !== TaskTier.LOW_RANK) {
        return false;
      }

      // 检查是否有空闲节点
      const idleNodes = await this.getIdleNodes(context.model);
      return idleNodes.length > 0;
      
    } catch (error) {
      this.logger.error('检查 Low Rank 策略可用性失败', error);
      return false;
    }
  }

  /**
   * 获取策略优先级
   */
  async getPriority(context: TaskDispatchContext): Promise<number> {
    if (context.tier !== TaskTier.LOW_RANK) {
      return 0;
    }

    // Low Rank 任务策略优先级较低
    return 50;
  }

  /**
   * 获取空闲节点列表
   */
  private async getIdleNodes(model?: string): Promise<NodeSelectionResult[]> {
    try {
      // 使用容量追踪器获取空闲节点
      const idleDeviceIds = await this.nodeCapacityTracker.getIdleNodes(model);
      
      if (idleDeviceIds.length === 0) {
        this.logger.debug('容量追踪器未找到空闲节点，尝试备选方法');
        return await this.getFallbackIdleNodes(model);
      }

      // 将设备ID转换为节点选择结果
      return idleDeviceIds.map((deviceId, index) => ({
        deviceId,
        score: 100 - index, // 简单的评分，保持原有顺序
        reason: '容量追踪器识别的空闲节点',
        metadata: {}
      }));

    } catch (error) {
      this.logger.error('获取空闲节点失败', error);
      return [];
    }
  }

  /**
   * 备选的空闲节点获取方法
   */
  private async getFallbackIdleNodes(model?: string): Promise<NodeSelectionResult[]> {
    try {
      // 使用节点选择服务获取可用节点
      const availableNodes = await this.nodeSelectionService.findAvailableNodes(model);

      if (availableNodes.length === 0) {
        return [];
      }

      // 使用现有的节点选择策略
      const bestNode = await this.nodeSelectionService.getBestNode(availableNodes, {
        strategy: 'composite',
        timeWindow: '5min'
      });

      return [{
        deviceId: bestNode.device_id,
        score: 40, // 备选方法的评分较低
        reason: '备选方法获取的可用节点',
        metadata: {
          responseTime: 0,
          successRate: 0,
          currentLoad: 0
        }
      }];

    } catch (error) {
      this.logger.error('备选空闲节点获取失败', error);
      return [];
    }
  }

  /**
   * 选择最佳空闲节点
   */
  private async selectBestIdleNode(
    candidates: NodeSelectionResult[], 
    context: TaskDispatchContext
  ): Promise<NodeSelectionResult | null> {
    if (candidates.length === 0) {
      return null;
    }

    // 对于 Low Rank 任务，简单选择第一个可用节点
    // 这样可以减少选择开销，提高调度效率
    for (const candidate of candidates) {
      const isAvailable = await this.isNodeAvailable(candidate.deviceId);
      if (isAvailable) {
        return candidate;
      }
    }

    return null;
  }

  /**
   * 检查节点是否可用
   */
  private async isNodeAvailable(deviceId: string): Promise<boolean> {
    try {
      const capacity = await this.nodeCapacityTracker.getNodeCapacity(deviceId);
      return capacity.status === 'idle';
    } catch (error) {
      this.logger.warn(`检查节点可用性失败: ${deviceId}`, error);
      return false;
    }
  }

  /**
   * 分配节点容量
   */
  private async allocateNodeCapacity(
    deviceId: string, 
    context: TaskDispatchContext
  ): Promise<{ success: boolean; reason?: string }> {
    try {
      const allocationResult = await this.nodeCapacityTracker.allocateTask({
        deviceId,
        taskId: context.taskId,
        estimatedDuration: 60000, // 预计60秒完成
        priority: 3 // 低优先级任务
      });

      if (!allocationResult.success) {
        throw new Error(allocationResult.reason || 'Task allocation failed');
      }
      return { success: true };
    } catch (error) {
      return { 
        success: false, 
        reason: error instanceof Error ? error.message : String(error)
      };
    }
  }

  /**
   * 尝试备选节点
   */
  private async tryAlternativeNodes(
    candidates: NodeSelectionResult[],
    excludeDeviceId: string,
    context: TaskDispatchContext,
    startTime: number
  ): Promise<DispatchResult> {
    const alternatives = candidates.filter(node => node.deviceId !== excludeDeviceId);
    
    for (const alternative of alternatives) {
      const capacityResult = await this.allocateNodeCapacity(alternative.deviceId, context);
      
      if (capacityResult.success) {
        this.logger.log(`使用备选节点: ${context.taskId} -> ${alternative.deviceId}`);
        
        return {
          status: DispatchStatus.SUCCESS,
          deviceId: alternative.deviceId,
          message: `任务分配到备选空闲节点: ${alternative.deviceId}`,
          metadata: {
            tier: context.tier,
            attemptCount: context.attemptCount,
            selectedStrategy: this.name,
            nodeSelectionTime: Date.now() - startTime - 50,
            totalDispatchTime: Date.now() - startTime
          }
        };
      }
    }

    return this.createFailureResult(
      DispatchStatus.CAPACITY_EXCEEDED,
      '所有候选空闲节点容量已满',
      context,
      Date.now() - startTime
    );
  }

  /**
   * 创建失败结果
   */
  private createFailureResult(
    status: DispatchStatus,
    message: string,
    context: TaskDispatchContext,
    totalTime: number,
    error?: Error
  ): DispatchResult {
    return {
      status,
      message,
      error,
      metadata: {
        tier: context.tier,
        attemptCount: context.attemptCount,
        selectedStrategy: this.name,
        nodeSelectionTime: 0,
        totalDispatchTime: totalTime
      }
    };
  }
}
