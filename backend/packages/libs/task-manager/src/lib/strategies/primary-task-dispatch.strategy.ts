import { Injectable, Logger } from '@nestjs/common';
import { NodeMetricsService } from '@saito/node-metrics';
import { NodeCapacityTrackerService, NodeSelectionService } from '@saito/node';
import {
  TaskDispatchStrategy,
  TaskTier,
  TaskDispatchContext,
  DispatchResult,
  DispatchStatus,
  NodeSelectionResult
} from '../tier-aware-dispatcher.interface';

/**
 * Primary 任务调度策略
 * 
 * 专门处理高级任务（携带 API_KEY）的调度逻辑：
 * 1. 获取5分钟窗口内表现优于平均的节点
 * 2. 从中选择响应时间最低的节点
 * 3. 如果无可用节点，回退到硬编码阈值筛选
 * 4. 检查节点容量并分配任务
 */
@Injectable()
export class PrimaryTaskDispatchStrategy implements TaskDispatchStrategy {
  private readonly logger = new Logger(PrimaryTaskDispatchStrategy.name);

  readonly name = 'PrimaryTaskDispatchStrategy';
  readonly supportedTiers = [TaskTier.PRIMARY];

  constructor(
    private readonly nodeMetricsService: NodeMetricsService,
    private readonly nodeCapacityTracker: NodeCapacityTrackerService,
    private readonly nodeSelectionService: NodeSelectionService
  ) {}

  /**
   * 执行 Primary 任务调度
   */
  async dispatch(task: any, context: TaskDispatchContext): Promise<DispatchResult> {
    const startTime = Date.now();
    
    try {
      this.logger.debug(`开始 Primary 任务调度: ${context.taskId}, 模型: ${context.model}`);

      // 1. 获取高性能节点候选列表
      const candidateNodes = await this.getHighPerformanceNodes(context.model);
      
      if (candidateNodes.length === 0) {
        this.logger.warn(`没有找到高性能节点，任务: ${context.taskId}`);
        return this.createFailureResult(
          DispatchStatus.NO_AVAILABLE_NODES,
          '没有找到符合 Primary 任务要求的高性能节点',
          context,
          Date.now() - startTime
        );
      }

      // 2. 从候选节点中选择最优节点
      const selectedNode = await this.selectOptimalNode(candidateNodes, context);
      
      if (!selectedNode) {
        return this.createFailureResult(
          DispatchStatus.NO_AVAILABLE_NODES,
          '无法从候选节点中选择最优节点',
          context,
          Date.now() - startTime
        );
      }

      // 3. 检查节点容量并分配任务
      const capacityResult = await this.allocateNodeCapacity(selectedNode.deviceId, context);
      
      if (!capacityResult.success) {
        this.logger.warn(`节点容量分配失败: ${selectedNode.deviceId}, 原因: ${capacityResult.reason}`);
        
        // 如果容量分配失败，尝试下一个候选节点
        return this.tryAlternativeNodes(candidateNodes, selectedNode.deviceId, context, startTime);
      }

      // 4. 调度成功
      this.logger.log(`Primary 任务调度成功: ${context.taskId} -> ${selectedNode.deviceId}`);
      
      return {
        status: DispatchStatus.SUCCESS,
        deviceId: selectedNode.deviceId,
        message: `任务成功分配到高性能节点: ${selectedNode.deviceId}`,
        metadata: {
          tier: context.tier,
          attemptCount: context.attemptCount,
          selectedStrategy: this.name,
          nodeSelectionTime: Date.now() - startTime - 100, // 估算节点选择时间
          totalDispatchTime: Date.now() - startTime
        }
      };

    } catch (error) {
      this.logger.error(`Primary 任务调度失败: ${context.taskId}`, error);
      
      return this.createFailureResult(
        DispatchStatus.FAILED,
        `调度过程中发生错误: ${error instanceof Error ? error.message : String(error)}`,
        context,
        Date.now() - startTime,
        error instanceof Error ? error : new Error(String(error))
      );
    }
  }

  /**
   * 检查策略是否可用
   */
  async isAvailable(context: TaskDispatchContext): Promise<boolean> {
    try {
      // 检查是否为 Primary 任务
      if (context.tier !== TaskTier.PRIMARY) {
        return false;
      }

      // 检查是否有可用的高性能节点
      const candidateNodes = await this.getHighPerformanceNodes(context.model);
      return candidateNodes.length > 0;
      
    } catch (error) {
      this.logger.error('检查 Primary 策略可用性失败', error);
      return false;
    }
  }

  /**
   * 获取策略优先级
   */
  async getPriority(context: TaskDispatchContext): Promise<number> {
    if (context.tier !== TaskTier.PRIMARY) {
      return 0;
    }

    // Primary 任务策略具有最高优先级
    return 100;
  }

  /**
   * 获取高性能节点候选列表
   */
  private async getHighPerformanceNodes(model?: string): Promise<NodeSelectionResult[]> {
    try {
      // 1. 获取所有可用节点
      const availableNodes = await this.nodeSelectionService.findAvailableNodes(model);

      if (availableNodes.length === 0) {
        this.logger.debug('没有找到可用节点');
        return [];
      }

      // 2. 为每个节点计算性能评分
      const nodeScores: Array<{
        deviceId: string;
        score: number;
        reason: string;
        metadata: any;
      }> = [];

      for (const node of availableNodes) {
        try {
          // 获取节点性能评分
          const performanceScore = await this.nodeMetricsService.getDevicePerformanceScore(
            node.device_id,
            '5min'
          );

          // 检查是否满足高性能阈值
          const meetsThresholds = await this.nodeMetricsService.isDeviceMeetingThresholds(
            node.device_id,
            0.8,    // 最小成功率 80%
            30,     // 最大响应时间 30 秒
            5,      // 最小TPS 5
            '5min'
          );

          if (meetsThresholds && performanceScore >= 60) { // 性能评分至少60分
            nodeScores.push({
              deviceId: node.device_id,
              score: performanceScore,
              reason: '5分钟窗口内高性能节点',
              metadata: {
                performanceScore,
                meetsThresholds: true
              }
            });
          }
        } catch (error) {
          this.logger.warn(`获取节点 ${node.device_id} 性能数据失败:`, error);
        }
      }

      if (nodeScores.length > 0) {
        this.logger.debug(`找到 ${nodeScores.length} 个高性能节点（基于5分钟窗口）`);
        // 按评分排序，返回前10个
        return nodeScores
          .sort((a, b) => b.score - a.score)
          .slice(0, 10);
      }

      // 2. 回退到硬编码阈值筛选
      this.logger.debug('回退到硬编码阈值筛选高性能节点');
      return await this.getFallbackHighPerformanceNodes(model);

    } catch (error) {
      this.logger.error('获取高性能节点失败', error);
      return [];
    }
  }

  /**
   * 回退的高性能节点获取方法
   */
  private async getFallbackHighPerformanceNodes(model?: string): Promise<NodeSelectionResult[]> {
    try {
      // 使用 NodeSelectionService 获取最佳节点
      const availableNodes = await this.nodeSelectionService.findAvailableNodes(model);

      if (availableNodes.length === 0) {
        return [];
      }

      // 使用现有的节点选择策略
      const bestNode = await this.nodeSelectionService.getBestNode(availableNodes, {
        strategy: 'threshold',
        minSuccessRate: 0.7,
        maxResponseTime: 60,
        timeWindow: '5min'
      });

      return [{
        deviceId: bestNode.device_id,
        score: 75,
        reason: '回退策略选择的高性能节点',
        metadata: {
          responseTime: 0,
          successRate: 0,
          currentLoad: 0
        }
      }];

    } catch (error) {
      this.logger.error('回退高性能节点获取失败', error);
      return [];
    }
  }

  /**
   * 从候选节点中选择最优节点
   */
  private async selectOptimalNode(
    candidates: NodeSelectionResult[], 
    context: TaskDispatchContext
  ): Promise<NodeSelectionResult | null> {
    if (candidates.length === 0) {
      return null;
    }

    // 按评分排序，选择最高分的节点
    const sortedCandidates = candidates.sort((a, b) => b.score - a.score);
    
    // 检查最优节点是否空闲
    const topCandidate = sortedCandidates[0];
    const isIdle = await this.isNodeIdle(topCandidate.deviceId);
    
    if (isIdle) {
      return topCandidate;
    }

    // 如果最优节点不空闲，选择第一个空闲的节点
    for (const candidate of sortedCandidates) {
      const idle = await this.isNodeIdle(candidate.deviceId);
      if (idle) {
        return candidate;
      }
    }

    return null;
  }

  /**
   * 检查节点是否空闲
   */
  private async isNodeIdle(deviceId: string): Promise<boolean> {
    try {
      const capacity = await this.nodeCapacityTracker.getNodeCapacity(deviceId);
      return capacity.status === 'idle';
    } catch (error) {
      this.logger.warn(`检查节点空闲状态失败: ${deviceId}`, error);
      return false;
    }
  }

  /**
   * 分配节点容量
   */
  private async allocateNodeCapacity(
    deviceId: string, 
    context: TaskDispatchContext
  ): Promise<{ success: boolean; reason?: string }> {
    try {
      const allocationResult = await this.nodeCapacityTracker.allocateTask({
        deviceId,
        taskId: context.taskId,
        estimatedDuration: 60000, // 预计60秒完成
        priority: 5
      });

      if (!allocationResult.success) {
        throw new Error(allocationResult.reason || 'Task allocation failed');
      }
      return { success: true };
    } catch (error) {
      return { 
        success: false, 
        reason: error instanceof Error ? error.message : String(error)
      };
    }
  }

  /**
   * 尝试备选节点
   */
  private async tryAlternativeNodes(
    candidates: NodeSelectionResult[],
    excludeDeviceId: string,
    context: TaskDispatchContext,
    startTime: number
  ): Promise<DispatchResult> {
    const alternatives = candidates.filter(node => node.deviceId !== excludeDeviceId);
    
    for (const alternative of alternatives) {
      const capacityResult = await this.allocateNodeCapacity(alternative.deviceId, context);
      
      if (capacityResult.success) {
        this.logger.log(`使用备选节点: ${context.taskId} -> ${alternative.deviceId}`);
        
        return {
          status: DispatchStatus.SUCCESS,
          deviceId: alternative.deviceId,
          message: `任务分配到备选高性能节点: ${alternative.deviceId}`,
          metadata: {
            tier: context.tier,
            attemptCount: context.attemptCount,
            selectedStrategy: this.name,
            nodeSelectionTime: Date.now() - startTime - 100,
            totalDispatchTime: Date.now() - startTime
          }
        };
      }
    }

    return this.createFailureResult(
      DispatchStatus.CAPACITY_EXCEEDED,
      '所有候选高性能节点容量已满',
      context,
      Date.now() - startTime
    );
  }

  /**
   * 计算节点评分
   */
  private calculateNodeScore(node: any): number {
    let score = 100;
    
    // 基于成功率的评分
    if (node.successRate) {
      score += (node.successRate - 0.8) * 100; // 成功率超过80%的部分加分
    }
    
    // 基于响应时间的评分（响应时间越低分数越高）
    if (node.avgResponseTime) {
      score -= Math.min(node.avgResponseTime / 1000, 50); // 响应时间惩罚，最多扣50分
    }
    
    return Math.max(score, 0);
  }

  /**
   * 创建失败结果
   */
  private createFailureResult(
    status: DispatchStatus,
    message: string,
    context: TaskDispatchContext,
    totalTime: number,
    error?: Error
  ): DispatchResult {
    return {
      status,
      message,
      error,
      metadata: {
        tier: context.tier,
        attemptCount: context.attemptCount,
        selectedStrategy: this.name,
        nodeSelectionTime: 0,
        totalDispatchTime: totalTime
      }
    };
  }
}
