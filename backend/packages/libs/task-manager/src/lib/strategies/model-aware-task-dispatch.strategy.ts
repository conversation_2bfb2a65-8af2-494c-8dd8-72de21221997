/**
 * 模型感知任务调度策略基类
 * 
 * 为任务调度策略提供模型兼容性检查功能
 */

import { Injectable, Logger } from '@nestjs/common';
import { ModelCompatibilityService, TaskExecutionMonitorService } from '@sight-ai/model-compatibility';
import {
  TaskDispatchStrategy,
  TaskDispatchContext,
  DispatchResult,
  DispatchStatus,
  NodeSelectionResult
} from '../tier-aware-dispatcher.interface';

/**
 * 模型感知调度策略的抽象基类
 */
@Injectable()
export abstract class ModelAwareTaskDispatchStrategy implements TaskDispatchStrategy {
  protected readonly logger = new Logger(this.constructor.name);

  abstract readonly name: string;
  abstract readonly supportedTiers: any[];

  constructor(
    protected readonly modelCompatibilityService?: ModelCompatibilityService,
    protected readonly taskExecutionMonitor?: TaskExecutionMonitorService
  ) {}

  /**
   * 抽象方法：子类需要实现具体的调度逻辑
   */
  abstract dispatch(task: any, context: TaskDispatchContext): Promise<DispatchResult>;

  /**
   * 抽象方法：检查策略是否可用
   */
  abstract isAvailable(context: TaskDispatchContext): Promise<boolean>;

  /**
   * 抽象方法：获取策略优先级
   */
  abstract getPriority(context: TaskDispatchContext): Promise<number>;

  /**
   * 过滤出与模型兼容的节点
   */
  protected async filterCompatibleNodes(
    nodes: NodeSelectionResult[],
    modelName?: string
  ): Promise<NodeSelectionResult[]> {
    if (!modelName || !this.modelCompatibilityService) {
      // 没有模型或兼容性服务，返回所有节点
      return nodes;
    }

    try {
      const compatibleNodes: NodeSelectionResult[] = [];

      // 并行检查所有节点的兼容性
      const compatibilityChecks = nodes.map(async (nodeResult) => {
        try {
          const isCompatible = await this.modelCompatibilityService!.isModelAvailable(
            nodeResult.deviceId,
            modelName
          );
          return { nodeResult, isCompatible };
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : String(error);
          this.logger.warn(`检查节点 ${nodeResult.deviceId} 兼容性失败: ${errorMessage}`);
          // 检查失败时默认认为兼容
          return { nodeResult, isCompatible: true };
        }
      });

      const results = await Promise.all(compatibilityChecks);

      // 收集兼容的节点
      for (const { nodeResult, isCompatible } of results) {
        if (isCompatible) {
          compatibleNodes.push(nodeResult);
        } else {
          this.logger.debug(`节点 ${nodeResult.deviceId} 与模型 ${modelName} 不兼容`);
        }
      }

      this.logger.debug(
        `模型 ${modelName} 兼容性过滤: ${nodes.length} -> ${compatibleNodes.length} 个节点`
      );

      return compatibleNodes;

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      const errorStack = error instanceof Error ? error.stack : undefined;
      this.logger.error(`模型兼容性过滤失败: ${errorMessage}`, errorStack);
      // 出错时返回所有节点，确保系统可用性
      return nodes;
    }
  }

  /**
   * 记录任务执行开始
   */
  protected async recordTaskStart(taskId: string, deviceId: string, model?: string) {
    if (this.taskExecutionMonitor) {
      try {
        await this.taskExecutionMonitor.recordTaskStart(taskId, deviceId, model);
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        this.logger.warn(`记录任务开始失败: ${errorMessage}`);
      }
    }
  }

  /**
   * 记录任务执行成功
   */
  protected async recordTaskSuccess(taskId: string, deviceId: string, model?: string, executionTime?: number) {
    if (this.taskExecutionMonitor) {
      try {
        await this.taskExecutionMonitor.recordTaskSuccess(taskId, deviceId, model, executionTime);
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        this.logger.warn(`记录任务成功失败: ${errorMessage}`);
      }
    }
  }

  /**
   * 记录任务执行失败
   */
  protected async recordTaskFailure(taskId: string, deviceId: string, model?: string, error?: string) {
    if (this.taskExecutionMonitor) {
      try {
        await this.taskExecutionMonitor.recordTaskFailure(taskId, deviceId, model, error);
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        this.logger.warn(`记录任务失败失败: ${errorMessage}`);
      }
    }
  }

  /**
   * 创建成功的调度结果
   */
  protected createSuccessResult(
    deviceId: string,
    context: TaskDispatchContext,
    nodeSelectionTime: number,
    totalDispatchTime: number,
    selectedStrategy: string,
    message?: string
  ): DispatchResult {
    return {
      status: DispatchStatus.SUCCESS,
      deviceId,
      message: message || `任务成功分配到设备 ${deviceId}`,
      metadata: {
        tier: context.tier,
        attemptCount: context.attemptCount,
        selectedStrategy,
        nodeSelectionTime,
        totalDispatchTime
      }
    };
  }

  /**
   * 创建失败的调度结果
   */
  protected createFailureResult(
    status: DispatchStatus,
    context: TaskDispatchContext,
    totalDispatchTime: number,
    message?: string,
    error?: Error,
    retryAfter?: number
  ): DispatchResult {
    return {
      status,
      message: message || `任务调度失败: ${status}`,
      error,
      retryAfter,
      metadata: {
        tier: context.tier,
        attemptCount: context.attemptCount,
        selectedStrategy: this.name,
        nodeSelectionTime: 0,
        totalDispatchTime
      }
    };
  }

  /**
   * 增强节点选择结果，添加兼容性信息
   */
  protected async enhanceNodeResults(
    results: NodeSelectionResult[],
    modelName?: string
  ): Promise<NodeSelectionResult[]> {
    if (!modelName || !this.modelCompatibilityService) {
      return results;
    }

    try {
      const enhancedResults: NodeSelectionResult[] = [];

      for (const result of results) {
        try {
          const compatibilityStatus = await this.modelCompatibilityService.getCompatibilityStatus(
            result.deviceId,
            modelName
          );

          let adjustedScore = result.score;
          let compatibilityReason = '';

          if (compatibilityStatus) {
            const { successCount, failureCount } = compatibilityStatus;
            
            // 根据历史表现调整评分
            if (successCount > 0) {
              const successBonus = Math.min(10, successCount * 2);
              adjustedScore += successBonus;
              compatibilityReason = `成功${successCount}次(+${successBonus})`;
            }
            
            if (failureCount > 0) {
              const failurePenalty = Math.min(5, failureCount);
              adjustedScore -= failurePenalty;
              compatibilityReason += (compatibilityReason ? '，' : '') + `失败${failureCount}次(-${failurePenalty})`;
            }
          }

          enhancedResults.push({
            ...result,
            score: Math.max(0, adjustedScore),
            reason: compatibilityReason ? `${result.reason}；兼容性：${compatibilityReason}` : result.reason
          });

        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : String(error);
          this.logger.warn(`增强节点 ${result.deviceId} 结果失败: ${errorMessage}`);
          enhancedResults.push(result);
        }
      }

      // 按调整后的评分重新排序
      enhancedResults.sort((a, b) => b.score - a.score);

      return enhancedResults;

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      const errorStack = error instanceof Error ? error.stack : undefined;
      this.logger.error(`增强节点结果失败: ${errorMessage}`, errorStack);
      return results;
    }
  }
}