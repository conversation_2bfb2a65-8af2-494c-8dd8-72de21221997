import { Injectable, Logger } from '@nestjs/common';
import {
  TierAwareDispatcher,
  TaskDispatchStrategy,
  TaskDispatchContext,
  DispatchResult,
  DispatchStatus,
  DispatchStats,
  TaskTier,
  DispatcherConfig
} from './tier-aware-dispatcher.interface';
import { PrimaryTaskDispatchStrategy } from './strategies/primary-task-dispatch.strategy';
import { LowRankTaskDispatchStrategy } from './strategies/low-rank-task-dispatch.strategy';

/**
 * 分层任务调度器实现
 * 
 * 协调不同的调度策略，实现基于任务等级的智能调度
 */
@Injectable()
export class TierAwareDispatcherService implements TierAwareDispatcher {
  private readonly logger = new Logger(TierAwareDispatcherService.name);
  
  private readonly strategies: Map<TaskTier, TaskDispatchStrategy[]> = new Map();
  private readonly stats: DispatchStats = {
    totalDispatches: 0,
    successfulDispatches: 0,
    failedDispatches: 0,
    averageDispatchTime: 0,
    primaryTaskDispatches: 0,
    lowRankTaskDispatches: 0,
    retryCount: 0,
    nodeUtilization: {}
  };

  private readonly config: DispatcherConfig = {
    maxRetries: 3,
    retryDelayMs: 1000,
    dispatchTimeoutMs: 30000,
    enableMetrics: true,
    primaryTaskConfig: {
      minSuccessRate: 0.8,
      maxResponseTime: 30000,
      preferredNodeCount: 5
    },
    lowRankTaskConfig: {
      maxWaitTime: 60000,
      fallbackEnabled: true
    }
  };

  constructor(
    private readonly primaryStrategy: PrimaryTaskDispatchStrategy,
    private readonly lowRankStrategy: LowRankTaskDispatchStrategy
  ) {
    this.initializeStrategies();
  }

  /**
   * 初始化调度策略
   */
  private initializeStrategies(): void {
    // 注册 Primary 任务策略
    this.strategies.set(TaskTier.PRIMARY, [this.primaryStrategy]);
    
    // 注册 Low Rank 任务策略
    this.strategies.set(TaskTier.LOW_RANK, [this.lowRankStrategy]);
    
    this.logger.log('分层任务调度器初始化完成');
  }

  /**
   * 调度任务到合适的节点
   */
  async dispatch(task: any, context: TaskDispatchContext): Promise<DispatchResult> {
    const startTime = Date.now();
    
    try {
      this.logger.debug(`开始调度任务: ${context.taskId}, 等级: ${context.tier}, 尝试次数: ${context.attemptCount}`);
      
      // 更新统计信息
      this.updateDispatchStats(context.tier, 'start');
      
      // 获取适用的调度策略
      const strategies = this.getStrategiesForTier(context.tier);
      
      if (strategies.length === 0) {
        const result = this.createFailureResult(
          DispatchStatus.FAILED,
          `没有找到适用于任务等级 ${context.tier} 的调度策略`,
          context,
          Date.now() - startTime
        );
        
        this.updateDispatchStats(context.tier, 'failed');
        return result;
      }

      // 按优先级排序策略
      const sortedStrategies = await this.sortStrategiesByPriority(strategies, context);
      
      // 尝试每个策略直到成功
      for (const strategy of sortedStrategies) {
        try {
          // 检查策略是否可用
          const isAvailable = await strategy.isAvailable(context);
          
          if (!isAvailable) {
            this.logger.debug(`策略 ${strategy.name} 不可用，跳过`);
            continue;
          }

          // 执行调度
          const result = await this.executeStrategyWithTimeout(strategy, task, context);
          
          if (result.status === DispatchStatus.SUCCESS) {
            this.logger.log(`任务调度成功: ${context.taskId} -> ${result.deviceId} (策略: ${strategy.name})`);
            
            // 更新统计信息
            this.updateDispatchStats(context.tier, 'success', result.deviceId);
            this.updateAverageDispatchTime(Date.now() - startTime);
            
            return result;
          } else {
            this.logger.warn(`策略 ${strategy.name} 调度失败: ${result.message}`);
          }
          
        } catch (strategyError) {
          this.logger.error(`策略 ${strategy.name} 执行异常:`, strategyError);
        }
      }

      // 所有策略都失败了
      const result = this.createFailureResult(
        DispatchStatus.NO_AVAILABLE_NODES,
        '所有调度策略都无法找到合适的节点',
        context,
        Date.now() - startTime
      );
      
      this.updateDispatchStats(context.tier, 'failed');
      return result;

    } catch (error) {
      this.logger.error(`任务调度过程中发生错误: ${context.taskId}`, error);
      
      const result = this.createFailureResult(
        DispatchStatus.FAILED,
        `调度过程中发生错误: ${error instanceof Error ? error.message : String(error)}`,
        context,
        Date.now() - startTime,
        error instanceof Error ? error : new Error(String(error))
      );
      
      this.updateDispatchStats(context.tier, 'failed');
      return result;
    }
  }

  /**
   * 获取调度统计信息
   */
  async getDispatchStats(): Promise<DispatchStats> {
    return { ...this.stats };
  }

  /**
   * 重置调度器状态
   */
  async reset(): Promise<void> {
    // 重置统计信息
    this.stats.totalDispatches = 0;
    this.stats.successfulDispatches = 0;
    this.stats.failedDispatches = 0;
    this.stats.averageDispatchTime = 0;
    this.stats.primaryTaskDispatches = 0;
    this.stats.lowRankTaskDispatches = 0;
    this.stats.retryCount = 0;
    this.stats.nodeUtilization = {};
    
    this.logger.log('分层任务调度器状态已重置');
  }

  /**
   * 获取指定等级的调度策略
   */
  private getStrategiesForTier(tier: TaskTier): TaskDispatchStrategy[] {
    return this.strategies.get(tier) || [];
  }

  /**
   * 按优先级排序策略
   */
  private async sortStrategiesByPriority(
    strategies: TaskDispatchStrategy[], 
    context: TaskDispatchContext
  ): Promise<TaskDispatchStrategy[]> {
    const strategiesWithPriority = await Promise.all(
      strategies.map(async (strategy) => ({
        strategy,
        priority: await strategy.getPriority(context)
      }))
    );

    return strategiesWithPriority
      .sort((a, b) => b.priority - a.priority)
      .map(item => item.strategy);
  }

  /**
   * 带超时的策略执行
   */
  private async executeStrategyWithTimeout(
    strategy: TaskDispatchStrategy,
    task: any,
    context: TaskDispatchContext
  ): Promise<DispatchResult> {
    const timeout = context.timeout || this.config.dispatchTimeoutMs;
    
    return Promise.race([
      strategy.dispatch(task, context),
      new Promise<DispatchResult>((_, reject) => {
        setTimeout(() => {
          reject(new Error(`策略 ${strategy.name} 执行超时 (${timeout}ms)`));
        }, timeout);
      })
    ]);
  }

  /**
   * 更新调度统计信息
   */
  private updateDispatchStats(tier: TaskTier, event: 'start' | 'success' | 'failed', deviceId?: string): void {
    if (!this.config.enableMetrics) {
      return;
    }

    switch (event) {
      case 'start':
        this.stats.totalDispatches++;
        if (tier === TaskTier.PRIMARY) {
          this.stats.primaryTaskDispatches++;
        } else {
          this.stats.lowRankTaskDispatches++;
        }
        break;
        
      case 'success':
        this.stats.successfulDispatches++;
        if (deviceId) {
          this.stats.nodeUtilization[deviceId] = (this.stats.nodeUtilization[deviceId] || 0) + 1;
        }
        break;
        
      case 'failed':
        this.stats.failedDispatches++;
        break;
    }
  }

  /**
   * 更新平均调度时间
   */
  private updateAverageDispatchTime(dispatchTime: number): void {
    if (!this.config.enableMetrics) {
      return;
    }

    const totalTime = this.stats.averageDispatchTime * (this.stats.successfulDispatches - 1) + dispatchTime;
    this.stats.averageDispatchTime = totalTime / this.stats.successfulDispatches;
  }

  /**
   * 创建失败结果
   */
  private createFailureResult(
    status: DispatchStatus,
    message: string,
    context: TaskDispatchContext,
    totalTime: number,
    error?: Error
  ): DispatchResult {
    return {
      status,
      message,
      error,
      metadata: {
        tier: context.tier,
        attemptCount: context.attemptCount,
        selectedStrategy: 'TierAwareDispatcherService',
        nodeSelectionTime: 0,
        totalDispatchTime: totalTime
      }
    };
  }
}
