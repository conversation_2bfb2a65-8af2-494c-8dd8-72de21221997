import { Injectable, Logger } from '@nestjs/common';
import { TaskEventListeners } from './task-manager.interface';
import {
  Task,
  TaskMetrics
} from '@saito/models';
import { TaskCoreService } from './task-core.service';

/**
 * TaskExecutionService - 向后兼容的任务执行服务
 * 职责：为外部模块提供向后兼容的接口，内部委托给 TaskCoreService
 * 设计原则：保持现有接口不变，简化迁移过程
 */
@Injectable()
export class TaskExecutionService {
  private readonly logger = new Logger(TaskExecutionService.name);

  constructor(
    private readonly taskCoreService: TaskCoreService
  ) { }

  /**
   * 调度任务执行
   * @param task 任务对象
   * @param listeners 事件监听器
   */
  async scheduleTaskExecution(task: Task, listeners?: TaskEventListeners): Promise<void> {
    try {
      this.logger.log(`Scheduling execution for task ${task.id}`);
      await this.taskCoreService.scheduleTask(task, listeners);
      this.logger.log(`Task ${task.id} scheduled successfully`);
    } catch (error) {
      this.logger.error(`Failed to schedule task execution: ${error instanceof Error ? error.message : String(error)}`);
      throw error;
    }
  }

  /**
   * 完成任务
   * @param taskId 任务ID
   * @param metrics 任务指标
   */
  async completeTask(taskId: string, metrics: TaskMetrics): Promise<void> {
    try {
      this.logger.log(`Completing task ${taskId}`);
      await this.taskCoreService.completeTask(taskId, metrics);
      this.logger.log(`Task ${taskId} completed successfully`);
    } catch (error) {
      this.logger.error(`Failed to complete task: ${error instanceof Error ? error.message : String(error)}`);
      throw error;
    }
  }

  /**
   * 失败任务
   * @param taskId 任务ID
   * @param errorMessage 错误信息
   */
  async failTask(taskId: string, errorMessage?: string): Promise<void> {
    try {
      this.logger.log(`Failing task ${taskId}${errorMessage ? `: ${errorMessage}` : ''}`);
      await this.taskCoreService.failTaskExternally(taskId, errorMessage);
      this.logger.log(`Task ${taskId} failed successfully`);
    } catch (error) {
      this.logger.error(`Failed to fail task: ${error instanceof Error ? error.message : String(error)}`);
      throw error;
    }
  }

  /**
   * 更新任务状态
   * @param taskId 任务ID
   * @param status 新状态
   */
  async updateTaskStatus(taskId: string, status: string): Promise<void> {
    try {
      this.logger.log(`Updating task ${taskId} status to ${status}`);
      await this.taskCoreService.updateTaskStatus(taskId, status as any);
      this.logger.log(`Task ${taskId} status updated successfully`);
    } catch (error) {
      this.logger.error(`Failed to update task status: ${error instanceof Error ? error.message : String(error)}`);
      throw error;
    }
  }

  /**
   * 获取执行统计信息
   */
  async getExecutionStats(): Promise<Record<string, unknown>> {
    try {
      // 这里可以返回一些基本的统计信息
      // 目前返回空对象，可以根据需要扩展
      return {
        message: 'Execution stats not implemented yet'
      };
    } catch (error) {
      this.logger.error(`Failed to get execution stats: ${error instanceof Error ? error.message : String(error)}`);
      throw error;
    }
  }
}

export const TaskExecutionServiceProvider = {
  provide: 'TaskExecutionService',
  useClass: TaskExecutionService,
};
