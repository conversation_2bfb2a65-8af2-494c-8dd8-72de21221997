import { Injectable, Logger, Inject } from '@nestjs/common';
import { TaskEventListeners, TaskManager as TaskManagerInterface } from './task-manager.interface';
import {
  CreateTaskRequest,
  Task,
  TaskMetrics
} from '@saito/models';
import { TaskCoreService } from './task-core.service';
import { NodeRepository } from '@saito/node';

/**
 * TaskManager 主服务 - 简化版本
 * 职责：作为外部接口的统一入口，直接使用核心服务
 * 设计原则：简单直接，减少抽象层次
 */
@Injectable()
export class TaskManagerImpl implements TaskManagerInterface {
  private readonly logger = new Logger(TaskManagerImpl.name);

  constructor(
    private readonly taskCoreService: TaskCoreService,
    @Inject(NodeRepository)
    private readonly nodeRepository: NodeRepository
  ) { }

  /**
   * 创建任务并调度执行
   */
  async createTask(taskData: CreateTaskRequest, listeners?: TaskEventListeners): Promise<Task> {
    try {
      this.logger.log(`Creating task for device ${taskData.device_id} with model ${taskData.model}${taskData.api_key_info ? ' (with API key)' : ' (anonymous)'}`);

      // 创建任务
      const task = await this.taskCoreService.createTask(taskData);

      // 调度执行（传递原始请求数据用于分层判断）
      await this.taskCoreService.scheduleTask(task, listeners, taskData);

      this.logger.log(`Task ${task.id} created and scheduled successfully using tier-aware dispatcher`);
      return task;
    } catch (error) {
      this.logger.error(`Failed to create task: ${error instanceof Error ? error.message : String(error)}`);
      throw error;
    }
  }

  /**
   * 外部触发任务失败（如REST API超时）
   */
  async failTask(taskId: string, errorMessage?: string): Promise<Task> {
    try {
      this.logger.log(`External failure request for task ${taskId}${errorMessage ? `: ${errorMessage}` : ''}`);

      const task = await this.taskCoreService.failTaskExternally(taskId, errorMessage);

      this.logger.log(`Task ${taskId} failed externally`);
      return task;
    } catch (error) {
      this.logger.error(`Failed to fail task externally: ${error instanceof Error ? error.message : String(error)}`);
      throw error;
    }
  }

  /**
   * 获取任务基础信息
   */
  async getTask(taskId: string): Promise<Task> {
    try {
      const task = await this.taskCoreService.getTask(taskId);
      if (!task) {
        throw new Error(`Task ${taskId} not found`);
      }
      return task;
    } catch (error) {
      this.logger.error(`Failed to get task: ${error instanceof Error ? error.message : String(error)}`);
      throw error;
    }
  }

  /**
   * 创建并立即启动任务
   */
  async createAndStartTask(deviceId: string, model: string): Promise<Task> {
    try {
      this.logger.log(`Creating and starting task for device ${deviceId} with model ${model}`);

      // 查询设备对应的用户ID
      let userId: string | undefined;
      try {
        userId = await this.nodeRepository.getUserOfDevice(deviceId);
        this.logger.log(`Found user ${userId} for device ${deviceId}`);
      } catch (error) {
        this.logger.warn(`Could not find user for device ${deviceId}: ${error instanceof Error ? error.message : String(error)}`);
        // 如果找不到用户，设为 undefined，任务仍然可以创建
        userId = undefined;
      }

      const taskData = {
        device_id: deviceId,
        model: model,
        user_id: userId
      };

      // 创建任务
      const task = await this.taskCoreService.createTask(taskData);

      // 调度执行
      await this.taskCoreService.scheduleTask(task);

      this.logger.log(`Task ${task.id} created and started successfully`);
      return task;
    } catch (error) {
      this.logger.error(`Failed to create and start task: ${error instanceof Error ? error.message : String(error)}`);
      throw error;
    }
  }

  /**
   * 完成任务并记录指标
   */
  async completeTaskWithMetrics(taskId: string, metrics: TaskMetrics): Promise<void> {
    try {
      this.logger.log(`Completing task ${taskId} with metrics`);
      await this.taskCoreService.completeTask(taskId, metrics);
      this.logger.log(`Task ${taskId} completed successfully`);
    } catch (error) {
      this.logger.error(`Failed to complete task with metrics: ${error instanceof Error ? error.message : String(error)}`);
      throw error;
    }
  }

  /**
   * 获取任务详细信息
   */
  async getTaskDetails(taskId: string): Promise<Record<string, unknown> | null> {
    try {
      const taskInfo = await this.taskCoreService.getTaskInfo(taskId);
      if (!taskInfo) {
        return null;
      }

      // 返回完整的任务信息
      return {
        id: taskInfo.id,
        status: taskInfo.status,
        device_id: taskInfo.device_id,
        model: taskInfo.model,
        created_at: taskInfo.created_at,
        updated_at: taskInfo.updated_at,
        user_id: taskInfo.user_id,
        total_duration: taskInfo.total_duration,
        load_duration: taskInfo.load_duration,
        prompt_eval_count: taskInfo.prompt_eval_count,
        prompt_eval_duration: taskInfo.prompt_eval_duration,
        eval_count: taskInfo.eval_count,
        eval_duration: taskInfo.eval_duration,
        // 扩展信息
        execution_status: taskInfo.execution_status,
        assigned_node: taskInfo.assigned_node,
        error_message: taskInfo.error_message,
        started_at: taskInfo.started_at,
        completed_at: taskInfo.completed_at
      };
    } catch (error) {
      this.logger.error(`Failed to get task details: ${error instanceof Error ? error.message : String(error)}`);
      return null;
    }
  }



}

export const TaskManagerProvider = {
  provide: 'TaskManager',
  useClass: TaskManagerImpl,
};
