import { Module } from '@nestjs/common';
import { RedisModule } from '@saito/redis';
import { PersistentModule } from '@saito/persistent';
import { SimpleTaskQueue } from './simple-task-queue.service';

/**
 * 简化的任务队列模块
 * 职责：提供统一的队列服务，包括Redis操作和数据库同步
 */
@Module({
  imports: [
    RedisModule,
    PersistentModule
  ],
  providers: [
    SimpleTaskQueue,
  ],
  exports: [
    SimpleTaskQueue,
  ],
})
export class TaskQueueModule {}


