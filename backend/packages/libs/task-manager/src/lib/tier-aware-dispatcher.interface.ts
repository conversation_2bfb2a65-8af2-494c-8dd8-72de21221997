/**
 * 分层任务调度器接口和类型定义
 * 
 * 实现基于任务等级的智能调度策略
 */

/**
 * 任务等级枚举
 */
export enum TaskTier {
  PRIMARY = 'primary',    // 高级任务，携带 API_KEY，优先分配给高性能节点
  LOW_RANK = 'low_rank'   // 普通任务，无 API_KEY，分配给可用节点
}

/**
 * 调度结果状态
 */
export enum DispatchStatus {
  SUCCESS = 'success',           // 调度成功
  NO_AVAILABLE_NODES = 'no_available_nodes',  // 无可用节点
  CAPACITY_EXCEEDED = 'capacity_exceeded',    // 容量超限
  MODEL_INCOMPATIBLE = 'model_incompatible',  // 模型不兼容
  RETRY_REQUIRED = 'retry_required',          // 需要重试
  FAILED = 'failed'              // 调度失败
}

/**
 * 调度结果接口
 */
export interface DispatchResult {
  status: DispatchStatus;
  deviceId?: string;           // 分配的设备ID
  message?: string;            // 状态消息
  retryAfter?: number;         // 重试延迟（毫秒）
  error?: Error;               // 错误信息
  metadata?: {
    tier: TaskTier;
    attemptCount: number;
    selectedStrategy: string;
    nodeSelectionTime: number;  // 节点选择耗时（毫秒）
    totalDispatchTime: number;  // 总调度耗时（毫秒）
  };
}

/**
 * 任务调度上下文
 */
export interface TaskDispatchContext {
  taskId: string;
  model?: string;
  tier: TaskTier;
  attemptCount: number;
  maxRetries: number;
  timeout?: number;            // 调度超时（毫秒）
  priority?: number;           // 任务优先级（0-10，10最高）
  requiredCapabilities?: string[];  // 所需能力
}

/**
 * 分层任务调度器接口
 */
export interface TierAwareDispatcher {
  /**
   * 调度任务到合适的节点
   * @param task 任务对象
   * @param context 调度上下文
   * @returns 调度结果
   */
  dispatch(task: any, context: TaskDispatchContext): Promise<DispatchResult>;

  /**
   * 获取调度统计信息
   * @returns 调度统计
   */
  getDispatchStats(): Promise<DispatchStats>;

  /**
   * 重置调度器状态
   */
  reset(): Promise<void>;
}

/**
 * 调度统计信息
 */
export interface DispatchStats {
  totalDispatches: number;
  successfulDispatches: number;
  failedDispatches: number;
  averageDispatchTime: number;
  primaryTaskDispatches: number;
  lowRankTaskDispatches: number;
  retryCount: number;
  nodeUtilization: Record<string, number>;  // 节点利用率
}

/**
 * 任务调度策略接口
 */
export interface TaskDispatchStrategy {
  /**
   * 策略名称
   */
  readonly name: string;

  /**
   * 支持的任务等级
   */
  readonly supportedTiers: TaskTier[];

  /**
   * 执行调度
   * @param task 任务对象
   * @param context 调度上下文
   * @returns 调度结果
   */
  dispatch(task: any, context: TaskDispatchContext): Promise<DispatchResult>;

  /**
   * 检查策略是否可用
   * @param context 调度上下文
   * @returns 是否可用
   */
  isAvailable(context: TaskDispatchContext): Promise<boolean>;

  /**
   * 获取策略优先级（数值越高优先级越高）
   * @param context 调度上下文
   * @returns 优先级分数
   */
  getPriority(context: TaskDispatchContext): Promise<number>;
}

/**
 * 节点选择结果
 */
export interface NodeSelectionResult {
  deviceId: string;
  score: number;               // 节点评分
  reason: string;              // 选择原因
  metadata?: {
    responseTime?: number;     // 响应时间
    successRate?: number;      // 成功率
    currentLoad?: number;      // 当前负载
    capabilities?: string[];   // 节点能力
  };
}

/**
 * 调度器配置
 */
export interface DispatcherConfig {
  maxRetries: number;
  retryDelayMs: number;
  dispatchTimeoutMs: number;
  enableMetrics: boolean;
  primaryTaskConfig: {
    minSuccessRate: number;    // 最小成功率要求
    maxResponseTime: number;   // 最大响应时间要求（毫秒）
    preferredNodeCount: number; // 优选节点数量
  };
  lowRankTaskConfig: {
    maxWaitTime: number;       // 最大等待时间（毫秒）
    fallbackEnabled: boolean;  // 是否启用回退策略
  };
}
