// Main exports - 简化版本
export * from './lib/task-manager.module';
export * from './lib/task-manager.service';
export * from './lib/task-manager.interface';
export * from './lib/task-manager.repository';
export * from './lib/task-execution.service';

// Core service - 统一的核心服务
export * from './lib/task-core.service';

// Queue exports - 简化的队列功能
export * from './lib/queue/task-queue.module';
export * from './lib/queue/simple-task-queue.service';

// Tier-aware dispatcher exports - 分层任务调度器
export * from './lib/tier-aware-dispatcher.interface';
export * from './lib/tier-aware-dispatcher.service';
export * from './lib/strategies/primary-task-dispatch.strategy';
export * from './lib/strategies/low-rank-task-dispatch.strategy';
