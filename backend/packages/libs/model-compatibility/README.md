# 模型适配检测模块 (Model Compatibility Detection Module)

## 概述

模型适配检测模块是自适应推理编排器系统的核心组件，实现了递进式冷却机制和模型兼容性跟踪功能。

## 主要功能

### 1. 递进式冷却机制
- **五级冷却策略**: Available → 5分钟 → 1小时 → 24小时 → 永久阻止
- **智能失败阈值**: 1次/3次/5次/10次失败触发不同级别的冷却
- **自动恢复**: 冷却期过后自动恢复可用状态

### 2. 模型兼容性跟踪
- **成功/失败统计**: 跟踪每个设备-模型组合的历史表现
- **状态管理**: 实时维护兼容性状态
- **性能监控**: 记录执行时间和成功率

### 3. 智能节点选择
- **兼容性感知**: 基于历史兼容性数据进行节点选择
- **分数调整**: 成功记录加分，失败记录减分
- **降级策略**: 兼容性不足时的智能降级

## 核心组件

### ModelCompatibilityService
主要服务类，提供模型可用性检查和状态管理功能。

```typescript
import { ModelCompatibilityService } from '@sight-ai/model-compatibility';

// 检查模型是否可用
const isAvailable = await service.isModelAvailable('device-123', 'llama-7b');

// 记录模型执行失败
await service.recordModelFailure('device-123', 'llama-7b', 'Connection timeout');

// 记录模型执行成功
await service.recordModelSuccess('device-123', 'llama-7b', 1500); // 1.5秒执行时间
```

### ModelAwareNodeSelectionStrategy
智能节点选择策略，基于兼容性数据优化节点选择。

```typescript
import { ModelAwareNodeSelectionStrategy } from '@sight-ai/model-compatibility';

const strategy = new ModelAwareNodeSelectionStrategy(compatibilityService);
const selectedNodes = await strategy.selectNodes(availableNodes, 'llama-7b', 3);
```

### TaskExecutionMonitorService
任务执行监控服务，自动跟踪任务状态并更新兼容性记录。

```typescript
import { TaskExecutionMonitorService } from '@sight-ai/model-compatibility';

// 监控服务会自动监听任务事件并更新兼容性状态
```

## 数据库架构

### model_compatibility 表
```sql
CREATE TABLE model_compatibility (
    id SERIAL PRIMARY KEY,
    device_id VARCHAR(255) NOT NULL,
    model_name VARCHAR(255) NOT NULL,
    status VARCHAR(50) NOT NULL DEFAULT 'AVAILABLE',
    failure_count INTEGER NOT NULL DEFAULT 0,
    success_count INTEGER NOT NULL DEFAULT 0,
    last_failure_at TIMESTAMP,
    last_success_at TIMESTAMP,
    cooldown_until TIMESTAMP,
    total_execution_time_ms BIGINT NOT NULL DEFAULT 0,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(device_id, model_name)
);
```

## 配置选项

### CooldownConfig
```typescript
interface CooldownConfig {
  cooldown5Min: number;      // 5分钟冷却时间 (毫秒)
  cooldown1Hour: number;     // 1小时冷却时间 (毫秒)
  cooldown24Hour: number;    // 24小时冷却时间 (毫秒)
  failureThresholds: {
    cooling5Min: number;     // 触发5分钟冷却的失败次数
    cooling1Hour: number;    // 触发1小时冷却的失败次数
    cooling24Hour: number;   // 触发24小时冷却的失败次数
    blocked: number;         // 触发永久阻止的失败次数
  };
}
```

## 使用方法

### 1. 模块导入
```typescript
import { ModelCompatibilityModule } from '@sight-ai/model-compatibility';

@Module({
  imports: [ModelCompatibilityModule],
  // ...
})
export class YourModule {}
```

### 2. 服务注入
```typescript
import { ModelCompatibilityService } from '@sight-ai/model-compatibility';

@Injectable()
export class YourService {
  constructor(
    private readonly compatibilityService: ModelCompatibilityService
  ) {}
}
```

## 测试

运行测试套件：
```bash
npm test
```

测试覆盖：
- ✅ 73个测试全部通过
- ✅ 5个测试套件完整覆盖
- ✅ 包含单元测试和集成测试

## 监控和维护

### 自动清理
- **定时任务**: 每小时清理过期的冷却记录
- **数据维护**: 自动更新统计信息
- **性能优化**: 定期清理无用数据

### 日志记录
- **详细日志**: 记录所有关键操作
- **错误跟踪**: 完整的错误信息和堆栈跟踪
- **性能指标**: 执行时间和成功率统计

## 架构设计原则

- **单一职责**: 每个组件专注于特定功能
- **依赖倒置**: 基于接口编程，便于测试和扩展
- **接口隔离**: 提供专门的接口，避免不必要的依赖
- **高内聚低耦合**: 模块内部紧密协作，对外接口简洁

## 版本信息

- **当前版本**: 1.0.0
- **兼容性**: NestJS 10.x, PostgreSQL 12+
- **依赖**: @saito/persistent, @saito/models, @saito/common
