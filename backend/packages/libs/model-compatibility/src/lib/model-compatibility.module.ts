/**
 * 模型适配检测模块
 */

import { Module } from '@nestjs/common';
import { ScheduleModule } from '@nestjs/schedule';
import { EventEmitterModule } from '@nestjs/event-emitter';
import { PersistentModule } from '@saito/persistent';
import { ModelCompatibilityService } from './model-compatibility.service';
import { ModelCompatibilityRepository } from './model-compatibility.repository';
import { TaskExecutionMonitorService } from './task-execution-monitor.service';
import { CooldownCleanupService } from './cooldown-cleanup.service';
// import { ModelAwareNodeSelectionStrategy } from './model-aware-node-selection.strategy';
import { CooldownConfig } from './model-compatibility.interface';

@Module({
  imports: [
    PersistentModule,
    ScheduleModule.forRoot(),
    EventEmitterModule.forRoot()
  ],
  providers: [
    ModelCompatibilityService,
    ModelCompatibilityRepository,
    TaskExecutionMonitorService,
    CooldownCleanupService,
    // ModelAwareNodeSelectionStrategy, // 暂时移除，避免复杂依赖
    {
      provide: 'CooldownConfig',
      useValue: {
        cooldown5Min: 5 * 60 * 1000,      // 5分钟
        cooldown1Hour: 60 * 60 * 1000,    // 1小时
        cooldown24Hour: 24 * 60 * 60 * 1000, // 24小时
        failureThresholds: {
          cooling5Min: 1,    // 1次失败触发5分钟冷却
          cooling1Hour: 3,   // 3次失败触发1小时冷却
          cooling24Hour: 5,  // 5次失败触发24小时冷却
          blocked: 10        // 10次失败永久阻止
        }
      } as CooldownConfig
    }
  ],
  exports: [
    ModelCompatibilityService,
    ModelCompatibilityRepository,
    TaskExecutionMonitorService,
    CooldownCleanupService
    // ModelAwareNodeSelectionStrategy // 暂时移除
  ]
})
export class ModelCompatibilityModule {}
