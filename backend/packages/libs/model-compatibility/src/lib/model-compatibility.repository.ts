/**
 * 模型兼容性数据库操作层
 */

import { Injectable, Logger } from '@nestjs/common';
import { PersistentService } from '@saito/persistent';
import { sql } from 'slonik';
import {
  ModelCompatibilityStatus,
  CooldownStatus,
  CreateModelCompatibilityParams,
  UpdateModelCompatibilityParams
} from './model-compatibility.interface';

@Injectable()
export class ModelCompatibilityRepository {
  private readonly logger = new Logger(ModelCompatibilityRepository.name);

  constructor(private readonly persistentService: PersistentService) {}

  /**
   * 创建模型兼容性记录
   */
  async create(params: CreateModelCompatibilityParams): Promise<ModelCompatibilityStatus> {
    const now = new Date();

    try {
      const result = await this.persistentService.pgPool.query(sql.unsafe`
        INSERT INTO saito_gateway.model_node_compatibility (
          device_id, model_name, status, failure_count, success_count,
          last_failure_error, created_at, updated_at
        ) VALUES (
          ${params.deviceId},
          ${params.modelName},
          ${params.status || CooldownStatus.AVAILABLE},
          ${params.failureCount || 0},
          ${params.successCount || 0},
          ${params.lastFailureError || null},
          ${now.toISOString()},
          ${now.toISOString()}
        )
        RETURNING *
      `);
      return this.mapRowToStatus(result.rows[0]);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      const errorStack = error instanceof Error ? error.stack : undefined;
      this.logger.error(`创建模型兼容性记录失败: ${errorMessage}`, errorStack);
      throw error;
    }
  }

  /**
   * 根据设备ID和模型名称查找记录
   */
  async findByDeviceAndModel(deviceId: string, modelName: string): Promise<ModelCompatibilityStatus | null> {
    try {
      const result = await this.persistentService.pgPool.query(sql.unsafe`
        SELECT * FROM saito_gateway.model_node_compatibility
        WHERE device_id = ${deviceId} AND model_name = ${modelName}
      `);
      return result.rows.length > 0 ? this.mapRowToStatus(result.rows[0]) : null;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      const errorStack = error instanceof Error ? error.stack : undefined;
      this.logger.error(`查询模型兼容性记录失败: ${errorMessage}`, errorStack);
      throw error;
    }
  }

  /**
   * 更新模型兼容性记录
   */
  async update(
    deviceId: string,
    modelName: string,
    params: UpdateModelCompatibilityParams
  ): Promise<ModelCompatibilityStatus | null> {
    if (Object.keys(params).length === 0) {
      return this.findByDeviceAndModel(deviceId, modelName);
    }

    const now = new Date().toISOString();

    try {
      const result = await this.persistentService.pgPool.query(sql.unsafe`
        UPDATE saito_gateway.model_node_compatibility
        SET
          status = COALESCE(${params.status || null}, status),
          failure_count = COALESCE(${params.failureCount || null}, failure_count),
          success_count = COALESCE(${params.successCount || null}, success_count),
          last_failure_at = COALESCE(${params.lastFailureAt?.toISOString() || null}, last_failure_at),
          last_success_at = COALESCE(${params.lastSuccessAt?.toISOString() || null}, last_success_at),
          last_failure_error = COALESCE(${params.lastFailureError || null}, last_failure_error),
          cooldown_until = COALESCE(${params.cooldownUntil?.toISOString() || null}, cooldown_until),
          updated_at = ${now}
        WHERE device_id = ${deviceId} AND model_name = ${modelName}
        RETURNING *
      `);
      return result.rows.length > 0 ? this.mapRowToStatus(result.rows[0]) : null;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      const errorStack = error instanceof Error ? error.stack : undefined;
      this.logger.error(`更新模型兼容性记录失败: ${errorMessage}`, errorStack);
      throw error;
    }
  }

  /**
   * 获取设备的所有模型兼容性状态
   */
  async findByDevice(deviceId: string): Promise<ModelCompatibilityStatus[]> {
    try {
      const result = await this.persistentService.pgPool.query(sql.unsafe`
        SELECT * FROM saito_gateway.model_node_compatibility
        WHERE device_id = ${deviceId}
        ORDER BY model_name
      `);
      return result.rows.map((row: any) => this.mapRowToStatus(row));
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      const errorStack = error instanceof Error ? error.stack : undefined;
      this.logger.error(`查询设备模型兼容性记录失败: ${errorMessage}`, errorStack);
      throw error;
    }
  }

  /**
   * 获取模型在所有节点上的兼容性状态
   */
  async findByModel(modelName: string): Promise<ModelCompatibilityStatus[]> {
    try {
      const result = await this.persistentService.pgPool.query(sql.unsafe`
        SELECT * FROM saito_gateway.model_node_compatibility
        WHERE model_name = ${modelName}
        ORDER BY device_id
      `);
      return result.rows.map((row: any) => this.mapRowToStatus(row));
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      const errorStack = error instanceof Error ? error.stack : undefined;
      this.logger.error(`查询模型兼容性记录失败: ${errorMessage}`, errorStack);
      throw error;
    }
  }

  /**
   * 清理过期的冷却状态
   */
  async cleanupExpiredCooldowns(): Promise<number> {
    const now = new Date().toISOString();

    try {
      const result = await this.persistentService.pgPool.query(sql.unsafe`
        UPDATE saito_gateway.model_node_compatibility
        SET status = ${CooldownStatus.AVAILABLE}, cooldown_until = NULL, updated_at = ${now}
        WHERE status != ${CooldownStatus.AVAILABLE} AND status != ${CooldownStatus.BLOCKED}
          AND (cooldown_until IS NULL OR cooldown_until <= ${now})
        RETURNING id
      `);
      const cleanedCount = result.rows.length;

      if (cleanedCount > 0) {
        this.logger.log(`清理了 ${cleanedCount} 个过期的冷却状态`);
      }

      return cleanedCount;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      const errorStack = error instanceof Error ? error.stack : undefined;
      this.logger.error(`清理过期冷却状态失败: ${errorMessage}`, errorStack);
      throw error;
    }
  }

  /**
   * 将数据库行映射为状态对象
   */
  private mapRowToStatus(row: any): ModelCompatibilityStatus {
    return {
      deviceId: row.device_id,
      modelName: row.model_name,
      status: row.status as CooldownStatus,
      failureCount: row.failure_count || 0,
      successCount: row.success_count || 0,
      lastFailureAt: row.last_failure_at ? new Date(row.last_failure_at) : undefined,
      lastSuccessAt: row.last_success_at ? new Date(row.last_success_at) : undefined,
      lastFailureError: row.last_failure_error || undefined,
      cooldownUntil: row.cooldown_until ? new Date(row.cooldown_until) : undefined,
      nextRetryAt: row.cooldown_until ? new Date(row.cooldown_until) : undefined,
      createdAt: new Date(row.created_at),
      updatedAt: new Date(row.updated_at)
    };
  }
}
