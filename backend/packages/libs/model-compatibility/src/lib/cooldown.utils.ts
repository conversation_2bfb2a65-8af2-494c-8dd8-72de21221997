/**
 * 冷却机制工具函数
 */

import { CooldownStatus, CooldownConfig } from './model-compatibility.interface';

/**
 * 冷却工具类
 */
export class CooldownUtils {
  /**
   * 获取冷却状态的显示名称
   */
  static getStatusDisplayName(status: CooldownStatus): string {
    switch (status) {
      case CooldownStatus.AVAILABLE:
        return '可用';
      case CooldownStatus.COOLING_5MIN:
        return '5分钟冷却';
      case CooldownStatus.COOLING_1H:
        return '1小时冷却';
      case CooldownStatus.COOLING_24H:
        return '24小时冷却';
      case CooldownStatus.BLOCKED:
        return '永久阻止';
      default:
        return '未知状态';
    }
  }

  /**
   * 获取冷却状态的严重程度等级（数值越高越严重）
   */
  static getStatusSeverity(status: CooldownStatus): number {
    switch (status) {
      case CooldownStatus.AVAILABLE:
        return 0;
      case CooldownStatus.COOLING_5MIN:
        return 1;
      case CooldownStatus.COOLING_1H:
        return 2;
      case CooldownStatus.COOLING_24H:
        return 3;
      case CooldownStatus.BLOCKED:
        return 4;
      default:
        return -1;
    }
  }

  /**
   * 检查状态是否处于冷却中
   */
  static isInCooldown(status: CooldownStatus): boolean {
    return status !== CooldownStatus.AVAILABLE && status !== CooldownStatus.BLOCKED;
  }

  /**
   * 检查状态是否被阻止
   */
  static isBlocked(status: CooldownStatus): boolean {
    return status === CooldownStatus.BLOCKED;
  }

  /**
   * 计算剩余冷却时间（毫秒）
   */
  static getRemainingCooldownTime(cooldownUntil?: Date): number {
    if (!cooldownUntil) {
      return 0;
    }

    const now = new Date();
    const remaining = cooldownUntil.getTime() - now.getTime();
    return Math.max(0, remaining);
  }

  /**
   * 格式化剩余冷却时间为可读字符串
   */
  static formatRemainingTime(cooldownUntil?: Date): string {
    const remaining = this.getRemainingCooldownTime(cooldownUntil);
    
    if (remaining <= 0) {
      return '已过期';
    }

    const seconds = Math.floor(remaining / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    const days = Math.floor(hours / 24);

    if (days > 0) {
      return `${days}天${hours % 24}小时`;
    } else if (hours > 0) {
      return `${hours}小时${minutes % 60}分钟`;
    } else if (minutes > 0) {
      return `${minutes}分钟${seconds % 60}秒`;
    } else {
      return `${seconds}秒`;
    }
  }

  /**
   * 验证冷却配置的合理性
   */
  static validateConfig(config: CooldownConfig): string[] {
    const errors: string[] = [];

    // 检查冷却时间是否递增
    if (config.cooldown5Min >= config.cooldown1Hour) {
      errors.push('5分钟冷却时间应小于1小时冷却时间');
    }
    if (config.cooldown1Hour >= config.cooldown24Hour) {
      errors.push('1小时冷却时间应小于24小时冷却时间');
    }

    // 检查失败阈值是否递增
    const thresholds = config.failureThresholds;
    if (thresholds.cooling5Min >= thresholds.cooling1Hour) {
      errors.push('5分钟冷却阈值应小于1小时冷却阈值');
    }
    if (thresholds.cooling1Hour >= thresholds.cooling24Hour) {
      errors.push('1小时冷却阈值应小于24小时冷却阈值');
    }
    if (thresholds.cooling24Hour >= thresholds.blocked) {
      errors.push('24小时冷却阈值应小于永久阻止阈值');
    }

    // 检查数值是否为正数
    if (config.cooldown5Min <= 0) {
      errors.push('5分钟冷却时间必须为正数');
    }
    if (config.cooldown1Hour <= 0) {
      errors.push('1小时冷却时间必须为正数');
    }
    if (config.cooldown24Hour <= 0) {
      errors.push('24小时冷却时间必须为正数');
    }

    if (thresholds.cooling5Min <= 0) {
      errors.push('5分钟冷却阈值必须为正数');
    }
    if (thresholds.cooling1Hour <= 0) {
      errors.push('1小时冷却阈值必须为正数');
    }
    if (thresholds.cooling24Hour <= 0) {
      errors.push('24小时冷却阈值必须为正数');
    }
    if (thresholds.blocked <= 0) {
      errors.push('永久阻止阈值必须为正数');
    }

    return errors;
  }

  /**
   * 创建默认冷却配置
   */
  static createDefaultConfig(): CooldownConfig {
    return {
      cooldown5Min: 5 * 60 * 1000,      // 5分钟
      cooldown1Hour: 60 * 60 * 1000,    // 1小时
      cooldown24Hour: 24 * 60 * 60 * 1000, // 24小时
      failureThresholds: {
        cooling5Min: 1,    // 1次失败触发5分钟冷却
        cooling1Hour: 3,   // 3次失败触发1小时冷却
        cooling24Hour: 5,  // 5次失败触发24小时冷却
        blocked: 10        // 10次失败永久阻止
      }
    };
  }

  /**
   * 创建宽松的冷却配置（用于测试环境）
   */
  static createLenientConfig(): CooldownConfig {
    return {
      cooldown5Min: 30 * 1000,          // 30秒
      cooldown1Hour: 5 * 60 * 1000,     // 5分钟
      cooldown24Hour: 30 * 60 * 1000,   // 30分钟
      failureThresholds: {
        cooling5Min: 2,    // 2次失败触发30秒冷却
        cooling1Hour: 5,   // 5次失败触发5分钟冷却
        cooling24Hour: 8,  // 8次失败触发30分钟冷却
        blocked: 15        // 15次失败永久阻止
      }
    };
  }

  /**
   * 创建严格的冷却配置（用于生产环境）
   */
  static createStrictConfig(): CooldownConfig {
    return {
      cooldown5Min: 10 * 60 * 1000,     // 10分钟
      cooldown1Hour: 2 * 60 * 60 * 1000, // 2小时
      cooldown24Hour: 48 * 60 * 60 * 1000, // 48小时
      failureThresholds: {
        cooling5Min: 1,    // 1次失败触发10分钟冷却
        cooling1Hour: 2,   // 2次失败触发2小时冷却
        cooling24Hour: 3,  // 3次失败触发48小时冷却
        blocked: 5         // 5次失败永久阻止
      }
    };
  }
}
