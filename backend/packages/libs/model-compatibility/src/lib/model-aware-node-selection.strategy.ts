/**
 * 模型感知节点选择策略
 * 
 * 在节点选择过程中集成模型兼容性检查，确保只选择与模型兼容的节点
 */

import { Injectable, Logger } from '@nestjs/common';
import { NodeSelectionStrategy } from '@saito/node';
import { SelectionCriteria, NodeSelectionResult } from '@saito/models';
import { ModelCompatibilityService } from './model-compatibility.service';
import { CooldownStatus } from './model-compatibility.interface';

@Injectable()
export class ModelAwareNodeSelectionStrategy implements NodeSelectionStrategy {
  private readonly logger = new Logger(ModelAwareNodeSelectionStrategy.name);

  constructor(
    private readonly modelCompatibilityService: ModelCompatibilityService,
    private readonly fallbackStrategy: NodeSelectionStrategy
  ) {}

  /**
   * 选择与模型兼容的节点
   */
  async selectNodes(
    availableNodes: Array<{ id: string, device_id: string }>,
    criteria: SelectionCriteria
  ): Promise<NodeSelectionResult[]> {
    this.logger.debug(`Applying model-aware selection for ${availableNodes.length} nodes with model: ${criteria.model}`);

    // 如果没有指定模型，直接使用回退策略
    if (!criteria.model) {
      this.logger.debug('No model specified, using fallback strategy');
      return this.fallbackStrategy.selectNodes(availableNodes, criteria);
    }

    try {
      // 1. 过滤出与模型兼容的节点
      const compatibleNodes = await this.filterCompatibleNodes(availableNodes, criteria.model);

      if (compatibleNodes.length === 0) {
        this.logger.warn(`No compatible nodes found for model ${criteria.model}`);
        return [];
      }

      this.logger.log(`Found ${compatibleNodes.length} compatible nodes for model ${criteria.model} out of ${availableNodes.length} available nodes`);

      // 2. 使用回退策略对兼容节点进行排序和评分
      const rankedNodes = await this.fallbackStrategy.selectNodes(compatibleNodes, criteria);

      // 3. 增强结果，添加兼容性信息
      const enhancedResults = await this.enhanceResultsWithCompatibilityInfo(rankedNodes, criteria.model);

      return enhancedResults;

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      const errorStack = error instanceof Error ? error.stack : undefined;
      this.logger.error(`Model compatibility check failed, using fallback strategy: ${errorMessage}`, errorStack);

      // 出错时使用回退策略，确保系统可用性
      return this.fallbackStrategy.selectNodes(availableNodes, criteria);
    }
  }

  /**
   * 过滤出与模型兼容的节点
   */
  private async filterCompatibleNodes(
    nodes: Array<{ id: string, device_id: string }>,
    modelName: string
  ): Promise<Array<{ id: string, device_id: string }>> {
    const compatibleNodes: Array<{ id: string, device_id: string }> = [];

    // 并行检查所有节点的兼容性
    const compatibilityChecks = nodes.map(async (node) => {
      try {
        const isCompatible = await this.modelCompatibilityService.isModelAvailable(node.device_id, modelName);
        return { node, isCompatible };
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        this.logger.warn(`Failed to check compatibility for node ${node.device_id}: ${errorMessage}`);
        // 检查失败时默认认为兼容，避免影响可用性
        return { node, isCompatible: true };
      }
    });

    const results = await Promise.all(compatibilityChecks);

    // 收集兼容的节点
    for (const { node, isCompatible } of results) {
      if (isCompatible) {
        compatibleNodes.push(node);
      } else {
        this.logger.debug(`Node ${node.device_id} is not compatible with model ${modelName}`);
      }
    }

    return compatibleNodes;
  }

  /**
   * 增强结果，添加兼容性信息
   */
  private async enhanceResultsWithCompatibilityInfo(
    results: NodeSelectionResult[],
    modelName: string
  ): Promise<NodeSelectionResult[]> {
    const enhancedResults: NodeSelectionResult[] = [];

    for (const result of results) {
      try {
        const compatibilityStatus = await this.modelCompatibilityService.getCompatibilityStatus(
          result.node.device_id,
          modelName
        );

        // 根据兼容性状态调整评分
        let adjustedScore = result.score;
        let compatibilityReason = '';

        if (compatibilityStatus) {
          const { status, failureCount, successCount } = compatibilityStatus;
          
          // 根据历史表现调整评分
          if (status === CooldownStatus.AVAILABLE) {
            if (successCount > 0) {
              // 有成功历史的节点获得加分
              const successBonus = Math.min(10, successCount * 2);
              adjustedScore += successBonus;
              compatibilityReason = `成功执行${successCount}次，获得${successBonus}分加成`;
            }
            
            if (failureCount > 0) {
              // 有失败历史但当前可用的节点轻微减分
              const failurePenalty = Math.min(5, failureCount);
              adjustedScore -= failurePenalty;
              compatibilityReason += (compatibilityReason ? '；' : '') + `历史失败${failureCount}次，减${failurePenalty}分`;
            }
          }
        } else {
          // 没有历史记录的节点保持原评分
          compatibilityReason = '新节点，无历史记录';
        }

        enhancedResults.push({
          ...result,
          score: Math.max(0, adjustedScore), // 确保评分不为负
          reason: `${result.reason}；兼容性：${compatibilityReason}`,

        });

      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        this.logger.warn(`Failed to enhance result for node ${result.node.device_id}: ${errorMessage}`);
        // 增强失败时保持原结果
        enhancedResults.push(result);
      }
    }

    // 按调整后的评分重新排序
    enhancedResults.sort((a, b) => b.score - a.score);

    return enhancedResults;
  }
}
