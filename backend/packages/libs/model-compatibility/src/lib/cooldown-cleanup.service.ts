/**
 * 冷却状态清理服务
 * 
 * 定期清理过期的冷却状态，确保系统状态的准确性
 */

import { Injectable, Logger, OnModuleInit, OnModuleDestroy } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';
import { ModelCompatibilityService } from './model-compatibility.service';

@Injectable()
export class CooldownCleanupService implements OnModuleInit, OnModuleDestroy {
  private readonly logger = new Logger(CooldownCleanupService.name);
  private isRunning = false;
  private cleanupStats = {
    totalRuns: 0,
    totalCleaned: 0,
    lastRunTime: null as Date | null,
    lastCleanedCount: 0,
    errors: 0
  };

  constructor(
    private readonly modelCompatibilityService: ModelCompatibilityService
  ) {}

  onModuleInit() {
    this.logger.log('Cooldown cleanup service initialized');
  }

  onModuleDestroy() {
    this.logger.log('Cooldown cleanup service destroyed');
  }

  /**
   * 每5分钟执行一次清理任务
   */
  @Cron(CronExpression.EVERY_5_MINUTES)
  async scheduledCleanup() {
    if (this.isRunning) {
      this.logger.debug('Cleanup already running, skipping this cycle');
      return;
    }

    await this.performCleanup();
  }

  /**
   * 每小时执行一次详细清理（包含统计报告）
   */
  @Cron(CronExpression.EVERY_HOUR)
  async hourlyDetailedCleanup() {
    if (this.isRunning) {
      this.logger.debug('Cleanup already running, skipping hourly detailed cleanup');
      return;
    }

    await this.performCleanup(true);
  }

  /**
   * 手动触发清理
   */
  async manualCleanup(): Promise<number> {
    if (this.isRunning) {
      throw new Error('Cleanup is already running');
    }

    return await this.performCleanup(true);
  }

  /**
   * 执行清理操作
   */
  private async performCleanup(detailed = false): Promise<number> {
    this.isRunning = true;
    const startTime = Date.now();

    try {
      this.logger.debug('Starting cooldown cleanup...');

      const cleanedCount = await this.modelCompatibilityService.cleanupExpiredCooldowns();

      // 更新统计信息
      this.cleanupStats.totalRuns++;
      this.cleanupStats.totalCleaned += cleanedCount;
      this.cleanupStats.lastRunTime = new Date();
      this.cleanupStats.lastCleanedCount = cleanedCount;

      const duration = Date.now() - startTime;

      if (cleanedCount > 0 || detailed) {
        this.logger.log(
          `Cooldown cleanup completed: ${cleanedCount} records cleaned in ${duration}ms`
        );
      } else {
        this.logger.debug(`Cooldown cleanup completed: no expired records found (${duration}ms)`);
      }

      if (detailed) {
        this.logDetailedStats();
      }

      return cleanedCount;

    } catch (error) {
      this.cleanupStats.errors++;
      const errorMessage = error instanceof Error ? error.message : String(error);
      const errorStack = error instanceof Error ? error.stack : undefined;
      this.logger.error(`Cooldown cleanup failed: ${errorMessage}`, errorStack);
      throw error;
    } finally {
      this.isRunning = false;
    }
  }

  /**
   * 记录详细统计信息
   */
  private logDetailedStats() {
    const stats = this.cleanupStats;
    this.logger.log(
      `Cleanup Statistics - Total runs: ${stats.totalRuns}, ` +
      `Total cleaned: ${stats.totalCleaned}, ` +
      `Last run: ${stats.lastRunTime?.toISOString()}, ` +
      `Last cleaned count: ${stats.lastCleanedCount}, ` +
      `Errors: ${stats.errors}`
    );
  }

  /**
   * 获取清理统计信息
   */
  getCleanupStats() {
    return {
      ...this.cleanupStats,
      isRunning: this.isRunning
    };
  }

  /**
   * 重置统计信息
   */
  resetStats() {
    this.cleanupStats = {
      totalRuns: 0,
      totalCleaned: 0,
      lastRunTime: null,
      lastCleanedCount: 0,
      errors: 0
    };
    this.logger.log('Cleanup statistics reset');
  }

  /**
   * 检查服务健康状态
   */
  getHealthStatus(): {
    status: 'healthy' | 'warning' | 'error';
    message: string;
    stats: any;
  } {
    const stats = this.getCleanupStats();
    
    // 检查是否有过多错误
    if (stats.errors > 10) {
      return {
        status: 'error',
        message: `Too many cleanup errors: ${stats.errors}`,
        stats
      };
    }

    // 检查最后运行时间
    if (stats.lastRunTime) {
      const timeSinceLastRun = Date.now() - stats.lastRunTime.getTime();
      const maxInterval = 10 * 60 * 1000; // 10分钟

      if (timeSinceLastRun > maxInterval) {
        return {
          status: 'warning',
          message: `Last cleanup was ${Math.round(timeSinceLastRun / 60000)} minutes ago`,
          stats
        };
      }
    }

    return {
      status: 'healthy',
      message: 'Cleanup service is running normally',
      stats
    };
  }
}
