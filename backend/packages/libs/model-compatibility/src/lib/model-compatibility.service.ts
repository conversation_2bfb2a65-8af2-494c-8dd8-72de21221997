/**
 * 模型兼容性验证服务
 * 
 * 实现递进式冷却机制和模型兼容性跟踪
 */

import { Injectable, Logger, Inject } from '@nestjs/common';
import {
  ModelCompatibilityValidator,
  ModelCompatibilityStatus,
  CooldownStatus,
  CooldownConfig
} from './model-compatibility.interface';
import { ModelCompatibilityRepository } from './model-compatibility.repository';

@Injectable()
export class ModelCompatibilityService implements ModelCompatibilityValidator {
  private readonly logger = new Logger(ModelCompatibilityService.name);

  // 默认冷却配置
  private readonly defaultConfig: CooldownConfig = {
    cooldown5Min: 5 * 60 * 1000,      // 5分钟
    cooldown1Hour: 60 * 60 * 1000,    // 1小时
    cooldown24Hour: 24 * 60 * 60 * 1000, // 24小时
    failureThresholds: {
      cooling5Min: 1,    // 1次失败触发5分钟冷却
      cooling1Hour: 3,   // 3次失败触发1小时冷却
      cooling24Hour: 5,  // 5次失败触发24小时冷却
      blocked: 10        // 10次失败永久阻止
    }
  };

  constructor(
    private readonly repository: ModelCompatibilityRepository,
    @Inject('CooldownConfig') private readonly config: CooldownConfig = this.defaultConfig
  ) {}

  /**
   * 检查模型在节点上是否可用
   */
  async isModelAvailable(deviceId: string, modelName: string): Promise<boolean> {
    try {
      const status = await this.repository.findByDeviceAndModel(deviceId, modelName);
      
      // 如果没有记录，认为是可用的
      if (!status) {
        return true;
      }

      // 检查是否处于冷却状态
      if (status.status === CooldownStatus.AVAILABLE) {
        return true;
      }

      if (status.status === CooldownStatus.BLOCKED) {
        return false;
      }

      // 检查冷却时间是否已过期
      if (status.cooldownUntil && status.cooldownUntil <= new Date()) {
        // 冷却时间已过期，更新状态为可用
        await this.repository.update(deviceId, modelName, {
          status: CooldownStatus.AVAILABLE,
          cooldownUntil: undefined
        });
        
        this.logger.log(`模型 ${modelName} 在设备 ${deviceId} 上的冷却时间已过期，恢复可用状态`);
        return true;
      }

      return false;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      const errorStack = error instanceof Error ? error.stack : undefined;
      this.logger.error(`检查模型可用性失败: ${errorMessage}`, errorStack);
      // 出错时默认返回可用，避免影响正常调度
      return true;
    }
  }

  /**
   * 记录模型执行失败
   */
  async recordModelFailure(deviceId: string, modelName: string, error: string): Promise<void> {
    try {
      let status = await this.repository.findByDeviceAndModel(deviceId, modelName);
      const now = new Date();

      if (!status) {
        // 创建新记录
        status = await this.repository.create({
          deviceId,
          modelName,
          failureCount: 1,
          lastFailureError: error
        });
      }

      // 增加失败次数
      const newFailureCount = status.failureCount + 1;
      const newStatus = this.calculateCooldownStatus(newFailureCount);
      const cooldownUntil = this.calculateCooldownTime(newStatus, now);

      await this.repository.update(deviceId, modelName, {
        status: newStatus,
        failureCount: newFailureCount,
        lastFailureAt: now,
        lastFailureError: error,
        cooldownUntil
      });

      this.logger.warn(
        `模型 ${modelName} 在设备 ${deviceId} 上执行失败 (第${newFailureCount}次)，` +
        `状态更新为: ${newStatus}，错误: ${error}`
      );

      if (cooldownUntil) {
        this.logger.warn(`冷却时间至: ${cooldownUntil.toISOString()}`);
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      const errorStack = error instanceof Error ? error.stack : undefined;
      this.logger.error(`记录模型执行失败时出错: ${errorMessage}`, errorStack);
      throw error;
    }
  }

  /**
   * 记录模型执行成功
   */
  async recordModelSuccess(deviceId: string, modelName: string): Promise<void> {
    try {
      let status = await this.repository.findByDeviceAndModel(deviceId, modelName);
      const now = new Date();

      if (!status) {
        // 创建新记录
        await this.repository.create({
          deviceId,
          modelName,
          successCount: 1
        });
      } else {
        // 更新成功次数，但保持失败计数（用于历史追踪）
        await this.repository.update(deviceId, modelName, {
          successCount: status.successCount + 1,
          lastSuccessAt: now
        });
      }

      this.logger.debug(`模型 ${modelName} 在设备 ${deviceId} 上执行成功`);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      const errorStack = error instanceof Error ? error.stack : undefined;
      this.logger.error(`记录模型执行成功时出错: ${errorMessage}`, errorStack);
      throw error;
    }
  }

  /**
   * 获取兼容性状态
   */
  async getCompatibilityStatus(deviceId: string, modelName: string): Promise<ModelCompatibilityStatus | null> {
    try {
      return await this.repository.findByDeviceAndModel(deviceId, modelName);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      const errorStack = error instanceof Error ? error.stack : undefined;
      this.logger.error(`获取兼容性状态失败: ${errorMessage}`, errorStack);
      throw error;
    }
  }

  /**
   * 获取设备的所有模型兼容性状态
   */
  async getDeviceCompatibilityStatuses(deviceId: string): Promise<ModelCompatibilityStatus[]> {
    try {
      return await this.repository.findByDevice(deviceId);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      const errorStack = error instanceof Error ? error.stack : undefined;
      this.logger.error(`获取设备兼容性状态失败: ${errorMessage}`, errorStack);
      throw error;
    }
  }

  /**
   * 获取模型在所有节点上的兼容性状态
   */
  async getModelCompatibilityStatuses(modelName: string): Promise<ModelCompatibilityStatus[]> {
    try {
      return await this.repository.findByModel(modelName);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      const errorStack = error instanceof Error ? error.stack : undefined;
      this.logger.error(`获取模型兼容性状态失败: ${errorMessage}`, errorStack);
      throw error;
    }
  }

  /**
   * 清理过期的冷却状态
   */
  async cleanupExpiredCooldowns(): Promise<number> {
    try {
      return await this.repository.cleanupExpiredCooldowns();
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      const errorStack = error instanceof Error ? error.stack : undefined;
      this.logger.error(`清理过期冷却状态失败: ${errorMessage}`, errorStack);
      throw error;
    }
  }

  /**
   * 根据失败次数计算冷却状态
   */
  private calculateCooldownStatus(failureCount: number): CooldownStatus {
    const thresholds = this.config.failureThresholds;

    if (failureCount >= thresholds.blocked) {
      return CooldownStatus.BLOCKED;
    } else if (failureCount >= thresholds.cooling24Hour) {
      return CooldownStatus.COOLING_24H;
    } else if (failureCount >= thresholds.cooling1Hour) {
      return CooldownStatus.COOLING_1H;
    } else if (failureCount >= thresholds.cooling5Min) {
      return CooldownStatus.COOLING_5MIN;
    }

    return CooldownStatus.AVAILABLE;
  }

  /**
   * 计算冷却结束时间
   */
  private calculateCooldownTime(status: CooldownStatus, baseTime: Date): Date | undefined {
    switch (status) {
      case CooldownStatus.COOLING_5MIN:
        return new Date(baseTime.getTime() + this.config.cooldown5Min);
      case CooldownStatus.COOLING_1H:
        return new Date(baseTime.getTime() + this.config.cooldown1Hour);
      case CooldownStatus.COOLING_24H:
        return new Date(baseTime.getTime() + this.config.cooldown24Hour);
      case CooldownStatus.BLOCKED:
      case CooldownStatus.AVAILABLE:
      default:
        return undefined;
    }
  }
}
