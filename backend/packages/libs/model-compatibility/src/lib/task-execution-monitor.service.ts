/**
 * 任务执行监控服务
 * 
 * 监听任务执行结果，自动记录模型兼容性状态
 */

import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { EventEmitter2, OnEvent } from '@nestjs/event-emitter';
import { ModelCompatibilityService } from './model-compatibility.service';

/**
 * 任务执行事件接口
 */
export interface TaskExecutionEvent {
  taskId: string;
  deviceId: string;
  model?: string;
  status: 'success' | 'failure';
  error?: string;
  executionTime?: number;
  timestamp: Date;
}

/**
 * 任务开始事件接口
 */
export interface TaskStartEvent {
  taskId: string;
  deviceId: string;
  model?: string;
  timestamp: Date;
}

@Injectable()
export class TaskExecutionMonitorService implements OnModuleInit {
  private readonly logger = new Logger(TaskExecutionMonitorService.name);

  constructor(
    private readonly modelCompatibilityService: ModelCompatibilityService,
    private readonly eventEmitter: EventEmitter2
  ) {}

  onModuleInit() {
    this.logger.log('Task execution monitor service initialized');
  }

  /**
   * 监听任务执行成功事件
   */
  @OnEvent('task.execution.success')
  async handleTaskSuccess(event: TaskExecutionEvent) {
    try {
      if (!event.model) {
        this.logger.debug(`Task ${event.taskId} completed successfully but no model specified`);
        return;
      }

      this.logger.debug(`Recording successful execution of model ${event.model} on device ${event.deviceId}`);

      await this.modelCompatibilityService.recordModelSuccess(event.deviceId, event.model);

      this.logger.log(`Successfully recorded model ${event.model} success on device ${event.deviceId} for task ${event.taskId}`);

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      const errorStack = error instanceof Error ? error.stack : undefined;
      this.logger.error(`Failed to record task success: ${errorMessage}`, errorStack);
    }
  }

  /**
   * 监听任务执行失败事件
   */
  @OnEvent('task.execution.failure')
  async handleTaskFailure(event: TaskExecutionEvent) {
    try {
      if (!event.model) {
        this.logger.debug(`Task ${event.taskId} failed but no model specified`);
        return;
      }

      const errorMessage = event.error || 'Unknown error';
      
      this.logger.warn(`Recording failed execution of model ${event.model} on device ${event.deviceId}: ${errorMessage}`);

      await this.modelCompatibilityService.recordModelFailure(event.deviceId, event.model, errorMessage);

      this.logger.log(`Successfully recorded model ${event.model} failure on device ${event.deviceId} for task ${event.taskId}`);

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      const errorStack = error instanceof Error ? error.stack : undefined;
      this.logger.error(`Failed to record task failure: ${errorMessage}`, errorStack);
    }
  }

  /**
   * 监听任务开始事件（用于调试和统计）
   */
  @OnEvent('task.execution.start')
  async handleTaskStart(event: TaskStartEvent) {
    try {
      if (event.model) {
        this.logger.debug(`Task ${event.taskId} started with model ${event.model} on device ${event.deviceId}`);
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      const errorStack = error instanceof Error ? error.stack : undefined;
      this.logger.error(`Failed to handle task start event: ${errorMessage}`, errorStack);
    }
  }

  /**
   * 手动记录任务执行成功
   */
  async recordTaskSuccess(taskId: string, deviceId: string, model?: string, executionTime?: number) {
    const event: TaskExecutionEvent = {
      taskId,
      deviceId,
      model,
      status: 'success',
      executionTime,
      timestamp: new Date()
    };

    this.eventEmitter.emit('task.execution.success', event);
  }

  /**
   * 手动记录任务执行失败
   */
  async recordTaskFailure(taskId: string, deviceId: string, model?: string, error?: string) {
    const event: TaskExecutionEvent = {
      taskId,
      deviceId,
      model,
      status: 'failure',
      error,
      timestamp: new Date()
    };

    this.eventEmitter.emit('task.execution.failure', event);
  }

  /**
   * 手动记录任务开始
   */
  async recordTaskStart(taskId: string, deviceId: string, model?: string) {
    const event: TaskStartEvent = {
      taskId,
      deviceId,
      model,
      timestamp: new Date()
    };

    this.eventEmitter.emit('task.execution.start', event);
  }

  /**
   * 获取监控统计信息
   */
  async getMonitoringStats(): Promise<{
    totalEventsProcessed: number;
    successfulRecords: number;
    failedRecords: number;
    lastEventTime?: Date;
  }> {
    // 这里可以实现统计逻辑，暂时返回占位数据
    return {
      totalEventsProcessed: 0,
      successfulRecords: 0,
      failedRecords: 0,
      lastEventTime: undefined
    };
  }
}
