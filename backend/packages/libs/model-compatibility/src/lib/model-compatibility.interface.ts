/**
 * 模型适配检测模块接口定义
 * 
 * 实现递进式冷却机制，跟踪模型在特定节点上的执行历史
 */

/**
 * 冷却状态枚举
 */
export enum CooldownStatus {
  /** 可用状态 */
  AVAILABLE = 'available',
  /** 5分钟冷却 */
  COOLING_5MIN = 'cooling_5min',
  /** 1小时冷却 */
  COOLING_1H = 'cooling_1h',
  /** 24小时冷却 */
  COOLING_24H = 'cooling_24h',
  /** 永久阻止 */
  BLOCKED = 'blocked'
}

/**
 * 模型兼容性状态
 */
export interface ModelCompatibilityStatus {
  /** 设备ID */
  deviceId: string;
  /** 模型名称 */
  modelName: string;
  /** 当前状态 */
  status: CooldownStatus;
  /** 失败次数 */
  failureCount: number;
  /** 成功次数 */
  successCount: number;
  /** 最后失败时间 */
  lastFailureAt?: Date;
  /** 最后成功时间 */
  lastSuccessAt?: Date;
  /** 最后失败错误信息 */
  lastFailureError?: string;
  /** 冷却结束时间 */
  cooldownUntil?: Date;
  /** 下次重试时间 */
  nextRetryAt?: Date;
  /** 创建时间 */
  createdAt: Date;
  /** 更新时间 */
  updatedAt: Date;
}

/**
 * 模型兼容性验证器接口
 */
export interface ModelCompatibilityValidator {
  /**
   * 检查模型在节点上是否可用
   * @param deviceId 设备ID
   * @param modelName 模型名称
   * @returns 是否可用
   */
  isModelAvailable(deviceId: string, modelName: string): Promise<boolean>;

  /**
   * 记录模型执行失败
   * @param deviceId 设备ID
   * @param modelName 模型名称
   * @param error 错误信息
   */
  recordModelFailure(deviceId: string, modelName: string, error: string): Promise<void>;

  /**
   * 记录模型执行成功
   * @param deviceId 设备ID
   * @param modelName 模型名称
   */
  recordModelSuccess(deviceId: string, modelName: string): Promise<void>;

  /**
   * 获取兼容性状态
   * @param deviceId 设备ID
   * @param modelName 模型名称
   * @returns 兼容性状态
   */
  getCompatibilityStatus(deviceId: string, modelName: string): Promise<ModelCompatibilityStatus | null>;

  /**
   * 获取设备的所有模型兼容性状态
   * @param deviceId 设备ID
   * @returns 兼容性状态列表
   */
  getDeviceCompatibilityStatuses(deviceId: string): Promise<ModelCompatibilityStatus[]>;

  /**
   * 获取模型在所有节点上的兼容性状态
   * @param modelName 模型名称
   * @returns 兼容性状态列表
   */
  getModelCompatibilityStatuses(modelName: string): Promise<ModelCompatibilityStatus[]>;

  /**
   * 清理过期的冷却状态
   * @returns 清理的记录数量
   */
  cleanupExpiredCooldowns(): Promise<number>;
}

/**
 * 冷却配置
 */
export interface CooldownConfig {
  /** 5分钟冷却时长（毫秒） */
  cooldown5Min: number;
  /** 1小时冷却时长（毫秒） */
  cooldown1Hour: number;
  /** 24小时冷却时长（毫秒） */
  cooldown24Hour: number;
  /** 触发各级冷却的失败次数阈值 */
  failureThresholds: {
    /** 触发5分钟冷却的失败次数 */
    cooling5Min: number;
    /** 触发1小时冷却的失败次数 */
    cooling1Hour: number;
    /** 触发24小时冷却的失败次数 */
    cooling24Hour: number;
    /** 触发永久阻止的失败次数 */
    blocked: number;
  };
}

/**
 * 模型兼容性记录创建参数
 */
export interface CreateModelCompatibilityParams {
  deviceId: string;
  modelName: string;
  status?: CooldownStatus;
  failureCount?: number;
  successCount?: number;
  lastFailureError?: string;
}

/**
 * 模型兼容性记录更新参数
 */
export interface UpdateModelCompatibilityParams {
  status?: CooldownStatus;
  failureCount?: number;
  successCount?: number;
  lastFailureAt?: Date;
  lastSuccessAt?: Date;
  lastFailureError?: string;
  cooldownUntil?: Date;
}
