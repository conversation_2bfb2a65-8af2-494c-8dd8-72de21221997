{"name": "@sight-ai/model-compatibility", "version": "0.0.1", "description": "Model compatibility validation module for adaptive inference orchestrator", "main": "./src/index.ts", "types": "./src/index.ts", "exports": {".": {"import": "./src/index.ts", "types": "./src/index.ts"}}, "dependencies": {"@nestjs/common": "^10.0.0", "@saito/persistent": "*", "@saito/models": "*", "slonik": "^37.0.0", "zod": "^3.22.0"}, "devDependencies": {"@types/jest": "^29.5.0", "jest": "^29.5.0", "typescript": "^5.0.0"}, "scripts": {"test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage"}}