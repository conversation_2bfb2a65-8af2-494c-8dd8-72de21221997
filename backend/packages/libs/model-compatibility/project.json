{"name": "model-compatibility", "$schema": "../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "packages/libs/model-compatibility/src", "projectType": "library", "targets": {"build": {"executor": "@nx/js:tsc", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/packages/libs/model-compatibility", "main": "packages/libs/model-compatibility/src/index.ts", "tsConfig": "packages/libs/model-compatibility/tsconfig.lib.json", "assets": ["packages/libs/model-compatibility/*.md"]}}, "lint": {"executor": "@nx/eslint:lint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["packages/libs/model-compatibility/**/*.ts", "packages/libs/model-compatibility/package.json"]}}, "test": {"executor": "@nx/jest:jest", "outputs": ["{options.outputPath}"], "options": {"jestConfig": "packages/libs/model-compatibility/jest.config.ts"}}}, "tags": []}