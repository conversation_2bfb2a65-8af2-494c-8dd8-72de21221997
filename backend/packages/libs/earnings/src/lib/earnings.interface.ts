import {
  GetServerStatusRequest,
  GetServerStatusResponse,
  GetTaskQueueRequest,
  GetTaskQueueResponse,
  GetNodePerformanceRequest,
  GetNodePerformanceResponse,
  GetEarningsStatsRequest,
  GetEarningsStatsResponse,
  GetEarningsRequest,
  GetEarningsResponse,
  GetDeviceEarningsRequest,
  GetDeviceEarningsResponse
} from '@saito/models';

export interface EarningsServiceInterface {
  getServerStatus(request: GetServerStatusRequest): Promise<GetServerStatusResponse>;
  getTaskQueue(request: GetTaskQueueRequest, userId: string): Promise<GetTaskQueueResponse>;
  getNodePerformance(request: GetNodePerformanceRequest, userId: string): Promise<GetNodePerformanceResponse>;
  getEarningsStats(request: GetEarningsStatsRequest, userId: string): Promise<GetEarningsStatsResponse>;
  getEarnings(request: GetEarningsRequest, userId: string): Promise<GetEarningsResponse>;
  getDeviceEarnings(deviceId: string, request: GetDeviceEarningsRequest, userId: string): Promise<GetDeviceEarningsResponse>;
}
