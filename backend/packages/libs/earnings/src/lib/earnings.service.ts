import { Injectable, Logger, HttpException, HttpStatus } from '@nestjs/common';
import { EarningsServiceInterface } from './earnings.interface';
import { EarningsRepository } from './earnings.repository';
import {
  GetServerStatusRequest,
  GetServerStatusResponse,
  GetTaskQueueRequest,
  GetTaskQueueResponse,
  GetNodePerformanceRequest,
  GetNodePerformanceResponse,
  GetEarningsStatsRequest,
  GetEarningsStatsResponse,
  GetEarningsRequest,
  GetEarningsResponse,
  GetDeviceEarningsRequest,
  GetDeviceEarningsResponse
} from '@saito/models';

@Injectable()
export class EarningsService implements EarningsServiceInterface {
  private readonly logger = new Logger(EarningsService.name);

  constructor(private readonly earningsRepository: EarningsRepository) {}

  async getServerStatus(request: GetServerStatusRequest): Promise<GetServerStatusResponse> {
    const { timeRange } = request;

    const data = await this.earningsRepository.getServerStatus(timeRange);

    return { data };
  }

  async getTaskQueue(request: GetTaskQueueRequest, userId: string): Promise<GetTaskQueueResponse> {
    const { page, pageSize, status, search, timeRange } = request;

    const result = await this.earningsRepository.getTaskQueue(
      page,
      pageSize,
      userId,
      status,
      search,
      timeRange
    );

    return {
      data: result.data,
      total: result.total,
      page,
      pageSize
    };
  }

  async getNodePerformance(request: GetNodePerformanceRequest, userId: string): Promise<GetNodePerformanceResponse> {
    const { page, pageSize, status, search, timeRange } = request;

    const result = await this.earningsRepository.getNodePerformance(
      page,
      pageSize,
      userId,
      status,
      search,
      timeRange
    );

    return {
      data: result.data,
      total: result.total,
      page,
      pageSize
    };
  }

  async getEarningsStats(request: GetEarningsStatsRequest, userId: string, year?: number, month?: number): Promise<GetEarningsStatsResponse> {
    const { timeRange } = request;

    const data = await this.earningsRepository.getEarningsStats(userId, timeRange, year, month);

    return { data };
  }

  async getEarnings(request: GetEarningsRequest, userId: string): Promise<GetEarningsResponse> {
    const { page, pageSize, timeRange } = request;

    const result = await this.earningsRepository.getEarnings(
      page,
      pageSize,
      userId,
      timeRange
    );

    return {
      data: result.data,
      total: result.total,
      page,
      pageSize
    };
  }

  async getDeviceEarnings(deviceId: string, request: GetDeviceEarningsRequest, userId: string): Promise<GetDeviceEarningsResponse> {
    try {
      const { page, pageSize, timeRange, status } = request;

      const result = await this.earningsRepository.getDeviceEarnings(
        deviceId,
        page,
        pageSize,
        userId,
        timeRange,
        status
      );

      return {
        data: result.data,
        total: result.total,
        page,
        pageSize
      };
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }
      this.logger.error(`Failed to get device earnings: ${error}`);
      throw new HttpException('Failed to get device earnings', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  async getRevenueChartData(userId: string, timeRange?: string, year?: number, month?: number): Promise<Array<{ date: string; value: number }>> {
    return await this.earningsRepository.getRevenueChartData(userId, timeRange, year, month);
  }

  async getRequestCountChartData(userId: string, timeRange?: string, year?: number, month?: number): Promise<Array<{ date: string; value: number }>> {
    return await this.earningsRepository.getRequestCountChartData(userId, timeRange, year, month);
  }

  async getChatHeatmapData(userId: string, timeRange?: string, year?: number, month?: number): Promise<Array<{ date: string; count: number }>> {
    return await this.earningsRepository.getChatHeatmapData(userId, timeRange, year, month);
  }
}
