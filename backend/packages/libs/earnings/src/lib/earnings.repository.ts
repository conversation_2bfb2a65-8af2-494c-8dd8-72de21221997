import { Injectable, Logger } from '@nestjs/common';
import { PersistentService } from '@saito/persistent';
import {
  ServerStatus,
  TaskQueueItem,
  NodePerformance,
  EarningsStats,
  EarningsItem,
  ServerStatusResultSchema,
  TaskQueueCountResultSchema,
  TaskQueueResultSchema,
  NodePerformanceCountResultSchema,
  NodePerformanceResultSchema,
  EarningsStatsResultSchema,
  EarningsCountResultSchema,
  EarningsResultSchema,
  DeviceCheckResultSchema,
  EarningsChartDataResultSchema,
  TaskCountChartDataResultSchema,
  ChatHeatmapDataResultSchema,
  CreateEarningsResultSchema
} from '@saito/models';
import { HttpException, HttpStatus } from '@nestjs/common';
import { SQL as sql } from '@saito/common';

@Injectable()
export class EarningsRepository {
  private readonly logger = new Logger(EarningsRepository.name);

  constructor(private readonly persistentService: PersistentService) {}

  async getServerStatus(timeRange?: string): Promise<ServerStatus[]> {
    try {


      // Get server status from API usage metrics
      // This is a simplified implementation - in a real system, you might have a dedicated server status table
      // or more complex metrics to determine server health

      // Build the SQL query as a raw string
      let timeFilterClause = '';
      if (timeRange) {
        const days = parseInt(timeRange, 10);
        if (!isNaN(days)) {
          timeFilterClause = `AND timestamp >= NOW() - INTERVAL '${days} days'`;

        }
      }

      // Build the complete SQL query as a raw string
      const rawSql = `
        WITH api_metrics AS (
          SELECT
            CASE
              WHEN endpoint LIKE '%/api/%' THEN 'API Server'
              WHEN endpoint LIKE '%/ws/%' THEN 'WebSocket Server'
              ELSE 'Gateway Server'
            END as server,
            COUNT(*) as request_count,
            AVG(CASE WHEN status_code >= 200 AND status_code < 300 THEN 1 ELSE 0 END) * 100 as success_rate,
            AVG(EXTRACT(EPOCH FROM (NOW() - timestamp)) * 1000) as avg_response_time
          FROM saito_gateway.api_usage
          WHERE 1=1 ${timeFilterClause}
          GROUP BY
            CASE
              WHEN endpoint LIKE '%/api/%' THEN 'API Server'
              WHEN endpoint LIKE '%/ws/%' THEN 'WebSocket Server'
              ELSE 'Gateway Server'
            END
        )
        SELECT
          server,
          CASE
            WHEN request_count > 1000 THEN 85
            WHEN request_count > 500 THEN 65
            ELSE 45
          END as "apiLoad",
          CASE
            WHEN success_rate > 95 THEN 'Connected'
            WHEN success_rate > 85 THEN 'Minor Latency'
            ELSE 'Connection Issues'
          END as "wsStatus",
          avg_response_time as "taskTime",
          CASE
            WHEN success_rate > 95 AND avg_response_time < 200 THEN 'Normal'
            WHEN success_rate > 85 AND avg_response_time < 500 THEN 'High Load'
            ELSE 'Critical'
          END as health
        FROM api_metrics
      `;


      const result = await this.persistentService.pgPool.query(
        sql.type(ServerStatusResultSchema)`${sql.unsafe([rawSql])}`
      );

      // If no data is found, return default values
      if (result.rows.length === 0) {
        return [
          { server: 'API Server', apiLoad: 45, wsStatus: 'Connected', taskTime: 125.6, health: 'Normal' },
          { server: 'WebSocket Server', apiLoad: 35, wsStatus: 'Connected', taskTime: 95.2, health: 'Normal' },
          { server: 'Gateway Server', apiLoad: 55, wsStatus: 'Connected', taskTime: 145.8, health: 'Normal' }
        ];
      }

      // Map the readonly rows to a new mutable array of ServerStatus objects
      return result.rows.map((row) => ({
        server: row.server,
        apiLoad: row.apiLoad,
        wsStatus: row.wsStatus,
        taskTime: row.taskTime,
        health: row.health
      }));
    } catch (error) {
      this.logger.error(`Error getting server status: ${error}`);
      // Return default values in case of error or if tables don't exist yet
      return [
        { server: 'API Server', apiLoad: 45, wsStatus: 'Connected', taskTime: 125.6, health: 'Normal' },
        { server: 'WebSocket Server', apiLoad: 35, wsStatus: 'Connected', taskTime: 95.2, health: 'Normal' },
        { server: 'Gateway Server', apiLoad: 55, wsStatus: 'Connected', taskTime: 145.8, health: 'Normal' }
      ];
    }
  }

  async getTaskQueue(
    page: number,
    pageSize: number,
    userId: string,
    status?: string,
    search?: string,
    timeRange?: string
  ): Promise<{ data: TaskQueueItem[], total: number }> {
    try {


      // Build the WHERE clause as a raw string
      let whereClause = `WHERE t.status IS NOT NULL`;

      // Only filter by userId if it's provided and not empty
      if (userId && userId.trim() !== '') {
        whereClause += ` AND d.user_id = '${userId}'`;
      }

      if (status) {
        whereClause += ` AND t.status = '${status}'`;

      }

      if (search) {
        whereClause += ` AND (
          t.id::text ILIKE '%${search}%' OR
          t.model ILIKE '%${search}%' OR
          d.id::text ILIKE '%${search}%'
        )`;

      }

      // Add time range filter if provided
      if (timeRange) {
        const days = parseInt(timeRange, 10);
        if (!isNaN(days)) {
          whereClause += ` AND t.created_at >= NOW() - INTERVAL '${days} days'`;

        }
      }

      // Build the count query as a raw string
      const countSql = `
        SELECT COUNT(*) as total
        FROM saito_gateway.tasks t
        JOIN saito_gateway.devices d ON t.device_id = d.id
        ${whereClause}
      `;

      // Execute count query

      const countResult = await this.persistentService.pgPool.query(
        sql.type(TaskQueueCountResultSchema)`${sql.unsafe([countSql])}`
      );


      const total = parseInt(String(countResult.rows[0].total), 10);


      // Calculate the offset
      const offset = (page - 1) * pageSize;


      // Build the main query as a raw string
      const mainSql = `
        SELECT
          t.id,
          t.model,
          t.status,
          EXTRACT(EPOCH FROM (t.updated_at - t.created_at)) * 1000 as queue_time,
          d.id as device_id,
          t.total_duration as processing_time,
          COALESCE(e.total_rewards, 0) as earnings,
          COALESCE(t.prompt_eval_count + t.eval_count, 0) as tokens,
          t.created_at,
          t.updated_at
        FROM saito_gateway.tasks t
        JOIN saito_gateway.devices d ON t.device_id = d.id
        LEFT JOIN saito_gateway.earnings e ON t.id = e.task_id
        ${whereClause}
        ORDER BY t.created_at DESC
        LIMIT ${pageSize}
        OFFSET ${offset}
      `;

      // Execute main query

      const result = await this.persistentService.pgPool.query(
        sql.type(TaskQueueResultSchema)`${sql.unsafe([mainSql])}`
      );

      // Transform the data to match the expected format
      const data: TaskQueueItem[] = result.rows.map((row) => ({
        id: row.id,
        model: row.model,
        status: row.status.charAt(0).toUpperCase() + row.status.slice(1), // Capitalize status
        queue: row.queue_time || '-',
        device: row.device_id || '-',
        ptime: row.processing_time || '-',
        earn: row.earnings || '-',
        tokens: row.tokens || 0,
        created_at: row.created_at,
        updated_at: row.updated_at
      }));

      return { data, total };
    } catch (error) {
      this.logger.error(`Error getting task queue: ${error}`);
      // Return empty data in case of error
      return { data: [], total: 0 };
    }
  }

  async getNodePerformance(
    page: number,
    pageSize: number,
    userId: string,
    status?: string,
    search?: string,
    timeRange?: string
  ): Promise<{ data: NodePerformance[], total: number }> {
    try {


      // Build the WHERE clause as a raw string
      let whereClause = `WHERE 1=1`; // Default condition that's always true

      // Only filter by userId if it's provided and not empty
      if (userId && userId.trim() !== '') {
        whereClause += ` AND d.user_id = '${userId}'`;
      }

      if (status) {
        whereClause += ` AND d.status = '${status}'`;

      }

      if (search) {
        whereClause += ` AND (
          d.id::text ILIKE '%${search}%' OR
          d.device_type ILIKE '%${search}%' OR
          d.gpu_model ILIKE '%${search}%'
        )`;

      }

      // Add time range filter for metrics if provided
      let timeFilterClause = '';
      if (timeRange) {
        const days = parseInt(timeRange, 10);
        if (!isNaN(days)) {
          timeFilterClause = `AND dm.date >= CURRENT_DATE - INTERVAL '${days} days'`;

        }
      }

      // Build the count query as a raw string
      const countSql = `
        SELECT COUNT(*) as total
        FROM saito_gateway.devices d
        ${whereClause}
      `;

      // Execute count query

      const countResult = await this.persistentService.pgPool.query(
        sql.type(NodePerformanceCountResultSchema)`${sql.unsafe([countSql])}`
      );


      const total = parseInt(String(countResult.rows[0].total), 10);


      // Calculate the offset for pagination
      const offset = (page - 1) * pageSize;


      // Build the complete query as a raw string
      const mainSql = `
        WITH device_metrics_summary AS (
          SELECT
            device_id,
            AVG(ram_usage_percent) as avg_ram_usage,
            COUNT(*) as metrics_count
          FROM saito_gateway.device_metrics dm
          WHERE 1=1 ${timeFilterClause}
          GROUP BY device_id
        ),
        device_tasks AS (
          SELECT
            device_id,
            COUNT(*) as total_tasks,
            COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_tasks,
            SUM(CASE
                WHEN status = 'running' THEN 1
                WHEN status = 'pending' THEN 1
                WHEN status = 'completed' THEN 1
                ELSE 0
            END) as running_tasks
          FROM saito_gateway.tasks
          WHERE device_id IS NOT NULL
          ${timeFilterClause.includes('INTERVAL') ? timeFilterClause.replace('dm.date', 'created_at') : ''}
          GROUP BY device_id
        )
        SELECT
          d.id,
          COALESCE(d.device_type, '') || ' (' || COALESCE(d.gpu_model, 'Unknown') || ')' as device_type,
          COALESCE(dt.running_tasks, 0) as current_tasks,
          COALESCE(dms.avg_ram_usage, 0) as memory_usage,
          CASE
            WHEN dt.total_tasks = 0 THEN 100
            ELSE (dt.completed_tasks::float / dt.total_tasks::float) * 100
          END as completion_rate,
          COALESCE(SUM(e.total_rewards), 0) as total_earnings,
          d.status,
          d.last_ping,
          d.name
        FROM saito_gateway.devices d
        LEFT JOIN device_metrics_summary dms ON d.id = dms.device_id
        LEFT JOIN device_tasks dt ON d.id = dt.device_id
        LEFT JOIN saito_gateway.earnings e ON d.id = e.device_id
        ${whereClause}
        GROUP BY d.id, d.device_type, d.gpu_model, dt.running_tasks, dms.avg_ram_usage, dt.total_tasks, dt.completed_tasks, d.status, d.last_ping
        ORDER BY total_earnings DESC
        LIMIT ${pageSize}
        OFFSET ${offset}
      `;

      // Execute main query

      const result = await this.persistentService.pgPool.query(
        sql.type(NodePerformanceResultSchema)`${sql.unsafe([mainSql])}`
      );

      // Transform the data to match the expected format
      const data: NodePerformance[] = result.rows.map((row) => ({
        id: row.id,
        type: row.device_type,
        tasks: row.current_tasks || 0,
        mem: Math.round(row.memory_usage || 0),
        rate: Math.round(row.completion_rate || 0),
        earn: parseFloat(String(row.total_earnings || 0)),
        status: row.status,
        last_ping: row.last_ping ? String(row.last_ping) : undefined
      }));

      return { data, total };
    } catch (error) {
      this.logger.error(`Error getting node performance: ${error}`);
      // Return empty data in case of error
      return { data: [], total: 0 };
    }
  }
  async getEarningsStats(userId: string, timeRange?: string, year?: number, month?: number): Promise<EarningsStats> {
    try {


      // Build the time filter clause as a raw string
      let timeFilterClause = '1=1'; // Default condition that's always true

      if (year && month) {
        // Filter by specific year and month
        timeFilterClause = `EXTRACT(YEAR FROM e.date) = ${year} AND EXTRACT(MONTH FROM e.date) = ${month}`;
      } else if (year) {
        // Filter by specific year only
        timeFilterClause = `EXTRACT(YEAR FROM e.date) = ${year}`;
      } else if (timeRange) {
        // Use timeRange as fallback
        const days = parseInt(timeRange, 10);
        if (!isNaN(days)) {
          timeFilterClause = `e.date >= CURRENT_DATE - INTERVAL '${days} days'`;
        }
      }

      // Build the user filter clause
      let userFilterClause = '';
      if (userId && userId.trim() !== '') {
        userFilterClause = ` AND d.user_id = '${userId}'`;
      }

      // Build the complete SQL query as a raw string
      const rawSql = `
        SELECT
          COALESCE(SUM(e.total_rewards), 0) as total_earnings,
          COUNT(DISTINCT t.id) as total_tasks,
          COUNT(DISTINCT d.id) as total_nodes
        FROM saito_gateway.earnings e
        JOIN saito_gateway.devices d ON e.device_id = d.id
        LEFT JOIN saito_gateway.tasks t ON e.task_id = t.id
        WHERE ${timeFilterClause}${userFilterClause}
      `;


      const result = await this.persistentService.pgPool.query(
        sql.type(EarningsStatsResultSchema)`${sql.unsafe([rawSql])}`
      );

      if (result.rows.length === 0) {
        return {
          earnings: 0,
          tasks: 0,
          nodes: 0
        };
      }

      const stats: EarningsStats = {
        earnings: parseFloat(String(result.rows[0].total_earnings || 0)),
        tasks: parseInt(String(result.rows[0].total_tasks || 0), 10),
        nodes: parseInt(String(result.rows[0].total_nodes || 0), 10)
      };

      return stats;
    } catch (error) {
      this.logger.error(`Error getting earnings stats: ${error}`);
      // Return default values in case of error
      return {
        earnings: 0,
        tasks: 0,
        nodes: 0
      };
    }
  }
  async getEarnings(
    page: number,
    pageSize: number,
    userId: string,
    timeRange?: string
  ): Promise<{ data: EarningsItem[], total: number }> {
    try {


      // Build the time filter clause as a raw string
      let timeFilterClause = '';
      if (timeRange) {
        const days = parseInt(timeRange, 10);
        if (!isNaN(days)) {
          timeFilterClause = `AND e.date >= CURRENT_DATE - INTERVAL '${days} days'`;

        }
      }

      // Build the user filter clause
      let userFilterClause = '';
      if (userId && userId.trim() !== '') {
        userFilterClause = `e.user_id = '${userId}'`;
      } else {
        userFilterClause = '1=1'; // Default condition that's always true
      }

      // Build the count query as a raw string
      const countSql = `
        SELECT COUNT(*) as total
        FROM saito_gateway.earnings e
        WHERE ${userFilterClause} ${timeFilterClause}
      `;

      // Execute count query

      const countResult = await this.persistentService.pgPool.query(
        sql.type(EarningsCountResultSchema)`${sql.unsafe([countSql])}`
      );


      const total = parseInt(String(countResult.rows[0].total), 10);


      // Calculate the offset for pagination
      const offset = (page - 1) * pageSize;


      // Build the main query as a raw string
      const mainSql = `
        SELECT
          e.id,
          e.block_rewards,
          e.job_rewards,
          e.total_rewards,
          e.date,
          e.created_at,
          d.name as device_name,
          t.model,
          e.task_id,
          e.device_id,
          e.created_at,
          e.updated_at
        FROM
          saito_gateway.earnings e
        LEFT JOIN
          saito_gateway.devices d ON e.device_id = d.id
        LEFT JOIN
          saito_gateway.tasks t ON e.task_id = t.id
        WHERE
          ${userFilterClause} ${timeFilterClause}
        ORDER BY
          e.date DESC, e.created_at DESC
        LIMIT ${pageSize}
        OFFSET ${offset}
      `;

      // Execute main query

      const result = await this.persistentService.pgPool.query(
        sql.type(EarningsResultSchema)`${sql.unsafe([mainSql])}`
      );

      // Transform the data to match the expected format
      const data: EarningsItem[] = result.rows.map((row) => ({
        id: row.id,
        block_rewards: parseFloat(String(row.block_rewards)),
        job_rewards: parseFloat(String(row.job_rewards)),
        total_rewards: parseFloat(String(row.total_rewards)),
        date: row.date,
        created_at: row.created_at,
        device_name: row.device_name,
        task_id: row.task_id,
        device_id: row.device_id,
        updated_at: row.updated_at,
        model: row.model
      }));

      return { data, total };
    } catch (error) {
      this.logger.error(`Error getting earnings: ${error}`);
      // Return empty data in case of error
      return { data: [], total: 0 };
    }
  }
  async getDeviceEarnings(
    deviceId: string,
    page: number,
    pageSize: number,
    userId: string,
    timeRange?: string,
    status?: string
  ): Promise<{ data: EarningsItem[], total: number }> {
    try {
      // Check if device exists (without user filtering)
      const deviceCheckSql = `
        SELECT id FROM saito_gateway.devices
        WHERE id = '${deviceId}'
      `;

      const deviceResult = await this.persistentService.pgPool.query(
        sql.type(DeviceCheckResultSchema)`${sql.unsafe([deviceCheckSql])}`
      );

      if (deviceResult.rows.length === 0) {
        throw new HttpException('Device not found', HttpStatus.NOT_FOUND);
      }

      // Skip user verification - we want to get all earnings regardless of user

      // Build the time filter clause as a raw string
      let timeFilterClause = '';
      if (timeRange) {
        const days = parseInt(timeRange, 10);
        if (!isNaN(days)) {
          timeFilterClause = `AND e.date >= CURRENT_DATE - INTERVAL '${days} days'`;

        }
      }

      // Build the status filter clause as a raw string
      let statusFilterClause = '';
      if (status && status !== 'all') {
        statusFilterClause = `AND t.status = '${status}'`;

      }

      // Build the count query as a raw string
      const countSql = `
        SELECT COUNT(*) as total
        FROM saito_gateway.earnings e
        LEFT JOIN saito_gateway.tasks t ON e.task_id = t.id
        WHERE e.device_id = '${deviceId}' ${timeFilterClause} ${statusFilterClause}
      `;

      // Execute count query

      const countResult = await this.persistentService.pgPool.query(
        sql.type(EarningsCountResultSchema)`${sql.unsafe([countSql])}`
      );


      const total = parseInt(String(countResult.rows[0].total), 10);


      // Calculate the offset
      const offset = (page - 1) * pageSize;


      // Build the main query as a raw string
      const mainSql = `
        SELECT
          e.id,
          e.block_rewards,
          e.job_rewards,
          e.total_rewards,
          e.date,
          e.created_at,
          d.name as device_name,
          t.model,
          e.task_id,
          e.device_id,
          e.created_at,
          e.updated_at
        FROM
          saito_gateway.earnings e
        LEFT JOIN
          saito_gateway.devices d ON e.device_id = d.id
        LEFT JOIN
          saito_gateway.tasks t ON e.task_id = t.id
        WHERE
          e.device_id = '${deviceId}' ${timeFilterClause} ${statusFilterClause}
        ORDER BY
          e.date DESC, e.created_at DESC
        LIMIT ${pageSize}
        OFFSET ${offset}
      `;

      // Execute main query

      const result = await this.persistentService.pgPool.query(
        sql.type(EarningsResultSchema)`${sql.unsafe([mainSql])}`
      );

      // Transform the data to match the expected format
      const data: EarningsItem[] = result.rows.map((row) => ({
        id: row.id,
        block_rewards: parseFloat(String(row.block_rewards)),
        job_rewards: parseFloat(String(row.job_rewards)),
        total_rewards: parseFloat(String(row.total_rewards)),
        date: row.date,
        created_at: row.created_at,
        device_name: row.device_name,
        model: row.model,
        task_id: row.task_id,
        device_id: row.device_id,
        updated_at: row.updated_at
      }));

      return { data, total };
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }
      this.logger.error(`Error getting device earnings: ${error}`);
      // Return empty data in case of error
      return { data: [], total: 0 };
    }
  }

  /**
   * Get revenue chart data for dashboard
   */
  async getRevenueChartData(userId: string, timeRange?: string, year?: number, month?: number): Promise<Array<{ date: string; value: number }>> {
    try {
      let timeFilterClause = '1=1';
      let groupByClause = '';
      let selectClause = '';

      if (year && month) {
        // Daily data for specific month - generate all days in the month
        const rawSql = `
          SELECT
            ('${year}-${String(month).padStart(2, '0')}-' || LPAD(day_num::text, 2, '0'))::date as date,
            COALESCE(SUM(e.total_rewards), 0) as value
          FROM generate_series(1, EXTRACT(DAY FROM DATE_TRUNC('month', DATE '${year}-${String(month).padStart(2, '0')}-01') + INTERVAL '1 month - 1 day')::integer) as day_num
          LEFT JOIN saito_gateway.earnings e ON e.date = ('${year}-${String(month).padStart(2, '0')}-' || LPAD(day_num::text, 2, '0'))::date
          LEFT JOIN saito_gateway.devices d ON e.device_id = d.id
          WHERE 1=1${userId ? ` AND (d.user_id = '${userId}' OR d.user_id IS NULL)` : ''}
          GROUP BY day_num, date
          ORDER BY day_num
        `;

        const result = await this.persistentService.pgPool.query(
          sql.type(EarningsChartDataResultSchema)`${sql.unsafe([rawSql])}`
        );
        return result.rows.map(row => ({
          date: row.date,
          value: parseFloat(String(row.value || 0))
        }));
      } else if (year) {
        // Monthly data for specific year - generate all 12 months
        timeFilterClause = `EXTRACT(YEAR FROM e.date) = ${year}`;
        selectClause = `
          generate_series(1, 12) as month_num,
          '${year}-' || LPAD(generate_series(1, 12)::text, 2, '0') || '-01' as date,
          COALESCE(SUM(e.total_rewards), 0) as value
        `;
        groupByClause = `
          FROM generate_series(1, 12) as month_num
          LEFT JOIN saito_gateway.earnings e ON EXTRACT(YEAR FROM e.date) = ${year} AND EXTRACT(MONTH FROM e.date) = month_num
          LEFT JOIN saito_gateway.devices d ON e.device_id = d.id
          WHERE 1=1${userId ? ` AND (d.user_id = '${userId}' OR d.user_id IS NULL)` : ''}
          GROUP BY month_num
          ORDER BY month_num
        `;
        // Override the main query structure for this case
        const rawSql = `
          SELECT
            '${year}-' || LPAD(month_num::text, 2, '0') || '-01' as date,
            COALESCE(SUM(e.total_rewards), 0) as value
          FROM generate_series(1, 12) as month_num
          LEFT JOIN saito_gateway.earnings e ON EXTRACT(YEAR FROM e.date) = ${year} AND EXTRACT(MONTH FROM e.date) = month_num
          LEFT JOIN saito_gateway.devices d ON e.device_id = d.id
          WHERE 1=1${userId ? ` AND (d.user_id = '${userId}' OR d.user_id IS NULL)` : ''}
          GROUP BY month_num
          ORDER BY month_num
        `;

        const result = await this.persistentService.pgPool.query(sql.unsafe([rawSql]));
        return result.rows.map(row => ({
          date: row.date,
          value: parseFloat(String(row.value || 0))
        }));
      } else if (timeRange) {
        // Recent days data
        const days = parseInt(timeRange, 10);
        if (!isNaN(days)) {
          timeFilterClause = `e.date >= CURRENT_DATE - INTERVAL '${days} days'`;
        }
        selectClause = `e.date::text as date, SUM(e.total_rewards) as value`;
        groupByClause = `GROUP BY e.date ORDER BY e.date`;
      } else {
        // Default: last 30 days
        timeFilterClause = `e.date >= CURRENT_DATE - INTERVAL '30 days'`;
        selectClause = `e.date::text as date, SUM(e.total_rewards) as value`;
        groupByClause = `GROUP BY e.date ORDER BY e.date`;
      }

      const userFilterClause = userId ? ` AND d.user_id = '${userId}'` : '';

      const rawSql = `
        SELECT ${selectClause}
        FROM saito_gateway.earnings e
        JOIN saito_gateway.devices d ON e.device_id = d.id
        WHERE ${timeFilterClause}${userFilterClause}
        ${groupByClause}
      `;

      const result = await this.persistentService.pgPool.query(sql.unsafe([rawSql]));

      return result.rows.map(row => ({
        date: row.date,
        value: parseFloat(String(row.value || 0))
      }));
    } catch (error) {
      this.logger.error(`Error getting revenue chart data: ${error}`);
      return [];
    }
  }

  /**
   * Get request count chart data for dashboard
   */
  async getRequestCountChartData(userId: string, timeRange?: string, year?: number, month?: number): Promise<Array<{ date: string; value: number }>> {
    try {
      let timeFilterClause = '1=1';
      let groupByClause = '';
      let selectClause = '';

      if (year && month) {
        // Daily data for specific month - generate all days in the month
        const rawSql = `
          SELECT
            ('${year}-${String(month).padStart(2, '0')}-' || LPAD(day_num::text, 2, '0'))::date as date,
            COALESCE(COUNT(t.id), 0) as value
          FROM generate_series(1, EXTRACT(DAY FROM DATE_TRUNC('month', DATE '${year}-${String(month).padStart(2, '0')}-01') + INTERVAL '1 month - 1 day')::integer) as day_num
          LEFT JOIN saito_gateway.tasks t ON DATE(t.created_at) = ('${year}-${String(month).padStart(2, '0')}-' || LPAD(day_num::text, 2, '0'))::date
          LEFT JOIN saito_gateway.devices d ON t.device_id = d.id
          WHERE 1=1${userId ? ` AND (d.user_id = '${userId}' OR d.user_id IS NULL)` : ''}
          GROUP BY day_num, date
          ORDER BY day_num
        `;

        const result = await this.persistentService.pgPool.query(
          sql.type(TaskCountChartDataResultSchema)`${sql.unsafe([rawSql])}`
        );
        return result.rows.map(row => ({
          date: row.date,
          value: parseInt(String(row.value || 0), 10)
        }));
      } else if (year) {
        // Monthly data for specific year - generate all 12 months
        const rawSql = `
          SELECT
            '${year}-' || LPAD(month_num::text, 2, '0') || '-01' as date,
            COALESCE(COUNT(t.id), 0) as value
          FROM generate_series(1, 12) as month_num
          LEFT JOIN saito_gateway.tasks t ON EXTRACT(YEAR FROM t.created_at) = ${year} AND EXTRACT(MONTH FROM t.created_at) = month_num
          LEFT JOIN saito_gateway.devices d ON t.device_id = d.id
          WHERE 1=1${userId ? ` AND (d.user_id = '${userId}' OR d.user_id IS NULL)` : ''}
          GROUP BY month_num
          ORDER BY month_num
        `;

        const result = await this.persistentService.pgPool.query(
          sql.type(TaskCountChartDataResultSchema)`${sql.unsafe([rawSql])}`
        );
        return result.rows.map(row => ({
          date: row.date,
          value: parseInt(String(row.value || 0), 10)
        }));
      } else if (timeRange) {
        // Recent days data
        const days = parseInt(timeRange, 10);
        if (!isNaN(days)) {
          timeFilterClause = `t.created_at >= CURRENT_DATE - INTERVAL '${days} days'`;
        }
        selectClause = `DATE(t.created_at) as date, COUNT(t.id) as value`;
        groupByClause = `GROUP BY DATE(t.created_at) ORDER BY date`;
      } else {
        // Default: last 30 days
        timeFilterClause = `t.created_at >= CURRENT_DATE - INTERVAL '30 days'`;
        selectClause = `DATE(t.created_at) as date, COUNT(t.id) as value`;
        groupByClause = `GROUP BY DATE(t.created_at) ORDER BY date`;
      }

      const userFilterClause = userId ? ` AND d.user_id = '${userId}'` : '';

      const rawSql = `
        SELECT ${selectClause}
        FROM saito_gateway.tasks t
        JOIN saito_gateway.devices d ON t.device_id = d.id
        WHERE ${timeFilterClause}${userFilterClause}
        ${groupByClause}
      `;

      const result = await this.persistentService.pgPool.query(
        sql.type(TaskCountChartDataResultSchema)`${sql.unsafe([rawSql])}`
      );

      return result.rows.map(row => ({
        date: row.date,
        value: parseInt(String(row.value || 0), 10)
      }));
    } catch (error) {
      this.logger.error(`Error getting request count chart data: ${error}`);
      return [];
    }
  }

  /**
   * Get chat heatmap data for dashboard calendar
   */
  async getChatHeatmapData(userId: string, timeRange?: string, year?: number, month?: number): Promise<Array<{ date: string; count: number }>> {
    try {
      let timeFilterClause = '1=1';

      if (year && month) {
        // Daily data for specific month
        timeFilterClause = `EXTRACT(YEAR FROM t.created_at) = ${year} AND EXTRACT(MONTH FROM t.created_at) = ${month}`;
      } else if (year) {
        // Monthly data for specific year
        timeFilterClause = `EXTRACT(YEAR FROM t.created_at) = ${year}`;
      } else if (timeRange) {
        // Recent days data
        const days = parseInt(timeRange, 10);
        if (!isNaN(days)) {
          timeFilterClause = `t.created_at >= CURRENT_DATE - INTERVAL '${days} days'`;
        }
      } else {
        // Default: current month
        timeFilterClause = `EXTRACT(YEAR FROM t.created_at) = EXTRACT(YEAR FROM CURRENT_DATE) AND EXTRACT(MONTH FROM t.created_at) = EXTRACT(MONTH FROM CURRENT_DATE)`;
      }

      const userFilterClause = userId ? ` AND d.user_id = '${userId}'` : '';

      const rawSql = `
        SELECT
          DATE(t.created_at) as date,
          COUNT(t.id) as count
        FROM saito_gateway.tasks t
        JOIN saito_gateway.devices d ON t.device_id = d.id
        WHERE ${timeFilterClause}${userFilterClause}
        GROUP BY DATE(t.created_at)
        ORDER BY date
      `;

      const result = await this.persistentService.pgPool.query(
        sql.type(ChatHeatmapDataResultSchema)`${sql.unsafe([rawSql])}`
      );

      return result.rows.map(row => ({
        date: row.date,
        count: parseInt(String(row.count || 0), 10)
      }));
    } catch (error) {
      this.logger.error(`Error getting chat heatmap data: ${error}`);
      return [];
    }
  }

  /**
   * Create an earnings record for a completed task
   * @param taskId Task ID
   * @param deviceId Device ID
   * @param metrics Task metrics for calculating earnings
   * @returns Created earnings record ID
   */
  async createEarningsRecord(taskId: string, deviceId: string, metrics: any): Promise<string> {
    try {
      // Calculate earnings based on task metrics
      // Base calculation: token count * rate + processing time bonus
      const tokenCount = (metrics.prompt_eval_count || 0) + (metrics.eval_count || 0);
      const processingTimeMs = metrics.total_duration || 0;

      // Base rates (can be adjusted based on business logic)
      const tokenRate = 0.001; // $0.001 per token
      const timeBonus = Math.min(processingTimeMs / 1000000, 1.0); // Bonus for processing time (max 1.0)

      // Calculate block rewards and job rewards
      const blockRewards = Math.round((tokenCount * tokenRate + timeBonus * 0.5) * 10000) / 10000;
      const jobRewards = Math.round((tokenCount * tokenRate * 1.5 + timeBonus * 0.8) * 10000) / 10000;
      const totalRewards = Math.round((blockRewards + jobRewards) * 10000) / 10000;

      // Generate unique earnings ID using UUID format
      const { randomUUID } = await import('crypto');
      const earningsId = randomUUID();

      const rawSql = `
        INSERT INTO saito_gateway.earnings (
          id,
          task_id,
          device_id,
          user_id,
          date,
          block_rewards,
          job_rewards,
          created_at,
          updated_at
        )
        SELECT
          '${earningsId}',
          '${taskId}',
          '${deviceId}',
          d.user_id,
          CURRENT_DATE,
          ${blockRewards},
          ${jobRewards},
          NOW(),
          NOW()
        FROM saito_gateway.devices d
        WHERE d.id = '${deviceId}'
        RETURNING id
      `;

      await this.persistentService.pgPool.query(
        sql.type(CreateEarningsResultSchema)`${sql.unsafe([rawSql])}`
      );

      this.logger.log(`Created earnings record ${earningsId} for task ${taskId}: $${totalRewards}`);

      return earningsId;
    } catch (error) {
      this.logger.error(`Error creating earnings record for task ${taskId}: ${error}`);
      throw error;
    }
  }
}