{"name": "lib-earnings", "$schema": "../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "packages/libs/earnings/src", "projectType": "library", "targets": {"build": {"executor": "@nx/js:tsc", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/packages/libs/earnings", "main": "packages/libs/earnings/src/index.ts", "tsConfig": "packages/libs/earnings/tsconfig.lib.json", "assets": ["packages/libs/earnings/*.md"]}}, "lint": {"executor": "@nx/eslint:lint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["packages/libs/earnings/**/*.ts", "packages/libs/earnings/package.json"]}}}, "tags": []}