import { NestFactory } from '@nestjs/core';
import { EthStorageModule, EthStorageService } from "@saito/ethstorage";
import { env } from '../env';
import * as console from "console";

describe('EthStorage API', () => {
  let ethStorageService: EthStorageService;

  beforeEach(async () => {
    const context = await NestFactory.createApplicationContext(
      EthStorageModule,
    );
    ethStorageService = context.get(EthStorageService);
  });

  it('ethstorage', async () => {
    await ethStorageService.write("******************************************:memory", Buffer.from('[]'))
    //await ethStorageService.write("userAddress:memory:basic_info:location", Buffer.from('User is now in Denver'));
    const value = await ethStorageService.read("ai.saito");
    expect(value).toEqual(Buffer.from('123123'));
  });
});
