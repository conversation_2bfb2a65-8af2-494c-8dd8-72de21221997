{"name": "external-api-e2e", "$schema": "../../../node_modules/nx/schemas/project-schema.json", "projectType": "application", "targets": {"e2e": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{e2eProjectRoot}"], "options": {"jestConfig": "packages/apps/external-api-e2e/jest.config.ts", "passWithNoTests": true}}, "lint": {"executor": "@nx/eslint:lint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["packages/apps/external-api-e2e/**/*.{js,ts}"]}}}}