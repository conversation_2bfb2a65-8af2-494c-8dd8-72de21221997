import {
  Controller,
  Get,
  Query,
  Logger,
  HttpException,
  HttpStatus,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiQuery } from '@nestjs/swagger';
import { PublicKeyService } from '@saito/executor';

export interface PublicKeyResponse {
  keyId: string;
  publicKey: string; // base64 encoded X25519 public key
  provider: string;
  region: string;
  executorUrl?: string;
  registeredAt: string;
  lastHeartbeat?: string;
  status: 'active' | 'inactive';
}

@ApiTags('public-keys')
@Controller('public-keys')
export class PublicKeysController {
  private readonly logger = new Logger(PublicKeysController.name);

  constructor(private readonly publicKeyService: PublicKeyService) {}

  /**
   * Get available public keys for encryption
   * Used by Provider (frontend) to get executor public keys for encryption
   * Endpoint: /public-keys?provider=openai&region=asia
   */
  @Get()
  @ApiOperation({
    summary: 'Get available public keys for encryption',
    description: 'Retrieves executor public keys for the specified provider and region. Used by Provider to encrypt API keys.',
  })
  @ApiQuery({
    name: 'provider',
    description: 'Provider name (openai, claude, anthropic, etc.)',
    example: 'openai',
    required: true,
  })
  @ApiQuery({
    name: 'region',
    description: 'Region name (asia, us, europe, etc.)',
    example: 'asia',
    required: false,
  })
  @ApiQuery({
    name: 'status',
    description: 'Filter by status',
    enum: ['active', 'inactive', 'all'],
    required: false,
  })
  @ApiResponse({
    status: 200,
    description: 'Public keys retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean' },
        data: {
          type: 'object',
          properties: {
            keys: {
              type: 'array',
              items: {
                type: 'object',
                properties: {
                  keyId: { type: 'string', description: 'Executor public key identifier' },
                  publicKey: { type: 'string', description: 'Base64 encoded X25519 public key' },
                  provider: { type: 'string', description: 'Provider name' },
                  region: { type: 'string', description: 'Region name' },
                  executorUrl: { type: 'string', description: 'Executor URL (optional)' },
                  registeredAt: { type: 'string', description: 'Registration timestamp' },
                  lastHeartbeat: { type: 'string', description: 'Last heartbeat timestamp' },
                  status: { type: 'string', enum: ['active', 'inactive'] },
                },
              },
            },
            totalCount: { type: 'number', description: 'Total number of keys' },
            provider: { type: 'string', description: 'Requested provider' },
            region: { type: 'string', description: 'Requested region' },
          },
        },
      },
    },
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - missing or invalid parameters',
  })
  @ApiResponse({
    status: 404,
    description: 'No public keys found for the specified criteria',
  })
  @ApiResponse({
    status: 500,
    description: 'Internal server error',
  })
  async getPublicKeys(
    @Query('provider') provider: string,
    @Query('region') region?: string,
    @Query('status') status: 'active' | 'inactive' | 'all' = 'active',
  ) {
    try {
      // Validate required parameters
      if (!provider) {
        throw new HttpException(
          {
            success: false,
            error: {
              message: 'Provider parameter is required',
              code: 'MISSING_PROVIDER',
            },
          },
          HttpStatus.BAD_REQUEST,
        );
      }

      // Validate provider format
      const validProviders = ['openai', 'claude', 'anthropic', 'google', 'cohere'];
      if (!validProviders.includes(provider.toLowerCase())) {
        throw new HttpException(
          {
            success: false,
            error: {
              message: `Invalid provider. Supported providers: ${validProviders.join(', ')}`,
              code: 'INVALID_PROVIDER',
            },
          },
          HttpStatus.BAD_REQUEST,
        );
      }

      this.logger.log(`Getting public keys for provider: ${provider}, region: ${region || 'all'}, status: ${status}`);

      // Get public keys from etcd
      const result = await this.publicKeyService.getPublicKeys(provider, region, status);

      if (result.totalCount === 0) {
        throw new HttpException(
          {
            success: false,
            error: {
              message: `No public keys found for provider: ${provider}${region ? `, region: ${region}` : ''}`,
              code: 'NO_KEYS_FOUND',
            },
          },
          HttpStatus.NOT_FOUND,
        );
      }

      this.logger.log(`Successfully retrieved ${result.totalCount} public keys for ${provider}:${region || 'all'}`);

      return {
        success: true,
        data: {
          keys: result.keys,
          totalCount: result.totalCount,
          provider,
          region: region || 'all',
        },
      };
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }

      this.logger.error(
        `Failed to get public keys for provider ${provider}: ${error instanceof Error ? error.message : String(error)}`,
      );

      throw new HttpException(
        {
          success: false,
          error: {
            message: 'Internal server error while retrieving public keys',
            code: 'INTERNAL_ERROR',
          },
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}
