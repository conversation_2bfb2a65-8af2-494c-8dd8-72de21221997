import {
  Controller,
  Post,
  Get,
  All,
  Body,
  Res,
  Req,
  HttpStatus,
  HttpException,
  Logger,
  UseGuards,
  Inject
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiBody, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { OpenAIService, OpenAIRouterService } from '@saito/openai';
import { NodeService } from '@saito/node';
import { OptionalApiKeyGuard } from '../guards/api-key.guard';
import { Response, Request } from 'express';
import {
  OpenAIChatCompletionRequestSchema,
  OpenAIChatCompletionResponseSchema,
  OpenAICompletionRequestSchema,
  OpenAICompletionResponseSchema,
  OpenAIEmbeddingRequestSchema,
  OpenAIEmbeddingResponseSchema,
  OpenAIErrorResponseSchema
} from '@saito/models';
import { z } from 'zod';

// 定义类型
export type OpenAIChatCompletionRequest = z.infer<typeof OpenAIChatCompletionRequestSchema>;
export type OpenAIChatCompletionResponse = z.infer<typeof OpenAIChatCompletionResponseSchema>;
export type OpenAICompletionRequest = z.infer<typeof OpenAICompletionRequestSchema>;
export type OpenAICompletionResponse = z.infer<typeof OpenAICompletionResponseSchema>;
export type OpenAIEmbeddingRequest = z.infer<typeof OpenAIEmbeddingRequestSchema>;
export type OpenAIEmbeddingResponse = z.infer<typeof OpenAIEmbeddingResponseSchema>;
export type OpenAIErrorResponse = z.infer<typeof OpenAIErrorResponseSchema>;

@ApiTags('OpenAI')
@Controller(['openai', 'openai/v1'])
export class OpenAIController {
  private readonly logger = new Logger(OpenAIController.name);

  constructor(
    @Inject('OpenAIService') private readonly openaiService: OpenAIService,
    private readonly nodeService: NodeService,
    private readonly openaiRouter: OpenAIRouterService
  ) { }

  handleError(error: any, res: Response) {
    this.logger.error(`Error in OpenAI controller: ${error.message || error}`);

    if (error instanceof HttpException) {
      return res.status(error.getStatus()).json(error.getResponse());
    }

    // Format error according to OpenAI API error format
    const statusCode = error?.status || HttpStatus.INTERNAL_SERVER_ERROR;
    const errorMessage = error?.message || 'Internal server error';
    return res.status(statusCode).json({
      error: {
        message: errorMessage,
        type: "api_error",
        param: null,
        code: null
      }
    });
  }

  @Post('chat/completions')
  @UseGuards(OptionalApiKeyGuard)
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Create chat completions',
    description: 'Creates a completion for the chat message'
  })
  @ApiBody({
    description: 'The chat completion request',
    schema: {
      type: 'object',
      properties: {
        model: {
          type: 'string',
          description: 'ID of the model to use'
        },
        messages: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              role: {
                type: 'string',
                description: 'The role of the message author (system, user, assistant)'
              },
              content: {
                type: 'string',
                description: 'The content of the message'
              }
            }
          },
          description: 'A list of messages comprising the conversation so far'
        },
        temperature: {
          type: 'number',
          description: 'Sampling temperature between 0 and 2'
        },
        top_p: {
          type: 'number',
          description: 'Nucleus sampling parameter'
        },
        stream: {
          type: 'boolean',
          description: 'Whether to stream back partial progress'
        }
      },
      required: ['model', 'messages']
    }
  })
  @ApiResponse({
    status: 200,
    description: 'Chat completion created successfully',
    schema: {
      type: 'object',
      properties: {
        id: { type: 'string' },
        object: { type: 'string' },
        created: { type: 'integer' },
        choices: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              index: { type: 'integer' },
              message: {
                type: 'object',
                properties: {
                  role: { type: 'string' },
                  content: { type: 'string' }
                }
              },
              finish_reason: { type: 'string' }
            }
          }
        },
        usage: {
          type: 'object',
          properties: {
            prompt_tokens: { type: 'integer' },
            completion_tokens: { type: 'integer' },
            total_tokens: { type: 'integer' }
          }
        }
      }
    }
  })
  @ApiResponse({ status: 400, description: 'Bad request' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 404, description: 'Model not found' })
  @ApiResponse({ status: 500, description: 'Internal server error' })
  async createChatCompletion(
    @Body() body: OpenAIChatCompletionRequest,
    @Res() res: Response,
    @Req() req: Request
  ) {
    try {
      this.logger.log(`Received chat completion request: ${JSON.stringify(body).substring(0, 200)}...`);

      // 检查是否应该路由到Executor服务
      const routeDecision = this.openaiRouter.makeRouteDecision(body.model);

      if (routeDecision.shouldRouteToExecutor) {
        this.logger.log(`Routing chat completion to Executor: ${routeDecision.reason}`);
        await this.openaiRouter.routeChatCompletion(body, res, req);
      } else {
        this.logger.log(`Handling chat completion locally: ${routeDecision.reason}`);
        await this.openaiService.handleChatCompletion(req.path, {...body}, res, req);
      }
    } catch (error) {
      this.handleError(error, res);
    }
  }

  @Post('completions')
  @UseGuards(OptionalApiKeyGuard)
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Create completions',
    description: 'Creates a completion for the provided prompt'
  })
  @ApiBody({
    description: 'The completion request',
    schema: {
      type: 'object',
      properties: {
        model: {
          type: 'string',
          description: 'ID of the model to use'
        },
        prompt: {
          type: 'string',
          description: 'The prompt to generate completions for'
        },
        temperature: {
          type: 'number',
          description: 'Sampling temperature between 0 and 2'
        },
        max_tokens: {
          type: 'integer',
          description: 'Maximum number of tokens to generate'
        },
        stream: {
          type: 'boolean',
          description: 'Whether to stream back partial progress'
        }
      },
      required: ['model', 'prompt']
    }
  })
  @ApiResponse({
    status: 200,
    description: 'Completion created successfully'
  })
  @ApiResponse({ status: 400, description: 'Bad request' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 404, description: 'Model not found' })
  @ApiResponse({ status: 500, description: 'Internal server error' })
  async createCompletion(
    @Body() body: OpenAICompletionRequest,
    @Res() res: Response,
    @Req() req: Request
  ) {
    try {
      // 检查是否应该路由到Executor服务
      const routeDecision = this.openaiRouter.makeRouteDecision(body.model);

      if (routeDecision.shouldRouteToExecutor) {
        this.logger.log(`Routing completion to Executor: ${routeDecision.reason}`);
        await this.openaiRouter.routeCompletion(body, res, req);
      } else {
        this.logger.log(`Handling completion locally: ${routeDecision.reason}`);
        await this.openaiService.handleCompletion(req.path, body, res, req);
      }
    } catch (error) {
      this.handleError(error, res);
    }
  }


  @All('models')
  @UseGuards(OptionalApiKeyGuard)
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'List models',
    description: 'Lists the currently available models, and provides basic information about each one such as the owner and availability.'
  })
  @ApiResponse({
    status: 200,
    description: 'List of models',
    schema: {
      type: 'object',
      properties: {
        object: { type: 'string', example: 'list' },
        data: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              id: { type: 'string', example: 'gpt-3.5-turbo' },
              object: { type: 'string', example: 'model' },
              created: { type: 'integer', example: 1686935002 },
              owned_by: { type: 'string', example: 'saito' }
            }
          }
        }
      }
    }
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 500, description: 'Internal server error' })
  async listModels(@Res() res: Response) {
    try {
      // Use the injected NodeService
      const models = await this.nodeService.getOnlineDeviceModels('openai');
      return res.json(models);
    } catch (error) {
      this.handleError(error, res);
    }
  }

  @Get('router/stats')
  @ApiOperation({
    summary: 'Get router statistics',
    description: 'Get statistics about the OpenAI router and executor services'
  })
  @ApiResponse({
    status: 200,
    description: 'Router statistics',
    schema: {
      type: 'object',
      properties: {
        executors: {
          type: 'object',
          properties: {
            total: { type: 'number' },
            healthy: { type: 'number' },
            lastUpdate: { type: 'number' }
          }
        },
        routing: {
          type: 'object',
          properties: {
            openaiRequests: { type: 'number' },
            localRequests: { type: 'number' }
          }
        }
      }
    }
  })
  async getRouterStats(@Res() res: Response) {
    try {
      const stats = await this.openaiRouter.getRouteStats();
      return res.json(stats);
    } catch (error) {
      this.handleError(error, res);
    }
  }

  @All('*')
  @ApiOperation({
    summary: 'Proxy a request to an AI model',
    description: 'Proxy a request to an AI model'
  })
  @ApiResponse({ status: 200, description: 'Proxy response' })
  @ApiResponse({ status: 400, description: 'Bad request' })
  @ApiResponse({ status: 500, description: 'Internal server error' })
  // @UseGuards(ApiKeyGuard)
  // @ApiBearerAuth()
  async proxy(@Req() req: Request, @Res() res: Response) {
    try {
      const result = await this.openaiService.proxyRequest(req);
      res.json(result);
    } catch (error) {
      this.logger.error(`Proxy error: ${error}`);

      // Check if headers have already been sent
      if (res.headersSent) {
        this.logger.error('Cannot send error response: Headers already sent');
        // If the response is still writable, try to end it
        if (!res.writableEnded) {
          try {
            res.end();
          } catch (endError) {
            this.logger.error(`Error ending response: ${endError}`);
          }
        }
        return;
      }

      // If headers haven't been sent, send an appropriate error response
      if (error instanceof HttpException) {
        res.status(error.getStatus()).json({
          message: error.message,
        });
      } else {
        res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
          message: 'Internal server error',
        });
      }
    }
  }
}
