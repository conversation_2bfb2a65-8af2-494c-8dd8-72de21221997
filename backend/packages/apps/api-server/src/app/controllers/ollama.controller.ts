import { Controller, Post, Body, Res, Req, Get, HttpException, HttpStatus, Logger, UseGuards, All, Inject } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBody, ApiBearerAuth } from '@nestjs/swagger';
import { Response, Request } from 'express';
import { OllamaService } from '@saito/ollama';
import { NodeService } from '@saito/node';
import { OptionalApiKeyGuard } from '../guards/api-key.guard';
import {
  OllamaChatRequestSchema,
  OllamaGenerateRequestSchema,
  OllamaEmbedRequestSchema,
  OllamaEmbedMultiRequestSchema
} from '@saito/models';
import { z } from 'zod';

export type OllamaChatRequestBody = z.infer<typeof OllamaChatRequestSchema>;
export type OllamaGenerateRequestBody = z.infer<typeof OllamaGenerateRequestSchema>;
export type OllamaEmbedRequestBody = z.infer<typeof OllamaEmbedRequestSchema>;
export type OllamaEmbedMultiRequestBody = z.infer<typeof OllamaEmbedMultiRequestSchema>;

@ApiTags('ollama')
@Controller('ollama')
export class OllamaChatController {
  private readonly logger = new Logger(OllamaChatController.name);

  constructor(
    @Inject('OllamaService') private readonly ollamaService: OllamaService,
    private readonly nodeService: NodeService
  ) {}

  @Post('api/chat')
  @UseGuards(OptionalApiKeyGuard)
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Chat with an AI model',
    description: 'Send a chat request to an AI model. Supports both authenticated (high priority) and anonymous (low priority) requests.'
  })
  @ApiResponse({ status: 200, description: 'Chat response' })
  @ApiResponse({ status: 400, description: 'Bad request' })
  @ApiResponse({ status: 401, description: 'Invalid API key (if provided)' })
  @ApiResponse({ status: 500, description: 'Internal server error' })
  async chat(@Body() body: OllamaChatRequestBody, @Res() res: Response, @Req() req: Request) {
    try {
      await this.ollamaService.handleChat(req.path, body, res, req);
    } catch (error) {
      this.logger.error(`Chat error: ${error}`);

      // Check if headers have already been sent
      if (res.headersSent) {
        this.logger.error('Cannot send error response: Headers already sent');
        // If the response is still writable, try to end it
        if (!res.writableEnded) {
          try {
            res.end();
          } catch (endError) {
            this.logger.error(`Error ending response: ${endError}`);
          }
        }
        return;
      }

      // If headers haven't been sent, send an appropriate error response
      if (error instanceof HttpException) {
        res.status(error.getStatus()).json({
          message: error.message,
        });
      } else {
        res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
          message: 'Internal server error',
        });
      }
    }
  }

  @Post('api/generate')
  @UseGuards(OptionalApiKeyGuard)
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Generate text with an AI model',
    description: 'Send a text generation request to an AI model. Supports both authenticated (high priority) and anonymous (low priority) requests.'
  })
  @ApiResponse({ status: 200, description: 'Generated text' })
  @ApiResponse({ status: 400, description: 'Bad request' })
  @ApiResponse({ status: 401, description: 'Invalid API key (if provided)' })
  @ApiResponse({ status: 500, description: 'Internal server error' })
  async generate(@Body() body: OllamaGenerateRequestBody, @Res() res: Response, @Req() req: Request) {
    try {
      await this.ollamaService.handleGenerate(req.path, body, res, req);
    } catch (error) {
      this.logger.error(`Generate error: ${error}`);

      // Check if headers have already been sent
      if (res.headersSent) {
        this.logger.error('Cannot send error response: Headers already sent');
        // If the response is still writable, try to end it
        if (!res.writableEnded) {
          try {
            res.end();
          } catch (endError) {
            this.logger.error(`Error ending response: ${endError}`);
          }
        }
        return;
      }

      // If headers haven't been sent, send an appropriate error response
      if (error instanceof HttpException) {
        res.status(error.getStatus()).json({
          message: error.message,
        });
      } else {
        res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
          message: 'Internal server error',
        });
      }
    }
  }

  @Get('api/tags')
  @ApiOperation({
    summary: 'List models',
    description: 'Lists the currently available models'
  })
  @ApiResponse({
    status: 200,
    description: 'List of models',
    schema: {
      type: 'object',
      properties: {
        models: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              name: { type: 'string', example: 'llama3:latest' },
              modified_at: { type: 'string', example: '2023-12-07T09:32:18.757212583-08:00' },
              size: { type: 'integer', example: 3825819519 },
              digest: { type: 'string', example: 'fe938a131f40e6f6d40083c9f0f430a515233eb2edaa6d72eb85c50d64f2300e' },
              details: {
                type: 'object',
                properties: {
                  format: { type: 'string', example: 'gguf' },
                  family: { type: 'string', example: 'llama' },
                  families: {
                    type: 'array',
                    items: { type: 'string' },
                    nullable: true
                  },
                  parameter_size: { type: 'string', example: '7B' },
                  quantization_level: { type: 'string', example: 'Q4_0' }
                }
              }
            }
          }
        }
      }
    }
  })
  @ApiResponse({ status: 500, description: 'Internal server error' })
  async listModels(@Res() res: Response) {
    try {
      // Get models in Ollama format
      const models = await this.nodeService.getOnlineDeviceModels('ollama');
      return res.send(models);
    } catch (error) {
      this.logger.error(`List models error: ${error}`);

      // If headers haven't been sent, send an appropriate error response
      if (!res.headersSent) {
        if (error instanceof HttpException) {
          res.status(error.getStatus()).json({
            message: error.message,
          });
        } else {
          res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
            message: 'Internal server error',
          });
        }
      }
    }
  }

  @All('*')
  @ApiOperation({
    summary: 'Proxy a request to an AI model',
    description: 'Proxy a request to an AI model'
  })
  @ApiResponse({ status: 200, description: 'Proxy response' })
  @ApiResponse({ status: 400, description: 'Bad request' })
  @ApiResponse({ status: 500, description: 'Internal server error' })
  // @UseGuards(ApiKeyGuard)
  // @ApiBearerAuth()
  async proxy(@Req() req: Request, @Res() res: Response) {
    try {
      this.logger.log(`Proxying request: ${req.method} ${req.url}`);
      const result = await this.ollamaService.proxyRequest(req);
      res.json(result);
    } catch (error) {
      this.logger.error(`Proxy error: ${error}`);

      // Check if headers have already been sent
      if (res.headersSent) {
        this.logger.error('Cannot send error response: Headers already sent');
        // If the response is still writable, try to end it
        if (!res.writableEnded) {
          try {
            res.end();
          } catch (endError) {
            this.logger.error(`Error ending response: ${endError}`);
          }
        }
        return;
      }

      // If headers haven't been sent, send an appropriate error response
      if (error instanceof HttpException) {
        res.status(error.getStatus()).json({
          message: error.message,
        });
      } else {
        res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
          message: 'Internal server error',
        });
      }
    }
  }
}
