import { Body, Controller, Inject, Logger, Post } from '@nestjs/common';
import { TunnelMessage } from '@saito/models';
import { TunnelService, MultiTransportService } from '@saito/tunnel';

@Controller('libp2p')
export class Libp2pController {
  private logger = new Logger(Libp2pController.name);

  constructor(@Inject('MultiTransportService') private readonly multiTransportService: MultiTransportService) {}

  @Post('message')
  async receiveLibp2pMessage(@Body() message: TunnelMessage) {
    await this.multiTransportService.handleIncomingMessage(message, 'libp2p');
    this.logger.debug(`Received from libp2p.`);

    return { status: 'ok' };
  }
}
