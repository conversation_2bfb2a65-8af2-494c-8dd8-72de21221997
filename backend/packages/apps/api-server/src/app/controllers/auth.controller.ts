// auth.controller.ts
import {
  Controller,
  Post,
  UnauthorizedException,
  Body,
} from "@nestjs/common";
import {ApiTags, ApiOperation, ApiBody, ApiResponse} from '@nestjs/swagger';
import {AuthService} from "@saito/auth";

@ApiTags('auth')
@Controller('auth')
export class AuthController {

  constructor(private readonly authService: AuthService) {}

  @Post('sign-in')
  @ApiOperation({ summary: 'Sign in with Ethereum', description: 'Signs in a user by verifying their Ethereum signature and message.' })
  @ApiBody({
    description: 'The message and signature provided by the user',
    type: Object,
    schema: {
      properties: {
        message: {
          type: 'string',
          description: 'The EIP-4361 formatted message signed by the user',
        },
        signature: {
          type: 'string',
          description: 'The signature of the message, created using the user\'s private key',
        },
        referralCode: {
          type: 'string',
          description: 'Optional, The referral code to be used',
        },
      },
      required: ['message', 'signature'],
    },
  })
  @ApiResponse({ status: 200, description: 'Successfully signed in', schema: { example: { accessToken: 'JWT_TOKEN_HERE', userId: 'USER_ID_HERE', ok: true } } })
  @ApiResponse({ status: 401, description: 'Unauthorized or invalid credentials' })
  async signIn(@Body() signInDto: { message: string; signature: string, referralCode?: string }) {
    const { message, signature, referralCode } = signInDto;

    // Call the AuthService to handle the sign-in logic
    try {
      const { accessToken, userId } = await this.authService.signIn(message, signature);
      return { accessToken, userId, ok: true };
    } catch (error) {
      throw new UnauthorizedException(error);
    }
  }
}
