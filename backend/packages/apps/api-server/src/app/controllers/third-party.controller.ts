import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  UseGuards,
  Request,
  HttpException,
  HttpStatus,
  Logger
} from "@nestjs/common";
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiParam, ApiBody } from '@nestjs/swagger';
import { JwtAuthGuard } from "@saito/auth";
import { EncryptedApiKeyService } from "@saito/third-party";
import { ThirdPartyKeyService, EtcdSyncService } from "@saito/third-party";
import { AuthenticatedRequest, ApiError } from '../types/request.types';

@ApiTags('third-party')
@Controller('/third-party')
export class ThirdPartyController {
  private readonly logger = new Logger(ThirdPartyController.name);

  constructor(
    private readonly thirdPartyKeyService: ThirdPartyKeyService,
    private readonly encryptedApiKeyService: EncryptedApiKeyService,
    private readonly etcdSyncService: EtcdSyncService
  ) {}

  // 辅助函数：处理API错误
  private handleApiError(error: unknown, defaultMessage: string): never {
    const apiError = error as ApiError;

    if (error instanceof HttpException) {
      throw error;
    }

    throw new HttpException(
      apiError.message || defaultMessage,
      apiError.status || HttpStatus.INTERNAL_SERVER_ERROR
    );
  }

  // =============================================
  // Third-Party Key Management Endpoints
  // =============================================

  @Get('third-party-keys')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Get third-party keys for user',
    description: 'Retrieves all third-party keys associated with the authenticated user'
  })
  @ApiResponse({
    status: 200,
    description: 'Third-party keys retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean' },
        data: {
          type: 'object',
          properties: {
            items: {
              type: 'array',
              items: {
                type: 'object',
                properties: {
                  id: { type: 'string' },
                  name: { type: 'string' },
                  provider: { type: 'string' },
                  status: { type: 'string' },
                  createdAt: { type: 'string' }
                }
              }
            },
            total: { type: 'number' },
            page: { type: 'number' },
            pageSize: { type: 'number' }
          }
        }
      }
    }
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 500, description: 'Internal server error' })
  async getThirdPartyKeys(
    @Request() req: AuthenticatedRequest
  ) {
    try {
      const userId = req.user.userId;
      const result = await this.thirdPartyKeyService.getThirdPartyKeys(userId);

      return {
        success: true,
        data: result
      };
    } catch (error: unknown) {
      return this.handleApiError(error, 'Failed to get third-party keys');
    }
  }

  @Delete(':keyId')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Remove a third-party API key',
    description: 'Removes a third-party API key and cleans up relationships'
  })
  @ApiParam({ name: 'keyId', description: 'Third-party API key ID' })
  @ApiResponse({
    status: 200,
    description: 'Third-party API key removed successfully',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean' }
      }
    }
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 404, description: 'Third-party key not found' })
  @ApiResponse({ status: 500, description: 'Internal server error' })
  async removeThirdPartyKey(
    @Param('keyId') keyId: string,
    @Request() req: AuthenticatedRequest
  ) {
    try {
      const userId = req.user.userId;

      await this.thirdPartyKeyService.removeThirdPartyKey(keyId, userId);

      return {
        success: true
      };
    } catch (error: unknown) {
      return this.handleApiError(error, 'Failed to remove third-party key');
    }
  }

  // =============================================
  // Encrypted API Key Management Endpoints
  // =============================================

  @Post('encrypted')
  @UseGuards(JwtAuthGuard)  // Temporarily disabled for testing
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Create an encrypted API key',
    description: 'Creates a new encrypted API key using X25519 + ChaCha20-Poly1305 encryption. The API key is encrypted by the frontend and stored in etcd for Executor decryption.'
  })
  @ApiBody({
    description: 'Encrypted API key creation request',
    schema: {
      type: 'object',
      required: ['name', 'provider', 'region', 'keyId', 'encryptedKey', 'nonce', 'tag', 'ephemeralPubKey'],
      properties: {
        name: { type: 'string', description: '密钥名称' },
        provider: { type: 'string', description: 'Provider name (e.g., openai, claude)' },
        region: { type: 'string', description: 'Region name' },
        keyId: { type: 'string', description: '目标 Executor 的 keyId' },
        encryptedKey: { type: 'string', description: 'base64(ciphertext) 加密后的Provider API KEY' },
        nonce: { type: 'string', description: 'base64(nonce) 随机数，ChaCha20-Poly1305要求解密用' },
        tag: { type: 'string', description: 'base64(tag) ChaCha20-Poly1305要求解密用' },
        ephemeralPubKey: { type: 'string', description: 'base64(X25519 公钥)' }
      }
    }
  })
  @ApiResponse({
    status: 201,
    description: 'Encrypted API key created successfully',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean' },
        data: {
          type: 'object',
          properties: {
            uuid: { type: 'string', description: 'Unique identifier for the encrypted key' },
            provider: { type: 'string', description: 'Provider name' },
            region: { type: 'string', description: 'Region name' },
            keyId: { type: 'string', description: 'Target executor key ID' },
            status: { type: 'string', description: 'Key status' },
            createdAt: { type: 'string', description: 'Creation timestamp' }
          }
        }
      }
    }
  })
  @ApiResponse({ status: 400, description: 'Bad request - invalid encryption data' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status:404, description: 'Target executor not found' })
  @ApiResponse({ status: 500, description: 'Internal server error' })
  async createEncryptedApiKey(
    @Body() request: {
      name: string;                  // 密钥名称
      provider: string;
      region: string;
      keyId: string;                  // 目标 Executor 的 keyId
      encryptedKey: string;          // base64(ciphertext) 加密后的Provider API KEY
      nonce: string;                 // base64(nonce) 随机数，ChaCha20-Poly1305要求解密用
      tag: string;                   // base64(tag) ChaCha20-Poly1305要求解密用
      ephemeralPubKey: string;       // base64(X25519 公钥)
    },
    @Request() req: AuthenticatedRequest
  ) {
    try {
      // For testing with disabled auth, use a default userId
      const userId = req.user?.userId;

      // Validate request data
      if (!request.name || !request.provider || !request.region || !request.keyId ||
          !request.encryptedKey || !request.nonce || request.tag === undefined || !request.ephemeralPubKey) {
        throw new HttpException(
          {
            success: false,
            error: {
              message: 'Missing required encryption fields',
              code: 'MISSING_ENCRYPTION_FIELDS',
            },
          },
          HttpStatus.BAD_REQUEST,
        );
      }

      this.logger.log(`Creating encrypted API key for provider: ${request.provider}, region: ${request.region}, keyId: ${request.keyId}`);
      const result = await this.encryptedApiKeyService.createEncryptedApiKey(request, userId);

      return {
        success: true,
        data: result
      };
    } catch (error: unknown) {
      return this.handleApiError(error, 'Failed to create encrypted API key');
    }
  }

  /**
   * Update encrypted API key
   */
  @Put('encrypted/:provider/:region/:uuid')
  @UseGuards(JwtAuthGuard)  // Temporarily disabled for testing
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Update an encrypted API key',
    description: 'Updates an existing encrypted API key. Can update name and/or encryption data.'
  })
  @ApiParam({ name: 'provider', description: 'Provider name' })
  @ApiParam({ name: 'region', description: 'Region name' })
  @ApiParam({ name: 'uuid', description: 'Encrypted key UUID' })
  @ApiBody({
    description: 'Encrypted API key update request',
    schema: {
      type: 'object',
      properties: {
        status: { type: 'string', description: 'New status for the key' }
      }
    }
  })
  @ApiResponse({
    status: 200,
    description: 'Encrypted API key updated successfully',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean' },
        message: { type: 'string' }
      }
    }
  })
  @ApiResponse({ status: 400, description: 'Bad request - invalid data' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 404, description: 'Encrypted key not found' })
  @ApiResponse({ status: 500, description: 'Internal server error' })
  async updateEncryptedApiKey(
    @Param('provider') provider: string,
    @Param('region') region: string,
    @Param('uuid') uuid: string,
    @Body() request: {
      status: 'active' | 'revoked' | 'waiting-to-verify' | 'inactive';
    }
  ) {
    try {
      // Validate that at least one field is provided
      if (!request.status) {
        throw new HttpException(
          {
            success: false,
            error: {
              message: 'At least one field must be provided for update',
              code: 'NO_UPDATE_FIELDS',
            },
          },
          HttpStatus.BAD_REQUEST,
        );
      }

      const success = await this.encryptedApiKeyService.updateEncryptedApiKey(
        provider,
        region,
        uuid,
        request
      );

      if (!success) {
        throw new HttpException(
          {
            success: false,
            error: {
              message: 'Encrypted API key not found',
              code: 'KEY_NOT_FOUND',
            },
          },
          HttpStatus.NOT_FOUND,
        );
      }

      return {
        success: true,
        message: 'Encrypted API key updated successfully'
      };
    } catch (error: unknown) {
      return this.handleApiError(error, 'Failed to update encrypted API key');
    }
  }

  // =============================================
  // Etcd Sync Management Endpoints
  // =============================================


  /**
   * Trigger manual sync (dynamic comparison)
   */
  @Post('sync/manual')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Trigger manual sync',
    description: 'Performs a dynamic sync that only updates changed or missing keys'
  })
  @ApiResponse({
    status: 200,
    description: 'Manual sync completed',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean' },
        message: { type: 'string' },
        stats: {
          type: 'object',
          properties: {
            added: { type: 'number' },
            updated: { type: 'number' },
            removed: { type: 'number' },
            errors: { type: 'number' }
          }
        }
      }
    }
  })
  async triggerManualSync() {
    try {
      const result = await this.etcdSyncService.triggerManualSync();

      return result;
    } catch (error: unknown) {
      return this.handleApiError(error, 'Failed to trigger manual sync');
    }
  }

}
