import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  UseGuards,
  Request,
  HttpException,
  HttpStatus,
  Logger
} from "@nestjs/common";
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiQuery, ApiParam } from '@nestjs/swagger';
import { JwtAuthGuard } from "@saito/auth";
import { ApiKeyService } from "@saito/apikey";
import {
  CreateApiKeyRequest,
  UpdateApiKeyRequest,
  GetApiKeysRequest,

  GetApiKeyUsageRequest,
  GetChannelUsageRequest,
} from '@saito/models';
import { AuthenticatedRequest, ApiError, ApiKeyStatus } from '../types/request.types';

@ApiTags('api-keys')
@Controller('api-keys')
export class ApiKeyController {
  private readonly logger = new Logger(ApiKeyController.name);

  constructor(
    private readonly apiKeyService: ApiKeyService,
  ) {}

  // 辅助函数：处理API错误
  private handleApiError(error: unknown, defaultMessage: string): never {
    const apiError = error as ApiError;

    if (error instanceof HttpException) {
      throw error;
    }

    throw new HttpException(
      apiError.message || defaultMessage,
      apiError.status || HttpStatus.INTERNAL_SERVER_ERROR
    );
  }
  
  @Post()
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Create a new API key',
    description: 'Creates a new API key for the authenticated user'
  })
  @ApiResponse({
    status: 201,
    description: 'API key created successfully',
    schema: {
      properties: {
        success: { type: 'boolean' },
        data: {
          type: 'object',
          properties: {
            id: { type: 'string' },
            key: { type: 'string' },
            name: { type: 'string' },
            category: { type: 'string' },
            provider: { type: 'string' },
            createdAt: { type: 'string' }
          }
        }
      }
    }
  })
  @ApiResponse({ status: 400, description: 'Bad request' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 500, description: 'Internal server error' })
  async createApiKey(
    @Body() request: CreateApiKeyRequest,
    @Request() req: AuthenticatedRequest
  ) {
    try {
      const userId = req.user.userId;

      const result = await this.apiKeyService.createApiKey(request, userId);

      return {
        success: true,
        data: result
      };
    } catch (error: unknown) {
      const apiError = error as ApiError;
      throw new HttpException(
        apiError.message || 'Failed to create API key',
        apiError.status || HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Put(':id')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Update an API key',
    description: 'Updates an existing API key'
  })
  @ApiParam({ name: 'id', description: 'API key ID' })
  @ApiResponse({
    status: 200,
    description: 'API key updated successfully',
    schema: {
      properties: {
        success: { type: 'boolean' }
      }
    }
  })
  @ApiResponse({ status: 400, description: 'Bad request' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 404, description: 'API key not found' })
  @ApiResponse({ status: 500, description: 'Internal server error' })
  async updateApiKey(
    @Param('id') id: string,
    @Body() request: UpdateApiKeyRequest,
    @Request() req: AuthenticatedRequest
  ) {
    try {
      const userId = req.user.userId;

      await this.apiKeyService.updateApiKey(id, request, userId);

      return {
        success: true
      };
    } catch (error: unknown) {
      return this.handleApiError(error, 'Failed to update API key');
    }
  }

  @Delete(':id')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Delete an API key',
    description: 'Deletes an existing API key'
  })
  @ApiParam({ name: 'id', description: 'API key ID' })
  @ApiResponse({
    status: 200,
    description: 'API key deleted successfully',
    schema: {
      properties: {
        success: { type: 'boolean' }
      }
    }
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 404, description: 'API key not found' })
  @ApiResponse({ status: 500, description: 'Internal server error' })
  async deleteApiKey(
    @Param('id') id: string,
    @Request() req: AuthenticatedRequest
  ) {
    try {
      const userId = req.user.userId;

      await this.apiKeyService.deleteApiKey(id, userId);

      return {
        success: true
      };
    } catch (error: unknown) {
      return this.handleApiError(error, 'Failed to delete API key');
    }
  }

  @Get()
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Get API keys',
    description: 'Gets API keys for the authenticated user'
  })
  @ApiQuery({ name: 'page', required: false, type: Number, description: 'Page number (default: 1)' })
  @ApiQuery({ name: 'pageSize', required: false, type: Number, description: 'Page size (default: 10)' })
  @ApiQuery({ name: 'status', required: false, type: String, description: 'Filter by status (active, inactive, revoked)' })
  @ApiQuery({ name: 'search', required: false, type: String, description: 'Search term' })
  @ApiResponse({
    status: 200,
    description: 'API keys retrieved successfully',
    schema: {
      properties: {
        success: { type: 'boolean' },
        data: {
          type: 'object',
          properties: {
            data: {
              type: 'array',
              items: {
                type: 'object',
                properties: {
                  id: { type: 'string' },
                  userId: { type: 'string' },
                  keyHash: { type: 'string' },
                  keyPrefix: { type: 'string' },
                  keyMask: { type: 'string' },
                  name: { type: 'string' },
                  status: { type: 'string' },
                  createdAt: { type: 'string' },
                  updatedAt: { type: 'string' },
                  deletedAt: { type: 'string', nullable: true }
                }
              }
            },
            total: { type: 'number' },
            page: { type: 'number' },
            pageSize: { type: 'number' }
          }
        }
      }
    }
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 500, description: 'Internal server error' })
  async getApiKeys(
    @Query('page') page: number = 1,
    @Query('pageSize') pageSize: number = 10,
    @Query('status') status: string,
    @Query('search') search: string,
    @Request() req: AuthenticatedRequest
  ) {
    try {
      const userId = req.user.userId;

      const request: GetApiKeysRequest = {
        page,
        pageSize,
        status: status ? (status as ApiKeyStatus) : undefined,
        search
      };

      const result = await this.apiKeyService.getApiKeys(request, userId);

      return {
        success: true,
        data: result
      };
    } catch (error: unknown) {
      return this.handleApiError(error, 'Failed to get API keys');
    }
  }



  @Get(':id')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Get API key detail',
    description: 'Gets detail for a specific API key'
  })
  @ApiParam({ name: 'id', description: 'API key ID' })
  @ApiResponse({
    status: 200,
    description: 'API key detail retrieved successfully',
    schema: {
      properties: {
        success: { type: 'boolean' },
        data: {
          type: 'object',
          properties: {
            data: {
              type: 'object',
              properties: {
                id: { type: 'string' },
                name: { type: 'string' },
                key: { type: 'string' },
                category: { type: 'string' },
                provider: { type: 'string' },
                status: { type: 'string' },
                createdAt: { type: 'string' },
                lastUsed: { type: 'string', nullable: true },
                stats: {
                  type: 'object',
                  properties: {
                    requests: { type: 'number' },
                    requestsChange: { type: 'number' },
                    tokens: { type: 'number' },
                    tokensChange: { type: 'number' },
                    cost: { type: 'number' },
                    lastCharge: { type: 'string' }
                  }
                }
              }
            }
          }
        }
      }
    }
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 404, description: 'API key not found' })
  @ApiResponse({ status: 500, description: 'Internal server error' })
  async getApiKeyDetail(
    @Param('id') id: string,
    @Request() req: AuthenticatedRequest
  ) {
    try {
      const userId = req.user.userId;

      // 验证UUID格式
      // if (!/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(id)) {
      //   this.logger.error(`Invalid API key ID format: ${id}`);
      //   throw new HttpException(
      //     'Invalid API key ID format',
      //     HttpStatus.BAD_REQUEST
      //   );
      // }

      const result = await this.apiKeyService.getApiKeyDetail(id, userId);

      if (!result || !result.data) {
        this.logger.error(`API key with ID ${id} not found for user ${userId}`);
        throw new HttpException(
          `API key with ID ${id} not found`,
          HttpStatus.NOT_FOUND
        );
      }

      return {
        success: true,
        data: result
      };
    } catch (error: unknown) {
      return this.handleApiError(error, 'Failed to get API key detail');
    }
  }

  @Get(':id/usage')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Get API key usage',
    description: 'Gets usage statistics for a specific API key'
  })
  @ApiParam({ name: 'id', description: 'API key ID' })
  @ApiQuery({ name: 'timeRange', required: false, type: String, description: 'Time range in days' })
  @ApiResponse({
    status: 200,
    description: 'API key usage retrieved successfully',
    schema: {
      properties: {
        success: { type: 'boolean' },
        data: {
          type: 'object',
          properties: {
            data: {
              type: 'object',
              properties: {
                dailyRequests: {
                  type: 'array',
                  items: {
                    type: 'object',
                    properties: {
                      date: { type: 'string' },
                      count: { type: 'number' }
                    }
                  }
                },
                totalRequests: { type: 'number' },
                totalTokens: { type: 'number' },
                avgResponseTime: { type: 'number' },
                costThisMonth: { type: 'number' },
                usageByModel: {
                  type: 'array',
                  items: {
                    type: 'object',
                    properties: {
                      model: { type: 'string' },
                      requests: { type: 'number' },
                      tokens: { type: 'number' },
                      cost: { type: 'number' }
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 404, description: 'API key not found' })
  @ApiResponse({ status: 500, description: 'Internal server error' })
  async getApiKeyUsage(
    @Param('id') id: string,
    @Query('timeRange') timeRange: string,
    @Request() req: AuthenticatedRequest
  ) {
    try {
      const userId = req.user.userId;

      // 验证UUID格式
      if (!/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(id)) {
        this.logger.error(`Invalid API key ID format: ${id}`);
        throw new HttpException(
          'Invalid API key ID format',
          HttpStatus.BAD_REQUEST
        );
      }

      const request: GetApiKeyUsageRequest = {
        keyId: id,
        timeRange
      };

      try {
        const result = await this.apiKeyService.getApiKeyUsage(request, userId);

        return {
          success: true,
          data: result
        };
      } catch (error: unknown) {
        const apiError = error as ApiError;
        this.logger.error(`Service error getting API key usage: ${apiError.message || JSON.stringify(error)}`);

        // 如果是找不到API密钥的错误，返回404
        if (apiError.message && apiError.message.includes('not found')) {
          throw new HttpException(
            apiError.message,
            HttpStatus.NOT_FOUND
          );
        }

        return this.handleApiError(error, 'Failed to get API key usage');
      }
    } catch (error: unknown) {
      return this.handleApiError(error, 'Failed to get API key usage');
    }
  }

  @Get('reports/channel-usage')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Get channel usage',
    description: 'Gets usage statistics by channel'
  })
  @ApiQuery({ name: 'timeRange', required: false, type: String, description: 'Time range in days' })
  @ApiResponse({
    status: 200,
    description: 'Channel usage retrieved successfully',
    schema: {
      properties: {
        success: { type: 'boolean' },
        data: {
          type: 'object',
          properties: {
            data: {
              type: 'array',
              items: {
                type: 'object',
                properties: {
                  provider: { type: 'string' },
                  model: { type: 'string' },
                  requests: { type: 'number' },
                  tokens: { type: 'number' },
                  cost: { type: 'number' },
                  avgResponseTime: { type: 'number' }
                }
              }
            }
          }
        }
      }
    }
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 500, description: 'Internal server error' })
  async getChannelUsage(
    @Query('timeRange') timeRange: string,
    @Request() req: AuthenticatedRequest
  ) {
    try {
      const userId = req.user.userId;

      const request: GetChannelUsageRequest = { timeRange };

      const result = await this.apiKeyService.getChannelUsage(request, userId);

      return {
        success: true,
        data: result
      };
    } catch (error: unknown) {
      return this.handleApiError(error, 'Failed to get channel usage');
    }
  }

  @Get('reports/usage-summary')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Get usage summary',
    description: 'Gets usage summary statistics for reports'
  })
  @ApiQuery({ name: 'timeRange', required: false, type: String, description: 'Time range in days' })
  @ApiResponse({
    status: 200,
    description: 'Usage summary retrieved successfully',
    schema: {
      properties: {
        success: { type: 'boolean' },
        data: {
          type: 'object',
          properties: {
            data: {
              type: 'object',
              properties: {
                totalRequests: { type: 'number' },
                totalTokens: { type: 'number' },
                avgResponseTime: { type: 'number' },
                costThisMonth: { type: 'number' }
              }
            }
          }
        }
      }
    }
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 500, description: 'Internal server error' })
  async getUsageSummary(
    @Query('timeRange') timeRange: string,
    @Request() req: AuthenticatedRequest
  ) {
    try {
      const userId = req.user.userId;

      const request = { timeRange };

      const result = await this.apiKeyService.getUsageSummary(request, userId);

      return {
        success: true,
        data: result
      };
    } catch (error: unknown) {
      return this.handleApiError(error, 'Failed to get usage summary');
    }
  }

  @Get('reports/overall-usage')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Get overall usage',
    description: 'Gets overall usage statistics for reports'
  })
  @ApiQuery({ name: 'timeRange', required: false, type: String, description: 'Time range in days' })
  @ApiResponse({
    status: 200,
    description: 'Overall usage retrieved successfully',
    schema: {
      properties: {
        success: { type: 'boolean' },
        data: {
          type: 'object',
          properties: {
            data: {
              type: 'object',
              properties: {
                dailyData: {
                  type: 'array',
                  items: {
                    type: 'object',
                    properties: {
                      date: { type: 'string' },
                      requests: { type: 'number' },
                      tokens: { type: 'number' },
                      cost: { type: 'number' }
                    }
                  }
                },
                monthlyData: {
                  type: 'array',
                  items: {
                    type: 'object',
                    properties: {
                      month: { type: 'string' },
                      year: { type: 'number' },
                      requests: { type: 'number' },
                      tokens: { type: 'number' },
                      cost: { type: 'number' }
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 500, description: 'Internal server error' })
  async getOverallUsage(
    @Query('timeRange') timeRange: string,
    @Request() req: AuthenticatedRequest
  ) {
    try {
      const userId = req.user.userId;

      const request = { timeRange };

      const result = await this.apiKeyService.getOverallUsage(request, userId);

      return {
        success: true,
        data: result
      };
    } catch (error: unknown) {
      return this.handleApiError(error, 'Failed to get overall usage');
    }
  }

}
