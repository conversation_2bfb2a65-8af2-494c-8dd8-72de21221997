import {
  Controller,
  Get,
  Query,
  UseGuards,
  Request,
  HttpException,
  HttpStatus,
  Logger
} from "@nestjs/common";
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiQuery } from '@nestjs/swagger';
import { JwtAuthGuard } from "@saito/auth";
import { EarningsService } from "@saito/earnings";
import {
  GetServerStatusRequest,
  GetTaskQueueRequest,
  GetNodePerformanceRequest,
  GetEarningsStatsRequest,
  GetEarningsRequest
} from '@saito/models';
import { AuthenticatedRequest } from "../types/request.types";

@ApiTags('earnings')
@Controller('earnings')
export class EarningsController {
  private readonly logger = new Logger(EarningsController.name);

  constructor(private readonly earningsService: EarningsService) {}





  @Get('server-status')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Get server status',
    description: 'Gets the status of gateway servers'
  })
  @ApiQuery({ name: 'timeRange', required: false, type: String, description: 'Time range for data (e.g., "7", "30", "90", "365")' })
  @ApiResponse({
    status: 200,
    description: 'Server status retrieved successfully',
    schema: {
      properties: {
        success: { type: 'boolean' },
        data: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              server: { type: 'string' },
              apiLoad: { type: 'number' },
              wsStatus: { type: 'string' },
              taskTime: { type: 'number' },
              health: { type: 'string' }
            }
          }
        }
      }
    }
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 500, description: 'Internal server error' })
  async getServerStatus(
    @Query('timeRange') timeRange?: string
  ) {
    try {
      const request: GetServerStatusRequest = { timeRange };

      const result = await this.earningsService.getServerStatus(request);

      return {
        success: true,
        data: result.data
      };
    } catch (error) {
      this.logger.error(`Failed to get server status: ${error}`);
      throw new HttpException('Failed to get server status', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  @Get('task-queue')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Get task queue',
    description: 'Gets the task queue data'
  })
  @ApiQuery({ name: 'page', required: false, type: Number, description: 'Page number (default: 1)' })
  @ApiQuery({ name: 'pageSize', required: false, type: Number, description: 'Page size (default: 10)' })
  @ApiQuery({ name: 'status', required: false, type: String, description: 'Filter by status' })
  @ApiQuery({ name: 'search', required: false, type: String, description: 'Search term' })
  @ApiQuery({ name: 'timeRange', required: false, type: String, description: 'Time range for data' })
  @ApiResponse({
    status: 200,
    description: 'Task queue retrieved successfully',
    schema: {
      properties: {
        success: { type: 'boolean' },
        data: {
          type: 'object',
          properties: {
            data: {
              type: 'array',
              items: {
                type: 'object',
                properties: {
                  id: { type: 'string' },
                  model: { type: 'string' },
                  status: { type: 'string' },
                  queue: { type: 'string' },
                  device: { type: 'string' },
                  ptime: { type: 'string' },
                  earn: { type: 'string' }
                }
              }
            },
            total: { type: 'number' },
            page: { type: 'number' },
            pageSize: { type: 'number' }
          }
        }
      }
    }
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 500, description: 'Internal server error' })
  async getTaskQueue(
    @Query('page') page: number = 1,
    @Query('pageSize') pageSize: number = 10,
    @Query('status') status: string,
    @Query('search') search: string,
    @Query('timeRange') timeRange: string,
    @Request() req: AuthenticatedRequest
  ) {
    try {
      const request: GetTaskQueueRequest = {
        page,
        pageSize,
        status,
        search,
        timeRange
      };

      // Use the authenticated user's ID to filter tasks
      const userId = req.user.userId;
      const result = await this.earningsService.getTaskQueue(request, '');

      return {
        success: true,
        data: result
      };
    } catch (error) {
      this.logger.error(`Failed to get task queue: ${error}`);
      throw new HttpException('Failed to get task queue', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  @Get('node-performance')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Get node performance',
    description: 'Gets the node performance data'
  })
  @ApiQuery({ name: 'page', required: false, type: Number, description: 'Page number (default: 1)' })
  @ApiQuery({ name: 'pageSize', required: false, type: Number, description: 'Page size (default: 10)' })
  @ApiQuery({ name: 'status', required: false, type: String, description: 'Filter by status' })
  @ApiQuery({ name: 'search', required: false, type: String, description: 'Search term' })
  @ApiQuery({ name: 'timeRange', required: false, type: String, description: 'Time range for data' })
  @ApiResponse({
    status: 200,
    description: 'Node performance retrieved successfully',
    schema: {
      properties: {
        success: { type: 'boolean' },
        data: {
          type: 'object',
          properties: {
            data: {
              type: 'array',
              items: {
                type: 'object',
                properties: {
                  id: { type: 'string' },
                  type: { type: 'string' },
                  tasks: { type: 'number' },
                  mem: { type: 'number' },
                  rate: { type: 'number' },
                  earn: { type: 'number' },
                  name:  { type: 'string' }
                }
              }
            },
            total: { type: 'number' },
            page: { type: 'number' },
            pageSize: { type: 'number' }
          }
        }
      }
    }
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 500, description: 'Internal server error' })
  async getNodePerformance(
    @Query('page') page: number = 1,
    @Query('pageSize') pageSize: number = 10,
    @Query('status') status: string,
    @Query('search') search: string,
    @Query('timeRange') timeRange: string,
    @Request() req: AuthenticatedRequest
  ) {
    try {
      const request: GetNodePerformanceRequest = {
        page,
        pageSize,
        status,
        search,
        timeRange
      };

      // Use the authenticated user's ID to filter node performance data
      const userId = req.user.userId;
      const result = await this.earningsService.getNodePerformance(request, '');

      return {
        success: true,
        data: result
      };
    } catch (error) {
      this.logger.error(`Failed to get node performance: ${error}`);
      throw new HttpException('Failed to get node performance', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  @Get('stats')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Get earnings stats',
    description: 'Gets the earnings statistics'
  })
  @ApiQuery({ name: 'timeRange', required: false, type: String, description: 'Time range for data' })
  @ApiQuery({ name: 'year', required: false, type: Number, description: 'Filter by year' })
  @ApiQuery({ name: 'month', required: false, type: Number, description: 'Filter by month (1-12)' })
  @ApiResponse({
    status: 200,
    description: 'Earnings stats retrieved successfully',
    schema: {
      properties: {
        success: { type: 'boolean' },
        data: {
          type: 'object',
          properties: {
            earnings: { type: 'number' },
            tasks: { type: 'number' },
            nodes: { type: 'number' }
          }
        }
      }
    }
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 500, description: 'Internal server error' })
  async getEarningsStats(
    @Request() req: AuthenticatedRequest,
    @Query('timeRange') timeRange?: string,
    @Query('year') year?: number,
    @Query('month') month?: number
  ) {
    try {
      const request: GetEarningsStatsRequest = { timeRange };

      // Use the authenticated user's ID to filter earnings stats
      const userId = req.user.userId;
      const result = await this.earningsService.getEarningsStats(request, '', year, month);

      return {
        success: true,
        data: result.data
      };
    } catch (error) {
      this.logger.error(`Failed to get earnings stats: ${error}`);
      throw new HttpException('Failed to get earnings stats', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  @Get('dashboard/revenue-chart')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Get revenue chart data',
    description: 'Gets time-series revenue data for dashboard charts'
  })
  @ApiQuery({ name: 'startDate', required: false, type: String, description: 'Start date (YYYY-MM-DD)' })
  @ApiQuery({ name: 'endDate', required: false, type: String, description: 'End date (YYYY-MM-DD)' })
  @ApiQuery({ name: 'timeRange', required: false, type: String, description: 'Time range in days (e.g., "7", "30", "90", "365")' })
  @ApiQuery({ name: 'year', required: false, type: Number, description: 'Filter by year' })
  @ApiQuery({ name: 'month', required: false, type: Number, description: 'Filter by month (1-12)' })
  @ApiResponse({
    status: 200,
    description: 'Revenue chart data retrieved successfully',
    schema: {
      properties: {
        success: { type: 'boolean' },
        data: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              date: { type: 'string' },
              value: { type: 'number' }
            }
          }
        }
      }
    }
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 500, description: 'Internal server error' })
  async getRevenueChartData(
    @Request() req: AuthenticatedRequest,
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
    @Query('timeRange') timeRange?: string,
    @Query('year') year?: number,
    @Query('month') month?: number
  ) {
    try {
      const userId = req.user.userId;

      // Use real database query instead of mock data
      const result = await this.earningsService.getRevenueChartData('', timeRange, year, month);

      return {
        success: true,
        data: result
      };
    } catch (error) {
      this.logger.error(`Failed to get revenue chart data: ${error}`);
      throw new HttpException('Failed to get revenue chart data', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  @Get('dashboard/request-count-chart')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Get request count chart data',
    description: 'Gets time-series chat request count data for dashboard charts'
  })
  @ApiQuery({ name: 'startDate', required: false, type: String, description: 'Start date (YYYY-MM-DD)' })
  @ApiQuery({ name: 'endDate', required: false, type: String, description: 'End date (YYYY-MM-DD)' })
  @ApiQuery({ name: 'timeRange', required: false, type: String, description: 'Time range in days (e.g., "7", "30", "90", "365")' })
  @ApiQuery({ name: 'year', required: false, type: Number, description: 'Filter by year' })
  @ApiQuery({ name: 'month', required: false, type: Number, description: 'Filter by month (1-12)' })
  @ApiResponse({
    status: 200,
    description: 'Request count chart data retrieved successfully',
    schema: {
      properties: {
        success: { type: 'boolean' },
        data: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              date: { type: 'string' },
              value: { type: 'number' }
            }
          }
        }
      }
    }
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 500, description: 'Internal server error' })
  async getRequestCountChartData(
    @Request() req: AuthenticatedRequest,
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
    @Query('timeRange') timeRange?: string,
    @Query('year') year?: number,
    @Query('month') month?: number
  ) {
    try {
      const userId = req.user.userId;

      // Use real database query instead of mock data
      const result = await this.earningsService.getRequestCountChartData('', timeRange, year, month);

      return {
        success: true,
        data: result
      };
    } catch (error) {
      this.logger.error(`Failed to get request count chart data: ${error}`);
      throw new HttpException('Failed to get request count chart data', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  @Get('dashboard/revenue-breakdown')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Get revenue breakdown chart data',
    description: 'Gets detailed breakdown of chat revenue by category'
  })
  @ApiQuery({ name: 'startDate', required: false, type: String, description: 'Start date (YYYY-MM-DD)' })
  @ApiQuery({ name: 'endDate', required: false, type: String, description: 'End date (YYYY-MM-DD)' })
  @ApiQuery({ name: 'timeRange', required: false, type: String, description: 'Time range in days (e.g., "7", "30", "90", "365")' })
  @ApiResponse({
    status: 200,
    description: 'Revenue breakdown data retrieved successfully',
    schema: {
      properties: {
        success: { type: 'boolean' },
        data: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              date: { type: 'string' },
              blockRewards: { type: 'number' },
              jobRewards: { type: 'number' },
              totalRewards: { type: 'number' }
            }
          }
        }
      }
    }
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 500, description: 'Internal server error' })
  async getRevenueBreakdownData(
    @Request() req: AuthenticatedRequest,
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
    @Query('timeRange') timeRange?: string
  ) {
    try {
      const userId = req.user.userId;

      // Return empty data for now since this endpoint is not used in the current dashboard
      const result: Array<{ date: string; blockRewards: number; jobRewards: number; totalRewards: number }> = [];

      return {
        success: true,
        data: result
      };
    } catch (error) {
      this.logger.error(`Failed to get revenue breakdown data: ${error}`);
      throw new HttpException('Failed to get revenue breakdown data', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  @Get('dashboard/chat-heatmap')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Get chat heatmap data',
    description: 'Gets daily chat count data for calendar heatmap visualization'
  })
  @ApiQuery({ name: 'startDate', required: false, type: String, description: 'Start date (YYYY-MM-DD)' })
  @ApiQuery({ name: 'endDate', required: false, type: String, description: 'End date (YYYY-MM-DD)' })
  @ApiQuery({ name: 'timeRange', required: false, type: String, description: 'Time range in days (e.g., "7", "30", "90", "365")' })
  @ApiQuery({ name: 'year', required: false, type: Number, description: 'Year for heatmap data' })
  @ApiQuery({ name: 'month', required: false, type: Number, description: 'Month for heatmap data (1-12)' })
  @ApiResponse({
    status: 200,
    description: 'Chat heatmap data retrieved successfully',
    schema: {
      properties: {
        success: { type: 'boolean' },
        data: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              date: { type: 'string' },
              count: { type: 'number' }
            }
          }
        }
      }
    }
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 500, description: 'Internal server error' })
  async getChatHeatmapData(
    @Request() req: AuthenticatedRequest,
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
    @Query('timeRange') timeRange?: string,
    @Query('year') year?: number,
    @Query('month') month?: number
  ) {
    try {
      const userId = req.user.userId;

      // Use real database query instead of mock data
      const result = await this.earningsService.getChatHeatmapData('', timeRange, year, month);

      return {
        success: true,
        data: result
      };
    } catch (error) {
      this.logger.error(`Failed to get chat heatmap data: ${error}`);
      throw new HttpException('Failed to get chat heatmap data', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  @Get()
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Get earnings',
    description: 'Gets the earnings data for the user'
  })
  @ApiQuery({ name: 'page', required: false, type: Number, description: 'Page number (default: 1)' })
  @ApiQuery({ name: 'pageSize', required: false, type: Number, description: 'Page size (default: 10)' })
  @ApiQuery({ name: 'timeRange', required: false, type: String, description: 'Time range for data' })
  @ApiResponse({
    status: 200,
    description: 'Earnings retrieved successfully',
    schema: {
      properties: {
        success: { type: 'boolean' },
        data: {
          type: 'object',
          properties: {
            data: {
              type: 'array',
              items: {
                type: 'object',
                properties: {
                  id: { type: 'string' },
                  block_rewards: { type: 'number' },
                  job_rewards: { type: 'number' },
                  total_rewards: { type: 'number' },
                  date: { type: 'string' },
                  created_at: { type: 'string' },
                  device_name: { type: 'string' },
                  model: { type: 'string' }
                }
              }
            },
            total: { type: 'number' },
            page: { type: 'number' },
            pageSize: { type: 'number' }
          }
        }
      }
    }
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 500, description: 'Internal server error' })
  async getEarnings(
    @Query('page') page: number = 1,
    @Query('pageSize') pageSize: number = 10,
    @Query('timeRange') timeRange: string,
    @Request() req: AuthenticatedRequest
  ) {
    try {
      const request: GetEarningsRequest = {
        page,
        pageSize,
        timeRange
      };

      // Use the authenticated user's ID to filter earnings
      const userId = req.user.userId;
      const result = await this.earningsService.getEarnings(request, '');

      return {
        success: true,
        data: result
      };
    } catch (error) {
      this.logger.error(`Failed to get earnings: ${error}`);
      throw new HttpException('Failed to get earnings', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }
}
