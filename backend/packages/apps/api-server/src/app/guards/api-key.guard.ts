import { Injectable, CanActivate, ExecutionContext, Logger, HttpException, HttpStatus } from '@nestjs/common';
import { Observable } from 'rxjs';
import { ApiKeyService } from '@saito/apikey';
import { ApiKeyInfo } from '@saito/models';
import { Request, Response } from 'express';
import * as CryptoJS from 'crypto-js';

// 导入类型声明文件
import type { ExtendedRequest, ExtendedResponse } from './express';

@Injectable()
export class ApiKeyGuard implements CanActivate {
  private readonly logger = new Logger(ApiKeyGuard.name);

  constructor(
    private readonly apiKeyService: ApiKeyService
  ) {}

  canActivate(
    context: ExecutionContext,
  ): boolean | Promise<boolean> | Observable<boolean> {
    return this.validateRequest(context);
  }

  private async validateRequest(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest<ExtendedRequest>();
    const response = context.switchToHttp().getResponse();

    try {
      // 使用通用方法提取API Key
      const apiKey = this.extractApiKey(request);

      if (!apiKey) {
        throw new HttpException({
          error: {
            message: 'You didn\'t provide an API key. You need to provide your API key in an Authorization header using Bearer auth (i.e. Authorization: Bearer YOUR_KEY), or as the password field (with blank username) if you\'re accessing the API from your browser and are prompted for a username and password. You can obtain an API key from https://platform.openai.com/account/api-keys.',
            type: 'invalid_request_error',
            param: null,
            code: 'invalid_api_key'
          }
        }, HttpStatus.UNAUTHORIZED);
      }

      // Get the API key prefix
      const keyPrefix = apiKey.split('-')[0] + '-';

      // Validate API key format (should start with 'sk-')
      if (!apiKey.startsWith('sk-')) {
        throw new HttpException({
          error: {
            message: `Incorrect API key provided: ${apiKey}. You can find your API key at https://platform.openai.com/account/api-keys.`,
            type: 'invalid_request_error',
            param: null,
            code: 'invalid_api_key'
          }
        }, HttpStatus.UNAUTHORIZED);
      }



      // Hash the key for lookup
      const salt = 'sight ai';
      const keyHash = CryptoJS.SHA256(salt + apiKey).toString();

      // Validate the key against the database
      const validationResult = await this.apiKeyService.validateApiKey({
        keyHash,
        keyPrefix
      });

      if (!validationResult.valid) {
        throw new HttpException({
          error: {
            message: `Incorrect API key provided: ${apiKey}. You can find your API key at https://platform.openai.com/account/api-keys.`,
            type: 'invalid_request_error',
            param: null,
            code: 'invalid_api_key'
          }
        }, HttpStatus.UNAUTHORIZED);
      }

      if (validationResult.status !== 'active') {
        throw new HttpException({
          error: {
            message: `API key is ${validationResult.status}. Please check your API key status in your account settings.`,
            type: 'invalid_request_error',
            param: null,
            code: 'api_key_not_active'
          }
        }, HttpStatus.FORBIDDEN);
      }

      // Key routing is now handled by Executor service
      // Gateway only validates platform keys

      // Set the API key info in the request
      request.apiKeyInfo = {
        keyId: validationResult.keyId,
        userId: validationResult.userId,
        category: 'platform',
        provider: 'Saito Gateway',
        model: request.body?.model || request.query?.model || 'default'
      };

      // Log the API usage (using the platform key ID for tracking)
      this.logApiUsage(request, apiKey);

      return true;
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      } else {
        this.logger.error(`API key validation error: ${error}`);
        throw new HttpException({
          error: {
            message: 'Internal server error during API key validation.',
            type: 'api_error',
            param: null,
            code: 'internal_error'
          }
        }, HttpStatus.INTERNAL_SERVER_ERROR);
      }
    }
  }

  /**
   * 提取API Key的通用方法
   */
  private extractApiKey(request: ExtendedRequest): string | null {
    // Try to get API key from Authorization header
    const authHeader = request.headers.authorization;
    if (authHeader && authHeader.startsWith('Bearer ')) {
      return authHeader.substring(7);
    }

    // Try to get API key from x-api-key header
    const apiKeyHeader = request.headers['x-api-key'];
    if (apiKeyHeader && typeof apiKeyHeader === 'string') {
      return apiKeyHeader;
    }

    return null;
  }

  private async logApiUsage(request: ExtendedRequest, apiKey: string) {
    // Usage tracking has been removed - Executor handles this now
    this.logger.debug(`API usage logged for key: ${request.apiKeyInfo?.keyId}`);
  }

  private calculateRequestSize(request: ExtendedRequest): number {
    try {
      return JSON.stringify(request.body || {}).length;
    } catch {
      return 0;
    }
  }
}

/**
 * 可选API Key Guard - 支持无Key低优先级和有Key高优先级模式
 * 用于OpenAI和Ollama模块的优先级控制
 */
@Injectable()
export class OptionalApiKeyGuard implements CanActivate {
  private readonly logger = new Logger(OptionalApiKeyGuard.name);

  constructor(private readonly apiKeyService: ApiKeyService) {}

  canActivate(
    context: ExecutionContext,
  ): boolean | Promise<boolean> | Observable<boolean> {
    return this.validateOptionalRequest(context);
  }

  /**
   * 可选API Key验证 - 支持无Key低优先级模式
   */
  private async validateOptionalRequest(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest<ExtendedRequest>();
    
    try {
      // 尝试获取API Key
      const apiKey = this.extractApiKey(request);
      
      if (!apiKey) {
        // 没有API Key - 设置为低优先级模式
        this.logger.debug('No API key provided, setting low priority mode');
        request.apiKeyInfo = undefined; // 设置为undefined表示低优先级
        return true;
      }

      // 有API Key - 进行验证
      this.logger.debug('API key provided, validating for high priority mode');
      
      // Validate API key format (should start with 'sk-')
      if (!apiKey.startsWith('sk-')) {
        throw new HttpException({
          error: {
            message: `Incorrect API key provided: ${apiKey}. You can find your API key at https://platform.openai.com/account/api-keys.`,
            type: 'invalid_request_error',
            param: null,
            code: 'invalid_api_key'
          }
        }, HttpStatus.UNAUTHORIZED);
      }



      // Hash the key for lookup
      const salt = 'sight ai';
      const keyHash = CryptoJS.SHA256(salt + apiKey).toString();

      // Validate the key against the database
      const validationResult = await this.apiKeyService.validateApiKey({
        keyHash,
        keyPrefix: 'sk-'
      });

      if (!validationResult.valid) {
        // API Key无效 - 直接退出
        throw new HttpException({
          error: {
            message: `Incorrect API key provided: ${apiKey}. You can find your API key at https://platform.openai.com/account/api-keys.`,
            type: 'invalid_request_error',
            param: null,
            code: 'invalid_api_key'
          }
        }, HttpStatus.UNAUTHORIZED);
      }

      if (validationResult.status !== 'active') {
        // API Key状态不正确 - 直接退出
        throw new HttpException({
          error: {
            message: `API key is ${validationResult.status}. Please check your API key status in your account settings.`,
            type: 'invalid_request_error',
            param: null,
            code: 'api_key_not_active'
          }
        }, HttpStatus.FORBIDDEN);
      }

      // API Key验证成功 - 设置为高优先级模式
      request.apiKeyInfo = {
        keyId: validationResult.keyId,
        userId: validationResult.userId,
        category: 'platform',
        provider: 'Saito Gateway',
        model: request.body?.model || request.query?.model || 'default'
      };



      this.logger.debug(`API key validated successfully, setting high priority mode for user ${validationResult.userId}`);
      return true;

    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      } else {
        this.logger.error(`API key validation error: ${error}`);
        throw new HttpException({
          error: {
            message: 'Internal server error during API key validation.',
            type: 'api_error',
            param: null,
            code: 'internal_error'
          }
        }, HttpStatus.INTERNAL_SERVER_ERROR);
      }
    }
  }

  /**
   * 提取API Key的通用方法
   */
  private extractApiKey(request: ExtendedRequest): string | null {
    // Try to get API key from Authorization header
    const authHeader = request.headers.authorization;
    if (authHeader && authHeader.startsWith('Bearer ')) {
      return authHeader.substring(7);
    }

    // Try to get API key from x-api-key header
    const apiKeyHeader = request.headers['x-api-key'];
    if (apiKeyHeader && typeof apiKeyHeader === 'string') {
      return apiKeyHeader;
    }

    return null;
  }




}
