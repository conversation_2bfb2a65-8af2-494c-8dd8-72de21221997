import {
  Injectable,
  CanActivate,
  ExecutionContext,
  UnauthorizedException,
  Logger,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Etcd3 } from 'etcd3';

export interface ExecutorInfo {
  id: string;
  region: string;
  modelType: string;
  publicUrl: string;
  status: string;
  lastHeartbeat: number;
}

@Injectable()
export class ExecutorAuthGuard implements CanActivate {
  private readonly logger = new Logger(ExecutorAuthGuard.name);
  private etcdClient!: Etcd3;

  constructor(private configService: ConfigService) {
    this.initializeEtcdClient();
  }

  private initializeEtcdClient(): void {
    const etcdHost = this.configService.get<string>('ETCD_HOST', 'localhost');
    const etcdPort = this.configService.get<number>('ETCD_PORT', 2379);

    this.etcdClient = new Etcd3({
      hosts: [`${etcdHost}:${etcdPort}`],
    });

    this.logger.log(`ExecutorAuthGuard connected to etcd at ${etcdHost}:${etcdPort}`);
  }

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();
    
    try {
      // Extract executor credentials from headers
      const executorId = request.headers['x-executor-id'];
      const executorSecret = request.headers['x-executor-secret'];
      const executorRegion = request.headers['x-executor-region'];

      if (!executorId || !executorSecret) {
        this.logger.warn('Missing executor credentials in headers');
        throw new UnauthorizedException('Missing executor credentials');
      }

      // Validate executor against etcd registry
      const executorInfo = await this.validateExecutor(executorId, executorSecret, executorRegion);
      
      if (!executorInfo) {
        this.logger.warn(`Invalid executor credentials for ID: ${executorId}`);
        throw new UnauthorizedException('Invalid executor credentials');
      }

      // Check if executor is active and healthy
      if (executorInfo.status !== 'active') {
        this.logger.warn(`Executor ${executorId} is not active (status: ${executorInfo.status})`);
        throw new UnauthorizedException('Executor is not active');
      }

      // Check heartbeat (executor should be alive within last 5 minutes)
      const now = Date.now();
      const heartbeatThreshold = 5 * 60 * 1000; // 5 minutes
      if (now - executorInfo.lastHeartbeat > heartbeatThreshold) {
        this.logger.warn(
          `Executor ${executorId} heartbeat is stale (last: ${new Date(executorInfo.lastHeartbeat).toISOString()})`,
        );
        throw new UnauthorizedException('Executor heartbeat is stale');
      }

      // Attach executor info to request for use in controllers
      request.executor = executorInfo;

      this.logger.debug(`Executor ${executorId} authenticated successfully`);
      return true;
    } catch (error) {
      if (error instanceof UnauthorizedException) {
        throw error;
      }

      this.logger.error(
        `Executor authentication failed: ${error instanceof Error ? error.message : String(error)}`,
      );
      throw new UnauthorizedException('Executor authentication failed');
    }
  }

  /**
   * Validate executor against etcd registry
   */
  private async validateExecutor(
    executorId: string,
    executorSecret: string,
    executorRegion?: string,
  ): Promise<ExecutorInfo | null> {
    try {
      // Search for executor in etcd registry
      // Path pattern: /executors/{region}/{model_type}/{executor_id}
      const executorPrefix = '/executors/';
      const kvs = await this.etcdClient.getAll().prefix(executorPrefix);

      for (const [fullPath, jsonValue] of Object.entries(kvs)) {
        try {
          const executorData = JSON.parse(jsonValue as string);
          const pathParts = fullPath.split('/');
          
          // Parse path: /executors/{region}/{model_type}/{executor_id}
          if (pathParts.length >= 5) {
            const pathExecutorId = pathParts[4];
            const pathRegion = pathParts[2];
            const pathModelType = pathParts[3];

            // Check if this is the executor we're looking for
            if (pathExecutorId === executorId) {
              // Validate region if provided
              if (executorRegion && pathRegion !== executorRegion) {
                this.logger.debug(
                  `Executor ${executorId} region mismatch: expected ${executorRegion}, found ${pathRegion}`,
                );
                continue;
              }

              // For now, we'll use a simple secret validation
              // In production, this should be a proper JWT or signed token
              const expectedSecret = this.generateExecutorSecret(executorId, pathRegion, pathModelType);
              if (executorSecret !== expectedSecret) {
                this.logger.debug(`Executor ${executorId} secret validation failed`);
                continue;
              }

              // Return executor info
              const executorInfo: ExecutorInfo = {
                id: executorId,
                region: pathRegion,
                modelType: pathModelType,
                publicUrl: executorData.url || executorData.publicUrl,
                status: executorData.status || 'active',
                lastHeartbeat: executorData.last_heartbeat || executorData.lastHeartbeat || 0,
              };

              this.logger.debug(`Found valid executor: ${executorId} in region ${pathRegion}`);
              return executorInfo;
            }
          }
        } catch (parseError) {
          this.logger.warn(`Failed to parse executor data for ${fullPath}: ${parseError}`);
        }
      }

      this.logger.debug(`Executor ${executorId} not found in registry`);
      return null;
    } catch (error) {
      this.logger.error(
        `Failed to validate executor ${executorId}: ${error instanceof Error ? error.message : String(error)}`,
      );
      return null;
    }
  }

  /**
   * Generate expected executor secret
   * In production, this should be a proper JWT or signed token
   * For now, we'll use a simple hash-based approach
   */
  private generateExecutorSecret(executorId: string, region: string, modelType: string): string {
    // This is a simplified implementation
    // In production, use proper cryptographic signing
    const secretKey = this.configService.get<string>('EXECUTOR_SECRET_KEY', 'default-secret-key');
    const crypto = require('crypto');
    
    const payload = `${executorId}:${region}:${modelType}:${secretKey}`;
    return crypto.createHash('sha256').update(payload).digest('hex').substring(0, 32);
  }

  /**
   * Generate executor secret for a given executor
   * This can be used by deployment scripts to generate secrets
   */
  static generateExecutorSecretStatic(
    executorId: string,
    region: string,
    modelType: string,
    secretKey: string = 'default-secret-key',
  ): string {
    const crypto = require('crypto');
    const payload = `${executorId}:${region}:${modelType}:${secretKey}`;
    return crypto.createHash('sha256').update(payload).digest('hex').substring(0, 32);
  }
}
