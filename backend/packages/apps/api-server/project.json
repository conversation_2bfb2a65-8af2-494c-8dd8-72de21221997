{"name": "api-server", "$schema": "../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "packages/apps/api-server/src", "projectType": "application", "targets": {"build": {"executor": "@nx/webpack:webpack", "outputs": ["{options.outputPath}"], "defaultConfiguration": "production", "options": {"target": "node", "compiler": "tsc", "outputPath": "dist/packages/apps/api-server", "main": "packages/apps/api-server/src/main.ts", "tsConfig": "packages/apps/api-server/tsconfig.app.json", "assets": ["packages/apps/api-server/src/assets"], "generatePackageJson": true, "isolatedConfig": true, "webpackConfig": "packages/apps/api-server/webpack.config.js"}, "configurations": {"development": {}, "production": {}}}, "serve": {"executor": "@nx/js:node", "defaultConfiguration": "development", "options": {"buildTarget": "api-server:build"}, "configurations": {"development": {"buildTarget": "api-server:build:development"}, "production": {"buildTarget": "api-server:build:production"}}}, "lint": {"executor": "@nx/eslint:lint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["packages/apps/api-server/**/*.ts"]}}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "packages/apps/api-server/jest.config.ts", "passWithNoTests": true}, "configurations": {"ci": {"ci": true, "codeCoverage": true}}}, "container": {"executor": "@nx-tools/nx-container:build", "options": {"load": true, "metadata": {"images": ["$DOCKER_IMAGE_TAG_GROUP/api-server"], "tags": ["type=schedule", "type=ref,event=branch", "type=ref,event=tag", "type=ref,event=pr", "type=semver,pattern={{version}}", "type=semver,pattern={{major}}.{{minor}}", "type=semver,pattern={{major}}", "type=sha,prefix=sha-"]}}}, "deploy": {"command": "gcloud run deploy b20-api-server --image {args.image} --region us-west1"}}, "tags": []}