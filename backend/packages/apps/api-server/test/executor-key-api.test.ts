import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import request from 'supertest';
import { ConfigModule } from '@nestjs/config';
import { ExecutorController } from '../src/app/controllers/executor.controller';
import { ExecutorKeyService } from '@saito/executor';
import { ExecutorAuthGuard } from '../src/app/guards/executor-auth.guard';

describe('ExecutorController (e2e)', () => {
  let app: INestApplication;
  let executorKeyService: ExecutorKeyService;

  beforeEach(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [
        ConfigModule.forRoot({
          isGlobal: true,
          envFilePath: '.env.test',
        }),
      ],
      controllers: [ExecutorController],
      providers: [ExecutorKeyService, ExecutorAuthGuard],
    }).compile();

    app = moduleFixture.createNestApplication();
    executorKeyService = moduleFixture.get<ExecutorKeyService>(ExecutorKeyService);
    await app.init();
  });

  afterEach(async () => {
    await app.close();
  });

  describe('/executor/key (GET)', () => {
    it('should return 401 without executor credentials', () => {
      return request(app.getHttpServer())
        .get('/executor/key?scope=claude:asia')
        .expect(401);
    });

    it('should return 400 for invalid scope format', () => {
      return request(app.getHttpServer())
        .get('/executor/key?scope=invalid-scope')
        .set('x-executor-id', 'test-executor')
        .set('x-executor-secret', 'test-secret')
        .set('x-executor-region', 'asia')
        .expect(400)
        .expect((res: any) => {
          expect(res.body.success).toBe(false);
          expect(res.body.error.code).toBe('INVALID_SCOPE_FORMAT');
        });
    });

    it('should return 400 for missing scope parameter', () => {
      return request(app.getHttpServer())
        .get('/executor/key')
        .set('x-executor-id', 'test-executor')
        .set('x-executor-secret', 'test-secret')
        .set('x-executor-region', 'asia')
        .expect(400)
        .expect((res: any) => {
          expect(res.body.success).toBe(false);
          expect(res.body.error.code).toBe('MISSING_SCOPE');
        });
    });

    // Note: This test would require a running etcd instance with test data
    it.skip('should return encrypted keys for valid scope', () => {
      return request(app.getHttpServer())
        .get('/executor/key?scope=claude:asia')
        .set('x-executor-id', 'test-executor')
        .set('x-executor-secret', 'valid-secret')
        .set('x-executor-region', 'asia')
        .expect(200)
        .expect((res: any) => {
          expect(res.body.success).toBe(true);
          expect(res.body.data.scope).toBe('claude:asia');
          expect(Array.isArray(res.body.data.keys)).toBe(true);
        });
    });
  });

  describe('/executor/health (GET)', () => {
    it('should return health status', () => {
      return request(app.getHttpServer())
        .get('/executor/health')
        .expect(200)
        .expect((res: any) => {
          expect(res.body.success).toBe(true);
          expect(res.body.data.status).toBe('healthy');
          expect(res.body.data.timestamp).toBeDefined();
          expect(res.body.data.version).toBeDefined();
        });
    });
  });
});

describe('ExecutorKeyService', () => {
  let service: ExecutorKeyService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      imports: [
        ConfigModule.forRoot({
          isGlobal: true,
          envFilePath: '.env.test',
        }),
      ],
      providers: [ExecutorKeyService],
    }).compile();

    service = module.get<ExecutorKeyService>(ExecutorKeyService);
  });

  describe('validateScope', () => {
    it('should validate correct scope format', () => {
      const result = ExecutorKeyService.validateScope('claude:asia');
      expect(result.valid).toBe(true);
      expect(result.provider).toBe('claude');
      expect(result.region).toBe('asia');
    });

    it('should reject invalid scope format', () => {
      const result = ExecutorKeyService.validateScope('invalid-scope');
      expect(result.valid).toBe(false);
      expect(result.error).toContain('format provider:region');
    });

    it('should reject empty scope', () => {
      const result = ExecutorKeyService.validateScope('');
      expect(result.valid).toBe(false);
      expect(result.error).toContain('required');
    });

    it('should reject scope with empty parts', () => {
      const result = ExecutorKeyService.validateScope(':asia');
      expect(result.valid).toBe(false);
      expect(result.error).toContain('non-empty');
    });

    it('should reject scope with invalid characters', () => {
      const result = ExecutorKeyService.validateScope('claude@asia');
      expect(result.valid).toBe(false);
      expect(result.error).toContain('alphanumeric');
    });
  });
});

describe('ExecutorAuthGuard', () => {
  describe('generateExecutorSecretStatic', () => {
    it('should generate consistent secrets for same input', () => {
      const secret1 = ExecutorAuthGuard.generateExecutorSecretStatic(
        'test-executor',
        'asia',
        'claude',
        'test-key',
      );
      const secret2 = ExecutorAuthGuard.generateExecutorSecretStatic(
        'test-executor',
        'asia',
        'claude',
        'test-key',
      );
      expect(secret1).toBe(secret2);
      expect(secret1).toHaveLength(32);
    });

    it('should generate different secrets for different inputs', () => {
      const secret1 = ExecutorAuthGuard.generateExecutorSecretStatic(
        'executor1',
        'asia',
        'claude',
        'test-key',
      );
      const secret2 = ExecutorAuthGuard.generateExecutorSecretStatic(
        'executor2',
        'asia',
        'claude',
        'test-key',
      );
      expect(secret1).not.toBe(secret2);
    });
  });
});
