import { EncryptedApiKeyService } from '@saito/third-party';
import { PublicKeyService } from '@saito/executor';
import { ThirdPartyRepository } from '@saito/third-party';

describe('EncryptedApiKeyService Database Sync', () => {
  let service: EncryptedApiKeyService;
  let mockRepository: jest.Mocked<ThirdPartyRepository>;
  let mockPublicKeyService: jest.Mocked<PublicKeyService>;

  beforeEach(() => {
    // Create mock repository
    mockRepository = {
      createThirdPartyKey: jest.fn(),
      updateThirdPartyKey: jest.fn(),
      getThirdPartyKeyByHash: jest.fn(),
    } as any;

    // Create mock public key service
    mockPublicKeyService = {
      isExecutorAvailable: jest.fn().mockResolvedValue(true),
    } as any;

    // Create mock config service
    const mockConfigService = {
      get: jest.fn((key: string, defaultValue?: any) => {
        switch (key) {
          case 'ETCD_HOST':
            return 'localhost';
          case 'ETCD_PORT':
            return 2379;
          default:
            return defaultValue;
        }
      }),
    };

    // Create mock etcd service
    const mockEtcdService = {
      put: jest.fn(),
      get: jest.fn(),
      delete: jest.fn(),
      getAllWithPrefix: jest.fn(),
    } as any;

    // Create service instance with mocked dependencies
    service = new EncryptedApiKeyService(
      mockPublicKeyService,
      mockRepository,
      mockRepository, // Using same mock for both repositories
      mockEtcdService,
    );
  });

  describe('createEncryptedApiKey', () => {
    it('should call database creation method with correct parameters', async () => {
      const request = {
        name: 'Test Shared Key',
        provider: 'openai',
        region: 'us-east-1',
        keyId: 'test-executor-key-id',
        encryptedKey: 'dGVzdC1lbmNyeXB0ZWQta2V5', // base64 encoded test data
        nonce: 'dGVzdC1ub25jZQ==', // base64 encoded test data
        tag: 'dGVzdC10YWc=', // base64 encoded test data
        ephemeralPubKey: 'dGVzdC1wdWJsaWMta2V5', // base64 encoded test data
      };
      const userId = 'test-user-001';

      // Mock successful database creation
      mockRepository.createThirdPartyKey.mockResolvedValue({
        id: 'mock-uuid',
        userId,
        name: request.name,
        provider: request.provider,
        keyHash: 'mock-uuid',
      } as any);

      const result = await service.createEncryptedApiKey(request, userId);

      expect(result).toBeDefined();
      expect(result.uuid).toBeDefined();
      expect(result.provider).toBe(request.provider);
      expect(result.region).toBe(request.region);
      expect(result.keyId).toBe(request.keyId);
      expect(result.status).toBe('waiting-to-verify');

      // Verify database method was called with correct parameters
      expect(mockRepository.createThirdPartyKey).toHaveBeenCalledWith({
        userId,
        name: request.name,
        description: `Shared key for ${request.provider} in ${request.region}`,
        provider: request.provider,
        keyHash: expect.any(String), // UUID
        encryptedKeyData: expect.stringContaining(request.encryptedKey),
      });
    });

    it('should handle database creation failure gracefully', async () => {
      // Mock repository to throw error
      mockRepository.createThirdPartyKey.mockRejectedValue(new Error('Database error'));

      const request = {
        name: 'Test Shared Key',
        provider: 'openai',
        region: 'us-east-1',
        keyId: 'test-executor-key-id',
        encryptedKey: 'dGVzdC1lbmNyeXB0ZWQta2V5',
        nonce: 'dGVzdC1ub25jZQ==',
        tag: 'dGVzdC10YWc=',
        ephemeralPubKey: 'dGVzdC1wdWJsaWMta2V5',
      };
      const userId = 'test-user-001';

      await expect(service.createEncryptedApiKey(request, userId)).rejects.toThrow();
    });
  });

  describe('updateEncryptedApiKeyStatus', () => {
    it('should call database update method when status changes', async () => {
      const provider = 'openai';
      const region = 'us-east-1';
      const uuid = 'test-uuid';
      const status = 'active';

      // Mock existing etcd data
      jest.spyOn(service, 'getEncryptedApiKey').mockResolvedValue({
        uuid: 'test-uuid',
        keyId: 'test-key-id',
        encryptedKey: 'test-encrypted-key',
        nonce: 'test-nonce',
        tag: 'test-tag',
        ephemeralPubKey: 'test-pub-key',
        status: 'waiting-to-verify',
        createdAt: new Date().toISOString(),
        createdBy: 'test-user',
      });

      // Mock successful database update
      mockRepository.updateThirdPartyKey.mockResolvedValue({} as any);

      const result = await service.updateEncryptedApiKeyStatus(provider, region, uuid, status);

      expect(result).toBe(true);

      // Verify database update was called
      expect(mockRepository.updateThirdPartyKey).toHaveBeenCalledWith(uuid, {
        status: 'active',
      });
    });
  });

  describe('deleteEncryptedApiKey', () => {
    it('should call database update method to mark as inactive', async () => {
      const provider = 'openai';
      const region = 'us-east-1';
      const uuid = 'test-uuid';

      // Mock successful database update
      mockRepository.updateThirdPartyKey.mockResolvedValue({} as any);

      const result = await service.deleteEncryptedApiKey(provider, region, uuid);

      expect(result).toBe(true);

      // Verify database update was called to mark as inactive
      expect(mockRepository.updateThirdPartyKey).toHaveBeenCalledWith(uuid, {
        status: 'inactive',
      });
    });
  });

  describe('updateEncryptedApiKey', () => {
    it('should call database update method when name is changed', async () => {
      const provider = 'openai';
      const region = 'us-east-1';
      const uuid = 'test-uuid';

      // Mock existing etcd data
      jest.spyOn(service, 'getEncryptedApiKey').mockResolvedValue({
        uuid: 'test-uuid',
        keyId: 'test-key-id',
        encryptedKey: 'test-encrypted-key',
        nonce: 'test-nonce',
        tag: 'test-tag',
        ephemeralPubKey: 'test-pub-key',
        status: 'active',
        createdAt: new Date().toISOString(),
        createdBy: 'test-user',
      });

      // Mock existing database record
      mockRepository.getThirdPartyKeyByHash.mockResolvedValue({
        id: uuid,
        encrypted_key_data: JSON.stringify({
          encryptedKey: 'test-encrypted-key',
          nonce: 'test-nonce',
          tag: 'test-tag',
          ephemeralPubKey: 'test-pub-key',
        }),
      } as any);

      // Mock successful database update
      mockRepository.updateThirdPartyKey.mockResolvedValue({} as any);

      const updateResult = await service.updateEncryptedApiKey(
        provider,
        region,
        uuid,
        { status: 'active' }
      );

      expect(updateResult).toBe(true);

      // Verify database update was called with name change
      expect(mockRepository.updateThirdPartyKey).toHaveBeenCalledWith(uuid, {
        name: 'Updated Shared Key',
      });
    });

    it('should update encryption data in database when encryption fields change', async () => {
      const provider = 'openai';
      const region = 'us-east-1';
      const uuid = 'test-uuid';

      // Mock existing etcd data
      jest.spyOn(service, 'getEncryptedApiKey').mockResolvedValue({
        uuid: 'test-uuid',
        keyId: 'test-key-id',
        encryptedKey: 'old-encrypted-key',
        nonce: 'old-nonce',
        tag: 'old-tag',
        ephemeralPubKey: 'old-pub-key',
        status: 'active',
        createdAt: new Date().toISOString(),
        createdBy: 'test-user',
      });

      // Mock existing database record
      mockRepository.getThirdPartyKeyByHash.mockResolvedValue({
        id: uuid,
        encrypted_key_data: JSON.stringify({
          encryptedKey: 'old-encrypted-key',
          nonce: 'old-nonce',
          tag: 'old-tag',
          ephemeralPubKey: 'old-pub-key',
        }),
      } as any);

      // Mock successful database update
      mockRepository.updateThirdPartyKey.mockResolvedValue({} as any);

      const newEncryptedKey = 'bmV3LWVuY3J5cHRlZC1rZXk='; // base64 encoded
      const updateResult = await service.updateEncryptedApiKey(
        provider,
        region,
        uuid,
        { status: 'revoked' }
      );

      expect(updateResult).toBe(true);

      // Verify database update was called with updated encrypted data
      expect(mockRepository.updateThirdPartyKey).toHaveBeenCalledWith(uuid, {
        encrypted_key_data: expect.stringContaining(newEncryptedKey),
      });
    });
  });
});
