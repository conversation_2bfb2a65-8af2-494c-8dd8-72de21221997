#!/usr/bin/env bash
set -euo pipefail

# Hasura Management Script
# This script provides easy commands to manage Hasura migrations and metadata

HASURA_ENDPOINT="http://localhost:28717"
ADMIN_SECRET="9AgJckEMHPRgrasj7Ey8jR"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Helper functions
log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Check if <PERSON><PERSON> is running
check_hasura() {
    log_info "Checking Hasura connection..."
    local response=$(curl -s -X GET "$HASURA_ENDPOINT/v1/version" -H "X-Hasura-Admin-Secret: $ADMIN_SECRET" 2>/dev/null || echo "error")
    
    if echo "$response" | jq -e '.version' > /dev/null 2>&1; then
        local version=$(echo "$response" | jq -r '.version')
        log_success "Hasura is running (version: $version)"
        return 0
    else
        log_error "Hasura is not accessible at $HASURA_ENDPOINT"
        return 1
    fi
}

# Apply migrations using SQL files
apply_migrations() {
    log_info "Applying migrations..."
    
    # Find all migration directories
    local migration_dirs=($(find migrations/main_db -name "*_up" -type d | sort))
    
    if [ ${#migration_dirs[@]} -eq 0 ]; then
        log_warning "No migration directories found"
        return 0
    fi
    
    for migration_dir in "${migration_dirs[@]}"; do
        local migration_version=$(basename "$migration_dir" | sed 's/_up$//')
        local up_file="$migration_dir/up.sql"
        
        if [ -f "$up_file" ]; then
            log_info "Applying migration: $migration_version"
            
            # Read SQL content and escape it properly
            local sql_content=$(cat "$up_file" | jq -R -s .)
            
            local response=$(curl -s -X POST "$HASURA_ENDPOINT/v2/query" \
                -H "X-Hasura-Admin-Secret: $ADMIN_SECRET" \
                -H "Content-Type: application/json" \
                -d "{\"type\": \"run_sql\", \"args\": {\"source\": \"main_db\", \"sql\": $sql_content}}")
            
            if echo "$response" | jq -e '.error' > /dev/null; then
                log_error "Failed to apply migration $migration_version:"
                echo "$response" | jq '.error'
                return 1
            else
                log_success "Migration $migration_version applied"
            fi
        else
            log_warning "No up.sql found in $migration_dir"
        fi
    done
    
    log_success "All migrations applied successfully"
}

# Track all tables
track_tables() {
    log_info "Tracking all tables..."
    
    # Get all tables from saito_gateway schema
    local tables_response=$(curl -s -X POST "$HASURA_ENDPOINT/v2/query" \
        -H "X-Hasura-Admin-Secret: $ADMIN_SECRET" \
        -H "Content-Type: application/json" \
        -d '{
            "type": "run_sql",
            "args": {
                "source": "main_db",
                "sql": "SELECT table_name FROM information_schema.tables WHERE table_schema = '\''saito_gateway'\'' ORDER BY table_name;"
            }
        }')
    
    # Track each table
    echo "$tables_response" | jq -r '.result[1:][] | .[0]' | while read -r table_name; do
        if [ -n "$table_name" ] && [ "$table_name" != "null" ]; then
            local response=$(curl -s -X POST "$HASURA_ENDPOINT/v1/metadata" \
                -H "X-Hasura-Admin-Secret: $ADMIN_SECRET" \
                -H "Content-Type: application/json" \
                -d "{
                    \"type\": \"pg_track_table\",
                    \"args\": {
                        \"source\": \"main_db\",
                        \"table\": {
                            \"name\": \"$table_name\",
                            \"schema\": \"saito_gateway\"
                        }
                    }
                }")
            
            if echo "$response" | jq -e '.error' > /dev/null; then
                # Table might already be tracked, check if it's already tracked error
                if echo "$response" | jq -r '.error' | grep -q "already tracked"; then
                    log_info "Table saito_gateway.$table_name already tracked"
                else
                    log_error "Failed to track table saito_gateway.$table_name:"
                    echo "$response" | jq '.error'
                fi
            else
                log_success "Table saito_gateway.$table_name tracked"
            fi
        fi
    done
}

# Reload metadata
reload_metadata() {
    log_info "Reloading metadata..."
    
    local response=$(curl -s -X POST "$HASURA_ENDPOINT/v1/metadata" \
        -H "X-Hasura-Admin-Secret: $ADMIN_SECRET" \
        -H "Content-Type: application/json" \
        -d '{"type": "reload_metadata", "args": {}}')
    
    if echo "$response" | jq -e '.is_consistent' > /dev/null; then
        if [ "$(echo "$response" | jq -r '.is_consistent')" = "true" ]; then
            log_success "Metadata reloaded successfully"
        else
            log_warning "Metadata has inconsistencies:"
            echo "$response" | jq '.inconsistent_objects'
        fi
    else
        log_error "Failed to reload metadata:"
        echo "$response" | jq '.error'
        return 1
    fi
}

# Show status
show_status() {
    log_info "Hasura Status:"
    
    # Check connection
    check_hasura || return 1
    
    # Get metadata info
    local metadata=$(curl -s -X POST "$HASURA_ENDPOINT/v1/metadata" \
        -H "X-Hasura-Admin-Secret: $ADMIN_SECRET" \
        -H "Content-Type: application/json" \
        -d '{"type": "export_metadata", "args": {}}')
    
    local table_count=$(echo "$metadata" | jq '.sources[0].tables | length')
    log_info "Tracked tables: $table_count"
    
    # List tables
    echo "$metadata" | jq -r '.sources[0].tables[] | "  - " + .table.schema + "." + .table.name' | sort
}

# Main command handler
case "${1:-help}" in
    "check")
        check_hasura
        ;;
    "migrate")
        check_hasura && apply_migrations
        ;;
    "track")
        check_hasura && track_tables
        ;;
    "reload")
        check_hasura && reload_metadata
        ;;
    "setup")
        check_hasura && apply_migrations && track_tables && reload_metadata
        ;;
    "status")
        show_status
        ;;
    "help"|*)
        echo "Hasura Manager - Database Migration and Metadata Management"
        echo ""
        echo "Usage: $0 <command>"
        echo ""
        echo "Commands:"
        echo "  check    - Check if Hasura is running"
        echo "  migrate  - Apply all SQL migrations"
        echo "  track    - Track all tables in Hasura"
        echo "  reload   - Reload Hasura metadata"
        echo "  setup    - Run migrate + track + reload (full setup)"
        echo "  status   - Show Hasura status and tracked tables"
        echo "  help     - Show this help message"
        echo ""
        echo "Environment:"
        echo "  Hasura Endpoint: $HASURA_ENDPOINT"
        echo "  Admin Secret: $ADMIN_SECRET"
        ;;
esac
