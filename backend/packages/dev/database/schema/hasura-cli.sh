#!/usr/bin/env bash
set -euo pipefail

# Hasura CLI wrapper script with proper environment variables
export HASURA_VERSION=2.35.1
export HASURA_GRAPHQL_SERVER_PORT=28717
export HASURA_GRAPHQL_ADMIN_SECRET=9AgJckEMHPRgrasj7Ey8jR
export HASURA_ENDPOINT=http://localhost:${HASURA_GRAPHQL_SERVER_PORT}

# Get the directory of this script
DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" >/dev/null 2>&1 && pwd)"

# Download Hasura CLI if not exists
CLI_PATH=cli-hasura-linux-amd64-${HASURA_VERSION}
if [[ "$OSTYPE" == "darwin"* ]]; then
    CLI_PATH=cli-hasura-darwin-amd64-${HASURA_VERSION}
fi

if [ ! -e "${DIR}/../bin/${CLI_PATH}" ]; then
    echo "Downloading Hasura CLI..."
    mkdir -p "${DIR}/../bin"
    if [[ "$OSTYPE" == "darwin"* ]]; then
        curl -L https://github.com/hasura/graphql-engine/releases/download/v${HASURA_VERSION}/cli-hasura-darwin-amd64 -o "${DIR}/../bin/${CLI_PATH}"
    else
        curl -L https://github.com/hasura/graphql-engine/releases/download/v${HASURA_VERSION}/cli-hasura-linux-amd64 -o "${DIR}/../bin/${CLI_PATH}"
    fi
    chmod +x "${DIR}/../bin/${CLI_PATH}"
fi

echo "Using Hasura endpoint: $HASURA_ENDPOINT"
echo "Using admin secret: $HASURA_GRAPHQL_ADMIN_SECRET"

# Execute Hasura CLI with proper arguments
if [ $# -eq 0 ]; then
    echo "Usage: $0 <hasura-command> [args...]"
    echo "Examples:"
    echo "  $0 migrate status"
    echo "  $0 migrate apply --all-databases"
    echo "  $0 metadata apply"
    echo "  $0 metadata reload"
    exit 1
fi

if [ $1 = "migrate" ] || [ $1 = "metadata" ] || [ $1 = "seed" ]; then
    "${DIR}/../bin/${CLI_PATH}" --skip-update-check --endpoint "$HASURA_ENDPOINT" --admin-secret "$HASURA_GRAPHQL_ADMIN_SECRET" "$@"
else
    "${DIR}/../bin/${CLI_PATH}" --skip-update-check "$@"
fi
