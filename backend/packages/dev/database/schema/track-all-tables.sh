#!/usr/bin/env bash
set -euo pipefail

# Track all tables in Hasura
HASURA_ENDPOINT="http://localhost:28717"
ADMIN_SECRET="9AgJckEMHPRgrasj7Ey8jR"

echo "🚀 Starting to track all tables in Hasura..."
echo "📍 Hasura endpoint: $HASURA_ENDPOINT"

# Function to track a table
track_table() {
    local schema=$1
    local table=$2
    
    echo "📝 Tracking table: $schema.$table"
    
    local response=$(curl -s -X POST "$HASURA_ENDPOINT/v1/metadata" \
        -H "X-Hasura-Admin-Secret: $ADMIN_SECRET" \
        -H "Content-Type: application/json" \
        -d "{
            \"type\": \"pg_track_table\",
            \"args\": {
                \"source\": \"main_db\",
                \"table\": {
                    \"name\": \"$table\",
                    \"schema\": \"$schema\"
                }
            }
        }")
    
    # Check for errors
    if echo "$response" | jq -e '.error' > /dev/null; then
        echo "❌ Error tracking table $schema.$table:"
        echo "$response" | jq '.error'
        return 1
    else
        echo "✅ Table $schema.$table tracked successfully"
        return 0
    fi
}

# Get all tables from saito_gateway schema
echo "🔍 Getting all tables from saito_gateway schema..."
tables_response=$(curl -s -X POST "$HASURA_ENDPOINT/v2/query" \
    -H "X-Hasura-Admin-Secret: $ADMIN_SECRET" \
    -H "Content-Type: application/json" \
    -d '{
        "type": "run_sql",
        "args": {
            "source": "main_db",
            "sql": "SELECT table_name FROM information_schema.tables WHERE table_schema = '\''saito_gateway'\'' ORDER BY table_name;"
        }
    }')

# Extract table names (skip the header row)
table_names=$(echo "$tables_response" | jq -r '.result[1:][0]')

# Track each table
echo "$tables_response" | jq -r '.result[1:][] | .[0]' | while read -r table_name; do
    if [ -n "$table_name" ] && [ "$table_name" != "null" ]; then
        track_table "saito_gateway" "$table_name"
    fi
done

echo "🎉 All tables tracked successfully!"

# Reload metadata to ensure consistency
echo "🔄 Reloading metadata..."
reload_response=$(curl -s -X POST "$HASURA_ENDPOINT/v1/metadata" \
    -H "X-Hasura-Admin-Secret: $ADMIN_SECRET" \
    -H "Content-Type: application/json" \
    -d '{"type": "reload_metadata", "args": {}}')

if echo "$reload_response" | jq -e '.is_consistent' > /dev/null; then
    if [ "$(echo "$reload_response" | jq -r '.is_consistent')" = "true" ]; then
        echo "✅ Metadata is consistent"
    else
        echo "⚠️  Metadata has inconsistencies:"
        echo "$reload_response" | jq '.inconsistent_objects'
    fi
else
    echo "❌ Error reloading metadata:"
    echo "$reload_response" | jq '.error'
fi

echo "🏁 Hasura table tracking completed!"
