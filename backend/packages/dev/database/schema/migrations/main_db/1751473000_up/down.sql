-- =============================================
-- 回滚文件：撤销客户端加密 + KMS 存储架构迁移
-- 版本：1751473000_down
-- =============================================

BEGIN;

-- 1. 删除创建的函数
DROP FUNCTION IF EXISTS saito_gateway.update_api_key_stats(UUID, INTEGER, INTEGER, DECIMAL);

-- 2. 删除创建的视图
DROP VIEW IF EXISTS saito_gateway.v_api_keys_summary;

-- 3. 删除索引
DROP INDEX IF EXISTS saito_gateway.idx_api_keys_key_type;
DROP INDEX IF EXISTS saito_gateway.idx_api_keys_provider;
DROP INDEX IF EXISTS saito_gateway.idx_api_keys_kms_key_id;
DROP INDEX IF EXISTS saito_gateway.idx_api_keys_type_status;
DROP INDEX IF EXISTS saito_gateway.idx_api_keys_user_type;
DROP INDEX IF EXISTS saito_gateway.idx_api_keys_user_provider;

-- 4. 删除约束
ALTER TABLE saito_gateway.api_keys DROP CONSTRAINT IF EXISTS chk_api_keys_key_type;
ALTER TABLE saito_gateway.api_keys DROP CONSTRAINT IF EXISTS chk_api_keys_provider;
ALTER TABLE saito_gateway.api_keys DROP CONSTRAINT IF EXISTS chk_api_keys_kms_data;

-- 5. 删除新添加的列
ALTER TABLE saito_gateway.api_keys DROP COLUMN IF EXISTS key_type;
ALTER TABLE saito_gateway.api_keys DROP COLUMN IF EXISTS provider;
ALTER TABLE saito_gateway.api_keys DROP COLUMN IF EXISTS kms_key_id;
ALTER TABLE saito_gateway.api_keys DROP COLUMN IF EXISTS encrypted_key_data;
ALTER TABLE saito_gateway.api_keys DROP COLUMN IF EXISTS total_requests;
ALTER TABLE saito_gateway.api_keys DROP COLUMN IF EXISTS total_tokens;
ALTER TABLE saito_gateway.api_keys DROP COLUMN IF EXISTS total_cost;

-- 6. 重新创建 api_categories 表
CREATE TABLE saito_gateway.api_categories (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(50) NOT NULL UNIQUE,
    category VARCHAR(50) NOT NULL,
    provider VARCHAR(50) NOT NULL,
    icon_url TEXT,
    description TEXT,
    auth_type VARCHAR(20) NOT NULL,
    auth_header VARCHAR(50) NOT NULL,
    auth_prefix VARCHAR(20),
    key_prefix VARCHAR(20) NOT NULL,
    key_pattern VARCHAR(100) NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- 7. 重新添加 category_id 列
ALTER TABLE saito_gateway.api_keys ADD COLUMN category_id UUID;
ALTER TABLE saito_gateway.api_usage ADD COLUMN category_id UUID;

-- 8. 重新创建外键约束
ALTER TABLE saito_gateway.api_keys 
ADD CONSTRAINT api_keys_category_id_fkey 
FOREIGN KEY (category_id) REFERENCES saito_gateway.api_categories(id);

ALTER TABLE saito_gateway.api_usage 
ADD CONSTRAINT api_usage_category_id_fkey 
FOREIGN KEY (category_id) REFERENCES saito_gateway.api_categories(id);

-- 9. 重新创建索引
CREATE INDEX idx_api_categories_name ON saito_gateway.api_categories(name);
CREATE INDEX idx_api_categories_category ON saito_gateway.api_categories(category);
CREATE INDEX idx_api_keys_category ON saito_gateway.api_keys(category_id);

-- 10. 插入基础数据
INSERT INTO saito_gateway.api_categories (name, category, provider, auth_type, auth_header, key_prefix, key_pattern) 
VALUES 
    ('openai', 'LLM', 'OpenAI', 'bearer', 'Authorization', 'sk-', '^sk-[A-Za-z0-9]{48}$'),
    ('deepseek', 'LLM', 'DeepSeek', 'bearer', 'Authorization', 'st-', '^st-[A-Za-z0-9]{48}$'),
    ('anthropic', 'LLM', 'Anthropic', 'bearer', 'x-api-key', 'sk-ant-', '^sk-ant-[A-Za-z0-9-]{95}$'),
    ('ollama', 'LLM', 'Ollama', 'bearer', 'Authorization', 'ollama-', '^ollama-[A-Za-z0-9]{32}$')
ON CONFLICT (name) DO NOTHING;

COMMIT;

SELECT '🔄 已回滚到原始架构' AS status;
