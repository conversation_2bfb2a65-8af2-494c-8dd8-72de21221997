-- Rollback: Make device_id NOT NULL again in connect_tasks table
-- WARNING: This will fail if there are any connect_tasks with NULL device_id

-- First, we need to handle any existing NULL device_id records
-- This is a destructive operation - you may want to backup data first

-- Drop the foreign key constraint
ALTER TABLE saito_gateway.connect_tasks 
DROP CONSTRAINT IF EXISTS connect_tasks_device_id_fkey;

-- Make device_id NOT NULL again
-- NOTE: This will fail if there are any NULL values
ALTER TABLE saito_gateway.connect_tasks 
ALTER COLUMN device_id SET NOT NULL;

-- Add the foreign key constraint back
ALTER TABLE saito_gateway.connect_tasks 
ADD CONSTRAINT connect_tasks_device_id_fkey 
FOREIGN KEY (device_id) REFERENCES saito_gateway.devices(id);

-- Remove the comment
COMMENT ON COLUMN saito_gateway.connect_tasks.device_id IS NULL;
