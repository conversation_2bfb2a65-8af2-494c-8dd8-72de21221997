-- Migration: Make device_id nullable in connect_tasks table
-- This allows creating connect tasks without pre-creating devices
-- Devices will be created during registration with their DID

-- Drop the foreign key constraint first
ALTER TABLE saito_gateway.connect_tasks 
DROP CONSTRAINT IF EXISTS connect_tasks_device_id_fkey;

-- Make device_id nullable
ALTER TABLE saito_gateway.connect_tasks 
ALTER COLUMN device_id DROP NOT NULL;

-- Add the foreign key constraint back, but now it allows NULL values
ALTER TABLE saito_gateway.connect_tasks 
ADD CONSTRAINT connect_tasks_device_id_fkey 
FOREIGN KEY (device_id) REFERENCES saito_gateway.devices(id);

-- Add a comment to explain the change
COMMENT ON COLUMN saito_gateway.connect_tasks.device_id IS 
'Device ID - nullable until device registration with DID';
