-- 添加设备支持的模型表
CREATE TABLE saito_gateway.device_models (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  device_id UUID NOT NULL REFERENCES saito_gateway.devices(id),
  model_name VARCHAR(255) NOT NULL,
  model_family VARCHAR(255),
  parameter_size VARCHAR(50),
  quantization_level VARCHAR(50),
  format VARCHAR(50),
  size_bytes BIGINT,
  digest VARCHAR(255),
  modified_at TIMESTAMPTZ,
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  UNIQUE(device_id, model_name)
);

-- 添加索引
CREATE INDEX idx_device_models_device_id ON saito_gateway.device_models(device_id);
CREATE INDEX idx_device_models_model_name ON saito_gateway.device_models(model_name);

-- 添加触发器
CREATE TRIGGER update_device_models_modtime
    BEFORE UPDATE ON saito_gateway.device_models
    FOR EACH ROW
    EXECUTE FUNCTION saito_gateway.update_modified_column();
