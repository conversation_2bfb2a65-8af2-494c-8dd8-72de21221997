-- Migration: Add user_id column to tasks table
-- Date: 2025-01-06
-- Description: Add user_id column to tasks table to support user-task association

-- Add user_id column to tasks table
ALTER TABLE saito_gateway.tasks 
ADD COLUMN user_id UUID REFERENCES saito_gateway.users(id);

-- Add index for user_id
CREATE INDEX idx_tasks_user ON saito_gateway.tasks(user_id);

-- Update existing tasks to have a default user_id (optional)
-- You can uncomment this if you want to set a default user for existing tasks
-- UPDATE saito_gateway.tasks 
-- SET user_id = (SELECT id FROM saito_gateway.users LIMIT 1)
-- WHERE user_id IS NULL;
