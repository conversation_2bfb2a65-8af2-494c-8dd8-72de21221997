-- 回滚模型兼容性跟踪表的创建

-- 删除触发器
DROP TRIGGER IF EXISTS trigger_update_model_compatibility_updated_at 
  ON saito_gateway.model_node_compatibility;

-- 删除触发器函数
DROP FUNCTION IF EXISTS update_model_compatibility_updated_at();

-- 删除索引
DROP INDEX IF EXISTS saito_gateway.idx_model_compatibility_device;
DROP INDEX IF EXISTS saito_gateway.idx_model_compatibility_model;
DROP INDEX IF EXISTS saito_gateway.idx_model_compatibility_status;
DROP INDEX IF EXISTS saito_gateway.idx_model_compatibility_cooldown;
DROP INDEX IF EXISTS saito_gateway.idx_model_compatibility_updated_at;
DROP INDEX IF EXISTS saito_gateway.idx_model_compatibility_device_status;
DROP INDEX IF EXISTS saito_gateway.idx_model_compatibility_model_status;

-- 删除表
DROP TABLE IF EXISTS saito_gateway.model_node_compatibility;
