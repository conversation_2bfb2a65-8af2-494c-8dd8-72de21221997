-- 创建模型兼容性跟踪表
-- 用于跟踪模型在特定节点上的执行历史和冷却状态

CREATE TABLE IF NOT EXISTS saito_gateway.model_node_compatibility (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  device_id TEXT NOT NULL REFERENCES saito_gateway.devices(id) ON DELETE CASCADE,
  model_name VARCHAR(255) NOT NULL,
  status VARCHAR(20) DEFAULT 'available' CHECK (
    status IN ('available', 'cooling_5min', 'cooling_1h', 'cooling_24h', 'blocked')
  ),
  failure_count INTEGER DEFAULT 0 CHECK (failure_count >= 0),
  success_count INTEGER DEFAULT 0 CHECK (success_count >= 0),
  last_failure_at TIMESTAMPTZ,
  last_success_at TIMESTAMPTZ,
  last_failure_error TEXT,
  cooldown_until TIMESTAMPTZ,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),

  -- 确保每个设备-模型组合只有一条记录
  UNIQUE(device_id, model_name)
);

-- 创建索引以优化查询性能
CREATE INDEX IF NOT EXISTS idx_model_compatibility_device 
  ON saito_gateway.model_node_compatibility(device_id);

CREATE INDEX IF NOT EXISTS idx_model_compatibility_model 
  ON saito_gateway.model_node_compatibility(model_name);

CREATE INDEX IF NOT EXISTS idx_model_compatibility_status 
  ON saito_gateway.model_node_compatibility(status);

CREATE INDEX IF NOT EXISTS idx_model_compatibility_cooldown 
  ON saito_gateway.model_node_compatibility(cooldown_until) 
  WHERE cooldown_until IS NOT NULL;

CREATE INDEX IF NOT EXISTS idx_model_compatibility_updated_at 
  ON saito_gateway.model_node_compatibility(updated_at);

-- 创建复合索引用于常见查询
CREATE INDEX IF NOT EXISTS idx_model_compatibility_device_status 
  ON saito_gateway.model_node_compatibility(device_id, status);

CREATE INDEX IF NOT EXISTS idx_model_compatibility_model_status 
  ON saito_gateway.model_node_compatibility(model_name, status);

-- 创建触发器自动更新 updated_at 字段
CREATE OR REPLACE FUNCTION update_model_compatibility_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS trigger_update_model_compatibility_updated_at 
  ON saito_gateway.model_node_compatibility;

CREATE TRIGGER trigger_update_model_compatibility_updated_at
  BEFORE UPDATE ON saito_gateway.model_node_compatibility
  FOR EACH ROW
  EXECUTE FUNCTION update_model_compatibility_updated_at();

-- 添加注释
COMMENT ON TABLE saito_gateway.model_node_compatibility IS '模型节点兼容性跟踪表，记录模型在各节点上的执行历史和冷却状态';
COMMENT ON COLUMN saito_gateway.model_node_compatibility.device_id IS '设备ID，关联devices表';
COMMENT ON COLUMN saito_gateway.model_node_compatibility.model_name IS '模型名称';
COMMENT ON COLUMN saito_gateway.model_node_compatibility.status IS '当前状态：available(可用), cooling_5min(5分钟冷却), cooling_1h(1小时冷却), cooling_24h(24小时冷却), blocked(永久阻止)';
COMMENT ON COLUMN saito_gateway.model_node_compatibility.failure_count IS '累计失败次数';
COMMENT ON COLUMN saito_gateway.model_node_compatibility.success_count IS '累计成功次数';
COMMENT ON COLUMN saito_gateway.model_node_compatibility.last_failure_at IS '最后失败时间';
COMMENT ON COLUMN saito_gateway.model_node_compatibility.last_success_at IS '最后成功时间';
COMMENT ON COLUMN saito_gateway.model_node_compatibility.last_failure_error IS '最后失败的错误信息';
COMMENT ON COLUMN saito_gateway.model_node_compatibility.cooldown_until IS '冷却结束时间，NULL表示无冷却';
