-- 创建节点性能统计表
-- 用于存储节点在不同时间窗口内的性能指标

CREATE TABLE saito_gateway.node_performance_metrics (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  device_id TEXT NOT NULL REFERENCES saito_gateway.devices(id),
  time_window VARCHAR(10) NOT NULL CHECK (time_window IN ('5min', '1h', '24h', 'lifetime')),
  window_start TIMESTAMPTZ NOT NULL,
  window_end TIMESTAMPTZ,
  
  -- 任务统计
  total_tasks INTEGER DEFAULT 0,
  successful_tasks INTEGER DEFAULT 0,
  failed_tasks INTEGER DEFAULT 0,
  
  -- 成功率（计算字段）
  success_rate DECIMAL(5,4) GENERATED ALWAYS AS (
    CASE WHEN total_tasks > 0 THEN successful_tasks::DECIMAL / total_tasks ELSE 0 END
  ) STORED,
  
  -- 响应时间统计（秒）
  avg_response_time DECIMAL(10,3),
  min_response_time DECIMAL(10,3),
  max_response_time DECIMAL(10,3),
  
  -- Token统计
  total_tokens INTEGER DEFAULT 0,
  tokens_per_second DECIMAL(10,3), -- TPS
  
  -- 总执行时间（秒）
  total_duration DECIMAL(15,3),
  
  -- 元数据
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  
  -- 复合唯一索引，确保每个设备在每个时间窗口的每个时间点只有一条记录
  UNIQUE(device_id, time_window, window_start)
);

-- 创建索引以优化查询性能
CREATE INDEX idx_node_performance_device_window ON saito_gateway.node_performance_metrics(device_id, time_window);
CREATE INDEX idx_node_performance_window_start ON saito_gateway.node_performance_metrics(window_start);
CREATE INDEX idx_node_performance_updated_at ON saito_gateway.node_performance_metrics(updated_at);
CREATE INDEX idx_node_performance_success_rate ON saito_gateway.node_performance_metrics(success_rate);
CREATE INDEX idx_node_performance_avg_response_time ON saito_gateway.node_performance_metrics(avg_response_time);

-- 添加触发器以自动更新 updated_at 字段
CREATE TRIGGER update_node_performance_metrics_modtime
    BEFORE UPDATE ON saito_gateway.node_performance_metrics
    FOR EACH ROW
    EXECUTE FUNCTION saito_gateway.update_modified_column();

-- 为任务表添加性能相关字段（如果不存在）
ALTER TABLE saito_gateway.tasks 
ADD COLUMN IF NOT EXISTS tier VARCHAR(20) DEFAULT 'low_rank' CHECK (tier IN ('primary', 'low_rank'));

ALTER TABLE saito_gateway.tasks 
ADD COLUMN IF NOT EXISTS response_time DECIMAL(10,3);

ALTER TABLE saito_gateway.tasks 
ADD COLUMN IF NOT EXISTS tokens_per_second DECIMAL(10,3);

ALTER TABLE saito_gateway.tasks 
ADD COLUMN IF NOT EXISTS failure_reason TEXT;

ALTER TABLE saito_gateway.tasks 
ADD COLUMN IF NOT EXISTS retry_count INTEGER DEFAULT 0;

-- 为任务表添加索引
CREATE INDEX IF NOT EXISTS idx_tasks_tier ON saito_gateway.tasks(tier);
CREATE INDEX IF NOT EXISTS idx_tasks_response_time ON saito_gateway.tasks(response_time);
CREATE INDEX IF NOT EXISTS idx_tasks_device_status ON saito_gateway.tasks(device_id, status);
CREATE INDEX IF NOT EXISTS idx_tasks_created_at ON saito_gateway.tasks(created_at);

-- 添加注释
COMMENT ON TABLE saito_gateway.node_performance_metrics IS '节点性能统计表，存储不同时间窗口的性能指标';
COMMENT ON COLUMN saito_gateway.node_performance_metrics.device_id IS '设备ID，关联到devices表';
COMMENT ON COLUMN saito_gateway.node_performance_metrics.time_window IS '时间窗口：5min, 1h, 24h, lifetime';
COMMENT ON COLUMN saito_gateway.node_performance_metrics.window_start IS '时间窗口开始时间';
COMMENT ON COLUMN saito_gateway.node_performance_metrics.window_end IS '时间窗口结束时间';
COMMENT ON COLUMN saito_gateway.node_performance_metrics.success_rate IS '成功率，自动计算字段';
COMMENT ON COLUMN saito_gateway.node_performance_metrics.avg_response_time IS '平均响应时间（秒）';
COMMENT ON COLUMN saito_gateway.node_performance_metrics.tokens_per_second IS '每秒处理的token数量';
