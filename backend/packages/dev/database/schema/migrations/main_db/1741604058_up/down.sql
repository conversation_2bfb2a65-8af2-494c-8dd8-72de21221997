-- 回滚节点性能统计表的创建

-- 删除任务表的新增字段
ALTER TABLE saito_gateway.tasks DROP COLUMN IF EXISTS tier;
ALTER TABLE saito_gateway.tasks DROP COLUMN IF EXISTS response_time;
ALTER TABLE saito_gateway.tasks DROP COLUMN IF EXISTS tokens_per_second;
ALTER TABLE saito_gateway.tasks DROP COLUMN IF EXISTS failure_reason;
ALTER TABLE saito_gateway.tasks DROP COLUMN IF EXISTS retry_count;

-- 删除任务表的新增索引
DROP INDEX IF EXISTS saito_gateway.idx_tasks_tier;
DROP INDEX IF EXISTS saito_gateway.idx_tasks_response_time;
DROP INDEX IF EXISTS saito_gateway.idx_tasks_device_status;
DROP INDEX IF EXISTS saito_gateway.idx_tasks_created_at;

-- 删除节点性能统计表的触发器
DROP TRIGGER IF EXISTS update_node_performance_metrics_modtime ON saito_gateway.node_performance_metrics;

-- 删除节点性能统计表的索引
DROP INDEX IF EXISTS saito_gateway.idx_node_performance_device_window;
DROP INDEX IF EXISTS saito_gateway.idx_node_performance_window_start;
DROP INDEX IF EXISTS saito_gateway.idx_node_performance_updated_at;
DROP INDEX IF EXISTS saito_gateway.idx_node_performance_success_rate;
DROP INDEX IF EXISTS saito_gateway.idx_node_performance_avg_response_time;

-- 删除节点性能统计表
DROP TABLE IF EXISTS saito_gateway.node_performance_metrics;
