-- Migration: Change devices table id from UUID to TEXT to support DID format
-- This allows using DID strings as device IDs instead of UUIDs

-- First, we need to handle foreign key constraints
-- Drop all foreign key constraints that reference devices.id
ALTER TABLE saito_gateway.connect_tasks
DROP CONSTRAINT IF EXISTS connect_tasks_device_id_fkey;

ALTER TABLE saito_gateway.connect_tasks
DROP CONSTRAINT IF EXISTS connect_tasks_node_id_fkey;

ALTER TABLE saito_gateway.device_status_changes
DROP CONSTRAINT IF EXISTS device_status_changes_device_id_fkey;

ALTER TABLE saito_gateway.device_metrics
DROP CONSTRAINT IF EXISTS device_metrics_device_id_fkey;

ALTER TABLE saito_gateway.tasks
DROP CONSTRAINT IF EXISTS tasks_device_id_fkey;

ALTER TABLE saito_gateway.earnings
DROP CONSTRAINT IF EXISTS earnings_device_id_fkey;

ALTER TABLE saito_gateway.device_models
DROP CONSTRAINT IF EXISTS device_models_device_id_fkey;

-- Change the devices.id column type from UUID to TEXT
ALTER TABLE saito_gateway.devices
ALTER COLUMN id TYPE TEXT;

-- Change the device_id column type in all referencing tables to TEXT
ALTER TABLE saito_gateway.connect_tasks
ALTER COLUMN device_id TYPE TEXT;

ALTER TABLE saito_gateway.device_status_changes
ALTER COLUMN device_id TYPE TEXT;

ALTER TABLE saito_gateway.device_metrics
ALTER COLUMN device_id TYPE TEXT;

ALTER TABLE saito_gateway.tasks
ALTER COLUMN device_id TYPE TEXT;

ALTER TABLE saito_gateway.earnings
ALTER COLUMN device_id TYPE TEXT;

ALTER TABLE saito_gateway.device_models
ALTER COLUMN device_id TYPE TEXT;

-- Recreate the foreign key constraints
ALTER TABLE saito_gateway.connect_tasks
ADD CONSTRAINT connect_tasks_device_id_fkey
FOREIGN KEY (device_id) REFERENCES saito_gateway.devices(id);

ALTER TABLE saito_gateway.device_status_changes
ADD CONSTRAINT device_status_changes_device_id_fkey
FOREIGN KEY (device_id) REFERENCES saito_gateway.devices(id);

ALTER TABLE saito_gateway.device_metrics
ADD CONSTRAINT device_metrics_device_id_fkey
FOREIGN KEY (device_id) REFERENCES saito_gateway.devices(id);

ALTER TABLE saito_gateway.tasks
ADD CONSTRAINT tasks_device_id_fkey
FOREIGN KEY (device_id) REFERENCES saito_gateway.devices(id);

ALTER TABLE saito_gateway.earnings
ADD CONSTRAINT earnings_device_id_fkey
FOREIGN KEY (device_id) REFERENCES saito_gateway.devices(id);

ALTER TABLE saito_gateway.device_models
ADD CONSTRAINT device_models_device_id_fkey
FOREIGN KEY (device_id) REFERENCES saito_gateway.devices(id);

-- Add comments to explain the change
COMMENT ON COLUMN saito_gateway.devices.id IS 
'Device ID - now supports DID format strings like did:sight:hoster:...';

COMMENT ON COLUMN saito_gateway.connect_tasks.device_id IS 
'Device ID - supports DID format, nullable until device registration';
