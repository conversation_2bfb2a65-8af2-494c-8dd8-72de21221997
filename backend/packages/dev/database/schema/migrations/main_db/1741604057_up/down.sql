-- Rollback: Change devices table id back from TEXT to UUID
-- WARNING: This will fail if there are any DID format device IDs in the database

-- Drop foreign key constraints
ALTER TABLE saito_gateway.connect_tasks 
DROP CONSTRAINT IF EXISTS connect_tasks_device_id_fkey;

-- Change back to UUID type
-- NOTE: This will fail if there are any non-UUID values
ALTER TABLE saito_gateway.devices 
ALTER COLUMN id TYPE UUID USING id::UUID;

ALTER TABLE saito_gateway.connect_tasks 
ALTER COLUMN device_id TYPE UUID USING device_id::UUID;

-- Recreate foreign key constraints
ALTER TABLE saito_gateway.connect_tasks 
ADD CONSTRAINT connect_tasks_device_id_fkey 
FOREIGN KEY (device_id) REFERENCES saito_gateway.devices(id);

-- Remove comments
COMMENT ON COLUMN saito_gateway.devices.id IS NULL;
COMMENT ON COLUMN saito_gateway.connect_tasks.device_id IS NULL;
