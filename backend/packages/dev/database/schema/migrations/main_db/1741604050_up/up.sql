-- 合并的数据库架构文件
-- 包含所有up.sql文件的内容

-- =============================================
-- 基础设置
-- =============================================
CREATE EXTENSION IF NOT EXISTS pgcrypto;

CREATE OR REPLACE FUNCTION public.set_current_timestamp_updated_at()
RETURNS TRIGGER AS
$$
DECLARE
    _new record;
BEGIN
    _new := NEW;
    _new.updated_at = NOW();
    RETURN _new;
END;
$$
LANGUAGE plpgsql;

CREATE SCHEMA IF NOT EXISTS saito_gateway;

-- =============================================
-- 用户和设备表
-- =============================================

-- 用户表：使用UUID作为用户ID
CREATE TABLE saito_gateway.users (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  wallet_address VARCHAR(42) UNIQUE,
  username VARCHAR(255),
  email VARCHAR(255),
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  first_name VARCHAR(100),
  last_name VARCHAR(100),
  phone VARCHAR(20),
  country VARCHAR(2),
  preferences JSONB,
  account_type VARCHAR(20) DEFAULT 'standard',
  verified BOOLEAN DEFAULT FALSE,
  verification_token UUID
);

CREATE INDEX idx_users_wallet ON saito_gateway.users(wallet_address);

-- 设备表：存储设备的详细信息
CREATE TABLE saito_gateway.devices (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name VARCHAR(255),
  user_id UUID NOT NULL REFERENCES saito_gateway.users(id),
  owner_address VARCHAR(42),
  reward_address VARCHAR(42),
  status VARCHAR(20) NOT NULL DEFAULT 'waiting' CHECK (status IN ('waiting', 'in-progress', 'connected', 'disconnected', 'failed')),
  device_type VARCHAR(255),
  -- 硬件信息
  cpu_model VARCHAR(255),
  cpu_cores INTEGER,
  cpu_threads INTEGER,
  cpu_usage_percent DECIMAL(5,2),
  ram_total BIGINT,  -- 内存总量(MB)
  ram_available BIGINT,  -- 可用内存(MB)
  gpu_model VARCHAR(255),
  gpu_count INTEGER,
  gpu_memory BIGINT,  -- GPU内存(MB)
  gpu_temperature DECIMAL(5,2),
  disk_total BIGINT,  -- 磁盘总量(GB)
  disk_available BIGINT,  -- 可用磁盘(GB)
  -- 网络信息
  ip_address VARCHAR(45),
  last_ping TIMESTAMPTZ,
  latency INTEGER,  -- 网络延迟(ms)
  -- 运行信息
  uptime_seconds BIGINT,
  last_boot TIMESTAMPTZ,
  os_info TEXT,
  -- 元数据
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  last_error TEXT,
  firmware_version VARCHAR(50),
  software_version VARCHAR(50),
  last_maintenance_at TIMESTAMPTZ,
  next_maintenance_at TIMESTAMPTZ,
  health_score INTEGER,
  tags TEXT[]
);

CREATE INDEX idx_devices_status ON saito_gateway.devices(status);
CREATE INDEX idx_devices_user ON saito_gateway.devices(user_id);

-- 连接任务表
CREATE TABLE saito_gateway.connect_tasks (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  task_name VARCHAR(255) NOT NULL,
  user_id UUID NOT NULL REFERENCES saito_gateway.users(id),
  owner_address VARCHAR(42) NOT NULL,
  reward_address VARCHAR(42),
  signature TEXT NOT NULL,
  device_id UUID NOT NULL REFERENCES saito_gateway.devices(id),
  one_time_code VARCHAR(12) NOT NULL UNIQUE,
  gateway_address VARCHAR(255),
  device_type VARCHAR(255),
  gpu_type VARCHAR(255),
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  status VARCHAR(20) NOT NULL DEFAULT 'waiting' CHECK (status IN ('waiting', 'in-progress', 'connected', 'disconnected', 'failed'))
);

CREATE INDEX idx_connect_tasks_code ON saito_gateway.connect_tasks(one_time_code);
CREATE INDEX idx_connect_tasks_status ON saito_gateway.connect_tasks(status);
CREATE INDEX idx_connect_tasks_user ON saito_gateway.connect_tasks(user_id);
CREATE INDEX idx_connect_tasks_device_id ON saito_gateway.devices(id);

-- =============================================
-- 触发器函数
-- =============================================

-- 重新创建触发器函数
DROP FUNCTION IF EXISTS saito_gateway.update_modified_column();
CREATE OR REPLACE FUNCTION saito_gateway.update_modified_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

ALTER TABLE saito_gateway.connect_tasks
    ALTER COLUMN updated_at SET DEFAULT NOW();

-- 创建触发器
CREATE TRIGGER update_connect_tasks_modtime
    BEFORE UPDATE ON saito_gateway.connect_tasks
    FOR EACH ROW
    EXECUTE FUNCTION saito_gateway.update_modified_column();

CREATE TRIGGER update_users_modtime
    BEFORE UPDATE ON saito_gateway.users
    FOR EACH ROW
    EXECUTE FUNCTION saito_gateway.update_modified_column();

CREATE TRIGGER update_devices_modtime
    BEFORE UPDATE ON saito_gateway.devices
    FOR EACH ROW
    EXECUTE FUNCTION saito_gateway.update_modified_column();

-- =============================================
-- 设备状态和指标表
-- =============================================

-- 删除旧表
DROP TABLE IF EXISTS saito_gateway.device_status_history;
DROP TABLE IF EXISTS saito_gateway.device_statistics;

-- 创建新的设备状态变更历史表
CREATE TABLE saito_gateway.device_status_changes (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  device_id UUID NOT NULL REFERENCES saito_gateway.devices(id),
  from_status VARCHAR(20) NOT NULL,  -- 变更前状态
  to_status VARCHAR(20) NOT NULL,    -- 变更后状态
  change_time TIMESTAMPTZ NOT NULL,  -- 变更时间
  date DATE NOT NULL,                -- 用于快速查询某天的记录
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- 添加索引
CREATE INDEX idx_device_status_changes_device_date ON saito_gateway.device_status_changes(device_id, date);
CREATE INDEX idx_device_status_changes_time ON saito_gateway.device_status_changes(change_time);

-- 设备性能数据表
CREATE TABLE saito_gateway.device_metrics (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  device_id UUID NOT NULL REFERENCES saito_gateway.devices(id),
  timestamp TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  cpu_usage_percent DECIMAL(5,2),
  ram_usage_percent DECIMAL(5,2),
  gpu_usage_percent DECIMAL(5,2),
  gpu_temperature DECIMAL(5,2),
  network_in_kbps DECIMAL(12,2),
  network_out_kbps DECIMAL(12,2),
  date DATE NOT NULL  -- 便于按日期查询
);

CREATE INDEX idx_device_metrics_device_date ON saito_gateway.device_metrics(device_id, date);

-- =============================================
-- 任务和收益表
-- =============================================

-- 任务表
CREATE TABLE saito_gateway.tasks (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    device_id UUID NOT NULL REFERENCES saito_gateway.devices(id),
    model TEXT NOT NULL,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    status TEXT NOT NULL CHECK (status IN ('pending', 'running', 'completed', 'failed', 'cancelled')),
    total_duration DOUBLE PRECISION,
    load_duration DOUBLE PRECISION,
    prompt_eval_count INTEGER,
    prompt_eval_duration DOUBLE PRECISION,
    eval_count INTEGER,
    eval_duration DOUBLE PRECISION,
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

CREATE INDEX idx_tasks_device ON saito_gateway.tasks(device_id);
CREATE INDEX idx_tasks_status ON saito_gateway.tasks(status);

-- 收益表
CREATE TABLE saito_gateway.earnings (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    device_id UUID NOT NULL REFERENCES saito_gateway.devices(id),
    user_id UUID NOT NULL REFERENCES saito_gateway.users(id),
    task_id UUID REFERENCES saito_gateway.tasks(id),
    reward_address VARCHAR(42),
    block_rewards DOUBLE PRECISION NOT NULL DEFAULT 0,
    job_rewards DOUBLE PRECISION NOT NULL DEFAULT 0,
    total_rewards DOUBLE PRECISION GENERATED ALWAYS AS (block_rewards + job_rewards) STORED,
    date DATE NOT NULL, -- 收益日期
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

CREATE INDEX idx_earnings_device ON saito_gateway.earnings(device_id);
CREATE INDEX idx_earnings_user ON saito_gateway.earnings(user_id);
CREATE INDEX idx_earnings_date ON saito_gateway.earnings(date);

-- 添加触发器
CREATE TRIGGER update_tasks_modtime
    BEFORE UPDATE ON saito_gateway.tasks
    FOR EACH ROW
    EXECUTE FUNCTION saito_gateway.update_modified_column();

CREATE TRIGGER update_earnings_modtime
    BEFORE UPDATE ON saito_gateway.earnings
    FOR EACH ROW
    EXECUTE FUNCTION saito_gateway.update_modified_column();

-- =============================================
-- API分类和密钥表
-- =============================================

-- API分类表
CREATE TABLE saito_gateway.api_categories (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(50) NOT NULL UNIQUE,     -- API名称，如 'openai', 'deepseek'
    category VARCHAR(50) NOT NULL,        -- 分类，如 'LLM', 'Image', 'Audio'
    provider VARCHAR(50) NOT NULL,        -- 提供商
    icon_url TEXT,                        -- 图标URL
    description TEXT,                     -- 描述
    auth_type VARCHAR(20) NOT NULL,       -- 认证类型：'bearer', 'jwt'等
    auth_header VARCHAR(50) NOT NULL,     -- 认证头
    auth_prefix VARCHAR(20),              -- 认证前缀
    key_prefix VARCHAR(20) NOT NULL,      -- 密钥前缀
    key_pattern VARCHAR(100) NOT NULL,    -- 密钥格式正则
    is_active BOOLEAN DEFAULT TRUE,       -- 是否启用
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- API密钥表
CREATE TABLE saito_gateway.api_keys (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES saito_gateway.users(id),
    category_id UUID NOT NULL REFERENCES saito_gateway.api_categories(id),
    key_hash TEXT NOT NULL,               -- 哈希后的API密钥
    key_prefix VARCHAR(20) NOT NULL,      -- 密钥前缀
    key_mask VARCHAR(20) NOT NULL,        -- 密钥掩码
    name VARCHAR(255),                    -- 密钥名称
    description TEXT,                     -- 密钥描述
    status VARCHAR(20) NOT NULL DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'revoked')),
    expiration_date TIMESTAMP WITH TIME ZONE, -- 过期时间
    last_used TIMESTAMP WITH TIME ZONE,   -- 最后使用时间
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    deleted_at TIMESTAMPTZ               -- 软删除时间戳
);

-- API使用记录表
CREATE TABLE saito_gateway.api_usage (
    id UUID NOT NULL DEFAULT gen_random_uuid(),
    api_key_id UUID NOT NULL REFERENCES saito_gateway.api_keys(id),
    user_id UUID NOT NULL REFERENCES saito_gateway.users(id),
    category_id UUID NOT NULL REFERENCES saito_gateway.api_categories(id),
    task_id UUID REFERENCES saito_gateway.tasks(id),      -- 关联到具体任务
    endpoint VARCHAR(255) NOT NULL,
    method VARCHAR(10) NOT NULL,
    status_code INTEGER,
    response_time INTEGER,
    error_message TEXT,
    request_size INTEGER,
    response_size INTEGER,
    timestamp TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    date DATE NOT NULL,
    PRIMARY KEY(id, date)
) PARTITION BY RANGE (date);

-- 创建索引
CREATE INDEX idx_api_categories_name ON saito_gateway.api_categories(name);
CREATE INDEX idx_api_categories_category ON saito_gateway.api_categories(category);
CREATE INDEX idx_api_keys_user ON saito_gateway.api_keys(user_id);
CREATE INDEX idx_api_keys_category ON saito_gateway.api_keys(category_id);
CREATE INDEX idx_api_keys_deleted_at ON saito_gateway.api_keys(deleted_at); -- 添加软删除索引
CREATE INDEX idx_api_usage_key_date ON saito_gateway.api_usage(api_key_id, date);
CREATE INDEX idx_api_usage_task ON saito_gateway.api_usage(task_id);

-- 添加触发器
CREATE TRIGGER update_api_categories_modtime
    BEFORE UPDATE ON saito_gateway.api_categories
    FOR EACH ROW
    EXECUTE FUNCTION saito_gateway.update_modified_column();

CREATE TRIGGER update_api_keys_modtime
    BEFORE UPDATE ON saito_gateway.api_keys
    FOR EACH ROW
    EXECUTE FUNCTION saito_gateway.update_modified_column();

-- =============================================
-- Token使用和计费表
-- =============================================

-- Token使用表
CREATE TABLE saito_gateway.token_usage (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  api_key_id UUID NOT NULL REFERENCES saito_gateway.api_keys(id),
  user_id UUID NOT NULL REFERENCES saito_gateway.users(id),
  task_id UUID REFERENCES saito_gateway.tasks(id),
  model VARCHAR(255) NOT NULL,
  prompt_tokens INTEGER NOT NULL DEFAULT 0,
  completion_tokens INTEGER NOT NULL DEFAULT 0,
  total_tokens INTEGER NOT NULL DEFAULT 0,
  cost DECIMAL(10, 6) NOT NULL DEFAULT 0,
  timestamp TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
  date DATE NOT NULL DEFAULT CURRENT_DATE
);

-- 添加索引
CREATE INDEX idx_token_usage_api_key_id ON saito_gateway.token_usage(api_key_id);
CREATE INDEX idx_token_usage_user_id ON saito_gateway.token_usage(user_id);
CREATE INDEX idx_token_usage_date ON saito_gateway.token_usage(date);
CREATE INDEX idx_token_usage_model ON saito_gateway.token_usage(model);

-- 计费记录表
CREATE TABLE saito_gateway.billing_records (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES saito_gateway.users(id),
  api_key_id UUID REFERENCES saito_gateway.api_keys(id),
  amount DECIMAL(10, 2) NOT NULL,
  currency VARCHAR(10) NOT NULL DEFAULT 'USD',
  description TEXT,
  status VARCHAR(50) NOT NULL DEFAULT 'pending',
  billing_date DATE NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

-- 添加索引
CREATE INDEX idx_billing_records_user_id ON saito_gateway.billing_records(user_id);
CREATE INDEX idx_billing_records_api_key_id ON saito_gateway.billing_records(api_key_id);
CREATE INDEX idx_billing_records_billing_date ON saito_gateway.billing_records(billing_date);
CREATE INDEX idx_billing_records_status ON saito_gateway.billing_records(status);

-- 模型价格表
CREATE TABLE saito_gateway.model_pricing (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  model VARCHAR(255) NOT NULL UNIQUE,
  provider VARCHAR(255) NOT NULL,
  prompt_price_per_1k DECIMAL(10, 6) NOT NULL,
  completion_price_per_1k DECIMAL(10, 6) NOT NULL,
  currency VARCHAR(10) NOT NULL DEFAULT 'USD',
  is_active BOOLEAN NOT NULL DEFAULT TRUE,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

-- 添加索引
CREATE INDEX idx_model_pricing_model ON saito_gateway.model_pricing(model);
CREATE INDEX idx_model_pricing_provider ON saito_gateway.model_pricing(provider);

-- API密钥配额表
CREATE TABLE saito_gateway.api_key_quotas (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  api_key_id UUID NOT NULL REFERENCES saito_gateway.api_keys(id),
  user_id UUID NOT NULL REFERENCES saito_gateway.users(id),
  max_requests INTEGER,
  max_tokens INTEGER,
  max_cost DECIMAL(10, 2),
  reset_period VARCHAR(50) NOT NULL DEFAULT 'monthly',
  is_active BOOLEAN NOT NULL DEFAULT TRUE,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
  UNIQUE(api_key_id)
);

-- 添加索引
CREATE INDEX idx_api_key_quotas_api_key_id ON saito_gateway.api_key_quotas(api_key_id);
CREATE INDEX idx_api_key_quotas_user_id ON saito_gateway.api_key_quotas(user_id);

-- =============================================
-- 默认数据
-- =============================================

-- 插入默认API分类
INSERT INTO saito_gateway.api_categories
    (name, category, provider, auth_type, auth_header, auth_prefix, key_prefix, key_pattern, description)
VALUES
    (
        'openai',
        'LLM',
        'OpenAI',
        'bearer',
        'Authorization',
        'Bearer',
        'sk-',
        '^sk-[A-Za-z0-9]{32}$',
        'OpenAI兼容的API接口，支持GPT系列模型'
    ),
    (
        'deepseek',
        'LLM',
        'DeepSeek',
        'bearer',
        'Authorization',
        'Bearer',
        'st-',
        '^st-[A-Za-z0-9]{32}$',
        'DeepSeek兼容的API接口，支持多种AI模型'
    ),
    (
        'anthropic',
        'LLM',
        'Claude',
        'bearer',
        'x-api-key',
        NULL,
        'sk-',
        '^sk-[a-zA-Z0-9]{32,}$',
        'Anthropic API for Claude models'
    ),
    (
        'ollama',
        'LLM',
        'Ollama',
        'bearer',
        'Authorization',
        'Bearer',
        'ollama-',
        '^ollama-[a-zA-Z0-9]{32,}$',
        'Ollama API for local AI models'
    );

-- 添加默认模型价格
INSERT INTO saito_gateway.model_pricing (model, provider, prompt_price_per_1k, completion_price_per_1k)
VALUES
  ('gpt-4o', 'OpenAI', 0.005, 0.015),
  ('gpt-3.5-turbo', 'OpenAI', 0.0005, 0.0015),
  ('claude-3-opus', 'Claude', 0.015, 0.075),
  ('claude-3-sonnet', 'Claude', 0.003, 0.015),
  ('deepseek-chat', 'DeepSeek', 0.001, 0.003),
  ('deepseek-coder', 'DeepSeek', 0.0015, 0.0045),
  ('llama2-7b', 'Ollama', 0, 0),
  ('mistral-7b', 'Ollama', 0, 0),
  ('stable-diffusion-3', 'Ollama', 0, 0);

-- =============================================
-- 测试数据生成（可选）
-- =============================================

-- 插入测试数据的存储过程
-- DO $$
-- DECLARE
--   api_key_id UUID;
--   user_id UUID;
--   models TEXT[] := ARRAY['gpt-4', 'gpt-3.5-turbo', 'claude-3-opus', 'claude-3-sonnet'];
--   model TEXT;
--   current_date DATE := CURRENT_DATE;
--   i INT;
--   j INT;
-- BEGIN
--   -- 获取第一个API密钥ID和用户ID（如果存在）
--   SELECT id, user_id INTO api_key_id, user_id FROM saito_gateway.api_keys LIMIT 1;

--   -- 如果找到API密钥，插入测试数据
--   IF api_key_id IS NOT NULL THEN
--     -- 插入API使用记录
--     FOR i IN 1..30 LOOP
--       FOR j IN 1..5 LOOP
--         INSERT INTO saito_gateway.api_usage (
--           api_key_id,
--           user_id,
--           category_id,
--           date,
--           endpoint,
--           method,
--           status_code,
--           response_time
--         ) VALUES (
--           api_key_id,
--           user_id,
--           (SELECT id FROM saito_gateway.api_categories LIMIT 1),
--           current_date - (i || ' days')::INTERVAL,
--           '/api/completions',
--           'POST',
--           200,
--           FLOOR(RANDOM() * 1000 + 100)::INT
--         );
--       END LOOP;
--     END LOOP;

--     -- 插入Token使用记录
--     FOR i IN 1..30 LOOP
--       FOR j IN 1..4 LOOP
--         model := models[j];
--         INSERT INTO saito_gateway.token_usage (
--           api_key_id,
--           user_id,
--           date,
--           model,
--           prompt_tokens,
--           completion_tokens,
--           total_tokens,
--           cost
--         ) VALUES (
--           api_key_id,
--           user_id,
--           current_date - (i || ' days')::INTERVAL,
--           model,
--           FLOOR(RANDOM() * 500 + 100)::INT,
--           FLOOR(RANDOM() * 1000 + 200)::INT,
--           FLOOR(RANDOM() * 1500 + 300)::INT,
--           (RANDOM() * 0.5 + 0.1)::DECIMAL(10, 6)
--         );
--       END LOOP;
--     END LOOP;
--   END IF;
-- END $$;
