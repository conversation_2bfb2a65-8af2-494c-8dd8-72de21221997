#!/usr/bin/env bash
set -euo pipefail

# Apply Hasura migrations using REST API
HASURA_ENDPOINT="http://localhost:28717"
ADMIN_SECRET="9AgJckEMHPRgrasj7Ey8jR"

echo "🚀 Starting Hasura migration process..."
echo "📍 Hasura endpoint: $HASURA_ENDPOINT"

# Function to apply a single migration
apply_migration() {
    local migration_version=$1
    local migration_file=$2
    
    echo "📝 Applying migration: $migration_version"
    
    # Read SQL content
    local sql_content=$(cat "$migration_file")
    
    # Apply migration using Hasura API
    local response=$(curl -s -X POST "$HASURA_ENDPOINT/v2/query" \
        -H "X-Hasura-Admin-Secret: $ADMIN_SECRET" \
        -H "Content-Type: application/json" \
        -d "{
            \"type\": \"run_sql\",
            \"args\": {
                \"source\": \"main_db\",
                \"sql\": $(echo "$sql_content" | jq -R -s .)
            }
        }")
    
    # Check for errors
    if echo "$response" | jq -e '.error' > /dev/null; then
        echo "❌ Error applying migration $migration_version:"
        echo "$response" | jq '.error'
        return 1
    else
        echo "✅ Migration $migration_version applied successfully"
        return 0
    fi
}

# Apply all migrations in order
echo "🔍 Finding migration files..."
migration_dirs=($(find migrations/main_db -name "*_up" -type d | sort))

for migration_dir in "${migration_dirs[@]}"; do
    migration_version=$(basename "$migration_dir" | sed 's/_up$//')
    up_file="$migration_dir/up.sql"
    
    if [ -f "$up_file" ]; then
        apply_migration "$migration_version" "$up_file"
    else
        echo "⚠️  Warning: No up.sql found in $migration_dir"
    fi
done

echo "🎉 Migration process completed!"

# Apply metadata
echo "📋 Applying metadata..."
metadata_response=$(curl -s -X POST "$HASURA_ENDPOINT/v1/metadata" \
    -H "X-Hasura-Admin-Secret: $ADMIN_SECRET" \
    -H "Content-Type: application/json" \
    -d "{
        \"type\": \"replace_metadata\",
        \"args\": $(cat metadata/databases/databases.yaml | yq eval -o=json)
    }")

if echo "$metadata_response" | jq -e '.error' > /dev/null; then
    echo "❌ Error applying metadata:"
    echo "$metadata_response" | jq '.error'
else
    echo "✅ Metadata applied successfully"
fi

echo "🏁 Hasura setup completed!"
