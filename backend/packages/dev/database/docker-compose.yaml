version: '3.8'
services:
  postgres:
    restart: always
    image: postgres:15.5-alpine
    shm_size: 1gb
    command: postgres -c 'max_connections=1000'
    environment:
      POSTGRES_PASSWORD: postgres
      POSTGRES_USER: postgres
      POSTGRES_DB: saito_db
    ports:
      - '7543:5432'
    volumes:
      - ${SAITO_POSTGRES_DATA:-pgdata}:/var/lib/postgresql/data
    networks:
      - default
  hasura:
    restart: always
    image: hasura/graphql-engine:v${HASURA_VERSION}
    environment:
      HASURA_GRAPHQL_METADATA_DATABASE_URL: '********************************************/saito_db'
      HASURA_GRAPHQL_DATABASE_URL: ********************************************/saito_db
      HASURA_GRAPHQL_ADMIN_SECRET: ${HASURA_GRAPHQL_ADMIN_SECRET}
      HASURA_GRAPHQL_SERVER_PORT: 8080
      HASURA_GRAPHQL_ENABLE_CONSOLE: 'true'
      HASURA_GRAPHQL_LOG_LEVEL: 'info'
      HASURA_GRAPHQL_UNAUTHORIZED_ROLE: 'public'
      HASURA_GRAPHQL_ENABLED_LOG_TYPES: 'startup, http-log, webhook-log, websocket-log, query-log'
      HASURA_GRAPHQL_DEV_MODE: 'true'
    ports:
      - '${HASURA_GRAPHQL_SERVER_PORT}:8080'
    networks:
      - default
  redis:
    image: redis:latest
    container_name: redis
    restart: always
    privileged: true
    ports:
      - "6379:6379"
    volumes:
      - /app/redis/data:/data
    command: redis-server
volumes:
  pgdata: {}
networks:
  default:
    name: ${DEV_NETWORK_NAME}
    external: true
