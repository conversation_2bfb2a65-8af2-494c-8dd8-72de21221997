# 优化后的Provider-Key系统设计

## 1. 核心概念重新定义

### 1.1 术语统一
- **Provider Key**: 替代原来的"三方API Key"，避免与Sight自己的API Key混淆
- **Provider**: OpenAI、<PERSON>、Gemini等AI服务提供商
- **Key Type**: `provider_key` (区别于Sight平台的 `platform_key`)
- **Status**: `active`, `out-of-balance`, `revoked`, `pending` (待验证)

### 1.2 Gateway验证逻辑

#### 验证流程
```typescript
// EncryptedApiKeyService.validateProviderKey()
async validateProviderKey(keyData: ProviderKeyData): Promise<ValidationResult> {
  // 1. 验证加密数据完整性
  if (!this.validateEncryptionData(keyData)) {
    throw new Error('Invalid encryption data');
  }
  
  // 2. 验证executor可用性
  const availableExecutors = await this.getAvailableExecutors(keyData.provider, keyData.region);
  if (availableExecutors.length === 0) {
    throw new Error('No available executors for this provider/region');
  }
  
  // 3. 触发Key验证测试
  const testResult = await this.testProviderKey(keyData);
  return testResult;
}
```

#### Key验证测试机制
```typescript
// 新增：Provider Key测试API
async testProviderKey(keyData: ProviderKeyData): Promise<TestResult> {
  // 选择一个可用的executor
  const executor = await this.selectExecutorForTest(keyData.provider, keyData.region);
  
  // 调用executor的测试API
  const response = await this.httpClient.post(`${executor.url}/test-provider-key`, {
    keyId: keyData.uuid,
    provider: keyData.provider,
    encryptedKeyData: keyData.encryptedKeyData
  });
  
  return response.data;
}
```

#### Executor测试API
```typescript
// sight-executor新增API
@Post('/test-provider-key')
async testProviderKey(@Body() request: TestKeyRequest): Promise<TestResult> {
  try {
    // 1. 解密provider key
    const decryptedKey = await this.encryptionService.decryptApiKey(request.encryptedKeyData);
    
    // 2. 调用最便宜的模型进行测试
    const testModel = this.getTestModel(request.provider); // 如: gpt-3.5-turbo, claude-3-haiku
    const response = await this.callAIModel(testModel, 'hello', decryptedKey);
    
    return {
      success: true,
      responseTime: response.responseTime,
      model: testModel
    };
  } catch (error) {
    return {
      success: false,
      error: error.message,
      errorCode: this.classifyError(error)
    };
  }
}
```

## 2. 数据库结构优化

### 2.1 重命名表结构
```sql
-- 重命名表（或创建新表）
CREATE TABLE saito_gateway.provider_keys (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL,
  name VARCHAR(255) NOT NULL,
  description TEXT,
  provider VARCHAR(100) NOT NULL, -- 'openai', 'claude', 'gemini'
  region VARCHAR(100) NOT NULL,   -- 'asia', 'us', 'eu'
  
  -- 显示相关
  display_text VARCHAR(50) NOT NULL, -- 'sk-****-****abcd' 用于前端显示
  
  -- 加密数据（JSON格式）
  encrypted_key_data JSONB NOT NULL, -- 包含 encryptedKey, nonce, tag, ephemeralPubKey
  
  -- 状态和元数据
  status VARCHAR(50) DEFAULT 'pending', -- 'pending', 'active', 'out-of-balance', 'revoked'
  key_type VARCHAR(50) DEFAULT 'provider_key',
  version INTEGER DEFAULT 1, -- 版本号，从1开始
  
  -- 统计信息
  total_requests BIGINT DEFAULT 0,
  total_tokens BIGINT DEFAULT 0,
  total_cost DECIMAL(10,4) DEFAULT 0,
  
  -- 时间戳
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW(),
  deleted_at TIMESTAMP NULL,
  last_used_at TIMESTAMP NULL,
  expiration_date TIMESTAMP NULL,
  
  -- 索引
  CONSTRAINT fk_provider_keys_user FOREIGN KEY (user_id) REFERENCES saito_gateway.users(id)
);

-- 创建索引
CREATE INDEX idx_provider_keys_user_id ON saito_gateway.provider_keys(user_id);
CREATE INDEX idx_provider_keys_provider ON saito_gateway.provider_keys(provider);
CREATE INDEX idx_provider_keys_region ON saito_gateway.provider_keys(region);
CREATE INDEX idx_provider_keys_status ON saito_gateway.provider_keys(status);
CREATE INDEX idx_provider_keys_provider_region ON saito_gateway.provider_keys(provider, region);
```

### 2.2 字段说明
- **provider**: AI服务提供商标识
- **key_type**: 固定为 `provider_key`，区别于平台自己的API key
- **display_text**: 前端显示用的脱敏文本，如 `sk-****-****abcd`
- **encrypted_key_data**: JSON格式，包含所有加密组件
- **version**: 版本号，用于乐观锁和变更追踪

## 3. etcd存储结构优化

### 3.1 Provider Keys存储
```
路径: /provider-keys/{provider}/{region}/{uuid}
数据: {
  "uuid": "唯一标识",
  "encryptedKey": "加密后的API key",
  "nonce": "随机数", 
  "tag": "认证标签",
  "ephemeralPubKey": "临时公钥",
  "status": "active",
  "version": 1,
  "createdAt": "创建时间",
  "updatedAt": "更新时间" // 仅在状态变更时更新，避免频繁通知
}
```

### 3.2 Executor注册（全局密钥对）
```
路径: /executor/{provider}/{region}/{executorId}
数据: {
  "executorId": "executor-openai-asia-001",
  "provider": "openai",
  "region": "asia", 
  "url": "http://executor-service:3001",
  "status": "active",
  "registeredAt": "注册时间",
  "lastHeartbeat": "最后心跳时间"
}
TTL: 60秒（心跳间隔30秒）
```

### 3.3 Rate Limit状态管理
```
路径: /provider-key-rate-limit/{provider}/{region}/{provider-key-id}
数据: {
  "keyId": "provider-key-uuid",
  "reason": "rate_limit_exceeded",
  "occurredAt": "发生时间",
  "estimatedRecoveryAt": "预计恢复时间"
}
TTL: 300秒（5分钟自动恢复）
```

### 3.4 Out-of-Balance状态管理
```
路径: /provider-key-out-of-balance/{provider}/{region}/{provider-key-id}
数据: {
  "keyId": "provider-key-uuid", 
  "reason": "insufficient_quota",
  "occurredAt": "发生时间",
  "requiresManualReview": true
}
无TTL（需要手动恢复）
```

## 4. 同步机制优化

### 4.1 孤立Provider Key定义
孤立的provider key指：
1. **etcd中存在但数据库中已删除**的key
2. **数据库中为active但etcd中不存在**的key  
3. **状态不一致**的key（数据库active但etcd为revoked）

### 4.2 同步逻辑
```typescript
async syncProviderKeysToEtcd(): Promise<SyncResult> {
  // 1. 获取数据库中所有active状态的provider key
  const dbKeys = await this.getActiveProviderKeysFromDB();
  
  // 2. 获取etcd中所有provider key
  const etcdKeys = await this.getAllProviderKeysFromEtcd();
  
  // 3. 执行同步操作
  const result = {
    added: 0,
    updated: 0, 
    removed: 0,
    errors: []
  };
  
  // 添加新key到etcd
  for (const dbKey of dbKeys) {
    const etcdPath = `/provider-keys/${dbKey.provider}/${dbKey.region}/${dbKey.uuid}`;
    if (!etcdKeys.has(etcdPath)) {
      await this.addKeyToEtcd(dbKey);
      result.added++;
    }
  }
  
  // 清理孤立的etcd key
  for (const [etcdPath, etcdKey] of etcdKeys) {
    const dbKey = dbKeys.find(k => k.uuid === etcdKey.uuid);
    if (!dbKey || dbKey.status !== 'active') {
      await this.etcdService.delete(etcdPath);
      result.removed++;
    }
  }
  
  return result;
}
```

## 5. Executor独立化设计

### 5.1 去除Gateway依赖
```typescript
// Executor直接从etcd读取provider keys
class ProviderKeyService {
  async getAvailableKeys(provider: string, region: string): Promise<ProviderKey[]> {
    // 1. 直接从etcd读取
    const keyPrefix = `/provider-keys/${provider}/${region}/`;
    const keys = await this.etcdService.getAllWithPrefix(keyPrefix);
    
    // 2. 过滤可用的key（排除rate-limited和out-of-balance）
    const availableKeys = [];
    for (const [path, keyData] of Object.entries(keys)) {
      const key = JSON.parse(keyData);
      
      // 检查是否在rate-limit状态
      const rateLimitPath = `/provider-key-rate-limit/${provider}/${region}/${key.uuid}`;
      const isRateLimited = await this.etcdService.exists(rateLimitPath);
      
      // 检查是否out-of-balance
      const outOfBalancePath = `/provider-key-out-of-balance/${provider}/${region}/${key.uuid}`;
      const isOutOfBalance = await this.etcdService.exists(outOfBalancePath);
      
      if (!isRateLimited && !isOutOfBalance && key.status === 'active') {
        availableKeys.push(key);
      }
    }
    
    return availableKeys;
  }
}
```

### 5.2 Watch机制
```typescript
// Executor订阅状态变化
class ProviderKeyWatcher {
  async startWatching(provider: string, region: string): Promise<void> {
    // 1. 订阅rate-limit状态变化
    const rateLimitPrefix = `/provider-key-rate-limit/${provider}/${region}/`;
    this.etcdService.watch(rateLimitPrefix, (event) => {
      if (event.type === 'PUT') {
        this.markKeyAsRateLimited(event.key, event.value);
      } else if (event.type === 'DELETE') {
        this.markKeyAsAvailable(event.key);
      }
    });
    
    // 2. 订阅out-of-balance状态变化
    const outOfBalancePrefix = `/provider-key-out-of-balance/${provider}/${region}/`;
    this.etcdService.watch(outOfBalancePrefix, (event) => {
      if (event.type === 'PUT') {
        this.markKeyAsOutOfBalance(event.key, event.value);
      } else if (event.type === 'DELETE') {
        this.markKeyAsAvailable(event.key);
      }
    });
  }
}
```

## 6. 错误处理和重试机制

### 6.1 Rate Limit处理
```typescript
class RateLimitHandler {
  async handleRateLimit(keyId: string, provider: string, region: string, error: any): Promise<void> {
    // 1. 写入rate-limit记录到etcd
    const rateLimitPath = `/provider-key-rate-limit/${provider}/${region}/${keyId}`;
    const rateLimitData = {
      keyId,
      reason: 'rate_limit_exceeded',
      occurredAt: new Date().toISOString(),
      estimatedRecoveryAt: new Date(Date.now() + 5 * 60 * 1000).toISOString(), // 5分钟后
      errorDetails: {
        statusCode: error.response?.status,
        message: error.message,
        retryAfter: error.response?.headers['retry-after']
      }
    };

    // 设置TTL为5分钟
    await this.etcdService.putWithTTL(rateLimitPath, JSON.stringify(rateLimitData), 300);

    // 2. 记录到监控系统
    this.metricsService.recordRateLimit(keyId, provider, region);

    // 3. 通知其他executor（通过etcd watch机制自动完成）
    this.logger.warn(`Provider key ${keyId} rate limited, will recover in 5 minutes`);
  }
}
```

### 6.2 Out-of-Balance处理
```typescript
class OutOfBalanceHandler {
  async handleOutOfBalance(keyId: string, provider: string, region: string, error: any): Promise<void> {
    // 1. 写入out-of-balance记录到etcd（无TTL，需要手动恢复）
    const outOfBalancePath = `/provider-key-out-of-balance/${provider}/${region}/${keyId}`;
    const outOfBalanceData = {
      keyId,
      reason: 'insufficient_quota',
      occurredAt: new Date().toISOString(),
      requiresManualReview: true,
      errorDetails: {
        statusCode: error.response?.status,
        message: error.message
      }
    };

    await this.etcdService.put(outOfBalancePath, JSON.stringify(outOfBalanceData));

    // 2. 更新数据库状态
    await this.providerKeyRepository.updateStatus(keyId, 'out-of-balance');

    // 3. 发送告警通知
    await this.alertService.sendAlert({
      type: 'PROVIDER_KEY_OUT_OF_BALANCE',
      keyId,
      provider,
      region,
      message: `Provider key ${keyId} is out of balance and requires manual review`
    });

    this.logger.error(`Provider key ${keyId} is out of balance, manual intervention required`);
  }
}
```

### 6.3 错误重试策略
```typescript
class ProviderKeyRetryStrategy {
  async executeWithRetry<T>(
    operation: () => Promise<T>,
    keyId: string,
    provider: string,
    region: string
  ): Promise<T> {
    let lastError: Error;
    const maxRetries = 3;

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        return await operation();
      } catch (error) {
        lastError = error as Error;

        // 分类错误并处理
        const errorType = this.classifyError(error);

        switch (errorType) {
          case 'RATE_LIMIT':
            await this.rateLimitHandler.handleRateLimit(keyId, provider, region, error);
            // 立即切换到其他key，不重试当前key
            throw error;

          case 'OUT_OF_BALANCE':
            await this.outOfBalanceHandler.handleOutOfBalance(keyId, provider, region, error);
            // 立即切换到其他key，不重试当前key
            throw error;

          case 'TEMPORARY_ERROR':
            // 临时错误，可以重试
            if (attempt < maxRetries) {
              const delay = Math.min(1000 * Math.pow(2, attempt - 1), 5000); // 指数退避，最大5秒
              await new Promise(resolve => setTimeout(resolve, delay));
              continue;
            }
            break;

          case 'PERMANENT_ERROR':
            // 永久错误，不重试
            throw error;
        }
      }
    }

    throw lastError!;
  }

  private classifyError(error: any): string {
    const status = error.response?.status;
    const message = error.message?.toLowerCase() || '';

    if (status === 429 || message.includes('rate limit')) {
      return 'RATE_LIMIT';
    }

    if (status === 402 || message.includes('quota') || message.includes('balance')) {
      return 'OUT_OF_BALANCE';
    }

    if (status >= 500 || message.includes('timeout') || message.includes('network')) {
      return 'TEMPORARY_ERROR';
    }

    if (status === 401 || status === 403) {
      return 'PERMANENT_ERROR';
    }

    return 'TEMPORARY_ERROR'; // 默认为临时错误
  }
}
```

## 7. Gateway Executor监控

### 7.1 Executor状态监控
```typescript
class ExecutorMonitorService {
  private activeExecutors = new Map<string, ExecutorInfo>();

  async startMonitoring(): Promise<void> {
    // 1. 启动时同步现有executor
    await this.syncExistingExecutors();

    // 2. 订阅executor变化
    this.etcdService.watch('/executor/', (event) => {
      if (event.type === 'PUT') {
        this.handleExecutorRegistration(event.key, event.value);
      } else if (event.type === 'DELETE') {
        this.handleExecutorDeregistration(event.key);
      }
    });
  }

  private async syncExistingExecutors(): Promise<void> {
    const executors = await this.etcdService.getAllWithPrefix('/executor/');

    for (const [path, data] of Object.entries(executors)) {
      const executorInfo = JSON.parse(data);
      this.activeExecutors.set(path, executorInfo);
    }

    this.logger.log(`Synced ${this.activeExecutors.size} active executors`);
  }

  private handleExecutorRegistration(path: string, data: string): void {
    const executorInfo = JSON.parse(data);
    this.activeExecutors.set(path, executorInfo);

    this.logger.log(`Executor registered: ${executorInfo.executorId} (${executorInfo.provider}:${executorInfo.region})`);
  }

  private handleExecutorDeregistration(path: string): void {
    const executorInfo = this.activeExecutors.get(path);
    this.activeExecutors.delete(path);

    if (executorInfo) {
      this.logger.warn(`Executor deregistered: ${executorInfo.executorId} (${executorInfo.provider}:${executorInfo.region})`);
    }
  }

  getAvailableExecutors(provider: string, region: string): ExecutorInfo[] {
    return Array.from(this.activeExecutors.values())
      .filter(executor => executor.provider === provider && executor.region === region);
  }
}
```

## 8. 性能优化考虑

### 8.1 updatedAt字段优化
为了避免频繁的updatedAt更新导致不必要的executor通知：

```typescript
// 只在关键状态变更时更新updatedAt
const SIGNIFICANT_UPDATES = ['status', 'version', 'encrypted_key_data'];

async updateProviderKey(keyId: string, updates: Partial<ProviderKey>): Promise<void> {
  const shouldUpdateTimestamp = Object.keys(updates).some(key =>
    SIGNIFICANT_UPDATES.includes(key)
  );

  if (shouldUpdateTimestamp) {
    updates.updatedAt = new Date().toISOString();
    updates.version = (updates.version || 1) + 1; // 版本号递增
  }

  // 更新数据库
  await this.providerKeyRepository.update(keyId, updates);

  // 只有重要更新才同步到etcd
  if (shouldUpdateTimestamp) {
    await this.syncKeyToEtcd(keyId, updates);
  }
}
```

### 8.2 etcd Watch优化
```typescript
// 使用版本号进行乐观更新
class OptimizedEtcdSync {
  async updateKeyInEtcd(keyData: ProviderKey): Promise<void> {
    const keyPath = `/provider-keys/${keyData.provider}/${keyData.region}/${keyData.uuid}`;

    // 获取当前版本
    const currentData = await this.etcdService.get(keyPath);
    const currentVersion = currentData ? JSON.parse(currentData).version : 0;

    // 只有版本号更新时才写入etcd
    if (keyData.version > currentVersion) {
      await this.etcdService.put(keyPath, JSON.stringify(keyData));
      this.logger.debug(`Updated key ${keyData.uuid} to version ${keyData.version}`);
    }
  }
}
```

这个优化后的设计解决了您提出的所有问题：

1. **术语统一**：使用provider_key避免混淆
2. **验证机制**：Gateway主动测试key有效性
3. **数据库优化**：添加display_text、version等字段
4. **etcd结构**：分离不同类型的状态管理
5. **Executor独立**：无需依赖Gateway，直接从etcd读取
6. **错误处理**：详细的重试和状态管理机制
7. **性能优化**：避免不必要的更新通知
```
