# 三方共享Key工作流程文档

## 系统架构概述

整个三方共享Key系统由三个核心组件组成：
1. **Gateway** - 管理界面和API网关，负责key的增删改查和分配
2. **etcd** - 分布式存储，作为key的中央存储和同步中心
3. **Executor** - 执行器，负责key的解密和实际使用

## 核心工作流程

### 1. Key添加和加密存储流程

#### 1.1 前端用户操作
- 用户在 `gateway-frontend/src/app/provider-keys` 页面添加新的API key
- 前端调用 `/public-keys` API获取可用的executor公钥
- 使用X25519 + ChaCha20-Poly1305算法在前端加密API key
- 加密结果包含：`encryptedKey`, `nonce`, `tag`, `ephemeralPubKey`

#### 1.2 Gateway处理
- `third-party.controller.ts` 接收加密后的key数据
- `EncryptedApiKeyService` 验证executor可用性和加密数据完整性
- 数据存储到PostgreSQL数据库 (`saito_gateway.api_keys` 表)
- 同时存储到etcd (`/api-keys/{provider}/{region}/{uuid}`)

#### 1.3 数据库存储结构
```sql
INSERT INTO saito_gateway.api_keys (
  user_id, name, provider, region, key_hash,
  encrypted_key_data, status, key_type
)
```

#### 1.4 etcd存储结构
```
路径: /api-keys/{provider}/{region}/{uuid}
数据: {
  "uuid": "唯一标识",
  "encryptedKey": "加密后的API key",
  "nonce": "随机数",
  "tag": "认证标签", 
  "ephemeralPubKey": "临时公钥",
  "keyId": "executor ID",
  "status": "active",
  "createdAt": "创建时间"
}
```

### 2. etcd同步机制

#### 2.1 启动时同步
- `EtcdSyncService` 在应用启动时自动执行
- 从数据库加载所有active状态的key
- 与etcd中现有数据进行动态比较
- 执行增量同步：添加新key、更新变更key、清理孤立key

#### 2.2 同步策略
- **动态比较**: 比较数据库和etcd的差异
- **最小化变更**: 只更新必要的数据
- **容错处理**: 同步失败不影响应用启动

### 3. Executor注册和心跳

#### 3.1 Executor启动注册
- Executor启动时生成X25519密钥对
- 将公钥注册到etcd: `/keys/public/{provider}/{region}/{executorId}`
- 包含executor URL、状态、注册时间等信息

#### 3.2 心跳机制
- 每30秒发送一次心跳到etcd
- 更新 `lastHeartbeat` 时间戳
- Gateway通过心跳判断executor健康状态

### 4. Key分配和获取流程

#### 4.1 Executor请求Key
- Executor调用Gateway `/executor/key?scope=provider:region`
- `ExecutorController` 验证executor身份和权限
- `ExecutorKeyService` 从etcd获取匹配的加密key列表

#### 4.2 分配策略
- **优先级顺序**: Gateway分配 > etcd直接获取 > 遗留三方key
- **负载均衡**: 使用LRU (Least Recently Used) 算法选择key
- **故障转移**: Gateway失败时直接从etcd获取

#### 4.3 Key锁定机制
```typescript
// Key状态管理
{
  status: 'active' | 'in_use' | 'rate_limited' | 'exhausted' | 'revoked',
  lockedAt: timestamp,
  lockTimeout: 5分钟超时,
  lastUsedAt: timestamp
}
```

### 5. 解密和缓存机制

#### 5.1 解密流程
- Executor接收加密的key数据
- 使用自己的私钥和ephemeralPubKey计算共享密钥
- 使用ChaCha20-Poly1305解密获得原始API key

#### 5.2 内存缓存
- 解密后的key存储在Executor内存中
- 实现ManagedApiKey接口，包含状态、使用次数、错误计数等
- 定期清理过期和无效的key

### 6. 异常状态处理

#### 6.1 Key状态监控
- `KeyStatusService` 监控key的使用状态
- 支持状态更新：active, rate_limited, exhausted, revoked
- 异常key自动从可用池中移除

#### 6.2 故障恢复
- **超时处理**: 5分钟锁定超时自动释放
- **错误重试**: 失败key标记后重新尝试其他key
- **健康检查**: 定期验证key有效性

### 7. GCP集成

#### 7.1 Secret Manager
- 生产环境中Executor私钥存储在GCP Secret Manager
- 支持本地环境变量fallback
- 自动密钥轮换和版本管理

#### 7.2 配置管理
```typescript
// GCP配置
GCP_KMS_PROJECT_ID: 项目ID
EXECUTOR_PRIVATE_KEY: 本地私钥(开发环境)
```

### 8. 系统协调机制

#### 8.1 三系统协调
- **Gateway**: 提供管理界面和API，处理用户请求
- **etcd**: 作为中央存储，确保数据一致性
- **Executor**: 执行实际的AI推理任务

#### 8.2 数据流向
```
用户 → Gateway前端 → Gateway后端 → 数据库 + etcd → Executor → AI模型
```

#### 8.3 容错设计
- 多级fallback机制
- 分布式锁防止并发冲突  
- 心跳监控确保服务可用性
- 自动故障转移和恢复

## 关键技术特性

1. **端到端加密**: 前端加密，executor解密，中间环节无法获取明文
2. **分布式协调**: 使用etcd实现分布式锁和状态同步
3. **负载均衡**: 智能key分配算法，避免单点过载
4. **故障容错**: 多层fallback和自动恢复机制
5. **实时监控**: 心跳、状态监控和异常处理
6. **安全隔离**: 不同provider和region的key完全隔离

这个架构确保了三方API key的安全共享、高可用性和良好的性能表现。

## 详细技术实现

### API接口清单

#### Gateway API接口
- `GET /public-keys` - 获取executor公钥列表
- `POST /third-party/encrypted` - 创建加密API key
- `PUT /third-party/encrypted/{provider}/{region}/{uuid}` - 更新加密key
- `DELETE /third-party/{keyId}` - 删除三方key
- `GET /executor/key` - executor请求key分配
- `GET /key-status/{provider}/{keyId}` - 获取key状态
- `PUT /key-status/{provider}/{keyId}` - 更新key状态

#### etcd存储路径
- `/api-keys/{provider}/{region}/{uuid}` - 加密的API key数据
- `/keys/public/{provider}/{region}/{executorId}` - executor公钥注册
- `/third-party-keys/{provider}/{userId}/{keyId}` - 遗留三方key
- `/keys/{provider}/{keyId}` - key状态信息

### 安全机制

#### 加密算法
- **密钥交换**: X25519椭圆曲线Diffie-Hellman
- **对称加密**: ChaCha20-Poly1305 AEAD
- **随机数生成**: 加密安全的随机数生成器
- **密钥派生**: 直接使用ECDH共享密钥

#### 访问控制
- JWT认证用于Gateway API访问
- Executor身份验证通过专用Guard
- 基于provider和region的权限隔离
- 用户级别的key所有权验证

### 性能优化

#### 缓存策略
- Executor内存缓存解密后的key
- LRU算法管理key使用顺序
- 定期清理过期和无效key
- 预分配key池减少延迟

#### 负载均衡
- 多executor实例自动发现
- 基于心跳的健康检查
- 智能key分配避免热点
- 故障自动转移机制

### 监控和运维

#### 日志记录
- 结构化日志记录所有关键操作
- 敏感信息脱敏处理
- 分级日志便于问题排查
- 集中化日志收集和分析

#### 指标监控
- Key使用率和成功率统计
- Executor健康状态监控
- etcd同步状态跟踪
- API响应时间和错误率

#### 故障处理
- 自动重试机制
- 降级和熔断保护
- 告警和通知系统
- 快速故障恢复流程
