# 三方共享Key工作流程文档

## 系统架构概述

整个三方共享Key系统由三个核心组件组成：
1. **Gateway** - 管理界面和API网关，负责key的增删改查和分配
2. **etcd** - 分布式存储，作为key的中央存储和同步中心
3. **Executor** - 执行器，负责key的解密和实际使用

## 核心工作流程

### 1. Key添加和加密存储流程

#### 1.1 前端用户操作
- 用户在 `gateway-frontend/src/app/provider-keys` 页面添加新的API key
- 前端调用 `/public-keys` API获取可用的executor公钥
- 使用X25519 + ChaCha20-Poly1305算法在前端加密API key
- 加密结果包含：`encryptedKey`, `nonce`, `tag`, `ephemeralPubKey`

#### 1.2 Gateway处理
- `third-party.controller.ts` 接收加密后的key数据
- `EncryptedApiKeyService` 验证executor可用性和加密数据完整性
- 数据存储到PostgreSQL数据库 (`saito_gateway.api_keys` 表)
- 同时存储到etcd (`/api-keys/{provider}/{region}/{uuid}`)

#### 1.3 数据库存储结构
```sql
INSERT INTO saito_gateway.api_keys (
  user_id, name, provider, region, key_hash,
  encrypted_key_data, status, key_type
)
```

#### 1.4 etcd存储结构
```
路径: /api-keys/{provider}/{region}/{uuid}
数据: {
  "uuid": "唯一标识",
  "encryptedKey": "加密后的API key",
  "nonce": "随机数",
  "tag": "认证标签", 
  "ephemeralPubKey": "临时公钥",
  "keyId": "executor ID",
  "status": "active",
  "createdAt": "创建时间"
}
```

### 2. etcd同步机制

#### 2.1 启动时同步
- `EtcdSyncService` 在应用启动时自动执行
- 从数据库加载所有active状态的key
- 与etcd中现有数据进行动态比较
- 执行增量同步：添加新key、更新变更key、清理孤立key

#### 2.2 同步策略
- **动态比较**: 比较数据库和etcd的差异
- **最小化变更**: 只更新必要的数据
- **容错处理**: 同步失败不影响应用启动

### 3. Executor注册和心跳

#### 3.1 Executor启动注册
- Executor启动时生成X25519密钥对
- 将公钥注册到etcd: `/keys/public/{provider}/{region}/{executorId}`
- 包含executor URL、状态、注册时间等信息

#### 3.2 心跳机制
- 每30秒发送一次心跳到etcd
- 更新 `lastHeartbeat` 时间戳
- Gateway通过心跳判断executor健康状态

### 4. Key分配和获取流程

#### 4.1 Executor请求Key
- Executor调用Gateway `/executor/key?scope=provider:region`
- `ExecutorController` 验证executor身份和权限
- `ExecutorKeyService` 从etcd获取匹配的加密key列表

#### 4.2 分配策略
- **优先级顺序**: Gateway分配 > etcd直接获取 > 遗留三方key
- **负载均衡**: 使用LRU (Least Recently Used) 算法选择key
- **故障转移**: Gateway失败时直接从etcd获取

#### 4.3 Key锁定机制
```typescript
// Key状态管理
{
  status: 'active' | 'in_use' | 'rate_limited' | 'exhausted' | 'revoked',
  lockedAt: timestamp,
  lockTimeout: 5分钟超时,
  lastUsedAt: timestamp
}
```

### 5. 解密和缓存机制

#### 5.1 解密流程
- Executor接收加密的key数据
- 使用自己的私钥和ephemeralPubKey计算共享密钥
- 使用ChaCha20-Poly1305解密获得原始API key

#### 5.2 内存缓存
- 解密后的key存储在Executor内存中
- 实现ManagedApiKey接口，包含状态、使用次数、错误计数等
- 定期清理过期和无效的key

### 6. 异常状态处理

#### 6.1 Key状态监控
- `KeyStatusService` 监控key的使用状态
- 支持状态更新：active, rate_limited, exhausted, revoked
- 异常key自动从可用池中移除

#### 6.2 故障恢复
- **超时处理**: 5分钟锁定超时自动释放
- **错误重试**: 失败key标记后重新尝试其他key
- **健康检查**: 定期验证key有效性

### 7. GCP集成

#### 7.1 Secret Manager
- 生产环境中Executor私钥存储在GCP Secret Manager
- 支持本地环境变量fallback
- 自动密钥轮换和版本管理

#### 7.2 配置管理
```typescript
// GCP配置
GCP_KMS_PROJECT_ID: 项目ID
EXECUTOR_PRIVATE_KEY: 本地私钥(开发环境)
```

### 8. 系统协调机制

#### 8.1 三系统协调
- **Gateway**: 提供管理界面和API，处理用户请求
- **etcd**: 作为中央存储，确保数据一致性
- **Executor**: 执行实际的AI推理任务

#### 8.2 数据流向
```
用户 → Gateway前端 → Gateway后端 → 数据库 + etcd → Executor → AI模型
```

#### 8.3 容错设计
- 多级fallback机制
- 分布式锁防止并发冲突  
- 心跳监控确保服务可用性
- 自动故障转移和恢复

## 关键技术特性

1. **端到端加密**: 前端加密，executor解密，中间环节无法获取明文
2. **分布式协调**: 使用etcd实现分布式锁和状态同步
3. **负载均衡**: 智能key分配算法，避免单点过载
4. **故障容错**: 多层fallback和自动恢复机制
5. **实时监控**: 心跳、状态监控和异常处理
6. **安全隔离**: 不同provider和region的key完全隔离

这个架构确保了三方API key的安全共享、高可用性和良好的性能表现。

## 详细技术实现

### API接口清单

#### Gateway API接口
- `GET /public-keys` - 获取executor公钥列表
- `POST /third-party/encrypted` - 创建加密API key
- `PUT /third-party/encrypted/{provider}/{region}/{uuid}` - 更新加密key
- `DELETE /third-party/{keyId}` - 删除三方key
- `GET /executor/key` - executor请求key分配
- `GET /key-status/{provider}/{keyId}` - 获取key状态
- `PUT /key-status/{provider}/{keyId}` - 更新key状态

#### etcd存储路径
- `/api-keys/{provider}/{region}/{uuid}` - 加密的API key数据
- `/keys/public/{provider}/{region}/{executorId}` - executor公钥注册
- `/third-party-keys/{provider}/{userId}/{keyId}` - 遗留三方key
- `/keys/{provider}/{keyId}` - key状态信息

### 安全机制

#### 加密算法
- **密钥交换**: X25519椭圆曲线Diffie-Hellman
- **对称加密**: ChaCha20-Poly1305 AEAD
- **随机数生成**: 加密安全的随机数生成器
- **密钥派生**: 直接使用ECDH共享密钥

#### 访问控制
- JWT认证用于Gateway API访问
- Executor身份验证通过专用Guard
- 基于provider和region的权限隔离
- 用户级别的key所有权验证

### 性能优化

#### 缓存策略
- Executor内存缓存解密后的key
- LRU算法管理key使用顺序
- 定期清理过期和无效key
- 预分配key池减少延迟

#### 负载均衡
- 多executor实例自动发现
- 基于心跳的健康检查
- 智能key分配避免热点
- 故障自动转移机制

### 监控和运维

#### 日志记录
- 结构化日志记录所有关键操作
- 敏感信息脱敏处理
- 分级日志便于问题排查
- 集中化日志收集和分析

#### 指标监控
- Key使用率和成功率统计
- Executor健康状态监控
- etcd同步状态跟踪
- API响应时间和错误率

#### 故障处理
- 自动重试机制
- 降级和熔断保护
- 告警和通知系统
- 快速故障恢复流程

## 详细操作流程

### 流程1: 用户添加新API Key的完整步骤

#### 步骤1: 前端准备和验证
```typescript
// 1. 用户访问页面
// gateway-frontend/src/app/provider-keys/page.tsx
const fetchProviders = async () => {
  const providersData = await getProviders();
  // 获取支持的provider列表 (OpenAI, Claude, Gemini等)
}

// 2. 用户选择provider并点击添加
const handleProviderCardClick = (provider: Provider) => {
  if (provider.isActive) {
    setSelectedProvider(provider);
    setAddModalOpen(true);
  }
}
```

#### 步骤2: 获取Executor公钥
```typescript
// gateway-frontend/src/components/provider-keys/AddProviderKeyModal.tsx
const handleSave = async () => {
  // 1. 获取可用的executor公钥
  const executorKeyInfo = await getExecutorPublicKey(
    selectedProvider.name, // 'openai'
    'asia'                 // region
  );

  // API调用: GET /public-keys?provider=openai&region=asia
  // 返回格式:
  // {
  //   "success": true,
  //   "data": {
  //     "keys": [{
  //       "keyId": "executor-123",
  //       "publicKey": "base64编码的X25519公钥",
  //       "provider": "openai",
  //       "region": "asia",
  //       "status": "active"
  //     }]
  //   }
  // }
}
```

#### 步骤3: 前端加密处理
```typescript
// gateway-frontend/src/utils/encryptApiKey.ts
export function encryptApiKey(apiKey: string, executorPublicKey: string): EncryptionResult {
  // 1. 生成临时密钥对
  const ephemeralKeyPair = nacl.box.keyPair();

  // 2. 计算共享密钥 (X25519 ECDH)
  const executorPubKeyBuffer = Buffer.from(executorPublicKey, 'base64');
  const sharedSecret = nacl.scalarMult(ephemeralKeyPair.secretKey, executorPubKeyBuffer);

  // 3. 生成随机nonce
  const nonce = nacl.randomBytes(12); // ChaCha20-Poly1305使用12字节nonce

  // 4. 使用ChaCha20-Poly1305加密
  const aead = chacha20poly1305(sharedSecret, nonce);
  const plaintext = new TextEncoder().encode(apiKey);
  const encrypted = aead.encrypt(plaintext);

  // 5. 分离密文和认证标签
  const ciphertext = encrypted.slice(0, -16); // 前面部分是密文
  const tag = encrypted.slice(-16);           // 最后16字节是认证标签

  return {
    encryptedKey: Buffer.from(ciphertext).toString('base64'),
    nonce: Buffer.from(nonce).toString('base64'),
    tag: Buffer.from(tag).toString('base64'),
    ephemeralPubKey: Buffer.from(ephemeralKeyPair.publicKey).toString('base64')
  };
}
```

#### 步骤4: 发送到Gateway后端
```typescript
// 构造请求数据
const requestData: CreateProviderKeyRequest = {
  name: values.name,           // 用户输入的key名称
  provider: 'openai',          // provider类型
  region: 'asia',              // 区域
  keyId: executorKeyInfo.keyId, // executor ID
  encryptedKey: encryptedResult.encryptedKey,
  nonce: encryptedResult.nonce,
  tag: encryptedResult.tag,
  ephemeralPubKey: encryptedResult.ephemeralPubKey
};

// API调用: POST /third-party/encrypted
const response = await createProviderKey(requestData);
```

#### 步骤5: Gateway后端处理
```typescript
// backend/packages/apps/api-server/src/app/controllers/third-party.controller.ts
@Post('encrypted')
async createEncryptedApiKey(@Body() request: CreateEncryptedApiKeyRequest, @Request() req: AuthenticatedRequest) {
  const userId = req.user.userId;

  // 1. 验证executor可用性
  await this.encryptedApiKeyService.validateExecutorAvailability(request);

  // 2. 验证加密数据完整性
  this.validateEncryptionData(request);

  // 3. 生成UUID作为key标识
  const uuid = uuidv4();

  // 4. 构建存储数据
  const encryptedKeyData = {
    uuid,
    encryptedKey: request.encryptedKey,
    nonce: request.nonce,
    tag: request.tag,
    ephemeralPubKey: request.ephemeralPubKey,
    keyId: request.keyId,
    status: 'active',
    createdAt: new Date().toISOString()
  };

  // 5. 存储到etcd
  const etcdKey = `/api-keys/${request.provider}/${request.region}/${uuid}`;
  await this.etcdService.put(etcdKey, JSON.stringify(encryptedKeyData));

  // 6. 存储到数据库
  await this.createSharedKeyInDatabase(uuid, request, userId);
}
```

#### 步骤6: 数据库存储
```sql
-- backend/packages/libs/third-party/src/lib/third-party.repository.ts
INSERT INTO saito_gateway.api_keys (
  user_id,                    -- 用户ID
  name,                       -- 用户输入的key名称
  description,                -- 描述信息
  status,                     -- 'active'
  key_type,                   -- 'third_party'
  provider,                   -- 'openai'
  key_prefix,                 -- 'tp-'
  key_mask,                   -- 'tp-***simple***'
  key_hash,                   -- UUID作为hash
  encrypted_key_data,         -- JSON格式的加密数据
  region                      -- 'asia'
) VALUES (
  ${userId},
  ${name},
  ${description},
  'active',
  'third_party',
  ${provider},
  'tp-',
  'tp-***simple***',
  ${uuid},
  ${JSON.stringify(encryptedKeyData)},
  ${region}
);
```

### 流程2: etcd同步机制详细流程

#### 启动时自动同步
```typescript
// backend/packages/libs/third-party/src/lib/etcd-sync.service.ts
@Injectable()
export class EtcdSyncService implements OnApplicationBootstrap {
  async onApplicationBootstrap(): Promise<void> {
    await this.syncThirdPartyKeysToEtcd();
  }

  async syncThirdPartyKeysToEtcd(): Promise<void> {
    // 1. 从数据库加载所有active的key
    const databaseKeys = await this.thirdPartyRepository.getAllThirdPartyKeysForSync();

    // 2. 从etcd加载现有数据
    const etcdKeys = await this.etcdService.getAllWithPrefix('/api-keys/');

    // 3. 执行动态比较同步
    const syncResult = await this.performDynamicSync(databaseKeys, etcdKeys);
  }

  async performDynamicSync(databaseKeys: any[], etcdKeys: Record<string, string>) {
    const result = { added: 0, updated: 0, removed: 0, errors: 0 };

    // 创建数据库key的映射
    const databaseKeyMap = new Map<string, any>();
    for (const key of databaseKeys) {
      const etcdPath = `/api-keys/${key.provider}/${key.region}/${key.keyHash}`;
      databaseKeyMap.set(etcdPath, key);
    }

    // 处理数据库中的key - 添加新的或更新现有的
    for (const [etcdPath, databaseKey] of databaseKeyMap) {
      const existingEtcdData = etcdKeys[etcdPath];

      if (!existingEtcdData) {
        // etcd中不存在，添加新key
        await this.syncSingleKeyToEtcd(databaseKey);
        result.added++;
      } else {
        // 检查是否需要更新
        const needsUpdate = await this.keyNeedsUpdate(databaseKey, existingEtcdData);
        if (needsUpdate) {
          await this.syncSingleKeyToEtcd(databaseKey);
          result.updated++;
        }
      }
    }

    // 清理etcd中的孤立key
    for (const etcdPath of Object.keys(etcdKeys)) {
      if (!databaseKeyMap.has(etcdPath)) {
        await this.etcdService.delete(etcdPath);
        result.removed++;
      }
    }

    return result;
  }
}
```

### 流程3: Executor启动和注册详细流程

#### 步骤1: Executor启动初始化
```typescript
// sight-executor/libs/key-manager/src/encryption.service.ts
@Injectable()
export class EncryptionService implements OnModuleInit {
  async onModuleInit() {
    // 1. 加载或生成密钥对
    await this.initializeKeyPair();

    // 2. 注册公钥到etcd
    await this.registerPublicKey();

    // 3. 启动心跳服务
    this.startHeartbeat();
  }

  private async initializeKeyPair(): Promise<void> {
    // 1. 尝试从GCP Secret Manager加载私钥
    let privateKeyBase64 = await this.secretManagerService.loadPrivateKey();

    if (!privateKeyBase64) {
      // 2. 从环境变量加载
      privateKeyBase64 = this.configService.get<string>('EXECUTOR_PRIVATE_KEY');
    }

    if (privateKeyBase64) {
      // 3. 使用现有私钥
      const privateKey = Buffer.from(privateKeyBase64, 'base64');
      const publicKey = nacl.scalarMult.base(privateKey);
      this.executorKeyPair = { secretKey: privateKey, publicKey };
    } else {
      // 4. 生成新的密钥对
      this.executorKeyPair = nacl.box.keyPair();

      // 5. 保存私钥到GCP (生产环境)
      if (this.configService.get('NODE_ENV') === 'production') {
        await this.secretManagerService.storePrivateKey(
          Buffer.from(this.executorKeyPair.secretKey).toString('base64')
        );
      }
    }
  }

  private async registerPublicKey(): Promise<void> {
    const keyPath = `/keys/public/${this.provider}/${this.region}/${this.executorId}`;

    const publicKeyRegistration = {
      keyId: this.executorId,
      publicKey: Buffer.from(this.executorKeyPair.publicKey).toString('base64'),
      provider: this.provider,
      region: this.region,
      status: 'active',
      registeredAt: new Date().toISOString(),
      lastHeartbeat: new Date().toISOString(),
      executorUrl: `http://localhost:${this.configService.get('port', 3001)}`
    };

    await this.etcdService.put(keyPath, JSON.stringify(publicKeyRegistration));
  }

  private startHeartbeat(): void {
    // 每30秒发送一次心跳
    setInterval(async () => {
      await this.sendHeartbeat();
    }, 30000);
  }
}
```

### 流程4: Key分配和获取详细流程

#### 步骤1: Executor请求Key分配
```typescript
// sight-executor/libs/key-manager/src/gateway-key-allocation.service.ts
async requestKeyAllocation(): Promise<void> {
  const scope = `${this.provider}:${this.region}`; // 例如: "openai:asia"

  try {
    // 1. 优先从Gateway获取
    const response = await this.httpClient.get<KeyAllocationResponse>('/executor/key', {
      params: { scope },
      headers: {
        'Authorization': `Bearer ${this.executorToken}`,
        'X-Executor-ID': this.executorId
      }
    });

    if (response.data.success && response.data.data.keys.length > 0) {
      // 2. 处理分配的key
      this.processAllocatedKeys(response.data.data);
      return;
    }
  } catch (gatewayError) {
    // 3. Gateway失败，fallback到直接etcd访问
    await this.requestKeysFromEtcd();
  }
}
```

#### 步骤2: Gateway处理Key分配请求
```typescript
// backend/packages/apps/api-server/src/app/controllers/executor.controller.ts
@Get('key')
@UseGuards(ExecutorAuthGuard)
async getExecutorKeys(@Query('scope') scope: string, @Request() req: AuthenticatedExecutorRequest) {
  // 1. 解析scope参数
  const [provider, region] = scope.split(':');
  const executorId = req.executor.id;

  // 2. 从etcd获取匹配的加密key
  const result = await this.executorKeyService.getEncryptedKeysForScope(
    provider,
    region,
    executorId
  );

  // 3. 应用分配策略 (LRU算法)
  const allocatedKeys = this.selectKeysForAllocation(result.keys, executorId);

  // 4. 锁定选中的key
  for (const key of allocatedKeys) {
    await this.lockKeyForExecutor(key, executorId);
  }

  return {
    success: true,
    data: {
      keys: allocatedKeys,
      allocationId: uuidv4(),
      allocatedAt: new Date().toISOString()
    }
  };
}

private selectKeysForAllocation(availableKeys: any[], executorId: string): any[] {
  // LRU算法选择key
  const sortedKeys = availableKeys
    .filter(key => key.status === 'active')
    .sort((a, b) => (a.lastUsedAt || 0) - (b.lastUsedAt || 0));

  // 分配2-3个key给executor
  return sortedKeys.slice(0, 3);
}

private async lockKeyForExecutor(key: any, executorId: string): Promise<void> {
  const keyPath = `/api-keys/${key.provider}/${key.region}/${key.uuid}`;
  const lockTimeout = Date.now() + 5 * 60 * 1000; // 5分钟超时

  const updatedKey = {
    ...key,
    status: 'allocated',
    allocatedTo: executorId,
    allocatedAt: Date.now(),
    lockTimeout: lockTimeout
  };

  await this.etcdService.put(keyPath, JSON.stringify(updatedKey));
}
```

#### 步骤3: Executor解密和缓存Key
```typescript
// sight-executor/libs/key-manager/src/gateway-key-allocation.service.ts
private processAllocatedKeys(allocationData: KeyAllocationResponse['data']): void {
  for (const allocatedKey of allocationData.keys) {
    try {
      // 1. 解密API key
      const decryptedApiKey = this.encryptionService.decryptApiKey({
        keyId: this.executorId,
        encryptedKey: allocatedKey.encryptedKey,
        nonce: allocatedKey.nonce,
        tag: allocatedKey.tag,
        ephemeralPubKey: allocatedKey.ephemeralPubKey,
        status: allocatedKey.status,
        createdAt: allocatedKey.createdAt
      });

      // 2. 创建管理对象
      const managedKey: ManagedApiKey = {
        id: allocatedKey.uuid,
        key: decryptedApiKey,
        status: 'active',
        lastUsedAt: 0,
        lastError: undefined,
        note: 'Gateway allocated',
        usageCount: 0,
        errorCount: 0,
        keyId: allocatedKey.uuid,
        lockedAt: Date.now(),
        lockTimeout: Date.now() + 5 * 60 * 1000
      };

      // 3. 存储到内存缓存
      this.allocatedKeys.set(allocatedKey.uuid, managedKey);

    } catch (error) {
      this.logger.error(`Failed to process allocated key: ${error}`);
    }
  }
}
```

### 流程5: Key使用和状态管理详细流程

#### 步骤1: 获取可用Key
```typescript
// sight-executor/libs/key-manager/src/key-manager.service.ts
async getAndLockKey(): Promise<ManagedApiKey | null> {
  // 1. 优先使用Gateway分配的key
  const gatewayKey = await this.getGatewayAllocatedKey();
  if (gatewayKey) {
    return gatewayKey;
  }

  // 2. 使用加密的共享key
  const encryptedKey = await this.getAndLockEncryptedKey();
  if (encryptedKey) {
    return encryptedKey;
  }

  // 3. 使用遗留的三方key
  return this.getAndLockThirdPartyKey();
}

private async getGatewayAllocatedKey(): Promise<ManagedApiKey | null> {
  const availableKey = await this.gatewayKeyAllocationService.getAvailableKey();

  if (availableKey) {
    // 标记为使用中
    availableKey.status = 'in_use';
    availableKey.lastUsedAt = Date.now();
    availableKey.usageCount++;

    return availableKey;
  }

  return null;
}
```

#### 步骤2: 使用Key调用AI API
```typescript
// 实际使用key的示例
async callAIModel(prompt: string): Promise<any> {
  let apiKey: ManagedApiKey | null = null;
  let retryCount = 0;
  const maxRetries = 3;

  while (retryCount < maxRetries) {
    try {
      // 1. 获取可用的key
      apiKey = await this.keyManagerService.getAndLockKey();

      if (!apiKey) {
        throw new Error('No available API keys');
      }

      // 2. 调用AI API
      const response = await this.httpClient.post('https://api.openai.com/v1/chat/completions', {
        model: 'gpt-4',
        messages: [{ role: 'user', content: prompt }]
      }, {
        headers: {
          'Authorization': `Bearer ${apiKey.key}`,
          'Content-Type': 'application/json'
        }
      });

      // 3. 成功后更新key状态
      await this.updateKeySuccess(apiKey);

      return response.data;

    } catch (error) {
      // 4. 处理错误
      await this.handleKeyError(apiKey, error);
      retryCount++;

      if (retryCount >= maxRetries) {
        throw error;
      }
    } finally {
      // 5. 释放key锁定
      if (apiKey) {
        await this.keyManagerService.releaseKey(apiKey.id);
      }
    }
  }
}

private async updateKeySuccess(apiKey: ManagedApiKey): Promise<void> {
  apiKey.status = 'active';
  apiKey.lastUsedAt = Date.now();
  apiKey.usageCount++;
  apiKey.lastError = undefined;
}

private async handleKeyError(apiKey: ManagedApiKey | null, error: any): Promise<void> {
  if (!apiKey) return;

  apiKey.errorCount++;
  apiKey.lastError = error.message;

  // 根据错误类型更新状态
  if (error.response?.status === 429) {
    // Rate limit
    apiKey.status = 'rate_limited';
    await this.reportKeyStatus(apiKey, 'rate_limited', 'Rate limit exceeded');
  } else if (error.response?.status === 401) {
    // Invalid key
    apiKey.status = 'revoked';
    await this.reportKeyStatus(apiKey, 'revoked', 'Invalid API key');
  } else if (error.response?.status === 402) {
    // Quota exceeded
    apiKey.status = 'exhausted';
    await this.reportKeyStatus(apiKey, 'exhausted', 'Quota exceeded');
  }
}

private async reportKeyStatus(apiKey: ManagedApiKey, status: string, reason: string): Promise<void> {
  try {
    // 向Gateway报告key状态
    await this.httpClient.put(`/key-status/${this.provider}/${apiKey.keyId}`, {
      status: status,
      reason: reason,
      note: `Reported by executor ${this.executorId}`
    });
  } catch (error) {
    this.logger.error(`Failed to report key status: ${error}`);
  }
}
```

### 流程6: 故障处理和恢复机制

#### 故障场景1: Gateway不可用时的处理
```typescript
// sight-executor/libs/key-manager/src/gateway-key-allocation.service.ts
async requestKeyAllocation(): Promise<void> {
  try {
    // 1. 尝试从Gateway获取
    const response = await this.httpClient.get('/executor/key', {
      params: { scope: `${this.provider}:${this.region}` },
      timeout: 5000 // 5秒超时
    });

    if (response.data.success) {
      this.processAllocatedKeys(response.data.data);
      return;
    }
  } catch (gatewayError) {
    this.logger.warn(`Gateway allocation failed: ${gatewayError.message}`);

    // 2. Fallback: 直接从etcd获取
    await this.requestKeysFromEtcd();
  }
}

private async requestKeysFromEtcd(): Promise<void> {
  try {
    // 1. 直接从etcd获取所有可用的加密key
    const encryptedKeys = await this.encryptionService.getDecryptedApiKeys();

    if (encryptedKeys.length === 0) {
      this.logger.warn('No encrypted API keys available for allocation');
      return;
    }

    // 2. 处理每个加密key
    for (const keyData of encryptedKeys) {
      if (this.allocatedKeys.has(keyData.uuid)) {
        continue; // 跳过已分配的key
      }

      // 3. 尝试锁定key
      const lockSuccess = await this.tryLockKeyInEtcd(keyData);
      if (lockSuccess) {
        const managedKey: ManagedApiKey = {
          id: keyData.uuid,
          key: keyData.apiKey,
          status: 'active',
          lastUsedAt: 0,
          lastError: undefined,
          note: 'Direct etcd allocation',
          usageCount: 0,
          errorCount: 0,
          keyId: keyData.uuid,
          lockedAt: Date.now(),
          lockTimeout: Date.now() + 5 * 60 * 1000
        };

        this.allocatedKeys.set(keyData.uuid, managedKey);
      }
    }
  } catch (error) {
    this.logger.error(`Failed to request keys from etcd: ${error}`);
    throw error;
  }
}

private async tryLockKeyInEtcd(keyData: any): Promise<boolean> {
  try {
    const keyPath = `/api-keys/${this.provider}/${this.region}/${keyData.uuid}`;

    // 1. 获取当前key状态
    const currentData = await this.etcdService.get(keyPath);
    if (!currentData) {
      return false;
    }

    const keyInfo = JSON.parse(currentData);

    // 2. 检查是否可以锁定
    if (keyInfo.status === 'allocated' && keyInfo.lockTimeout > Date.now()) {
      return false; // 已被其他executor锁定
    }

    // 3. 尝试锁定
    const updatedKeyInfo = {
      ...keyInfo,
      status: 'allocated',
      allocatedTo: this.executorId,
      allocatedAt: Date.now(),
      lockTimeout: Date.now() + 5 * 60 * 1000
    };

    await this.etcdService.put(keyPath, JSON.stringify(updatedKeyInfo));
    return true;

  } catch (error) {
    this.logger.error(`Failed to lock key in etcd: ${error}`);
    return false;
  }
}
```

#### 故障场景2: Key过期和清理机制
```typescript
// sight-executor/libs/key-manager/src/key-manager.service.ts
@Cron('*/5 * * * *') // 每5分钟执行一次
async cleanupExpiredKeys(): Promise<void> {
  const now = Date.now();
  const expiredKeys: string[] = [];

  // 1. 检查内存中的key
  for (const [keyId, managedKey] of this.allocatedKeys.entries()) {
    // 检查锁定超时
    if (managedKey.lockTimeout && managedKey.lockTimeout < now) {
      expiredKeys.push(keyId);
      continue;
    }

    // 检查错误率
    if (managedKey.errorCount > 5 && managedKey.usageCount > 0) {
      const errorRate = managedKey.errorCount / managedKey.usageCount;
      if (errorRate > 0.5) { // 错误率超过50%
        expiredKeys.push(keyId);
        continue;
      }
    }

    // 检查最后使用时间 (超过1小时未使用)
    if (managedKey.lastUsedAt && (now - managedKey.lastUsedAt) > 60 * 60 * 1000) {
      expiredKeys.push(keyId);
    }
  }

  // 2. 清理过期的key
  for (const keyId of expiredKeys) {
    await this.releaseKey(keyId);
    this.allocatedKeys.delete(keyId);
    this.logger.debug(`Cleaned up expired key: ${keyId}`);
  }

  // 3. 释放etcd中的锁定
  await this.releaseExpiredLocksInEtcd();
}

private async releaseExpiredLocksInEtcd(): Promise<void> {
  try {
    const keyPrefix = `/api-keys/${this.provider}/${this.region}/`;
    const allKeys = await this.etcdService.getAllWithPrefix(keyPrefix);

    const now = Date.now();

    for (const [keyPath, keyDataStr] of Object.entries(allKeys)) {
      try {
        const keyData = JSON.parse(keyDataStr);

        // 检查是否是当前executor锁定的过期key
        if (keyData.allocatedTo === this.executorId &&
            keyData.lockTimeout &&
            keyData.lockTimeout < now) {

          // 释放锁定
          const updatedKeyData = {
            ...keyData,
            status: 'active',
            allocatedTo: undefined,
            allocatedAt: undefined,
            lockTimeout: undefined
          };

          await this.etcdService.put(keyPath, JSON.stringify(updatedKeyData));
          this.logger.debug(`Released expired lock for key: ${keyPath}`);
        }
      } catch (parseError) {
        this.logger.warn(`Failed to parse key data for ${keyPath}: ${parseError}`);
      }
    }
  } catch (error) {
    this.logger.error(`Failed to release expired locks in etcd: ${error}`);
  }
}
```

#### 故障场景3: 网络分区和重连机制
```typescript
// sight-executor/libs/etcd/src/etcd.service.ts
@Injectable()
export class EtcdService implements OnModuleInit, OnModuleDestroy {
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 10;
  private reconnectDelay = 1000; // 1秒

  async onModuleInit(): Promise<void> {
    await this.connectWithRetry();
  }

  private async connectWithRetry(): Promise<void> {
    while (this.reconnectAttempts < this.maxReconnectAttempts) {
      try {
        const etcdEndpoints = this.configService.get<string[]>('etcd.endpoints', ['localhost:2379']);

        this.etcdClient = new Etcd3({
          hosts: etcdEndpoints,
          dialTimeout: 5000,
          retry: {
            retries: 3,
            retryDelayMs: 1000
          }
        });

        // 测试连接
        await this.etcdClient.get('health-check').string();

        this.logger.log(`Connected to etcd: ${etcdEndpoints.join(', ')}`);
        this.reconnectAttempts = 0;

        // 设置连接监听器
        this.setupConnectionMonitoring();
        return;

      } catch (error) {
        this.reconnectAttempts++;
        this.logger.error(`etcd connection attempt ${this.reconnectAttempts} failed: ${error}`);

        if (this.reconnectAttempts >= this.maxReconnectAttempts) {
          throw new Error(`Failed to connect to etcd after ${this.maxReconnectAttempts} attempts`);
        }

        // 指数退避
        const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1);
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }
  }

  private setupConnectionMonitoring(): void {
    // 监听连接事件
    this.etcdClient.on('disconnected', () => {
      this.logger.warn('etcd connection lost, attempting to reconnect...');
      this.connectWithRetry();
    });

    this.etcdClient.on('connected', () => {
      this.logger.log('etcd connection restored');
    });
  }

  // 包装所有etcd操作，添加重试机制
  async put(key: string, value: string): Promise<void> {
    return this.executeWithRetry(async () => {
      await this.etcdClient.put(key).value(value);
    });
  }

  async get(key: string): Promise<string | null> {
    return this.executeWithRetry(async () => {
      return await this.etcdClient.get(key).string();
    });
  }

  private async executeWithRetry<T>(operation: () => Promise<T>): Promise<T> {
    let lastError: Error;

    for (let attempt = 1; attempt <= 3; attempt++) {
      try {
        return await operation();
      } catch (error) {
        lastError = error as Error;
        this.logger.warn(`etcd operation attempt ${attempt} failed: ${error}`);

        if (attempt < 3) {
          await new Promise(resolve => setTimeout(resolve, 1000 * attempt));
        }
      }
    }

    throw lastError!;
  }
}
```

### 流程7: 监控和告警机制

#### 性能指标收集
```typescript
// sight-executor/libs/monitoring/src/metrics.service.ts
@Injectable()
export class MetricsService {
  private keyUsageMetrics = new Map<string, KeyMetrics>();

  interface KeyMetrics {
    totalRequests: number;
    successfulRequests: number;
    failedRequests: number;
    averageResponseTime: number;
    lastUsedAt: number;
    errorRate: number;
  }

  recordKeyUsage(keyId: string, success: boolean, responseTime: number): void {
    let metrics = this.keyUsageMetrics.get(keyId);

    if (!metrics) {
      metrics = {
        totalRequests: 0,
        successfulRequests: 0,
        failedRequests: 0,
        averageResponseTime: 0,
        lastUsedAt: 0,
        errorRate: 0
      };
      this.keyUsageMetrics.set(keyId, metrics);
    }

    metrics.totalRequests++;
    metrics.lastUsedAt = Date.now();

    if (success) {
      metrics.successfulRequests++;
    } else {
      metrics.failedRequests++;
    }

    // 更新平均响应时间
    metrics.averageResponseTime = (
      (metrics.averageResponseTime * (metrics.totalRequests - 1) + responseTime) /
      metrics.totalRequests
    );

    // 更新错误率
    metrics.errorRate = metrics.failedRequests / metrics.totalRequests;

    // 检查是否需要告警
    this.checkAlerts(keyId, metrics);
  }

  private checkAlerts(keyId: string, metrics: KeyMetrics): void {
    // 错误率告警
    if (metrics.errorRate > 0.3 && metrics.totalRequests > 10) {
      this.sendAlert('HIGH_ERROR_RATE', {
        keyId,
        errorRate: metrics.errorRate,
        totalRequests: metrics.totalRequests
      });
    }

    // 响应时间告警
    if (metrics.averageResponseTime > 10000) { // 10秒
      this.sendAlert('SLOW_RESPONSE', {
        keyId,
        averageResponseTime: metrics.averageResponseTime
      });
    }
  }

  private async sendAlert(type: string, data: any): Promise<void> {
    try {
      // 发送到监控系统
      await this.httpClient.post('/monitoring/alerts', {
        type,
        data,
        timestamp: Date.now(),
        executorId: this.executorId
      });
    } catch (error) {
      this.logger.error(`Failed to send alert: ${error}`);
    }
  }

  @Cron('*/1 * * * *') // 每分钟
  async reportMetrics(): Promise<void> {
    const summary = {
      totalKeys: this.keyUsageMetrics.size,
      activeKeys: 0,
      totalRequests: 0,
      totalErrors: 0,
      averageErrorRate: 0
    };

    for (const [keyId, metrics] of this.keyUsageMetrics.entries()) {
      if (Date.now() - metrics.lastUsedAt < 5 * 60 * 1000) { // 5分钟内使用过
        summary.activeKeys++;
      }

      summary.totalRequests += metrics.totalRequests;
      summary.totalErrors += metrics.failedRequests;
    }

    if (summary.totalRequests > 0) {
      summary.averageErrorRate = summary.totalErrors / summary.totalRequests;
    }

    // 上报到监控系统
    await this.reportToMonitoring(summary);
  }
}
```

## 配置和部署指南

### 环境配置

#### Gateway配置
```yaml
# backend/.env
# 数据库配置
DATABASE_URL=postgresql://user:password@localhost:5432/saito_gateway
POSTGRES_HOST=localhost
POSTGRES_PORT=5432
POSTGRES_DB=saito_gateway
POSTGRES_USER=saito_user
POSTGRES_PASSWORD=secure_password

# etcd配置
ETCD_HOST=localhost
ETCD_PORT=2379
ETCD_ENDPOINTS=localhost:2379,localhost:2380,localhost:2381

# JWT配置
JWT_SECRET=your-jwt-secret-key
JWT_EXPIRES_IN=24h

# 服务配置
PORT=3000
NODE_ENV=production
LOG_LEVEL=info

# 同步配置
ETCD_SYNC_ENABLED=true
ETCD_SYNC_INTERVAL=300000  # 5分钟
```

#### Executor配置
```yaml
# sight-executor/.env
# Executor身份配置
EXECUTOR_ID=executor-openai-asia-001
EXECUTOR_PROVIDER=openai
EXECUTOR_REGION=asia
EXECUTOR_PORT=3001

# etcd配置
ETCD_ENDPOINTS=localhost:2379,localhost:2380,localhost:2381

# Gateway配置
GATEWAY_URL=http://localhost:3000
GATEWAY_API_KEY=executor-api-key

# GCP配置 (生产环境)
GCP_KMS_PROJECT_ID=your-gcp-project
GCP_SECRET_MANAGER_ENABLED=true
GOOGLE_APPLICATION_CREDENTIALS=/path/to/service-account.json

# 本地密钥配置 (开发环境)
EXECUTOR_PRIVATE_KEY=base64-encoded-private-key

# 性能配置
KEY_ALLOCATION_SIZE=3
KEY_REFRESH_INTERVAL=300000  # 5分钟
KEY_LOCK_TIMEOUT=300000      # 5分钟
HEARTBEAT_INTERVAL=30000     # 30秒

# 监控配置
METRICS_ENABLED=true
METRICS_INTERVAL=60000       # 1分钟
ALERT_WEBHOOK_URL=https://your-monitoring-system/webhooks
```

### 数据库初始化

#### PostgreSQL表结构
```sql
-- 创建数据库
CREATE DATABASE saito_gateway;

-- 创建schema
CREATE SCHEMA IF NOT EXISTS saito_gateway;

-- API Keys表
CREATE TABLE saito_gateway.api_keys (
    id SERIAL PRIMARY KEY,
    user_id VARCHAR(255) NOT NULL,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    status VARCHAR(50) DEFAULT 'active',
    key_type VARCHAR(50) DEFAULT 'third_party',
    provider VARCHAR(100) NOT NULL,
    key_prefix VARCHAR(20),
    key_mask VARCHAR(255),
    key_hash VARCHAR(255) UNIQUE NOT NULL,
    encrypted_key_data TEXT,
    region VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP NULL
);

-- 创建索引
CREATE INDEX idx_api_keys_user_id ON saito_gateway.api_keys(user_id);
CREATE INDEX idx_api_keys_provider ON saito_gateway.api_keys(provider);
CREATE INDEX idx_api_keys_region ON saito_gateway.api_keys(region);
CREATE INDEX idx_api_keys_status ON saito_gateway.api_keys(status);
CREATE INDEX idx_api_keys_key_hash ON saito_gateway.api_keys(key_hash);

-- 用户表 (如果不存在)
CREATE TABLE IF NOT EXISTS saito_gateway.users (
    id SERIAL PRIMARY KEY,
    user_id VARCHAR(255) UNIQUE NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    name VARCHAR(255),
    status VARCHAR(50) DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### etcd集群部署

#### etcd配置文件
```yaml
# etcd.yml
name: etcd-node-1
data-dir: /var/lib/etcd
wal-dir: /var/lib/etcd/wal

listen-peer-urls: http://0.0.0.0:2380
listen-client-urls: http://0.0.0.0:2379

advertise-client-urls: http://etcd-node-1:2379
initial-advertise-peer-urls: http://etcd-node-1:2380

initial-cluster: etcd-node-1=http://etcd-node-1:2380,etcd-node-2=http://etcd-node-2:2380,etcd-node-3=http://etcd-node-3:2380
initial-cluster-state: new
initial-cluster-token: etcd-cluster-token

# 安全配置
client-transport-security:
  cert-file: /etc/etcd/server.crt
  key-file: /etc/etcd/server.key
  trusted-ca-file: /etc/etcd/ca.crt

peer-transport-security:
  cert-file: /etc/etcd/peer.crt
  key-file: /etc/etcd/peer.key
  trusted-ca-file: /etc/etcd/ca.crt

# 性能配置
heartbeat-interval: 100
election-timeout: 1000
max-snapshots: 5
max-wals: 5
snapshot-count: 10000
```

### Docker部署配置

#### Gateway Dockerfile
```dockerfile
# backend/Dockerfile
FROM node:18-alpine

WORKDIR /app

# 复制package文件
COPY package*.json ./
COPY packages/apps/api-server/package*.json ./packages/apps/api-server/
COPY packages/libs/*/package*.json ./packages/libs/*/

# 安装依赖
RUN npm ci --only=production

# 复制源代码
COPY . .

# 构建应用
RUN npm run build

# 暴露端口
EXPOSE 3000

# 健康检查
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:3000/health || exit 1

# 启动命令
CMD ["npm", "run", "start:prod"]
```

#### Executor Dockerfile
```dockerfile
# sight-executor/Dockerfile
FROM node:18-alpine

WORKDIR /app

# 安装系统依赖
RUN apk add --no-cache curl

# 复制package文件
COPY package*.json ./
COPY libs/*/package*.json ./libs/*/

# 安装依赖
RUN npm ci --only=production

# 复制源代码
COPY . .

# 构建应用
RUN npm run build

# 暴露端口
EXPOSE 3001

# 健康检查
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:3001/health || exit 1

# 启动命令
CMD ["npm", "run", "start:prod"]
```

#### Docker Compose配置
```yaml
# docker-compose.yml
version: '3.8'

services:
  # PostgreSQL数据库
  postgres:
    image: postgres:15-alpine
    environment:
      POSTGRES_DB: saito_gateway
      POSTGRES_USER: saito_user
      POSTGRES_PASSWORD: secure_password
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - "5432:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U saito_user -d saito_gateway"]
      interval: 30s
      timeout: 10s
      retries: 3

  # etcd集群
  etcd1:
    image: quay.io/coreos/etcd:v3.5.9
    command:
      - /usr/local/bin/etcd
      - --name=etcd1
      - --data-dir=/etcd-data
      - --listen-client-urls=http://0.0.0.0:2379
      - --advertise-client-urls=http://etcd1:2379
      - --listen-peer-urls=http://0.0.0.0:2380
      - --initial-advertise-peer-urls=http://etcd1:2380
      - --initial-cluster=etcd1=http://etcd1:2380,etcd2=http://etcd2:2380,etcd3=http://etcd3:2380
      - --initial-cluster-state=new
      - --initial-cluster-token=etcd-cluster
    volumes:
      - etcd1_data:/etcd-data
    ports:
      - "2379:2379"
      - "2380:2380"

  etcd2:
    image: quay.io/coreos/etcd:v3.5.9
    command:
      - /usr/local/bin/etcd
      - --name=etcd2
      - --data-dir=/etcd-data
      - --listen-client-urls=http://0.0.0.0:2379
      - --advertise-client-urls=http://etcd2:2379
      - --listen-peer-urls=http://0.0.0.0:2380
      - --initial-advertise-peer-urls=http://etcd2:2380
      - --initial-cluster=etcd1=http://etcd1:2380,etcd2=http://etcd2:2380,etcd3=http://etcd3:2380
      - --initial-cluster-state=new
      - --initial-cluster-token=etcd-cluster
    volumes:
      - etcd2_data:/etcd-data

  etcd3:
    image: quay.io/coreos/etcd:v3.5.9
    command:
      - /usr/local/bin/etcd
      - --name=etcd3
      - --data-dir=/etcd-data
      - --listen-client-urls=http://0.0.0.0:2379
      - --advertise-client-urls=http://etcd3:2379
      - --listen-peer-urls=http://0.0.0.0:2380
      - --initial-advertise-peer-urls=http://etcd3:2380
      - --initial-cluster=etcd1=http://etcd1:2380,etcd2=http://etcd2:2380,etcd3=http://etcd3:2380
      - --initial-cluster-state=new
      - --initial-cluster-token=etcd-cluster
    volumes:
      - etcd3_data:/etcd-data

  # Gateway服务
  gateway:
    build:
      context: ./backend
      dockerfile: Dockerfile
    environment:
      DATABASE_URL: *****************************************************/saito_gateway
      ETCD_ENDPOINTS: etcd1:2379,etcd2:2379,etcd3:2379
      NODE_ENV: production
    ports:
      - "3000:3000"
    depends_on:
      - postgres
      - etcd1
      - etcd2
      - etcd3
    restart: unless-stopped

  # Executor服务 (OpenAI Asia)
  executor-openai-asia:
    build:
      context: ./sight-executor
      dockerfile: Dockerfile
    environment:
      EXECUTOR_ID: executor-openai-asia-001
      EXECUTOR_PROVIDER: openai
      EXECUTOR_REGION: asia
      ETCD_ENDPOINTS: etcd1:2379,etcd2:2379,etcd3:2379
      GATEWAY_URL: http://gateway:3000
    ports:
      - "3001:3001"
    depends_on:
      - gateway
    restart: unless-stopped

  # Executor服务 (Claude Asia)
  executor-claude-asia:
    build:
      context: ./sight-executor
      dockerfile: Dockerfile
    environment:
      EXECUTOR_ID: executor-claude-asia-001
      EXECUTOR_PROVIDER: claude
      EXECUTOR_REGION: asia
      ETCD_ENDPOINTS: etcd1:2379,etcd2:2379,etcd3:2379
      GATEWAY_URL: http://gateway:3000
    ports:
      - "3002:3001"
    depends_on:
      - gateway
    restart: unless-stopped

volumes:
  postgres_data:
  etcd1_data:
  etcd2_data:
  etcd3_data:
```

### 部署和运维脚本

#### 启动脚本
```bash
#!/bin/bash
# scripts/deploy.sh

set -e

echo "🚀 开始部署三方共享Key系统..."

# 1. 检查依赖
echo "📋 检查系统依赖..."
command -v docker >/dev/null 2>&1 || { echo "❌ Docker未安装"; exit 1; }
command -v docker-compose >/dev/null 2>&1 || { echo "❌ Docker Compose未安装"; exit 1; }

# 2. 构建镜像
echo "🔨 构建Docker镜像..."
docker-compose build

# 3. 启动服务
echo "🏃 启动服务..."
docker-compose up -d

# 4. 等待服务就绪
echo "⏳ 等待服务启动..."
sleep 30

# 5. 健康检查
echo "🔍 执行健康检查..."
./scripts/health-check.sh

echo "✅ 部署完成!"
```

#### 健康检查脚本
```bash
#!/bin/bash
# scripts/health-check.sh

set -e

echo "🔍 执行系统健康检查..."

# 检查PostgreSQL
echo "📊 检查PostgreSQL..."
docker-compose exec postgres pg_isready -U saito_user -d saito_gateway || {
  echo "❌ PostgreSQL不健康"
  exit 1
}

# 检查etcd集群
echo "🗄️ 检查etcd集群..."
for i in {1..3}; do
  docker-compose exec etcd$i etcdctl endpoint health || {
    echo "❌ etcd$i不健康"
    exit 1
  }
done

# 检查Gateway
echo "🚪 检查Gateway服务..."
curl -f http://localhost:3000/health || {
  echo "❌ Gateway服务不健康"
  exit 1
}

# 检查Executor
echo "⚙️ 检查Executor服务..."
curl -f http://localhost:3001/health || {
  echo "❌ Executor服务不健康"
  exit 1
}

echo "✅ 所有服务健康!"
```

## 总结

这份详细的文档涵盖了三方共享Key系统的完整工作流程，包括：

1. **详细的代码实现** - 每个关键步骤的具体代码示例
2. **完整的配置指南** - 环境配置、数据库设置、etcd集群配置
3. **故障处理机制** - 网络分区、服务不可用、key过期等场景的处理
4. **监控和告警** - 性能指标收集、异常检测、告警机制
5. **部署和运维** - Docker配置、集群部署、健康检查脚本

这个系统确保了三方API key的安全共享、高可用性和良好的性能表现，同时提供了完善的监控和运维能力。
```
```
```
