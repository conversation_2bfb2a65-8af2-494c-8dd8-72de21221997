# 三方共享Key系统核心机制详细实现

## 1. etcd重连机制

### 1.1 连接监控和自动重连
```typescript
// sight-executor/libs/etcd/src/etcd.service.ts
@Injectable()
export class EtcdService implements OnModuleInit, OnModuleDestroy {
  private etcdClient!: Etcd3;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 10;
  private reconnectDelay = 1000; // 初始延迟1秒
  private isReconnecting = false;
  private connectionState: 'connected' | 'disconnected' | 'reconnecting' = 'disconnected';

  async onModuleInit(): Promise<void> {
    await this.connectWithRetry();
    this.setupConnectionMonitoring();
  }

  private async connectWithRetry(): Promise<void> {
    this.isReconnecting = true;
    this.connectionState = 'reconnecting';
    
    while (this.reconnectAttempts < this.maxReconnectAttempts) {
      try {
        const etcdEndpoints = this.configService.get<string[]>('etcd.endpoints', ['localhost:2379']);
        
        this.etcdClient = new Etcd3({
          hosts: etcdEndpoints,
          dialTimeout: 5000,
          retry: {
            retries: 3,
            retryDelayMs: 1000
          },
          // 启用自动重连
          keepalive: {
            keepaliveTimeMs: 30000,
            keepaliveTimeoutMs: 5000,
            keepalivePermitWithoutCalls: true
          }
        });
        
        // 测试连接
        await this.etcdClient.get('health-check').string();
        
        this.logger.log(`✅ Connected to etcd: ${etcdEndpoints.join(', ')}`);
        this.reconnectAttempts = 0;
        this.isReconnecting = false;
        this.connectionState = 'connected';
        
        // 重连成功后，重新注册公钥和恢复心跳
        await this.onReconnected();
        return;
        
      } catch (error) {
        this.reconnectAttempts++;
        this.logger.error(`❌ etcd connection attempt ${this.reconnectAttempts} failed: ${error}`);
        
        if (this.reconnectAttempts >= this.maxReconnectAttempts) {
          this.isReconnecting = false;
          this.connectionState = 'disconnected';
          throw new Error(`Failed to connect to etcd after ${this.maxReconnectAttempts} attempts`);
        }
        
        // 指数退避策略
        const delay = Math.min(this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1), 30000);
        this.logger.warn(`⏳ Retrying in ${delay}ms...`);
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }
  }

  private setupConnectionMonitoring(): void {
    // 监听连接断开事件
    this.etcdClient.on('disconnected', () => {
      this.logger.warn('🔌 etcd connection lost, attempting to reconnect...');
      this.connectionState = 'disconnected';
      
      if (!this.isReconnecting) {
        this.connectWithRetry().catch(error => {
          this.logger.error(`Failed to reconnect to etcd: ${error}`);
        });
      }
    });
    
    // 监听连接恢复事件
    this.etcdClient.on('connected', () => {
      this.logger.log('🔗 etcd connection restored');
      this.connectionState = 'connected';
    });
    
    // 定期健康检查
    setInterval(async () => {
      await this.healthCheck();
    }, 60000); // 每分钟检查一次
  }

  private async healthCheck(): Promise<void> {
    if (this.connectionState === 'connected') {
      try {
        await this.etcdClient.get('health-check').string();
      } catch (error) {
        this.logger.warn('🏥 Health check failed, connection may be unstable');
        this.connectionState = 'disconnected';
        
        if (!this.isReconnecting) {
          this.connectWithRetry().catch(err => {
            this.logger.error(`Health check reconnect failed: ${err}`);
          });
        }
      }
    }
  }

  private async onReconnected(): Promise<void> {
    try {
      // 重新注册executor公钥
      await this.reregisterExecutorPublicKey();
      
      // 恢复心跳服务
      await this.resumeHeartbeat();
      
      // 重新同步本地缓存的key状态
      await this.resyncKeyStates();
      
      this.logger.log('🔄 Post-reconnection recovery completed');
    } catch (error) {
      this.logger.error(`Post-reconnection recovery failed: ${error}`);
    }
  }
}
```

### 1.2 操作重试机制
```typescript
// 包装所有etcd操作，添加重试和降级机制
private async executeWithRetry<T>(operation: () => Promise<T>, operationName: string): Promise<T> {
  let lastError: Error;
  const maxRetries = 3;
  
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      // 检查连接状态
      if (this.connectionState === 'disconnected') {
        throw new Error('etcd connection is not available');
      }
      
      return await operation();
      
    } catch (error) {
      lastError = error as Error;
      this.logger.warn(`⚠️ ${operationName} attempt ${attempt} failed: ${error}`);
      
      // 如果是连接错误，触发重连
      if (this.isConnectionError(error)) {
        this.connectionState = 'disconnected';
        if (!this.isReconnecting) {
          this.connectWithRetry().catch(err => {
            this.logger.error(`Auto-reconnect failed: ${err}`);
          });
        }
      }
      
      if (attempt < maxRetries) {
        // 等待后重试
        const delay = 1000 * attempt; // 线性退避
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }
  }
  
  // 所有重试都失败，抛出最后的错误
  throw new Error(`${operationName} failed after ${maxRetries} attempts: ${lastError!.message}`);
}

private isConnectionError(error: any): boolean {
  const connectionErrors = [
    'ECONNREFUSED',
    'ENOTFOUND',
    'ETIMEDOUT',
    'ECONNRESET',
    'connection refused',
    'network is unreachable'
  ];
  
  const errorMessage = error.message?.toLowerCase() || '';
  return connectionErrors.some(errType => errorMessage.includes(errType));
}

// 使用重试机制的操作方法
async put(key: string, value: string): Promise<void> {
  return this.executeWithRetry(async () => {
    await this.etcdClient.put(key).value(value);
  }, `PUT ${key}`);
}

async get(key: string): Promise<string | null> {
  return this.executeWithRetry(async () => {
    return await this.etcdClient.get(key).string();
  }, `GET ${key}`);
}
```

## 2. 服务不可用回退机制

### 2.1 Gateway不可用时的Fallback
```typescript
// sight-executor/libs/key-manager/src/gateway-key-allocation.service.ts
async requestKeyAllocation(): Promise<void> {
  const scope = `${this.provider}:${this.region}`;
  
  try {
    // 第一优先级: 尝试从Gateway获取
    await this.requestFromGateway(scope);
    this.logger.log('✅ Successfully allocated keys from Gateway');
    
  } catch (gatewayError) {
    this.logger.warn(`🚪 Gateway allocation failed: ${gatewayError.message}`);
    
    try {
      // 第二优先级: 直接从etcd获取
      await this.requestKeysFromEtcd();
      this.logger.log('✅ Successfully allocated keys from etcd (fallback)');
      
    } catch (etcdError) {
      this.logger.error(`🗄️ etcd fallback failed: ${etcdError.message}`);
      
      try {
        // 第三优先级: 使用本地缓存的key
        await this.useLocalCachedKeys();
        this.logger.log('✅ Using locally cached keys (last resort)');
        
      } catch (localError) {
        this.logger.error(`💾 Local cache fallback failed: ${localError.message}`);
        throw new Error('All key allocation methods failed');
      }
    }
  }
}

private async requestFromGateway(scope: string): Promise<void> {
  const response = await this.httpClient.get<KeyAllocationResponse>('/executor/key', {
    params: { scope },
    timeout: 5000, // 5秒超时
    headers: {
      'Authorization': `Bearer ${this.executorToken}`,
      'X-Executor-ID': this.executorId
    }
  });
  
  if (response.data.success && response.data.data.keys.length > 0) {
    this.processAllocatedKeys(response.data.data);
    this.allocationId = response.data.data.allocationId;
  } else {
    throw new Error('Gateway returned no keys');
  }
}

private async requestKeysFromEtcd(): Promise<void> {
  // 直接从etcd获取所有可用的加密key
  const encryptedKeys = await this.encryptionService.getDecryptedApiKeys();
  
  if (encryptedKeys.length === 0) {
    throw new Error('No encrypted API keys available in etcd');
  }
  
  let allocatedCount = 0;
  const targetCount = 3; // 尝试分配3个key
  
  for (const keyData of encryptedKeys) {
    if (allocatedCount >= targetCount) break;
    
    // 跳过已分配的key
    if (this.allocatedKeys.has(keyData.uuid)) {
      continue;
    }
    
    // 尝试锁定key
    const lockSuccess = await this.tryLockKeyInEtcd(keyData);
    if (lockSuccess) {
      const managedKey: ManagedApiKey = {
        id: keyData.uuid,
        key: keyData.apiKey,
        status: 'active',
        lastUsedAt: 0,
        lastError: undefined,
        note: 'Direct etcd allocation (fallback)',
        usageCount: 0,
        errorCount: 0,
        keyId: keyData.uuid,
        lockedAt: Date.now(),
        lockTimeout: Date.now() + 5 * 60 * 1000
      };
      
      this.allocatedKeys.set(keyData.uuid, managedKey);
      allocatedCount++;
    }
  }
  
  if (allocatedCount === 0) {
    throw new Error('Failed to allocate any keys from etcd');
  }
}

private async useLocalCachedKeys(): Promise<void> {
  // 使用本地持久化缓存的key作为最后手段
  const cachedKeys = await this.loadCachedKeysFromDisk();
  
  if (cachedKeys.length === 0) {
    throw new Error('No cached keys available');
  }
  
  for (const cachedKey of cachedKeys) {
    // 验证缓存的key是否仍然有效
    const isValid = await this.validateCachedKey(cachedKey);
    if (isValid) {
      this.allocatedKeys.set(cachedKey.id, cachedKey);
    }
  }
  
  if (this.allocatedKeys.size === 0) {
    throw new Error('No valid cached keys found');
  }
}
```

### 2.2 服务降级策略
```typescript
// sight-executor/libs/key-manager/src/service-degradation.service.ts
@Injectable()
export class ServiceDegradationService {
  private degradationLevel: 'normal' | 'partial' | 'minimal' = 'normal';
  private serviceHealth = {
    gateway: true,
    etcd: true,
    gcp: true
  };

  async checkServiceHealth(): Promise<void> {
    // 检查Gateway健康状态
    try {
      await this.httpClient.get('/health', { timeout: 3000 });
      this.serviceHealth.gateway = true;
    } catch {
      this.serviceHealth.gateway = false;
    }
    
    // 检查etcd健康状态
    try {
      await this.etcdService.get('health-check');
      this.serviceHealth.etcd = true;
    } catch {
      this.serviceHealth.etcd = false;
    }
    
    // 检查GCP健康状态
    try {
      await this.gcpService.testConnection();
      this.serviceHealth.gcp = true;
    } catch {
      this.serviceHealth.gcp = false;
    }
    
    // 根据服务健康状态调整降级级别
    this.adjustDegradationLevel();
  }

  private adjustDegradationLevel(): void {
    const healthyServices = Object.values(this.serviceHealth).filter(Boolean).length;
    
    if (healthyServices === 3) {
      this.degradationLevel = 'normal';
    } else if (healthyServices >= 1) {
      this.degradationLevel = 'partial';
    } else {
      this.degradationLevel = 'minimal';
    }
    
    this.logger.log(`🎚️ Service degradation level: ${this.degradationLevel}`);
  }

  getDegradedKeyAllocationStrategy(): KeyAllocationStrategy {
    switch (this.degradationLevel) {
      case 'normal':
        return {
          sources: ['gateway', 'etcd', 'cache'],
          maxKeys: 5,
          refreshInterval: 300000 // 5分钟
        };
      case 'partial':
        return {
          sources: ['etcd', 'cache'],
          maxKeys: 3,
          refreshInterval: 600000 // 10分钟
        };
      case 'minimal':
        return {
          sources: ['cache'],
          maxKeys: 1,
          refreshInterval: 1800000 // 30分钟
        };
    }
  }
}
```

## 3. LRU算法选择Key机制

### 3.1 LRU Key选择实现
```typescript
// sight-executor/libs/key-manager/src/lru-key-selector.service.ts
@Injectable()
export class LRUKeySelector {
  private keyUsageMap = new Map<string, KeyUsageInfo>();

  interface KeyUsageInfo {
    keyId: string;
    lastUsedAt: number;
    usageCount: number;
    errorCount: number;
    averageResponseTime: number;
    status: 'active' | 'rate_limited' | 'exhausted' | 'revoked';
    priority: number; // 优先级权重
  }

  /**
   * 使用LRU算法选择最佳的key
   * 考虑因素：最后使用时间、错误率、响应时间、优先级
   */
  selectOptimalKey(availableKeys: ManagedApiKey[]): ManagedApiKey | null {
    if (availableKeys.length === 0) {
      return null;
    }

    // 过滤出可用的key
    const usableKeys = availableKeys.filter(key =>
      key.status === 'active' &&
      !this.isKeyLocked(key) &&
      !this.isKeyInCooldown(key)
    );

    if (usableKeys.length === 0) {
      return null;
    }

    // 计算每个key的综合得分
    const scoredKeys = usableKeys.map(key => ({
      key,
      score: this.calculateKeyScore(key)
    }));

    // 按得分排序，选择最佳key
    scoredKeys.sort((a, b) => b.score - a.score);

    const selectedKey = scoredKeys[0].key;
    this.updateKeyUsage(selectedKey);

    return selectedKey;
  }

  private calculateKeyScore(key: ManagedApiKey): number {
    const usage = this.keyUsageMap.get(key.id);
    const now = Date.now();

    // 基础分数
    let score = 100;

    // LRU因子：最近使用时间越久，分数越高
    const timeSinceLastUse = now - (key.lastUsedAt || 0);
    const lruScore = Math.min(timeSinceLastUse / (60 * 1000), 50); // 最多50分
    score += lruScore;

    // 错误率惩罚
    if (usage && usage.usageCount > 0) {
      const errorRate = usage.errorCount / usage.usageCount;
      score -= errorRate * 30; // 错误率每10%扣3分
    }

    // 响应时间因子
    if (usage && usage.averageResponseTime > 0) {
      const responseTimePenalty = Math.min(usage.averageResponseTime / 1000, 20); // 最多扣20分
      score -= responseTimePenalty;
    }

    // 使用频率平衡：避免某个key被过度使用
    if (usage && usage.usageCount > 0) {
      const avgUsage = this.getAverageUsageCount();
      if (usage.usageCount > avgUsage * 1.5) {
        score -= 15; // 过度使用惩罚
      }
    }

    // 优先级加成
    if (usage && usage.priority > 0) {
      score += usage.priority;
    }

    return Math.max(score, 0);
  }

  private updateKeyUsage(key: ManagedApiKey): void {
    const now = Date.now();
    let usage = this.keyUsageMap.get(key.id);

    if (!usage) {
      usage = {
        keyId: key.id,
        lastUsedAt: now,
        usageCount: 0,
        errorCount: 0,
        averageResponseTime: 0,
        status: key.status,
        priority: 0
      };
      this.keyUsageMap.set(key.id, usage);
    }

    usage.lastUsedAt = now;
    usage.usageCount++;
    usage.status = key.status;

    // 更新key对象
    key.lastUsedAt = now;
    key.usageCount = usage.usageCount;
  }

  recordKeyPerformance(keyId: string, responseTime: number, success: boolean): void {
    const usage = this.keyUsageMap.get(keyId);
    if (!usage) return;

    // 更新平均响应时间
    usage.averageResponseTime = (
      (usage.averageResponseTime * (usage.usageCount - 1) + responseTime) /
      usage.usageCount
    );

    // 更新错误计数
    if (!success) {
      usage.errorCount++;
    }

    // 动态调整优先级
    this.adjustKeyPriority(usage);
  }

  private adjustKeyPriority(usage: KeyUsageInfo): void {
    const errorRate = usage.errorCount / usage.usageCount;

    if (errorRate < 0.05) { // 错误率低于5%
      usage.priority = Math.min(usage.priority + 1, 10);
    } else if (errorRate > 0.2) { // 错误率高于20%
      usage.priority = Math.max(usage.priority - 2, -10);
    }

    // 响应时间调整
    if (usage.averageResponseTime < 2000) { // 响应时间小于2秒
      usage.priority = Math.min(usage.priority + 1, 10);
    } else if (usage.averageResponseTime > 10000) { // 响应时间大于10秒
      usage.priority = Math.max(usage.priority - 1, -10);
    }
  }

  private isKeyLocked(key: ManagedApiKey): boolean {
    return key.status === 'in_use' &&
           key.lockTimeout &&
           key.lockTimeout > Date.now();
  }

  private isKeyInCooldown(key: ManagedApiKey): boolean {
    // 如果key最近有错误，给它一个冷却期
    if (key.lastError && key.errorCount > 0) {
      const cooldownPeriod = Math.min(key.errorCount * 30000, 300000); // 最多5分钟
      return (Date.now() - (key.lastUsedAt || 0)) < cooldownPeriod;
    }
    return false;
  }

  private getAverageUsageCount(): number {
    if (this.keyUsageMap.size === 0) return 0;

    const totalUsage = Array.from(this.keyUsageMap.values())
      .reduce((sum, usage) => sum + usage.usageCount, 0);

    return totalUsage / this.keyUsageMap.size;
  }

  // 获取key使用统计
  getKeyStatistics(): KeyStatistics[] {
    return Array.from(this.keyUsageMap.values()).map(usage => ({
      keyId: usage.keyId,
      usageCount: usage.usageCount,
      errorCount: usage.errorCount,
      errorRate: usage.usageCount > 0 ? usage.errorCount / usage.usageCount : 0,
      averageResponseTime: usage.averageResponseTime,
      priority: usage.priority,
      lastUsedAt: usage.lastUsedAt,
      status: usage.status
    }));
  }
}

interface KeyStatistics {
  keyId: string;
  usageCount: number;
  errorCount: number;
  errorRate: number;
  averageResponseTime: number;
  priority: number;
  lastUsedAt: number;
  status: string;
}
```

## 4. 异常处理机制

### 4.1 etcd异常处理
```typescript
// sight-executor/libs/key-manager/src/etcd-exception-handler.service.ts
@Injectable()
export class EtcdExceptionHandler {

  async handleEtcdException(error: any, operation: string, keyId?: string): Promise<void> {
    const errorType = this.classifyError(error);

    switch (errorType) {
      case 'CONNECTION_LOST':
        await this.handleConnectionLost(operation, keyId);
        break;

      case 'TIMEOUT':
        await this.handleTimeout(operation, keyId);
        break;

      case 'KEY_NOT_FOUND':
        await this.handleKeyNotFound(operation, keyId);
        break;

      case 'PERMISSION_DENIED':
        await this.handlePermissionDenied(operation, keyId);
        break;

      case 'STORAGE_FULL':
        await this.handleStorageFull(operation, keyId);
        break;

      default:
        await this.handleGenericError(error, operation, keyId);
    }
  }

  private classifyError(error: any): string {
    const message = error.message?.toLowerCase() || '';

    if (message.includes('connection') || message.includes('econnrefused')) {
      return 'CONNECTION_LOST';
    }
    if (message.includes('timeout') || message.includes('etimedout')) {
      return 'TIMEOUT';
    }
    if (message.includes('key not found') || error.code === 'KEY_NOT_FOUND') {
      return 'KEY_NOT_FOUND';
    }
    if (message.includes('permission') || error.code === 'PERMISSION_DENIED') {
      return 'PERMISSION_DENIED';
    }
    if (message.includes('no space') || message.includes('storage full')) {
      return 'STORAGE_FULL';
    }

    return 'GENERIC_ERROR';
  }

  private async handleConnectionLost(operation: string, keyId?: string): Promise<void> {
    this.logger.error(`🔌 etcd connection lost during ${operation} for key ${keyId}`);

    // 1. 标记etcd为不可用
    this.etcdService.markAsUnavailable();

    // 2. 如果是key操作，将key状态保存到本地缓存
    if (keyId) {
      await this.saveKeyStateToLocalCache(keyId, operation);
    }

    // 3. 触发重连机制
    this.etcdService.triggerReconnect();

    // 4. 切换到本地缓存模式
    this.keyManagerService.switchToLocalMode();
  }

  private async handleTimeout(operation: string, keyId?: string): Promise<void> {
    this.logger.warn(`⏰ etcd timeout during ${operation} for key ${keyId}`);

    // 1. 记录超时事件
    this.metricsService.recordTimeout(operation, keyId);

    // 2. 如果是写操作，尝试验证是否实际成功
    if (this.isWriteOperation(operation) && keyId) {
      setTimeout(async () => {
        await this.verifyWriteOperation(operation, keyId);
      }, 5000); // 5秒后验证
    }

    // 3. 临时降低该key的优先级
    if (keyId) {
      this.lruSelector.adjustKeyPriority(keyId, -5);
    }
  }

  private async handleKeyNotFound(operation: string, keyId?: string): Promise<void> {
    this.logger.warn(`🔍 Key not found during ${operation} for key ${keyId}`);

    if (keyId) {
      // 1. 从本地缓存中移除该key
      this.keyManagerService.removeKeyFromCache(keyId);

      // 2. 如果是关键key，尝试重新分配
      if (this.isCriticalKey(keyId)) {
        await this.keyManagerService.requestKeyAllocation();
      }

      // 3. 记录key丢失事件
      this.auditService.recordKeyLoss(keyId, operation);
    }
  }

  private async saveKeyStateToLocalCache(keyId: string, operation: string): Promise<void> {
    try {
      const keyState = this.keyManagerService.getKeyState(keyId);
      if (keyState) {
        await this.localCacheService.saveKeyState(keyId, {
          ...keyState,
          lastOperation: operation,
          savedAt: Date.now(),
          reason: 'etcd_connection_lost'
        });
      }
    } catch (error) {
      this.logger.error(`Failed to save key state to local cache: ${error}`);
    }
  }
}
```

### 4.2 Executor内存异常处理
```typescript
// sight-executor/libs/key-manager/src/memory-exception-handler.service.ts
@Injectable()
export class MemoryExceptionHandler {
  private memoryUsage = {
    heapUsed: 0,
    heapTotal: 0,
    external: 0,
    rss: 0
  };

  private keyCache = new Map<string, ManagedApiKey>();
  private maxCacheSize = 1000; // 最大缓存key数量
  private memoryThreshold = 0.85; // 内存使用率阈值

  @Cron('*/30 * * * * *') // 每30秒检查一次
  async monitorMemoryUsage(): Promise<void> {
    const memUsage = process.memoryUsage();
    this.memoryUsage = {
      heapUsed: memUsage.heapUsed,
      heapTotal: memUsage.heapTotal,
      external: memUsage.external,
      rss: memUsage.rss
    };

    const heapUsageRatio = memUsage.heapUsed / memUsage.heapTotal;

    if (heapUsageRatio > this.memoryThreshold) {
      await this.handleMemoryPressure();
    }

    // 检查缓存大小
    if (this.keyCache.size > this.maxCacheSize) {
      await this.cleanupKeyCache();
    }
  }

  private async handleMemoryPressure(): Promise<void> {
    this.logger.warn(`🧠 Memory pressure detected: ${(this.memoryUsage.heapUsed / 1024 / 1024).toFixed(2)}MB used`);

    // 1. 清理过期的key
    await this.cleanupExpiredKeys();

    // 2. 清理错误率高的key
    await this.cleanupErrorProneKeys();

    // 3. 减少缓存大小
    this.maxCacheSize = Math.max(this.maxCacheSize * 0.8, 100);

    // 4. 强制垃圾回收
    if (global.gc) {
      global.gc();
    }

    // 5. 如果内存仍然紧张，进入紧急模式
    const newMemUsage = process.memoryUsage();
    const newHeapRatio = newMemUsage.heapUsed / newMemUsage.heapTotal;

    if (newHeapRatio > 0.9) {
      await this.enterEmergencyMode();
    }
  }

  private async cleanupExpiredKeys(): Promise<void> {
    const now = Date.now();
    const expiredKeys: string[] = [];

    for (const [keyId, managedKey] of this.keyCache.entries()) {
      // 清理超过1小时未使用的key
      if (managedKey.lastUsedAt && (now - managedKey.lastUsedAt) > 3600000) {
        expiredKeys.push(keyId);
      }

      // 清理锁定超时的key
      if (managedKey.lockTimeout && managedKey.lockTimeout < now) {
        expiredKeys.push(keyId);
      }
    }

    for (const keyId of expiredKeys) {
      await this.removeKeyFromMemory(keyId, 'expired');
    }

    this.logger.log(`🧹 Cleaned up ${expiredKeys.length} expired keys from memory`);
  }

  private async cleanupErrorProneKeys(): Promise<void> {
    const errorProneKeys: string[] = [];

    for (const [keyId, managedKey] of this.keyCache.entries()) {
      // 清理错误率超过50%的key
      if (managedKey.usageCount > 10) {
        const errorRate = managedKey.errorCount / managedKey.usageCount;
        if (errorRate > 0.5) {
          errorProneKeys.push(keyId);
        }
      }
    }

    for (const keyId of errorProneKeys) {
      await this.removeKeyFromMemory(keyId, 'error_prone');
    }

    this.logger.log(`🚫 Cleaned up ${errorProneKeys.length} error-prone keys from memory`);
  }

  private async enterEmergencyMode(): Promise<void> {
    this.logger.error(`🚨 Entering emergency mode due to critical memory pressure`);

    // 1. 只保留最近使用的key
    const sortedKeys = Array.from(this.keyCache.entries())
      .sort(([, a], [, b]) => (b.lastUsedAt || 0) - (a.lastUsedAt || 0));

    const keysToKeep = Math.min(50, sortedKeys.length); // 只保留50个最近使用的key

    for (let i = keysToKeep; i < sortedKeys.length; i++) {
      const [keyId] = sortedKeys[i];
      await this.removeKeyFromMemory(keyId, 'emergency_cleanup');
    }

    // 2. 禁用新key分配
    this.keyManagerService.disableNewKeyAllocation(300000); // 5分钟

    // 3. 发送紧急告警
    await this.alertService.sendEmergencyAlert('MEMORY_CRITICAL', {
      heapUsed: this.memoryUsage.heapUsed,
      heapTotal: this.memoryUsage.heapTotal,
      keysRemaining: this.keyCache.size
    });
  }

  private async removeKeyFromMemory(keyId: string, reason: string): Promise<void> {
    const managedKey = this.keyCache.get(keyId);
    if (!managedKey) return;

    try {
      // 1. 如果key正在使用中，等待完成
      if (managedKey.status === 'in_use') {
        managedKey.status = 'pending_removal';
        // 设置超时强制移除
        setTimeout(() => {
          this.keyCache.delete(keyId);
        }, 30000); // 30秒后强制移除
        return;
      }

      // 2. 保存key状态到持久化存储（如果需要）
      if (this.shouldPersistKey(managedKey)) {
        await this.localCacheService.saveKeyState(keyId, {
          ...managedKey,
          removedAt: Date.now(),
          removalReason: reason
        });
      }

      // 3. 释放etcd中的锁定
      if (managedKey.lockTimeout && managedKey.lockTimeout > Date.now()) {
        await this.etcdService.releaseKeyLock(keyId);
      }

      // 4. 从内存中移除
      this.keyCache.delete(keyId);

      this.logger.debug(`🗑️ Removed key ${keyId} from memory (reason: ${reason})`);

    } catch (error) {
      this.logger.error(`Failed to remove key ${keyId} from memory: ${error}`);
      // 强制移除
      this.keyCache.delete(keyId);
    }
  }

  private shouldPersistKey(managedKey: ManagedApiKey): boolean {
    // 如果key使用次数较多或者最近使用过，则持久化
    return managedKey.usageCount > 5 ||
           (managedKey.lastUsedAt && (Date.now() - managedKey.lastUsedAt) < 300000);
  }

  // 获取内存使用统计
  getMemoryStatistics(): MemoryStatistics {
    return {
      memoryUsage: this.memoryUsage,
      cacheSize: this.keyCache.size,
      maxCacheSize: this.maxCacheSize,
      memoryThreshold: this.memoryThreshold,
      heapUsageRatio: this.memoryUsage.heapUsed / this.memoryUsage.heapTotal
    };
  }
}

interface MemoryStatistics {
  memoryUsage: {
    heapUsed: number;
    heapTotal: number;
    external: number;
    rss: number;
  };
  cacheSize: number;
  maxCacheSize: number;
  memoryThreshold: number;
  heapUsageRatio: number;
}
```

## 5. 异常情况对比处理表

### 5.1 各种异常情况的处理策略对比

| 异常类型 | etcd处理方式 | Executor内存处理方式 | 恢复策略 |
|---------|-------------|-------------------|---------|
| **连接断开** | • 自动重连机制<br/>• 指数退避重试<br/>• 保持连接状态监控 | • 切换到本地缓存模式<br/>• 保存key状态到磁盘<br/>• 标记etcd不可用 | • 重连成功后重新注册<br/>• 同步本地状态到etcd<br/>• 恢复正常服务 |
| **操作超时** | • 重试机制(最多3次)<br/>• 线性退避策略<br/>• 验证写操作结果 | • 降低key优先级<br/>• 记录超时统计<br/>• 切换到其他key | • 超时key进入冷却期<br/>• 监控网络状况<br/>• 动态调整超时时间 |
| **Key不存在** | • 从etcd中删除引用<br/>• 清理孤立数据<br/>• 记录审计日志 | • 从内存缓存移除<br/>• 触发重新分配<br/>• 更新本地索引 | • 重新从Gateway获取<br/>• 验证key有效性<br/>• 更新分配策略 |
| **存储空间满** | • 清理过期数据<br/>• 压缩存储空间<br/>• 告警通知管理员 | • 强制垃圾回收<br/>• 清理低优先级key<br/>• 减少缓存大小 | • 扩容存储空间<br/>• 优化数据结构<br/>• 调整清理策略 |
| **权限拒绝** | • 重新认证<br/>• 检查证书有效性<br/>• 降级到只读模式 | • 使用备用认证<br/>• 切换到本地模式<br/>• 记录权限错误 | • 更新认证信息<br/>• 重新配置权限<br/>• 验证服务账户 |
| **网络分区** | • 检测分区状态<br/>• 等待网络恢复<br/>• 保持本地状态 | • 进入离线模式<br/>• 使用本地缓存<br/>• 暂停同步操作 | • 网络恢复后重连<br/>• 状态一致性检查<br/>• 冲突解决机制 |
| **内存压力** | • 减少连接池大小<br/>• 限制并发操作<br/>• 清理缓存数据 | • 清理过期key<br/>• 强制垃圾回收<br/>• 进入紧急模式 | • 重启服务释放内存<br/>• 调整内存配置<br/>• 优化内存使用 |
| **CPU过载** | • 降低操作频率<br/>• 延迟非关键操作<br/>• 批量处理请求 | • 暂停key分配<br/>• 减少并发处理<br/>• 优先处理关键key | • 负载均衡调整<br/>• 扩容计算资源<br/>• 优化算法效率 |

### 5.2 LRU算法的详细评分机制

```typescript
// LRU算法评分详细计算
private calculateDetailedKeyScore(key: ManagedApiKey): KeyScoreBreakdown {
  const usage = this.keyUsageMap.get(key.id);
  const now = Date.now();

  const breakdown: KeyScoreBreakdown = {
    baseScore: 100,
    lruScore: 0,
    errorPenalty: 0,
    responseTimePenalty: 0,
    usageBalancePenalty: 0,
    priorityBonus: 0,
    finalScore: 0
  };

  // 1. LRU时间因子 (0-50分)
  const timeSinceLastUse = now - (key.lastUsedAt || 0);
  breakdown.lruScore = Math.min(timeSinceLastUse / (60 * 1000), 50);

  // 2. 错误率惩罚 (0-30分扣除)
  if (usage && usage.usageCount > 0) {
    const errorRate = usage.errorCount / usage.usageCount;
    breakdown.errorPenalty = -(errorRate * 30);
  }

  // 3. 响应时间惩罚 (0-20分扣除)
  if (usage && usage.averageResponseTime > 0) {
    breakdown.responseTimePenalty = -Math.min(usage.averageResponseTime / 1000, 20);
  }

  // 4. 使用频率平衡 (0-15分扣除)
  if (usage && usage.usageCount > 0) {
    const avgUsage = this.getAverageUsageCount();
    if (usage.usageCount > avgUsage * 1.5) {
      breakdown.usageBalancePenalty = -15;
    }
  }

  // 5. 优先级加成 (-10到+10分)
  if (usage && usage.priority !== 0) {
    breakdown.priorityBonus = usage.priority;
  }

  // 计算最终得分
  breakdown.finalScore = Math.max(
    breakdown.baseScore +
    breakdown.lruScore +
    breakdown.errorPenalty +
    breakdown.responseTimePenalty +
    breakdown.usageBalancePenalty +
    breakdown.priorityBonus,
    0
  );

  return breakdown;
}

interface KeyScoreBreakdown {
  baseScore: number;
  lruScore: number;
  errorPenalty: number;
  responseTimePenalty: number;
  usageBalancePenalty: number;
  priorityBonus: number;
  finalScore: number;
}
```

### 5.3 异常恢复的优先级策略

```typescript
// 异常恢复优先级管理
@Injectable()
export class RecoveryPriorityManager {
  private recoveryQueue = new PriorityQueue<RecoveryTask>();

  interface RecoveryTask {
    id: string;
    type: 'etcd_reconnect' | 'key_reallocation' | 'memory_cleanup' | 'service_restart';
    priority: number; // 1-10, 10为最高优先级
    keyId?: string;
    retryCount: number;
    maxRetries: number;
    createdAt: number;
    execute: () => Promise<boolean>;
  }

  async addRecoveryTask(task: Omit<RecoveryTask, 'id' | 'createdAt'>): Promise<void> {
    const recoveryTask: RecoveryTask = {
      ...task,
      id: uuidv4(),
      createdAt: Date.now()
    };

    this.recoveryQueue.enqueue(recoveryTask, task.priority);
    this.processRecoveryQueue();
  }

  private async processRecoveryQueue(): Promise<void> {
    while (!this.recoveryQueue.isEmpty()) {
      const task = this.recoveryQueue.dequeue();
      if (!task) break;

      try {
        const success = await task.execute();

        if (!success && task.retryCount < task.maxRetries) {
          // 重试任务，降低优先级
          task.retryCount++;
          task.priority = Math.max(task.priority - 1, 1);
          this.recoveryQueue.enqueue(task, task.priority);
        } else if (!success) {
          this.logger.error(`Recovery task ${task.id} failed after ${task.maxRetries} attempts`);
          await this.handleRecoveryFailure(task);
        } else {
          this.logger.log(`✅ Recovery task ${task.id} completed successfully`);
        }

      } catch (error) {
        this.logger.error(`Recovery task ${task.id} threw exception: ${error}`);
        await this.handleRecoveryFailure(task);
      }

      // 避免阻塞，给其他任务执行机会
      await new Promise(resolve => setImmediate(resolve));
    }
  }

  private async handleRecoveryFailure(task: RecoveryTask): Promise<void> {
    // 记录失败的恢复任务
    await this.auditService.recordRecoveryFailure({
      taskId: task.id,
      taskType: task.type,
      keyId: task.keyId,
      retryCount: task.retryCount,
      failedAt: Date.now()
    });

    // 根据任务类型采取不同的失败处理策略
    switch (task.type) {
      case 'etcd_reconnect':
        // etcd重连失败，切换到完全本地模式
        await this.keyManagerService.switchToFullLocalMode();
        break;

      case 'key_reallocation':
        // key重新分配失败，标记该key为永久不可用
        if (task.keyId) {
          await this.keyManagerService.markKeyAsPermanentlyUnavailable(task.keyId);
        }
        break;

      case 'memory_cleanup':
        // 内存清理失败，触发服务重启
        await this.serviceManager.scheduleRestart('memory_cleanup_failed');
        break;

      case 'service_restart':
        // 服务重启失败，发送紧急告警
        await this.alertService.sendCriticalAlert('SERVICE_RESTART_FAILED', {
          taskId: task.id,
          timestamp: Date.now()
        });
        break;
    }
  }
}
```

## 总结

这些核心机制确保了系统在各种异常情况下的稳定性和可用性：

1. **etcd重连机制**：通过指数退避和连接监控，确保分布式存储的高可用性
2. **服务降级机制**：多层fallback策略，从Gateway → etcd → 本地缓存，保证服务连续性
3. **LRU算法**：综合考虑时间、错误率、响应时间等因素，智能选择最优key
4. **异常处理**：针对不同异常类型，etcd和内存分别采用相应的处理策略
5. **恢复优先级**：基于优先级队列的恢复机制，确保关键任务优先处理

这些机制协同工作，构建了一个健壮、高效、可靠的三方共享Key管理系统。
```
```
```
