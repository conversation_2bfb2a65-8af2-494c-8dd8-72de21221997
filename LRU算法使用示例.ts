import { LRUKeySelector } from './LRU算法选择Key完整实现';

/**
 * LRU算法选择Key的使用示例和测试
 */

// 模拟API Key数据
const mockApiKeys: ManagedApiKey[] = [
  {
    id: 'key-001',
    key: 'sk-1234567890abcdef',
    status: 'active',
    lastUsedAt: Date.now() - 300000, // 5分钟前
    usageCount: 10,
    errorCount: 1,
    keyId: 'key-001',
    lockedAt: 0,
    lockTimeout: 0,
    provider: 'openai',
    region: 'asia'
  },
  {
    id: 'key-002',
    key: 'sk-abcdef1234567890',
    status: 'active',
    lastUsedAt: Date.now() - 600000, // 10分钟前
    usageCount: 25,
    errorCount: 5,
    keyId: 'key-002',
    lockedAt: 0,
    lockTimeout: 0,
    provider: 'openai',
    region: 'asia'
  },
  {
    id: 'key-003',
    key: 'sk-fedcba0987654321',
    status: 'active',
    lastUsedAt: Date.now() - 60000, // 1分钟前
    usageCount: 5,
    errorCount: 0,
    keyId: 'key-003',
    lockedAt: 0,
    lockTimeout: 0,
    provider: 'openai',
    region: 'asia'
  },
  {
    id: 'key-004',
    key: 'sk-9876543210fedcba',
    status: 'active',
    lastUsedAt: Date.now() - 1800000, // 30分钟前
    usageCount: 50,
    errorCount: 2,
    keyId: 'key-004',
    lockedAt: 0,
    lockTimeout: 0,
    provider: 'openai',
    region: 'asia'
  }
];

interface ManagedApiKey {
  id: string;
  key: string;
  status: 'active' | 'in_use' | 'rate_limited' | 'exhausted' | 'revoked' | 'cooling';
  lastUsedAt: number;
  lastError?: string;
  usageCount: number;
  errorCount: number;
  keyId: string;
  lockedAt: number;
  lockTimeout: number;
  provider: string;
  region: string;
}

/**
 * 使用示例类
 */
class LRUKeyUsageExample {
  private lruSelector: LRUKeySelector;

  constructor() {
    this.lruSelector = new LRUKeySelector();
  }

  /**
   * 示例1: 基本的key选择
   */
  async basicKeySelection() {
    console.log('\n=== 示例1: 基本的Key选择 ===');
    
    const selectedKey = await this.lruSelector.selectOptimalKey(mockApiKeys);
    
    if (selectedKey) {
      console.log(`选中的Key: ${selectedKey.id}`);
      console.log(`使用次数: ${selectedKey.usageCount}`);
      console.log(`错误次数: ${selectedKey.errorCount}`);
      console.log(`最后使用时间: ${new Date(selectedKey.lastUsedAt).toLocaleString()}`);
    } else {
      console.log('没有可用的Key');
    }
  }

  /**
   * 示例2: 模拟性能记录和动态优先级调整
   */
  async performanceRecordingExample() {
    console.log('\n=== 示例2: 性能记录和动态优先级调整 ===');
    
    // 选择一个key
    const selectedKey = await this.lruSelector.selectOptimalKey(mockApiKeys);
    if (!selectedKey) return;

    console.log(`使用Key: ${selectedKey.id}`);

    // 模拟成功的API调用
    console.log('模拟成功调用...');
    await this.lruSelector.recordKeyPerformance(selectedKey.id, 1500, true); // 1.5秒响应时间，成功
    await this.lruSelector.recordKeyPerformance(selectedKey.id, 1200, true); // 1.2秒响应时间，成功
    await this.lruSelector.recordKeyPerformance(selectedKey.id, 800, true);  // 0.8秒响应时间，成功

    // 模拟失败的API调用
    console.log('模拟失败调用...');
    await this.lruSelector.recordKeyPerformance(selectedKey.id, 5000, false, 'Rate limit exceeded'); // 5秒响应时间，失败
    await this.lruSelector.recordKeyPerformance(selectedKey.id, 3000, false, 'API key invalid'); // 3秒响应时间，失败

    // 查看统计信息
    const stats = this.lruSelector.getKeyStatistics();
    const keyStats = stats.find(s => s.keyId === selectedKey.id);
    if (keyStats) {
      console.log(`Key统计信息:`);
      console.log(`  使用次数: ${keyStats.usageCount}`);
      console.log(`  错误次数: ${keyStats.errorCount}`);
      console.log(`  错误率: ${(keyStats.errorRate * 100).toFixed(2)}%`);
      console.log(`  平均响应时间: ${keyStats.averageResponseTime.toFixed(2)}ms`);
      console.log(`  优先级: ${keyStats.priority}`);
      console.log(`  连续错误: ${keyStats.consecutiveErrors}`);
      console.log(`  是否在冷却期: ${keyStats.isInCooldown}`);
    }
  }

  /**
   * 示例3: 冷却机制演示
   */
  async cooldownMechanismExample() {
    console.log('\n=== 示例3: 冷却机制演示 ===');
    
    const selectedKey = await this.lruSelector.selectOptimalKey(mockApiKeys);
    if (!selectedKey) return;

    console.log(`使用Key: ${selectedKey.id} 进行冷却机制测试`);

    // 模拟连续失败，触发冷却机制
    console.log('模拟连续失败调用...');
    for (let i = 0; i < 5; i++) {
      await this.lruSelector.recordKeyPerformance(
        selectedKey.id, 
        2000 + Math.random() * 3000, 
        false, 
        `Error ${i + 1}: Connection timeout`
      );
      console.log(`  失败调用 ${i + 1}/5`);
    }

    // 检查key状态
    const stats = this.lruSelector.getKeyStatistics();
    const keyStats = stats.find(s => s.keyId === selectedKey.id);
    if (keyStats) {
      console.log(`Key状态:`);
      console.log(`  连续错误: ${keyStats.consecutiveErrors}`);
      console.log(`  是否在冷却期: ${keyStats.isInCooldown}`);
      console.log(`  冷却结束时间: ${keyStats.cooldownUntil > 0 ? new Date(keyStats.cooldownUntil).toLocaleString() : 'N/A'}`);
    }

    // 尝试再次选择key（应该跳过冷却中的key）
    console.log('\n尝试重新选择key（应该跳过冷却中的key）...');
    const newSelectedKey = await this.lruSelector.selectOptimalKey(mockApiKeys);
    if (newSelectedKey) {
      console.log(`新选中的Key: ${newSelectedKey.id} (跳过了冷却中的key)`);
    }
  }

  /**
   * 示例4: 负载均衡演示
   */
  async loadBalancingExample() {
    console.log('\n=== 示例4: 负载均衡演示 ===');

    // 模拟不均衡的使用情况
    console.log('模拟不均衡使用...');
    
    // 让key-001被大量使用
    for (let i = 0; i < 20; i++) {
      await this.lruSelector.recordKeyPerformance('key-001', 1000 + Math.random() * 500, true);
    }

    // 让key-002适度使用
    for (let i = 0; i < 5; i++) {
      await this.lruSelector.recordKeyPerformance('key-002', 1200 + Math.random() * 300, true);
    }

    // key-003和key-004很少使用
    await this.lruSelector.recordKeyPerformance('key-003', 800, true);

    // 获取负载均衡报告
    const report = this.lruSelector.getPerformanceReport();
    
    console.log('\n负载均衡统计:');
    console.log(`  总Key数: ${report.summary.totalKeys}`);
    console.log(`  活跃Key数: ${report.summary.activeKeys}`);
    console.log(`  平均使用次数: ${report.summary.averageUsage.toFixed(2)}`);
    console.log(`  最大使用次数: ${report.summary.maxUsage}`);
    console.log(`  最小使用次数: ${report.summary.minUsage}`);
    console.log(`  使用方差: ${report.summary.usageVariance.toFixed(2)}`);

    console.log('\nKey详细信息:');
    report.keyDetails.forEach(key => {
      console.log(`  ${key.keyId}: 使用${key.usageCount}次, 错误率${(key.errorRate * 100).toFixed(1)}%, 优先级${key.priority}`);
    });

    console.log('\n优化建议:');
    report.recommendations.forEach(rec => {
      console.log(`  - ${rec}`);
    });
  }

  /**
   * 示例5: 综合选择演示
   */
  async comprehensiveSelectionExample() {
    console.log('\n=== 示例5: 综合选择演示 ===');

    console.log('进行10次key选择，观察LRU算法的选择策略...');
    
    for (let i = 0; i < 10; i++) {
      console.log(`\n--- 第 ${i + 1} 次选择 ---`);
      
      const selectedKey = await this.lruSelector.selectOptimalKey(mockApiKeys);
      if (selectedKey) {
        console.log(`选中: ${selectedKey.id}`);
        
        // 模拟使用key
        const responseTime = 800 + Math.random() * 2000; // 0.8-2.8秒
        const success = Math.random() > 0.1; // 90%成功率
        
        await this.lruSelector.recordKeyPerformance(selectedKey.id, responseTime, success);
        
        console.log(`  响应时间: ${responseTime.toFixed(0)}ms, 成功: ${success}`);
        
        // 释放key
        await this.lruSelector.releaseKey(selectedKey.id);
      }
      
      // 短暂延迟
      await new Promise(resolve => setTimeout(resolve, 100));
    }

    // 最终统计
    console.log('\n=== 最终统计 ===');
    const finalStats = this.lruSelector.getKeyStatistics();
    finalStats.forEach(stat => {
      console.log(`${stat.keyId}: 使用${stat.usageCount}次, 错误${stat.errorCount}次, 优先级${stat.priority}, 平均响应${stat.averageResponseTime.toFixed(0)}ms`);
    });
  }

  /**
   * 示例6: 管理员功能演示
   */
  async adminFunctionsExample() {
    console.log('\n=== 示例6: 管理员功能演示 ===');

    // 设置key优先级
    console.log('设置key-003的优先级为最高(10)...');
    await this.lruSelector.setKeyPriority('key-003', 10);

    // 设置key-002的优先级为最低(-5)
    console.log('设置key-002的优先级为较低(-5)...');
    await this.lruSelector.setKeyPriority('key-002', -5);

    // 选择key，观察优先级影响
    console.log('\n选择key，观察优先级影响...');
    const selectedKey = await this.lruSelector.selectOptimalKey(mockApiKeys);
    console.log(`选中的key: ${selectedKey?.id} (应该倾向于选择高优先级的key-003)`);

    // 强制重置key统计
    console.log('\n强制重置key-001的所有统计信息...');
    await this.lruSelector.forceResetKey('key-001');

    // 查看重置后的统计
    const stats = this.lruSelector.getKeyStatistics();
    const resetKeyStats = stats.find(s => s.keyId === 'key-001');
    if (resetKeyStats) {
      console.log(`重置后的key-001统计:`);
      console.log(`  使用次数: ${resetKeyStats.usageCount}`);
      console.log(`  错误次数: ${resetKeyStats.errorCount}`);
      console.log(`  优先级: ${resetKeyStats.priority}`);
    }
  }

  /**
   * 运行所有示例
   */
  async runAllExamples() {
    console.log('🚀 开始LRU算法选择Key的完整演示...\n');

    await this.basicKeySelection();
    await this.performanceRecordingExample();
    await this.cooldownMechanismExample();
    await this.loadBalancingExample();
    await this.comprehensiveSelectionExample();
    await this.adminFunctionsExample();

    console.log('\n✅ 所有示例演示完成！');
  }
}

// 运行示例
async function main() {
  const example = new LRUKeyUsageExample();
  await example.runAllExamples();
}

// 如果直接运行此文件
if (require.main === module) {
  main().catch(console.error);
}

export { LRUKeyUsageExample };
