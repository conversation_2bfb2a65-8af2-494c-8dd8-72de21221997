import { Injectable, Logger } from '@nestjs/common';

/**
 * LRU算法选择Key的完整实现
 * 包含综合评分、动态优先级、负载均衡、冷却机制
 */

// 接口定义
interface ManagedApiKey {
  id: string;
  key: string;
  status: 'active' | 'in_use' | 'rate_limited' | 'exhausted' | 'revoked' | 'cooling';
  lastUsedAt: number;
  lastError?: string;
  usageCount: number;
  errorCount: number;
  keyId: string;
  lockedAt: number;
  lockTimeout: number;
  provider: string;
  region: string;
}

interface KeyUsageInfo {
  keyId: string;
  lastUsedAt: number;
  usageCount: number;
  errorCount: number;
  averageResponseTime: number;
  status: 'active' | 'rate_limited' | 'exhausted' | 'revoked' | 'cooling';
  priority: number; // 动态优先级 (-10 到 +10)
  cooldownUntil: number; // 冷却结束时间
  consecutiveErrors: number; // 连续错误次数
  lastErrorTime: number; // 最后错误时间
  totalResponseTime: number; // 总响应时间（用于计算平均值）
}

interface KeyScoreBreakdown {
  keyId: string;
  baseScore: number;
  lruScore: number;
  errorPenalty: number;
  responseTimePenalty: number;
  usageBalancePenalty: number;
  priorityBonus: number;
  cooldownPenalty: number;
  finalScore: number;
  reason: string;
}

interface LoadBalanceStats {
  totalKeys: number;
  activeKeys: number;
  averageUsage: number;
  maxUsage: number;
  minUsage: number;
  usageVariance: number;
}

@Injectable()
export class LRUKeySelector {
  private readonly logger = new Logger(LRUKeySelector.name);
  private keyUsageMap = new Map<string, KeyUsageInfo>();
  
  // 配置参数
  private readonly config = {
    baseScore: 100,
    maxLruScore: 50,        // LRU最大加分
    maxErrorPenalty: 40,    // 错误率最大扣分
    maxResponsePenalty: 25, // 响应时间最大扣分
    overusePenalty: 20,     // 过度使用扣分
    maxPriorityBonus: 15,   // 优先级最大加分
    cooldownPenalty: 50,    // 冷却期扣分
    
    // 冷却机制配置
    baseCooldownMs: 30000,  // 基础冷却时间30秒
    maxCooldownMs: 300000,  // 最大冷却时间5分钟
    errorThreshold: 3,      // 连续错误阈值
    
    // 负载均衡配置
    overuseThreshold: 1.5,  // 过度使用阈值（平均值的1.5倍）
    underuseThreshold: 0.3, // 使用不足阈值（平均值的0.3倍）
  };

  /**
   * 主要方法：使用LRU算法选择最佳的key
   */
  async selectOptimalKey(availableKeys: ManagedApiKey[]): Promise<ManagedApiKey | null> {
    if (availableKeys.length === 0) {
      this.logger.warn('No available keys provided for selection');
      return null;
    }

    // 1. 过滤出可用的key（排除锁定、冷却中的key）
    const usableKeys = this.filterUsableKeys(availableKeys);
    
    if (usableKeys.length === 0) {
      this.logger.warn('No usable keys after filtering');
      return null;
    }

    // 2. 计算每个key的综合得分
    const scoredKeys = usableKeys.map(key => {
      const scoreBreakdown = this.calculateKeyScore(key);
      return {
        key,
        scoreBreakdown,
        score: scoreBreakdown.finalScore
      };
    });

    // 3. 按得分排序，选择最佳key
    scoredKeys.sort((a, b) => b.score - a.score);

    // 4. 记录选择过程（用于调试）
    this.logSelectionProcess(scoredKeys);

    // 5. 选择得分最高的key
    const selectedKey = scoredKeys[0].key;
    
    // 6. 更新key使用统计
    await this.updateKeyUsage(selectedKey);

    // 7. 执行负载均衡检查
    await this.performLoadBalanceCheck();

    this.logger.log(`Selected key ${selectedKey.id} with score ${scoredKeys[0].score.toFixed(2)}`);
    return selectedKey;
  }

  /**
   * 过滤可用的key
   */
  private filterUsableKeys(keys: ManagedApiKey[]): ManagedApiKey[] {
    const now = Date.now();
    
    return keys.filter(key => {
      // 排除非活跃状态的key
      if (key.status !== 'active') {
        return false;
      }

      // 排除被锁定的key
      if (this.isKeyLocked(key)) {
        return false;
      }

      // 排除在冷却期的key
      if (this.isKeyInCooldown(key, now)) {
        return false;
      }

      return true;
    });
  }

  /**
   * 计算key的综合得分
   */
  private calculateKeyScore(key: ManagedApiKey): KeyScoreBreakdown {
    const usage = this.keyUsageMap.get(key.id);
    const now = Date.now();
    
    const breakdown: KeyScoreBreakdown = {
      keyId: key.id,
      baseScore: this.config.baseScore,
      lruScore: 0,
      errorPenalty: 0,
      responseTimePenalty: 0,
      usageBalancePenalty: 0,
      priorityBonus: 0,
      cooldownPenalty: 0,
      finalScore: 0,
      reason: ''
    };

    // 1. LRU时间因子：最近使用时间越久，分数越高
    const timeSinceLastUse = now - (key.lastUsedAt || 0);
    breakdown.lruScore = Math.min(
      (timeSinceLastUse / (60 * 1000)) * 10, // 每分钟10分
      this.config.maxLruScore
    );

    // 2. 错误率惩罚
    if (usage && usage.usageCount > 0) {
      const errorRate = usage.errorCount / usage.usageCount;
      breakdown.errorPenalty = -(errorRate * this.config.maxErrorPenalty);
      
      // 连续错误额外惩罚
      if (usage.consecutiveErrors > 0) {
        breakdown.errorPenalty -= usage.consecutiveErrors * 5;
      }
    }

    // 3. 响应时间惩罚
    if (usage && usage.averageResponseTime > 0) {
      const responseTimePenalty = Math.min(
        usage.averageResponseTime / 1000 * 5, // 每秒5分惩罚
        this.config.maxResponsePenalty
      );
      breakdown.responseTimePenalty = -responseTimePenalty;
    }

    // 4. 使用频率平衡：避免某个key被过度使用
    if (usage && usage.usageCount > 0) {
      const avgUsage = this.getAverageUsageCount();
      if (avgUsage > 0 && usage.usageCount > avgUsage * this.config.overuseThreshold) {
        breakdown.usageBalancePenalty = -this.config.overusePenalty;
      }
      // 对使用不足的key给予奖励
      else if (avgUsage > 0 && usage.usageCount < avgUsage * this.config.underuseThreshold) {
        breakdown.usageBalancePenalty = 10; // 奖励分
      }
    }

    // 5. 动态优先级加成
    if (usage && usage.priority !== 0) {
      breakdown.priorityBonus = (usage.priority / 10) * this.config.maxPriorityBonus;
    }

    // 6. 冷却期检查（虽然已经过滤，但可能刚进入冷却期）
    if (usage && usage.cooldownUntil > now) {
      breakdown.cooldownPenalty = -this.config.cooldownPenalty;
    }

    // 计算最终得分
    breakdown.finalScore = Math.max(
      breakdown.baseScore + 
      breakdown.lruScore + 
      breakdown.errorPenalty + 
      breakdown.responseTimePenalty + 
      breakdown.usageBalancePenalty + 
      breakdown.priorityBonus + 
      breakdown.cooldownPenalty,
      0
    );

    // 生成选择原因
    breakdown.reason = this.generateScoreReason(breakdown);

    return breakdown;
  }

  /**
   * 更新key使用统计
   */
  private async updateKeyUsage(key: ManagedApiKey): Promise<void> {
    const now = Date.now();
    let usage = this.keyUsageMap.get(key.id);

    if (!usage) {
      usage = {
        keyId: key.id,
        lastUsedAt: now,
        usageCount: 0,
        errorCount: 0,
        averageResponseTime: 0,
        status: key.status,
        priority: 0,
        cooldownUntil: 0,
        consecutiveErrors: 0,
        lastErrorTime: 0,
        totalResponseTime: 0
      };
      this.keyUsageMap.set(key.id, usage);
    }

    // 更新使用统计
    usage.lastUsedAt = now;
    usage.usageCount++;
    usage.status = key.status;

    // 更新key对象
    key.lastUsedAt = now;
    key.usageCount = usage.usageCount;
    key.status = 'in_use'; // 标记为使用中
    key.lockedAt = now;
    key.lockTimeout = now + 5 * 60 * 1000; // 5分钟锁定

    this.logger.debug(`Updated usage for key ${key.id}: count=${usage.usageCount}, errors=${usage.errorCount}`);
  }

  /**
   * 记录key性能表现（响应时间、成功/失败）
   */
  async recordKeyPerformance(keyId: string, responseTime: number, success: boolean, errorMessage?: string): Promise<void> {
    const usage = this.keyUsageMap.get(keyId);
    if (!usage) {
      this.logger.warn(`Attempting to record performance for unknown key: ${keyId}`);
      return;
    }

    const now = Date.now();

    // 更新响应时间
    usage.totalResponseTime += responseTime;
    usage.averageResponseTime = usage.totalResponseTime / usage.usageCount;

    if (success) {
      // 成功调用：重置连续错误计数
      usage.consecutiveErrors = 0;

      // 提升优先级（成功率高的key）
      this.adjustKeyPriority(usage, 'success');

    } else {
      // 失败调用：更新错误统计
      usage.errorCount++;
      usage.consecutiveErrors++;
      usage.lastErrorTime = now;

      // 降低优先级
      this.adjustKeyPriority(usage, 'error');

      // 检查是否需要进入冷却期
      if (usage.consecutiveErrors >= this.config.errorThreshold) {
        await this.putKeyInCooldown(keyId, usage, errorMessage);
      }

      this.logger.warn(`Key ${keyId} failed: consecutive errors = ${usage.consecutiveErrors}, total errors = ${usage.errorCount}`);
    }

    // 记录性能指标
    this.logger.debug(`Key ${keyId} performance: responseTime=${responseTime}ms, success=${success}, avgResponseTime=${usage.averageResponseTime.toFixed(2)}ms`);
  }

  /**
   * 动态调整key优先级
   */
  private adjustKeyPriority(usage: KeyUsageInfo, event: 'success' | 'error' | 'timeout'): void {
    const oldPriority = usage.priority;

    switch (event) {
      case 'success':
        // 成功调用提升优先级
        if (usage.usageCount > 5) { // 有足够样本后才调整
          const errorRate = usage.errorCount / usage.usageCount;
          if (errorRate < 0.05) { // 错误率低于5%
            usage.priority = Math.min(usage.priority + 1, 10);
          }
        }

        // 响应时间优秀也提升优先级
        if (usage.averageResponseTime < 2000) { // 小于2秒
          usage.priority = Math.min(usage.priority + 0.5, 10);
        }
        break;

      case 'error':
        // 错误调用降低优先级
        usage.priority = Math.max(usage.priority - 1, -10);

        // 连续错误额外惩罚
        if (usage.consecutiveErrors > 1) {
          usage.priority = Math.max(usage.priority - usage.consecutiveErrors * 0.5, -10);
        }
        break;

      case 'timeout':
        // 超时降低优先级
        usage.priority = Math.max(usage.priority - 0.5, -10);
        break;
    }

    if (oldPriority !== usage.priority) {
      this.logger.debug(`Key ${usage.keyId} priority adjusted: ${oldPriority} -> ${usage.priority} (event: ${event})`);
    }
  }

  /**
   * 将key放入冷却期
   */
  private async putKeyInCooldown(keyId: string, usage: KeyUsageInfo, errorMessage?: string): Promise<void> {
    const now = Date.now();

    // 计算冷却时间：基于连续错误次数
    const cooldownDuration = Math.min(
      this.config.baseCooldownMs * Math.pow(2, usage.consecutiveErrors - this.config.errorThreshold),
      this.config.maxCooldownMs
    );

    usage.cooldownUntil = now + cooldownDuration;
    usage.status = 'cooling';

    this.logger.warn(`Key ${keyId} entered cooldown for ${cooldownDuration / 1000}s due to ${usage.consecutiveErrors} consecutive errors. Last error: ${errorMessage || 'unknown'}`);

    // 通知监控系统
    await this.notifyKeyCooldown(keyId, cooldownDuration, usage.consecutiveErrors, errorMessage);
  }

  /**
   * 检查key是否被锁定
   */
  private isKeyLocked(key: ManagedApiKey): boolean {
    return key.status === 'in_use' &&
           key.lockTimeout &&
           key.lockTimeout > Date.now();
  }

  /**
   * 检查key是否在冷却期
   */
  private isKeyInCooldown(key: ManagedApiKey, now: number = Date.now()): boolean {
    const usage = this.keyUsageMap.get(key.id);
    if (!usage) return false;

    if (usage.cooldownUntil > now) {
      return true;
    }

    // 如果冷却期已过，重置状态
    if (usage.status === 'cooling' && usage.cooldownUntil <= now) {
      usage.status = 'active';
      usage.consecutiveErrors = 0; // 重置连续错误计数
      this.logger.log(`Key ${key.id} cooldown period ended, returning to active status`);
    }

    return false;
  }

  /**
   * 执行负载均衡检查
   */
  private async performLoadBalanceCheck(): Promise<void> {
    const stats = this.getLoadBalanceStats();

    // 如果使用方差过大，进行负载均衡调整
    if (stats.usageVariance > stats.averageUsage * 2) {
      await this.rebalanceKeyUsage(stats);
    }
  }

  /**
   * 重新平衡key使用
   */
  private async rebalanceKeyUsage(stats: LoadBalanceStats): Promise<void> {
    this.logger.log(`Performing load rebalancing: variance=${stats.usageVariance.toFixed(2)}, avg=${stats.averageUsage.toFixed(2)}`);

    for (const [keyId, usage] of this.keyUsageMap.entries()) {
      // 对过度使用的key降低优先级
      if (usage.usageCount > stats.averageUsage * this.config.overuseThreshold) {
        usage.priority = Math.max(usage.priority - 2, -10);
        this.logger.debug(`Reduced priority for overused key ${keyId}: usage=${usage.usageCount}, avg=${stats.averageUsage.toFixed(2)}`);
      }

      // 对使用不足的key提升优先级
      else if (usage.usageCount < stats.averageUsage * this.config.underuseThreshold && usage.usageCount > 0) {
        usage.priority = Math.min(usage.priority + 1, 10);
        this.logger.debug(`Increased priority for underused key ${keyId}: usage=${usage.usageCount}, avg=${stats.averageUsage.toFixed(2)}`);
      }
    }
  }

  /**
   * 获取负载均衡统计信息
   */
  private getLoadBalanceStats(): LoadBalanceStats {
    const usageCounts = Array.from(this.keyUsageMap.values()).map(usage => usage.usageCount);
    const activeKeys = usageCounts.filter(count => count > 0);

    if (activeKeys.length === 0) {
      return {
        totalKeys: this.keyUsageMap.size,
        activeKeys: 0,
        averageUsage: 0,
        maxUsage: 0,
        minUsage: 0,
        usageVariance: 0
      };
    }

    const totalUsage = activeKeys.reduce((sum, count) => sum + count, 0);
    const averageUsage = totalUsage / activeKeys.length;
    const maxUsage = Math.max(...activeKeys);
    const minUsage = Math.min(...activeKeys);

    // 计算方差
    const variance = activeKeys.reduce((sum, count) => sum + Math.pow(count - averageUsage, 2), 0) / activeKeys.length;

    return {
      totalKeys: this.keyUsageMap.size,
      activeKeys: activeKeys.length,
      averageUsage,
      maxUsage,
      minUsage,
      usageVariance: Math.sqrt(variance) // 标准差
    };
  }

  /**
   * 获取平均使用次数
   */
  private getAverageUsageCount(): number {
    if (this.keyUsageMap.size === 0) return 0;

    const totalUsage = Array.from(this.keyUsageMap.values())
      .reduce((sum, usage) => sum + usage.usageCount, 0);

    return totalUsage / this.keyUsageMap.size;
  }

  /**
   * 生成评分原因说明
   */
  private generateScoreReason(breakdown: KeyScoreBreakdown): string {
    const reasons: string[] = [];

    if (breakdown.lruScore > 30) {
      reasons.push('long unused (LRU+)');
    } else if (breakdown.lruScore < 10) {
      reasons.push('recently used (LRU-)');
    }

    if (breakdown.errorPenalty < -20) {
      reasons.push('high error rate');
    } else if (breakdown.errorPenalty > -5) {
      reasons.push('low error rate');
    }

    if (breakdown.responseTimePenalty < -15) {
      reasons.push('slow response');
    } else if (breakdown.responseTimePenalty > -5) {
      reasons.push('fast response');
    }

    if (breakdown.usageBalancePenalty < 0) {
      reasons.push('overused');
    } else if (breakdown.usageBalancePenalty > 0) {
      reasons.push('underused');
    }

    if (breakdown.priorityBonus > 5) {
      reasons.push('high priority');
    } else if (breakdown.priorityBonus < -5) {
      reasons.push('low priority');
    }

    if (breakdown.cooldownPenalty < 0) {
      reasons.push('in cooldown');
    }

    return reasons.length > 0 ? reasons.join(', ') : 'balanced performance';
  }

  /**
   * 记录选择过程（用于调试和监控）
   */
  private logSelectionProcess(scoredKeys: Array<{key: ManagedApiKey, scoreBreakdown: KeyScoreBreakdown, score: number}>): void {
    if (scoredKeys.length <= 3) {
      // 如果key数量少，记录所有key的详细信息
      scoredKeys.forEach((item, index) => {
        this.logger.debug(`Key ${index + 1}: ${item.key.id} - Score: ${item.score.toFixed(2)} (${item.scoreBreakdown.reason})`);
      });
    } else {
      // 如果key数量多，只记录前3名和后3名
      const top3 = scoredKeys.slice(0, 3);
      const bottom3 = scoredKeys.slice(-3);

      this.logger.debug('Top 3 keys:');
      top3.forEach((item, index) => {
        this.logger.debug(`  ${index + 1}. ${item.key.id} - Score: ${item.score.toFixed(2)} (${item.scoreBreakdown.reason})`);
      });

      this.logger.debug('Bottom 3 keys:');
      bottom3.forEach((item, index) => {
        const rank = scoredKeys.length - 2 + index;
        this.logger.debug(`  ${rank}. ${item.key.id} - Score: ${item.score.toFixed(2)} (${item.scoreBreakdown.reason})`);
      });
    }
  }

  /**
   * 通知key进入冷却期
   */
  private async notifyKeyCooldown(keyId: string, cooldownDuration: number, consecutiveErrors: number, errorMessage?: string): Promise<void> {
    // 这里可以集成监控系统、告警系统等
    const notification = {
      type: 'KEY_COOLDOWN',
      keyId,
      cooldownDuration,
      consecutiveErrors,
      errorMessage,
      timestamp: Date.now()
    };

    // 发送到监控系统
    // await this.monitoringService.sendAlert(notification);

    this.logger.warn(`Key cooldown notification: ${JSON.stringify(notification)}`);
  }

  /**
   * 释放key锁定
   */
  async releaseKey(keyId: string): Promise<void> {
    const usage = this.keyUsageMap.get(keyId);
    if (usage) {
      usage.status = 'active';
    }

    this.logger.debug(`Released key ${keyId}`);
  }

  /**
   * 清理过期的key统计信息
   */
  async cleanupExpiredKeys(): Promise<void> {
    const now = Date.now();
    const expiredKeys: string[] = [];
    const maxIdleTime = 24 * 60 * 60 * 1000; // 24小时

    for (const [keyId, usage] of this.keyUsageMap.entries()) {
      // 清理24小时未使用的key统计
      if (now - usage.lastUsedAt > maxIdleTime) {
        expiredKeys.push(keyId);
      }
    }

    expiredKeys.forEach(keyId => {
      this.keyUsageMap.delete(keyId);
    });

    if (expiredKeys.length > 0) {
      this.logger.log(`Cleaned up ${expiredKeys.length} expired key statistics`);
    }
  }

  /**
   * 获取key使用统计
   */
  getKeyStatistics(): Array<KeyUsageInfo & { errorRate: number; isInCooldown: boolean }> {
    const now = Date.now();

    return Array.from(this.keyUsageMap.values()).map(usage => ({
      ...usage,
      errorRate: usage.usageCount > 0 ? usage.errorCount / usage.usageCount : 0,
      isInCooldown: usage.cooldownUntil > now
    }));
  }

  /**
   * 获取详细的性能报告
   */
  getPerformanceReport(): {
    summary: LoadBalanceStats;
    keyDetails: Array<KeyUsageInfo & { errorRate: number; isInCooldown: boolean; scoreBreakdown?: KeyScoreBreakdown }>;
    recommendations: string[];
  } {
    const summary = this.getLoadBalanceStats();
    const keyDetails = this.getKeyStatistics();
    const recommendations: string[] = [];

    // 生成优化建议
    if (summary.usageVariance > summary.averageUsage) {
      recommendations.push('Consider rebalancing key usage - high variance detected');
    }

    const highErrorKeys = keyDetails.filter(key => key.errorRate > 0.2);
    if (highErrorKeys.length > 0) {
      recommendations.push(`${highErrorKeys.length} keys have high error rates (>20%) - consider investigation`);
    }

    const coolingKeys = keyDetails.filter(key => key.isInCooldown);
    if (coolingKeys.length > 0) {
      recommendations.push(`${coolingKeys.length} keys are currently in cooldown period`);
    }

    const slowKeys = keyDetails.filter(key => key.averageResponseTime > 5000);
    if (slowKeys.length > 0) {
      recommendations.push(`${slowKeys.length} keys have slow response times (>5s) - performance issue possible`);
    }

    return {
      summary,
      keyDetails,
      recommendations
    };
  }

  /**
   * 强制重置key状态（管理员功能）
   */
  async forceResetKey(keyId: string): Promise<boolean> {
    const usage = this.keyUsageMap.get(keyId);
    if (!usage) {
      this.logger.warn(`Cannot reset unknown key: ${keyId}`);
      return false;
    }

    // 重置所有统计信息
    usage.errorCount = 0;
    usage.consecutiveErrors = 0;
    usage.priority = 0;
    usage.cooldownUntil = 0;
    usage.status = 'active';
    usage.averageResponseTime = 0;
    usage.totalResponseTime = 0;

    this.logger.log(`Force reset key ${keyId} - all statistics cleared`);
    return true;
  }

  /**
   * 设置key优先级（管理员功能）
   */
  async setKeyPriority(keyId: string, priority: number): Promise<boolean> {
    if (priority < -10 || priority > 10) {
      this.logger.error(`Invalid priority ${priority} for key ${keyId} - must be between -10 and 10`);
      return false;
    }

    const usage = this.keyUsageMap.get(keyId);
    if (!usage) {
      this.logger.warn(`Cannot set priority for unknown key: ${keyId}`);
      return false;
    }

    const oldPriority = usage.priority;
    usage.priority = priority;

    this.logger.log(`Set key ${keyId} priority: ${oldPriority} -> ${priority}`);
    return true;
  }
}
