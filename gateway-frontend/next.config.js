/** @type {import('next').NextConfig} */
const nextConfig = {
  ...(process.env.NEXT_PUBLIC_BASE_PATH !== '/' ? {
    basePath: process.env.NEXT_PUBLIC_BASE_PATH,
    assetPrefix: process.env.NEXT_PUBLIC_BASE_PATH,
  }:{}),
  ...{
    reactStrictMode: true,
    transpilePackages: [
      "@ant-design",
      "antd",
      "rc-util",
      "rc-pagination",
      "rc-picker",
      "rc-tree",
      "rc-input",
      "rc-table"
    ],
    // 只在开发环境使用 Next.js 的 rewrites
    ...(process.env.NODE_ENV === 'development' && process.env.NEXT_PUBLIC_SOURCE_URL && process.env.NEXT_PUBLIC_API_URL ? {
      async rewrites() {
        return [
          {
            source: process.env.NEXT_PUBLIC_SOURCE_URL,
            destination: `${process.env.NEXT_PUBLIC_API_URL}/:path*`
          }
        ]
      }
    } : {}),
    // Fix ESLint configuration
    eslint: {
      ignoreDuringBuilds: true
    }
  }
};

module.exports = nextConfig;
