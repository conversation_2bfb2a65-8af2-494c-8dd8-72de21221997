{"name": "gateway-frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@ant-design/charts": "^2.3.0", "@ant-design/icons": "^5.0.0", "@ant-design/pro-components": "^2.8.7", "@ant-design/web3": "^1.23.0", "@ant-design/web3-wagmi": "^2.10.3", "@noble/ciphers": "^1.3.0", "@tanstack/react-query": "^5.74.11", "@web3modal/ethereum": "^2.7.1", "@web3modal/wagmi": "^5.1.11", "antd": "5.12.0", "axios": "^1.9.0", "date-fns": "^4.1.0", "dayjs": "^1.11.13", "echarts": "^5.6.0", "echarts-for-react": "^3.0.2", "next": "13.5.6", "next-intl": "^4.1.0", "ramda": "^0.30.1", "react": "18.2.0", "react-dom": "18.2.0", "recharts": "^2.15.3", "tweetnacl": "^1.0.3", "wagmi": "^2.15.1", "zod": "^3.24.3", "zustand": "^5.0.5"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20.17.32", "@types/react": "18.2.0", "@types/react-dom": "18.2.0", "eslint": "^9", "eslint-config-next": "13.5.6", "tailwindcss": "^4", "typescript": "^5"}, "resolutions": {"react": "18.2.0", "react-dom": "18.2.0"}}