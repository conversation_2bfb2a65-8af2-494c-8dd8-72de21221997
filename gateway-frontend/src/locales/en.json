{"menu": {"keys": {"apiGroup": "API Management", "apiKeys": "Dashboard", "reports": "Usage Chart", "billing": "Billing & Balance", "models": "Models"}, "earnings": {"title": "Earnings & Rewards", "dashboard": "Earnings Dashboard"}, "nodes": {"title": "Device Management", "description": "Connect your devices to start earning rewards. Monitor your device status and performance in real-time.", "createButton": "Connect New Device", "dashboard": "Device Status"}, "docs": {"title": "Documentation", "sysGroup": "System Management"}}, "header": {"title": "OpenAI API (Bearer Authentication)", "user": "L<PERSON><PERSON>", "profile": "Profile", "logout": "Logout", "language": {"chinese": "中文", "english": "English"}}, "login": {"title": "Sight AI Gateway", "subtitle": "Sign in to your account", "username": "Username", "password": "Password", "usernameRequired": "Please input your username!", "passwordRequired": "Please input your password!", "signIn": "Sign In", "or": "Or", "loginSuccess": "Login successful", "loginFailed": "<PERSON><PERSON> failed. Please check your credentials.", "connectWith": "Connect with", "connectedAddress": "Connected Address", "disconnect": "Disconnect", "connecting": "Connecting to wallet...", "addressCopied": "Address copied to clipboard", "backToHome": "Back to Home", "authenticating": "Authenticating with your wallet...", "footer": "All rights reserved.", "walletConnected": "Your wallet has been connected successfully!", "loginPrompt": "Click \"Sign In\" to authenticate with your wallet.", "connectWallet": "Connect Wallet"}, "lang": "EN", "common": {"back": "Back", "cancel": "Cancel", "confirm": "Confirm", "save": "Save", "delete": "Delete", "edit": "Edit", "view": "View", "loading": "Loading...", "refreshSuccess": "Data refreshed successfully", "refresh": "Refresh Page"}, "apiKeys": {"title": "Overall API Keys", "description": "As an owner of this project, you can view and manage all API keys in this project. <br> Do not share your API key with others or expose it in the browser or other client-side code. To protect your account's security, OpenAI may automatically disable any API key that has leaked publicly.", "createButton": "Create new secret key", "availableChannel": "Available Channel", "table": {"name": "Name", "type": "Type", "secretKey": "Secret Key", "created": "Created", "lastUsed": "Last Used", "createdBy": "Created By", "permissions": "Permissions", "actions": "Actions", "copy": "Copy", "delete": "Delete", "view": "View", "disable": "Disable", "enable": "Enable", "status": "Status", "title": "My API Keys", "refresh": "Refresh", "noData": "No API keys found", "copied": "Copied to clipboard", "deleteConfirmTitle": "Delete API Key", "deleteConfirmMessage": "Are you sure you want to delete this API key? This action cannot be undone.", "deleteConfirm": "Delete", "deleteCancel": "Cancel", "deleteSuccess": "API key deleted successfully", "deleteError": "Failed to delete API key", "enableSuccess": "API key enabled successfully", "disableSuccess": "API key disabled successfully", "updateError": "Failed to update API key"}, "createModal": {"title": "Create New API Key", "description": "Select a API key type you want to create below:", "channelDesc": {"openai": "OpenAI API support for accessing GPT-3.5, GPT-4, and more", "claude": "Anthropic Claude API support for accessing Claude models", "deepseek": "DeepSeek API support for accessing DeepSeek models", "ollama": "Ollama API support for accessing Ollama models"}, "step1": "Name", "step2": "Channel", "step3": "Create", "nameDescription": "Enter a name for your API key to help you identify it later.", "nameLabel": "API Key Name", "nameRequired": "Please enter a name", "namePlaceholder": "e.g. Production API Key", "channelLabel": "Channel", "back": "Back", "next": "Next", "create": "Create", "summaryTitle": "Review Your API Key Details", "summaryNote": "Click 'Create' to generate your API key. The key will be shown only once, so make sure to copy it."}, "generatedModal": {"title": "API Key Generated", "warning": "Important: This API key will only be shown once. Please save it securely as you won't be able to view it again.", "copy": "Copy", "copied": "Copied!", "copyFailed": "Failed to copy", "created": "API Key Created!", "createFailed": "Failed to create API Key", "learnMore": "Learn more about API usage in our documentation →"}}, "detail": {"requests": "API Requests", "tokens": "Token Usage", "cost": "Cost", "lastCharge": "Last time charge: {date}", "tokensChange": "Token Usage Change", "requestsChange": "API Requests Change", "createdAt": "Created", "updatedAt": "Updated", "lastUsed": "Last used", "expirationDate": "Expires", "notFound": "API Key Not Found", "loadError": "Could not load API key details, please check if the key ID is correct", "usageLoadError": "Could not load usage data, but API key information is displayed", "viewAllKeys": "View All API Keys", "quickstartTitle": "Developer quickstart", "quickstartDesc": "Make your first API request in minutes. Learn the basics of the OpenAI platform.", "quickstartTime": "5 min", "quickstartLang": "javascript", "quickstartCopy": "Copy code", "usageChart": {"title": "API Key Usage", "requests": "Requests", "tokens": "Tokens", "cost": "Cost", "totalRequests": "Total Requests", "totalTokens": "Total Tokens", "totalCost": "Total Cost", "avgResponseTime": "Avg Response Time"}, "timeRange": {"days7": "Last 7 days", "days30": "Last 30 days", "days90": "Last 90 days", "year": "Last year"}}, "nodes": {"table": {"gateway": {"server": "Gateway Server", "apiLoad": "API Load (%)", "wsStatus": "WebSocket Connection Status", "taskTime": "Latest Task Processing Time (ms)", "health": "System Health Status"}, "task": {"requestId": "Request ID", "model": "AI Model", "status": "Task Status", "queueTime": "Queue Time (ms)", "deviceId": "Assigned Device ID", "processTime": "Processing Time (ms)", "earnings": "Earnings (SIGHT)"}, "performance": {"deviceId": "Device ID", "deviceType": "Device Type", "currentTasks": "Current Tasks", "memoryUsage": "Memory Usage (%)", "completionRate": "Task Completion Rate (%)", "totalEarnings": "Total Earnings (SIGHT)"}}, "stats": {"earnings": "Total Earnings Distributed", "tasks": "Total Tasks Processed", "nodes": "Total Compute Nodes Connected"}, "sections": {"gatewayStatus": "Gateway Server Status", "taskQueue": "Task Queue", "nodePerformance": "Node Performance Tracking", "more": "More"}, "earnings": {"description": "Monitor your earnings and performance metrics across all connected devices.", "search": "Search tasks or devices..."}, "management": {"description": "Connect and manage your compute devices to earn rewards by contributing to the network.", "createButton": "Active New Device", "deviceTypes": "Available Device Types", "search": "Search devices...", "filters": {"all": "All Devices", "online": "Online", "offline": "Offline", "busy": "Busy"}, "table": {"deviceId": "Device ID", "deviceName": "Device Name", "deviceType": "Device Type", "gpuModel": "GPU Model", "status": "Status", "currentModel": "Current Model", "lastActivity": "Last Activity", "connectedGateway": "Connected Gateway", "totalEarnings": "Total Earnings", "pendingEarnings": "Pending Earnings", "actions": "Actions", "view": "View Details", "disconnect": "Disconnect", "deviceList": "Node Management Overview", "addDevice": "Active New Device", "searchPlaceholder": "Search device name or type", "statusFilter": "Status Filter", "ownerFilter": "<PERSON><PERSON> Owner", "showMyDevicesOnly": "Show My Devices Only", "showAllDevices": "Show All Devices", "search": "Search", "refresh": "Refresh", "totalRecords": "Total {total} records"}, "status": {"online": "Online", "offline": "Offline", "busy": "Busy", "error": "Error", "waiting": "Waiting for Connection", "in-progress": "Connecting", "connected": "Connected", "disconnected": "Disconnected", "failed": "Connection Failed"}, "createSuccess": "<PERSON>ce created successfully", "createFailed": "Failed to create device", "loadFailed": "Failed to load device list", "createModal": {"title": "Connect New Device", "deviceName": "Device Name", "deviceNamePlaceholder": "Enter device name", "deviceNameRequired": "Please enter a device name", "deviceType": "Device Type", "deviceTypeRequired": "Please select a device type", "gpuModel": "GPU Model", "gpuModelPlaceholder": "e.g. NVIDIA RTX 3080", "cancel": "Cancel", "create": "Create", "registerDevice": "Register Device", "back": "Back", "next": "Next", "step1": "Name", "step2": "Device Type", "step3": "Create", "nameDescription": "Enter a name for your device to help you identify it later.", "typeDescription": "Select the type of device you want to connect.", "summaryTitle": "Review Your Device Details", "summaryNote": "Click 'Create' to connect your device. You will need to run a command on your device to complete the connection.", "commandTitle": "Installation Command", "commandDescription": "Run the following command on your device to connect to the network:", "copy": "Copy", "copied": "Copied!", "copyFailed": "Co<PERSON> failed", "waitingTitle": "Waiting for Connection", "waitingDescription": "Establishing connection with your device, please wait...", "connected": "<PERSON>ce connected successfully!", "connectionFailed": "Connection failed", "retry": "Retry", "registerSuccess": "Device registered successfully", "registerFailed": "Device registration failed, please try again", "copySuccess": "Copied to clipboard", "copyFailedMsg": "Co<PERSON> failed", "connectionStatus": "Connection Status", "waitingDevice": "Waiting for device connection...", "deviceConnected": "<PERSON>ce successfully connected!", "deviceConnectionFailed": "Device connection failed", "connectionTimeout": "Connection timeout", "runCommand": "Please run the command below to connect your device (checking every 5 seconds)", "deviceConnectedDesc": "Your device has been successfully connected to the gateway", "checkCommand": "Please check if the command was executed correctly, then try again", "ensureCommand": "Please make sure you have correctly executed the connection command", "connectionInfo": "Connection Information", "oneTimeCode": "One-time Code:", "gatewayAddress": "Gateway Address:", "connectCommand": "Connection Command", "useCommand": "Use the following command to connect your device:", "installInstructions": "Device Connection Steps", "steps": {"step1": "Step 1: Download Program", "step2": "Step 2: Start Program", "step3": "Step 3: View Logs (Optional)", "step4": "Step 4: Register to Gateway", "step5": "Step 5: Report Models", "step6": "Step 6: Refresh <PERSON><PERSON> List"}, "commands": {"download": "Download Link", "start": "Start Command", "logs": "View Logs", "models": "Report Models", "register": "Register Command", "refresh": "Refresh device list to check connection status"}, "platforms": [{"key": "linux", "name": "Linux", "downloadUrl": "https://github.com/sight-ai/sight-depin-maas/releases/download/0.0.1/sightai-linux-x86_64", "executable": "./sightai-linux-x86_64", "startCommand": "./sightai-linux-x86_64 start -d", "logsCommand": "./sightai-linux-x86_64 logs", "modelsCommand": "./sightai-linux-x86_64 models report"}, {"key": "windows", "name": "Windows", "downloadUrl": "https://github.com/sight-ai/sight-depin-maas/releases/download/0.0.1/sightai-win-x86_64.exe", "executable": ".\\sightai-win-x86_64.exe", "startCommand": ".\\sightai-win-x86_64.exe start -d", "logsCommand": ".\\sightai-win-x86_64.exe logs", "modelsCommand": ".\\sightai-win-x86_64.exe models report"}, {"key": "macos x86_64", "name": "macOS", "downloadUrl": "https://github.com/sight-ai/sight-depin-maas/releases/download/0.0.1/sightai-macos-x86_64", "executable": "./sightai-macos-x86_64", "startCommand": "./sightai-macos-x86_64 start -d", "logsCommand": "./sightai-macos-x86_64 logs", "modelsCommand": "./sightai-macos-x86_64 models report"}, {"key": "macos arm64", "name": "macOS (Apple Silicon)", "downloadUrl": "https://github.com/sight-ai/sight-depin-maas/releases/download/0.0.1/sightai-macos-arm64", "executable": "./sightai-macos-arm64", "startCommand": "./sightai-macos-arm64 start -d", "logsCommand": "./sightai-macos-arm64 logs", "modelsCommand": "./sightai-macos-arm64 models report"}], "note": "Note", "noteDesc": "Please ensure your device has downloaded and started the program correctly before running the registration command. After successful registration, the device will automatically connect to the gateway.", "registerAnother": "Register Another Device", "finish": "Finish"}}, "deviceDetail": {"backToList": "Back to <PERSON><PERSON> List", "breadcrumb": {"home": "Home", "deviceManagement": "Device Management", "deviceDetail": "<PERSON><PERSON>", "loading": "Loading device details..."}, "loading": "Loading device details...", "deviceNotFound": "Device Not Found", "deviceNotFoundDesc": "Cannot find device with ID {deviceId}", "tabs": {"deviceInfo": "Device Information", "statusHistory": "Status History", "taskList": "Task List", "models": "Supported Models"}, "stats": {"cpuUsage": "CPU Usage", "memoryUsage": "Memory Usage", "gpuUsage": "GPU Usage", "gpuTemp": "GPU Temperature", "totalEarnings": "Total Earnings", "networkIn": "Network In", "networkOut": "Network Out", "pendingEarnings": "Pending Earnings"}, "basicInfo": "Basic Information", "hardwareInfo": "Hardware Information", "infoItems": {"deviceId": "Device ID", "deviceType": "Device Type", "createdAt": "Created At", "lastActivity": "Last Activity", "ownerAddress": "Owner Address", "rewardAddress": "<PERSON><PERSON> Address", "currentModel": "Current Model", "uptime": "Uptime", "cpuModel": "CPU Model", "cpuCores": "CPU Cores", "cpuThreads": "CPU Threads", "totalMemory": "Total Memory", "gpuModel": "GPU Model", "gpuCount": "GPU Count", "gpuMemory": "GPU Memory", "diskTotal": "Total Disk", "osInfo": "Operating System", "ipAddress": "IP Address"}, "statusHistory": {"loading": "Loading status history...", "noHistory": "No status history available", "statusChanged": "Status changed from {from} to {to}"}, "taskList": {"loading": "Loading task list...", "columns": {"taskId": "Task ID", "model": "Model", "status": "Status", "createdAt": "Created At", "duration": "Duration"}, "status": {"pending": "Pending", "running": "Running", "completed": "Completed", "failed": "Failed", "cancelled": "Cancelled"}}, "refreshStatusHistory": "Refresh Status History", "refreshTaskList": "Refresh Task List", "refreshModels": "Refresh Models List", "unknown": "Unknown", "none": "None", "hours": "hours", "minutes": "minutes", "seconds": "seconds"}, "deviceTypes": {"gpu": "NVIDIA GPU devices for high-performance AI processing", "cpu": "CPU-based devices for general-purpose computing", "mac": "M1/M2/M3 devices optimized for AI workloads", "cloud": "Cloud-based virtual machines for scalable deployment"}, "createWizard": {"title": "Create new device", "subtitle": "Each machine can be registered as a single device. Registering again on the same machine will replace the previous registration. Please install Ollama to use this feature.", "downloadOllama": "Download Ollama", "steps": {"step1": {"title": "Enter your device name", "description": "Name your device"}, "step2": {"title": "Select your device type", "description": "Choose your device type"}, "step3": {"title": "Device connection detail", "description": "Get connection information and complete device connection"}}, "step1": {"title": "Step 1: Enter your device name", "deviceNameLabel": "Device name *", "deviceNameHint": "Input device name (2-3 characters, letters only)", "deviceNamePlaceholder": "Please enter device name", "deviceNameRequired": "Please enter device name", "deviceNameLength": "Device name length should be 2-3 characters", "deviceNamePattern": "Device name can only contain letters", "cancel": "Cancel", "next": "Next"}, "step2": {"title": "Step 2: Select your device type", "deviceTypeLabel": "Device type *", "deviceTypeRequired": "Please select device type", "deviceTypes": {"macosAppleSilicon": {"label": "macOS (Apple Silicon)", "description": "Mac devices with M1, M2, M3 chips"}, "macosIntel": {"label": "macOS (Intel)", "description": "Mac devices with Intel chips"}, "linux": {"label": "Linux", "description": "Linux system devices"}, "windows": {"label": "Windows", "description": "Windows system devices"}}, "previous": "Previous", "register": "Register Device"}, "step3": {"title": "Device connection detail", "subtitle": "Note: If your previous device registration session was interrupted, you can resume it from the Node Management list in the \"Resume\" action.", "successAlert": {"title": "<PERSON>ce created successfully", "description": "Click Copy to connect your device. You need to run command on your device to complete the connection."}, "connectionStatus": {"waiting": "Waiting for device connection...", "connected": "<PERSON>ce connected successfully!", "failed": "Device connection failed", "timeout": "Connection timeout", "waitingDesc": "Please run the command below to connect device (auto-check every 5 seconds)", "connectedDesc": "Your device has been successfully connected to the gateway", "failedDesc": "Please check if the command was executed correctly, then retry", "timeoutDesc": "Please ensure you have run the connection command correctly"}, "connectionInfo": "Connection Information", "oneTimeCode": "One-time code:", "gatewayAddress": "Gateway address:", "copy": "Copy", "copySuccess": "Copied to clipboard", "copyFailed": "Co<PERSON> failed", "finish": "Finished"}, "messages": {"createTaskFailed": "Failed to create connection task, please retry", "deviceRegistered": "Device registered successfully", "deviceRegisterFailed": "Device registration failed", "deviceConnected": "<PERSON>ce connected successfully!", "deviceConnectionFailed": "Device connection failed", "connectionTimeout": "Connection timeout"}}}, "profile": {"title": "Profile", "basicInfo": "Basic Information", "security": "Security Settings", "avatar": {"title": "Avatar", "change": "Change Avatar"}, "role": "Administrator", "form": {"email": "Email", "emailPlaceholder": "Please enter your email", "phone": "Phone", "phonePlaceholder": "Please enter your phone number", "bio": "Bio", "bioPlaceholder": "Please enter your bio", "oldPassword": "Current Password", "oldPasswordPlaceholder": "Please enter your current password", "newPassword": "New Password", "newPasswordPlaceholder": "Please enter your new password", "confirmPassword": "Confirm Password", "confirmPasswordPlaceholder": "Please confirm your new password", "save": "Save Changes", "updatePassword": "Update Password"}, "messages": {"updateSuccess": "Profile updated successfully", "updateFailed": "Update failed, please try again", "passwordUpdateSuccess": "Password updated successfully", "passwordUpdateFailed": "Password update failed, please try again", "passwordMismatch": "The two passwords do not match"}}, "roles": {"switchRole": "Switch Role", "role1": "Staking ", "role2": "Deplorer", "role3": "User", "role4": "Explorer", "role5": "Coming soon"}, "userProvider": {"user": "User", "provider": "Provider"}, "providerKeys": {"title": "Provider API Keys", "description": "Manage your provider API keys to enable AI model access through the SIGHT network. <br> Configure your OpenAI, Claude, DeepSeek, and other provider credentials securely.", "addButton": "Add Provider Key", "availableProviders": "Available Providers", "table": {"provider": "Provider", "keyName": "Key Name", "status": "Status", "created": "Created", "lastUsed": "Last Used", "actions": "Actions", "edit": "Edit", "delete": "Delete", "test": "Test Connection", "enable": "Enable", "disable": "Disable", "noData": "No provider keys found", "refresh": "Refresh", "deleteConfirmTitle": "Delete Provider Key", "deleteConfirmMessage": "Are you sure you want to delete this provider key? This action cannot be undone.", "deleteConfirm": "Delete", "deleteCancel": "Cancel", "deleteSuccess": "Provider key deleted successfully", "deleteError": "Failed to delete provider key", "testSuccess": "Connection test successful", "testError": "Connection test failed", "enableSuccess": "Provider key enabled successfully", "disableSuccess": "Provider key disabled successfully", "updateError": "Failed to update provider key"}, "addModal": {"title": "Add Provider Key", "step1": "Select Provider", "step2": "Configure Key", "step3": "Test & Save", "providerDescription": "Choose the AI provider you want to configure:", "keyDescription": "Enter your API key and configuration details:", "testDescription": "Test the connection and save your configuration:", "providerLabel": "Provider", "keyNameLabel": "Key Name", "keyNamePlaceholder": "e.g., Production OpenAI Key", "apiKeyLabel": "API Key", "apiKeyPlaceholder": "Enter your provider API key", "endpointLabel": "API Endpoint (Optional)", "endpointPlaceholder": "Custom API endpoint URL", "modelLabel": "Available Models", "modelPlaceholder": "Select available models", "keyNameRequired": "Please enter a key name", "apiKeyRequired": "Please enter your API key", "back": "Back", "next": "Next", "test": "Test Connection", "save": "Save", "testing": "Testing...", "saving": "Saving...", "testSuccess": "Connection successful!", "testFailed": "Connection failed. Please check your API key.", "saveSuccess": "Provider key saved successfully", "saveFailed": "Failed to save provider key"}}, "home": {"login": "Please connect wallet", "create": "Active New Device", "signup": "Sign up", "logout": "Log out", "welcome": "Welcome to SIGHT AI Gateway", "description": "Connect your devices and start earning rewards", "getStarted": "Get Started", "learnMore": "Learn More", "features": {"speed": {"title": "Lightning Fast", "description": "Experience ultra-fast AI processing with our distributed network"}, "api": {"title": "Simple API", "description": "Integrate with our easy-to-use API for seamless AI capabilities"}, "dashboard": {"title": "Real-time Dashboard", "description": "Monitor your nodes and earnings with our intuitive dashboard"}, "community": {"title": "Join Community", "description": "Be part of a growing ecosystem of AI enthusiasts and developers"}}, "language": {"select": "Language"}}, "models": {"description": "Browse and manage AI models available in your network.", "search": "Search models...", "filters": {"all": "All Models", "local": "Local Models", "remote": "Remote Models"}, "table": {"name": "Model Name", "provider": "Provider", "type": "Type", "size": "Size", "status": "Status", "actions": "Actions"}, "types": {"local": "Local", "remote": "Remote"}, "status": {"available": "Available", "downloading": "Downloading", "offline": "Offline"}, "actions": {"view": "View Details", "download": "Download Model"}, "detail": {"parameters": "Parameters", "context": "Context Length", "size": "Size", "license": "License", "updated": "Last Updated", "status": "Status"}, "tabs": {"usage": "Usage", "examples": "Code Examples", "api": "API Reference"}, "usage": {"title": "Model Usage", "days": "Days", "requests": "Total Requests", "tokens": "Total Tokens", "avgLatency": "Average Latency", "fromPrevious": "from previous period"}, "examples": {"copied": "Code copied to clipboard!", "copyFailed": "Failed to copy code"}, "api": {"title": "API Endpoints", "description": "Use the following endpoints to interact with this model:", "completions": "Generate text completions based on a prompt", "chat": "Generate conversational responses based on a series of messages", "embeddings": "Generate vector embeddings for input text"}}, "reports": {"title": "API Keys Usage Report", "description": "Monitor and analyze your API usage across all keys and channels.", "timeRange": {"days7": "Last 7 days", "days30": "Last 30 days", "days90": "Last 90 days", "year": "Last year"}, "refresh": "Refresh", "export": "Export", "tabs": {"overview": "Overall Usage", "monthly": "Monthly Usage"}, "monthlyUsage": {"title": "Monthly Usage Chart", "description": "View your monthly usage trends by requests, tokens, and cost."}, "metrics": {"requests": "Requests", "tokens": "Tokens", "cost": "Cost"}, "stats": {"requests": "Total Requests", "tokens": "Total Tokens", "responseTime": "Avg Response Time", "cost": "Cost This Month"}, "channels": {"title": "Channel Usage", "description": "Usage breakdown by channel and model.", "channel": "Channel", "models": "Models", "requests": "Requests", "tokens": "Tokens", "cost": "Cost", "quota": "<PERSON><PERSON><PERSON>", "actions": "Actions", "viewKeys": "View Keys"}}, "nodeManagement": {}}