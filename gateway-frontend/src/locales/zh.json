{"menu": {"keys": {"apiGroup": "API管理", "apiKeys": "API密钥", "reports": "使用报表", "billing": "账单与余额", "models": "AI模型"}, "earnings": {"title": "收益与奖励", "dashboard": "收益仪表盘"}, "nodes": {"title": "设备管理", "description": "连接您的设备开始获取收益。实时监控设备状态和性能。", "createButton": "连接新设备", "dashboard": "设备状态"}, "docs": {"title": "文档", "sysGroup": "系统管理"}}, "header": {"title": "OpenAI API（Bearer认证）", "user": "", "profile": "个人中心", "logout": "退出登录", "language": {"chinese": "中文", "english": "English"}}, "login": {"title": "Sight AI Gateway", "subtitle": "登录您的账户", "username": "用户名", "password": "密码", "usernameRequired": "请输入用户名", "passwordRequired": "请输入密码", "signIn": "登录", "or": "或", "loginSuccess": "登录成功", "loginFailed": "登录失败，请检查您的凭据", "connectWith": "使用", "connectedAddress": "已连接钱包地址", "disconnect": "断开连接", "connecting": "正在连接钱包...", "addressCopied": "地址已复制到剪贴板", "backToHome": "返回首页", "authenticating": "正在使用您的钱包进行身份验证...", "footer": "版权所有", "walletConnected": "您的钱包已成功连接！", "loginPrompt": "点击“登录”使用您的钱包进行身份验证。", "connectWallet": "连接钱包"}, "lang": "中文", "common": {"back": "返回", "cancel": "取消", "confirm": "确认", "save": "保存", "delete": "删除", "edit": "编辑", "view": "查看", "loading": "加载中...", "refreshSuccess": "数据刷新成功", "refresh": "刷新页面"}, "apiKeys": {"title": "API密钥管理", "description": "作为项目所有者，您可以查看和管理此项目中的所有API密钥。请勿与他人共享您的API密钥或在浏览器或其他客户端代码中公开它。为了保护您账户的安全，如果API密钥泄露，系统可能会自动禁用它。", "createButton": "创建新密钥", "availableChannel": "可用渠道", "table": {"name": "名称", "type": "类型", "secretKey": "密钥", "created": "创建时间", "lastUsed": "最后使用", "createdBy": "创建者", "permissions": "权限", "actions": "操作", "copy": "复制", "delete": "删除", "view": "查看", "disable": "禁用", "enable": "启用", "status": "状态", "title": "您的API密钥", "refresh": "刷新", "noData": "未找到API密钥", "copied": "已复制到剪贴板", "deleteConfirmTitle": "删除API密钥", "deleteConfirmMessage": "确定要删除此API密钥吗？此操作无法撤销。", "deleteConfirm": "删除", "deleteCancel": "取消", "deleteSuccess": "API密钥删除成功", "deleteError": "删除API密钥失败", "enableSuccess": "API密钥已启用", "disableSuccess": "API密钥已禁用", "updateError": "更新API密钥失败"}, "createModal": {"title": "创建新的 API 密钥", "description": "请选择要创建的 API 密钥类型：", "channelDesc": {"openai": "OpenAI API 支持，用于访问 GPT-3.5、GPT-4 等模型", "claude": "Anthropic Claude API 支持，用于访问 Claude 系列模型", "deepseek": "DeepSeek API 支持，用于访问 DeepSeek 系列模型", "ollama": "Ollama API 支持，用于访问 Ollama 系列模型"}, "step1": "命名", "step2": "选择渠道", "step3": "创建", "nameDescription": "为您的 API 密钥输入一个名称，以便于以后识别。", "nameLabel": "API 密钥名称", "nameRequired": "请输入名称", "namePlaceholder": "例如：生产环境 API 密钥", "channelLabel": "渠道", "back": "返回", "next": "下一步", "create": "创建", "summaryTitle": "确认您的 API 密钥详情", "summaryNote": "点击“创建”生成您的 API 密钥。密钥将只显示一次，请确保复制保存。"}, "generatedModal": {"title": "API 密钥已生成", "warning": "重要提示：此 API 密钥仅显示一次，请妥善保存，因为您将无法再次查看它。", "copy": "复制", "copied": "已复制！", "copyFailed": "复制失败", "created": "API 密钥已创建！", "createFailed": "创建 API 密钥失败", "learnMore": "在文档中了解更多 API 使用方法 →"}}, "detail": {"requests": "API 请求数", "tokens": "Token 用量", "cost": "费用", "tokensChange": "Token 用量变化", "requestsChange": "API 请求数变化", "lastCharge": "上次扣费时间: {date}", "createdAt": "创建时间", "updatedAt": "更新时间", "lastUsed": "最后使用时间", "expirationDate": "过期时间", "notFound": "找不到API密钥", "loadError": "无法加载API密钥详情，请确认密钥ID是否正确", "usageLoadError": "无法加载使用数据，但API密钥信息已显示", "viewAllKeys": "查看所有API密钥", "quickstartTitle": "开发者快速上手", "quickstartDesc": "几分钟内完成你的第一个 API 请求，了解 OpenAI 平台的基础。", "quickstartTime": "5 分钟", "quickstartLang": "javascript", "quickstartCopy": "复制代码", "usageChart": {"title": "API 密钥使用情况", "requests": "请求数", "tokens": "Token数", "cost": "费用", "totalRequests": "总请求数", "totalTokens": "总Token数", "totalCost": "总费用", "avgResponseTime": "平均响应时间"}, "timeRange": {"days7": "最近7天", "days30": "最近30天", "days90": "最近90天", "year": "最近一年"}}, "profile": {"title": "个人中心", "basicInfo": "基本信息", "security": "安全设置", "avatar": {"title": "头像", "change": "更换头像"}, "role": "管理员", "form": {"email": "邮箱", "emailPlaceholder": "请输入邮箱", "phone": "手机号", "phonePlaceholder": "请输入手机号", "bio": "个人简介", "bioPlaceholder": "请输入个人简介", "oldPassword": "当前密码", "oldPasswordPlaceholder": "请输入当前密码", "newPassword": "新密码", "newPasswordPlaceholder": "请输入新密码", "confirmPassword": "确认新密码", "confirmPasswordPlaceholder": "请确认新密码", "save": "保存修改", "updatePassword": "更新密码"}, "messages": {"updateSuccess": "个人信息更新成功", "updateFailed": "更新失败，请重试", "passwordUpdateSuccess": "密码更新成功", "passwordUpdateFailed": "密码更新失败，请重试", "passwordMismatch": "两次输入的密码不一致"}}, "roles": {"switchRole": "切换角色", "role1": "开发者", "role2": "节点运营商", "role3": "API用户", "role4": "设备管理员", "role5": "系统管理员"}, "userProvider": {"user": "用户", "provider": "提供商"}, "providerKeys": {"title": "提供商API密钥", "description": "管理您的提供商API密钥，通过SIGHT网络启用AI模型访问。<br>安全配置您的OpenAI、<PERSON>、DeepSeek和其他提供商凭据。", "addButton": "添加提供商密钥", "availableProviders": "可用提供商", "table": {"provider": "提供商", "keyName": "密钥名称", "status": "状态", "created": "创建时间", "lastUsed": "最后使用", "actions": "操作", "edit": "编辑", "delete": "删除", "test": "测试连接", "enable": "启用", "disable": "禁用", "noData": "未找到提供商密钥", "refresh": "刷新", "deleteConfirmTitle": "删除提供商密钥", "deleteConfirmMessage": "确定要删除此提供商密钥吗？此操作无法撤销。", "deleteConfirm": "删除", "deleteCancel": "取消", "deleteSuccess": "提供商密钥删除成功", "deleteError": "删除提供商密钥失败", "testSuccess": "连接测试成功", "testError": "连接测试失败", "enableSuccess": "提供商密钥已启用", "disableSuccess": "提供商密钥已禁用", "updateError": "更新提供商密钥失败"}, "addModal": {"title": "添加提供商密钥", "step1": "选择提供商", "step2": "配置密钥", "step3": "测试并保存", "providerDescription": "选择您要配置的AI提供商：", "keyDescription": "输入您的API密钥和配置详情：", "testDescription": "测试连接并保存您的配置：", "providerLabel": "提供商", "keyNameLabel": "密钥名称", "keyNamePlaceholder": "例如：生产环境OpenAI密钥", "apiKeyLabel": "API密钥", "apiKeyPlaceholder": "输入您的提供商API密钥", "endpointLabel": "API端点（可选）", "endpointPlaceholder": "自定义API端点URL", "modelLabel": "可用模型", "modelPlaceholder": "选择可用模型", "keyNameRequired": "请输入密钥名称", "apiKeyRequired": "请输入您的API密钥", "back": "返回", "next": "下一步", "test": "测试连接", "save": "保存", "testing": "测试中...", "saving": "保存中...", "testSuccess": "连接成功！", "testFailed": "连接失败，请检查您的API密钥。", "saveSuccess": "提供商密钥保存成功", "saveFailed": "保存提供商密钥失败"}}, "home": {"login": "请连接钱包", "create": "激活新设备", "signup": "注册", "logout": "退出登录", "welcome": "欢迎使用 SIGHT AI Gateway", "description": "连接您的设备，开始获取收益", "getStarted": "开始使用", "learnMore": "了解更多", "features": {"speed": {"title": "极速处理", "description": "通过我们的分布式网络体验超快的AI处理能力"}, "api": {"title": "简单API", "description": "集成我们易于使用的API，获得无缝的AI能力"}, "dashboard": {"title": "实时仪表盘", "description": "通过直观的仪表盘监控您的节点和收益"}, "community": {"title": "加入社区", "description": "成为不断发展的AI爱好者和开发者生态系统的一部分"}}, "language": {"select": "语言"}}, "models": {"description": "浏览和管理网络中可用的AI模型。", "search": "搜索模型...", "filters": {"all": "所有模型", "local": "本地模型", "remote": "远程模型"}, "table": {"name": "模型名称", "provider": "提供商", "type": "类型", "size": "大小", "status": "状态", "actions": "操作"}, "types": {"local": "本地", "remote": "远程"}, "status": {"available": "可用", "downloading": "下载中", "offline": "离线"}, "actions": {"view": "查看详情", "download": "下载模型"}, "detail": {"parameters": "参数量", "context": "上下文长度", "size": "大小", "license": "许可证", "updated": "最后更新", "status": "状态"}, "tabs": {"overview": "整体使用情况", "monthly": "每月使用情况", "usage": "使用情况", "examples": "代码示例", "api": "API参考"}, "monthlyUsage": {"title": "月度使用图表", "description": "查看您的请求量、令牌数和成本的月度使用趋势。"}, "usage": {"title": "模型使用情况", "days": "天", "requests": "总请求数", "tokens": "总Token数", "avgLatency": "平均延迟", "fromPrevious": "相比上一周期"}, "examples": {"copied": "代码已复制到剪贴板！", "copyFailed": "复制代码失败"}, "api": {"title": "API端点", "description": "使用以下端点与此模型交互：", "completions": "根据提示生成文本补全", "chat": "根据一系列消息生成对话响应", "embeddings": "为输入文本生成向量嵌入"}}, "reports": {"title": "API密钥使用报表", "description": "监控和分析所有密钥和渠道的API使用情况。", "timeRange": {"days7": "最近7天", "days30": "最近30天", "days90": "最近90天", "year": "最近一年"}, "refresh": "刷新", "export": "导出", "tabs": {"overview": "整体使用情况", "monthly": "每月使用情况"}, "monthlyUsage": {"title": "月度使用图表", "description": "查看您的请求量、令牌数和成本的月度使用趋势。"}, "metrics": {"requests": "请求数", "tokens": "Token数", "cost": "费用"}, "stats": {"requests": "总请求数", "tokens": "总Token数", "responseTime": "平均响应时间", "cost": "本月费用"}, "channels": {"title": "渠道使用情况", "description": "按渠道和模型划分的使用情况。", "channel": "渠道", "models": "模型", "requests": "请求数", "tokens": "Token数", "cost": "费用", "quota": "配额使用", "actions": "操作", "viewKeys": "查看密钥"}}, "nodes": {"stats": {"earnings": "总收益分配", "tasks": "总任务处理数", "nodes": "总计算节点连接数"}, "sections": {"gatewayStatus": "网关服务器状态", "taskQueue": "任务队列", "nodePerformance": "节点性能追踪", "more": "更多"}, "earnings": {"description": "监控所有已连接设备的收益和性能指标。", "search": "搜索任务或设备..."}, "management": {"description": "连接和管理您的计算设备，通过为网络做贡献来获取奖励。", "createButton": "连接新设备", "deviceTypes": "可用设备类型", "search": "搜索设备...", "filters": {"all": "所有设备", "online": "在线", "offline": "离线", "busy": "忙碌"}, "table": {"deviceId": "设备ID", "deviceName": "设备名称", "deviceType": "设备类型", "gpuModel": "GPU型号", "status": "状态", "currentModel": "当前模型", "lastActivity": "最后活动", "connectedGateway": "已连接网关", "totalEarnings": "总收益", "pendingEarnings": "待结算收益", "actions": "操作", "view": "查看详情", "disconnect": "断开连接", "deviceList": "设备列表", "addDevice": "添加设备", "searchPlaceholder": "搜索设备名称或类型", "statusFilter": "状态筛选", "ownerFilter": "设备归属", "showMyDevicesOnly": "只显示我的设备", "showAllDevices": "显示所有设备", "search": "搜索", "refresh": "刷新", "totalRecords": "共 {total} 条记录"}, "status": {"online": "在线", "offline": "离线", "busy": "忙碌", "error": "错误", "waiting": "等待连接", "in-progress": "连接中", "connected": "已连接", "disconnected": "已断开", "failed": "连接失败"}, "createSuccess": "设备创建成功", "createFailed": "设备创建失败", "loadFailed": "加载设备列表失败", "createModal": {"title": "连接新设备", "deviceName": "设备名称", "deviceNamePlaceholder": "请输入设备名称", "deviceNameRequired": "请输入设备名称", "deviceType": "设备类型", "deviceTypeRequired": "请选择设备类型", "gpuModel": "GPU型号", "gpuModelPlaceholder": "例如：NVIDIA RTX 3080", "cancel": "取消", "create": "创建", "registerDevice": "注册设备", "back": "返回", "next": "下一步", "step1": "命名", "step2": "设备类型", "step3": "创建", "nameDescription": "为您的设备输入一个名称，以便于以后识别。", "typeDescription": "选择您要连接的设备类型。", "summaryTitle": "确认您的设备详情", "summaryNote": "点击“创建”连接您的设备。您需要在设备上运行命令来完成连接。", "commandTitle": "安装命令", "commandDescription": "在您的设备上运行以下命令以连接到网络：", "copy": "复制", "copied": "已复制！", "copyFailed": "复制失败", "waitingTitle": "等待连接", "waitingDescription": "正在与您的设备建立连接，请稍候...", "connected": "设备连接成功！", "connectionFailed": "连接失败", "retry": "重试", "registerSuccess": "设备注册成功", "registerFailed": "设备注册失败，请重试", "copySuccess": "已复制到剪贴板", "copyFailedMsg": "复制失败", "connectionStatus": "连接状态", "waitingDevice": "等待设备连接...", "deviceConnected": "设备已成功连接！", "deviceConnectionFailed": "设备连接失败", "connectionTimeout": "连接超时", "runCommand": "请运行下方命令连接设备（每5秒自动检查一次）", "deviceConnectedDesc": "您的设备已成功连接到网关", "checkCommand": "请检查命令是否正确执行，然后重试", "ensureCommand": "请确保您已正确运行连接命令", "connectionInfo": "连接信息", "oneTimeCode": "一次性代码：", "gatewayAddress": "网关地址：", "connectCommand": "连接命令", "useCommand": "使用以下命令连接设备：", "installInstructions": "设备连接步骤", "steps": {"step1": "第一步：下载程序", "step2": "第二步：启动程序", "step3": "第三步：查看日志（可选）", "step4": "第四步：注册到网关", "step5": "第五步：上报模型", "step6": "第六步：刷新设备列表"}, "commands": {"download": "下载链接", "start": "启动命令", "logs": "查看日志", "register": "注册命令", "models": "上报模型", "refresh": "刷新设备列表查看连接状态"}, "platforms": [{"key": "linux", "name": "Linux", "downloadUrl": "https://github.com/sight-ai/sight-depin-maas/releases/download/0.0.1/sightai-linux-x86_64", "executable": "./sightai-linux-x86_64", "startCommand": "./sightai-linux-x86_64 start -d", "logsCommand": "./sightai-linux-x86_64 logs", "modelsCommand": "./sightai-linux-x86_64 models report"}, {"key": "windows", "name": "Windows", "downloadUrl": "https://github.com/sight-ai/sight-depin-maas/releases/download/0.0.1/sightai-win-x86_64.exe", "executable": ".\\sightai-win-x86_64.exe", "startCommand": ".\\sightai-win-x86_64.exe start -d", "logsCommand": ".\\sightai-win-x86_64.exe logs", "modelsCommand": ".\\sightai-win-x86_64.exe models report"}, {"key": "macos x86_64", "name": "macOS", "downloadUrl": "https://github.com/sight-ai/sight-depin-maas/releases/download/0.0.1/sightai-macos-x86_64", "executable": "./sightai-macos-x86_64", "startCommand": "./sightai-macos-x86_64 start -d", "logsCommand": "./sightai-macos-x86_64 logs", "modelsCommand": "./sightai-macos-x86_64 models report"}, {"key": "macos arm64", "name": "macOS (Apple Silicon)", "downloadUrl": "https://github.com/sight-ai/sight-depin-maas/releases/download/0.0.1/sightai-macos-arm64", "executable": "./sightai-macos-arm64", "startCommand": "./sightai-macos-arm64 start -d", "logsCommand": "./sightai-macos-arm64 logs", "modelsCommand": "./sightai-macos-arm64 models report"}], "note": "注意事项", "noteDesc": "请确保您的设备已正确下载并启动程序后，再运行注册命令。注册成功后，设备将自动连接到网关。", "registerAnother": "注册另一个设备", "finish": "完成"}, "createWizard": {"title": "创建新设备", "subtitle": "每台机器都可以注册为一个单独的设备。在同一台机器上注册多个设备将替换之前的注册。请安装 Ollama 以使用此功能。", "downloadOllama": "下载 Ollama", "steps": {"step1": {"title": "输入设备名称", "description": "为您的设备命名"}, "step2": {"title": "选择设备类型", "description": "选择您的设备类型"}, "step3": {"title": "设备连接详情", "description": "获取连接信息并完成设备连接"}}, "step1": {"title": "第1步：输入设备名称", "deviceNameLabel": "设备名称 *", "deviceNameHint": "输入设备名称（长度为2-3个字符，仅限字母）", "deviceNamePlaceholder": "请输入设备名称", "deviceNameRequired": "请输入设备名称", "deviceNameLength": "设备名称长度为2-3个字符", "deviceNamePattern": "设备名称只能包含字母", "cancel": "取消", "next": "下一步"}, "step2": {"title": "第2步：选择设备类型", "deviceTypeLabel": "设备类型 *", "deviceTypeRequired": "请选择设备类型", "deviceTypes": {"macosAppleSilicon": {"label": "macOS (Apple Silicon)", "description": "M1, M2, M3 芯片的 Mac 设备"}, "macosIntel": {"label": "macOS (Intel)", "description": "Intel 芯片的 Mac 设备"}, "linux": {"label": "Linux", "description": "Linux 系统设备"}, "windows": {"label": "Windows", "description": "Windows 系统设备"}}, "previous": "上一步", "register": "注册设备"}, "step3": {"title": "设备连接详情", "subtitle": "注意：如果您之前的设备注册会话被中断，您可以从\"恢复\"操作中的节点管理列表中恢复。", "successAlert": {"title": "设备创建成功", "description": "点击复制连接您的设备。您需要在设备上运行命令来完成连接。"}, "connectionStatus": {"waiting": "等待设备连接...", "connected": "设备已成功连接！", "failed": "设备连接失败", "timeout": "连接超时", "waitingDesc": "请运行下方命令连接设备（每5秒自动检查一次）", "connectedDesc": "您的设备已成功连接到网关", "failedDesc": "请检查命令是否正确执行，然后重试", "timeoutDesc": "请确保您已正确运行连接命令"}, "connectionInfo": "连接信息", "oneTimeCode": "一次性代码：", "gatewayAddress": "网关地址：", "copy": "复制", "copySuccess": "已复制到剪贴板", "copyFailed": "复制失败", "finish": "完成"}, "messages": {"createTaskFailed": "创建连接任务失败，请重试", "deviceRegistered": "设备注册成功", "deviceRegisterFailed": "设备注册失败", "deviceConnected": "设备连接成功！", "deviceConnectionFailed": "设备连接失败", "connectionTimeout": "连接超时"}}}, "deviceDetail": {"breadcrumb": {"home": "首页", "deviceManagement": "设备管理", "deviceDetail": "设备详情"}, "backToList": "返回设备列表", "loading": "加载设备详情...", "deviceNotFound": "未找到设备", "deviceNotFoundDesc": "无法找到ID为 {deviceId} 的设备", "tabs": {"deviceInfo": "设备信息", "statusHistory": "状态历史", "taskList": "任务列表", "models": "支持的模型", "overview": "整体使用情况", "monthly": "每月使用情况"}, "monthlyUsage": {"title": "月度使用图表", "description": "查看您的请求量、令牌数和成本的月度使用趋势。"}, "stats": {"cpuUsage": "CPU使用率", "memoryUsage": "内存使用", "gpuUsage": "GPU使用率", "gpuTemp": "GPU温度", "totalEarnings": "总收益", "networkIn": "网络入站", "networkOut": "网络出站", "pendingEarnings": "待结算收益"}, "basicInfo": "基本信息", "hardwareInfo": "硬件信息", "infoItems": {"deviceId": "设备ID", "deviceType": "设备类型", "createdAt": "创建时间", "lastActivity": "最后活动", "ownerAddress": "所有者地址", "rewardAddress": "奖励地址", "currentModel": "当前模型", "uptime": "运行时间", "cpuModel": "CPU型号", "cpuCores": "CPU核心数", "cpuThreads": "CPU线程数", "totalMemory": "内存总量", "gpuModel": "GPU型号", "gpuCount": "GPU数量", "gpuMemory": "GPU内存", "diskTotal": "磁盘总量", "osInfo": "操作系统", "ipAddress": "IP地址"}, "statusHistory": {"loading": "加载状态历史...", "noHistory": "暂无状态历史记录", "statusChanged": "状态从 {from} 变更为 {to}"}, "taskList": {"loading": "加载任务列表...", "columns": {"taskId": "任务ID", "model": "模型", "status": "状态", "createdAt": "创建时间", "duration": "总时长"}, "status": {"pending": "等待中", "running": "运行中", "completed": "已完成", "failed": "失败", "cancelled": "已取消"}}, "refreshStatusHistory": "刷新状态历史", "refreshTaskList": "刷新任务列表", "refreshModels": "刷新模型列表", "unknown": "未知", "none": "无", "hours": "小时", "minutes": "分钟", "seconds": "秒"}, "deviceTypes": {"gpu": "NVIDIA GPU设备，用于高性能AI处理", "cpu": "基于CPU的设备，用于通用计算", "mac": "M1/M2/M3设备，针对AI工作负载优化", "cloud": "基于云的虚拟机，用于可扩展部署"}, "table": {"gateway": {"server": "网关服务器", "apiLoad": "API负载 (%)", "wsStatus": "WebSocket连接状态", "taskTime": "最新任务处理时间 (ms)", "health": "系统健康状态"}, "task": {"requestId": "请求ID", "model": "AI模型", "status": "任务状态", "queueTime": "队列时间 (ms)", "deviceId": "设备ID", "processTime": "处理时间 (ms)", "earnings": "收益 (SIGHT)"}, "performance": {"deviceId": "设备ID", "deviceType": "设备类型", "currentTasks": "当前任务数", "memoryUsage": "内存使用率 (%)", "completionRate": "任务完成率 (%)", "totalEarnings": "总收益 (SIGHT)"}}}}