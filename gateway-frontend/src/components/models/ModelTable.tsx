'use client';

import { Table, Tag, message, Card } from 'antd';
import { useLocaleContext } from '@/contexts/LocaleContext';
import { useState, useEffect } from 'react';
import styles from './ModelTable.module.css';
import { nodeApi } from '@/api/node';

interface DeviceModel {
  id: string;
  device_id: string;
  model_name: string;
  model_family?: string | null;
  parameter_size?: string | null;
  quantization_level?: string | null;
  format?: string | null;
  size_bytes?: number | null;
  digest?: string | null;
  modified_at?: string | null;
  created_at: string;
  updated_at: string;
}

interface ModelData {
  id: string;
  name: string;
  provider: string;
  type: string;
  size: string;
  status: 'available' | 'downloading' | 'offline';
  tags: string[];
}

export default function ModelTable({
  searchText,
  filterType,
  deviceId,
  refreshTrigger = 0
}: {
  searchText: string;
  filterType: string;
  deviceId?: string;
  refreshTrigger?: number;
}) {
  const { messages } = useLocaleContext();
  const [loading, setLoading] = useState(false);

  // Data source
  const [data, setData] = useState<ModelData[]>([]);

  // 获取模型数据
  useEffect(() => {
    const fetchModels = async () => {
      setLoading(true);
      try {
        // 根据是否提供了 deviceId 参数来决定调用哪个 API
        const response = deviceId
          ? await nodeApi.getDeviceModels(deviceId)
          : await nodeApi.getModels();

        if (response.success && response.data) {
          // 将 API 返回的数据转换为组件需要的格式
          const modelData: ModelData[] = response.data.map((model: DeviceModel) => {
            // 从模型名称中提取提供商信息
            let provider = 'Unknown';
            if (model.model_name.toLowerCase().includes('llama')) {
              provider = 'Meta';
            } else if (model.model_name.toLowerCase().includes('mistral')) {
              provider = 'Mistral AI';
            } else if (model.model_name.toLowerCase().includes('deepseek')) {
              provider = 'DeepSeek';
            } else if (model.model_name.toLowerCase().includes('gpt')) {
              provider = 'OpenAI';
            } else if (model.model_name.toLowerCase().includes('claude')) {
              provider = 'Anthropic';
            } else if (model.model_name.toLowerCase().includes('gemma')) {
              provider = 'Google';
            }

            // 计算模型大小的可读格式
            const sizeInGB = model.size_bytes ? (model.size_bytes / (1024 * 1024 * 1024)).toFixed(2) + ' GB' : 'Unknown';

            // 生成标签
            const tags: string[] = [];
            if (model.parameter_size) tags.push(model.parameter_size);
            if (model.quantization_level) tags.push(model.quantization_level);
            if (model.format) tags.push(model.format.toUpperCase());

            return {
              id: model.id,
              name: model.model_name,
              provider,
              type: 'local', // 设备上的模型都是本地模型
              size: sizeInGB,
              status: 'available', // 假设设备上的模型都是可用的
              tags
            };
          });

          setData(modelData);
        }
      } catch (error) {
        console.error('Failed to fetch models:', error);
        if (deviceId) {
          message.error('Failed to load device models');
        } else {
          message.error('Failed to load models');
        }
      } finally {
        setLoading(false);
      }
    };

    fetchModels();
  }, [deviceId, refreshTrigger]);

  // Filter data
  const filteredData = data.filter(model => {
    const matchesSearch = searchText === '' ||
      model.name.toLowerCase().includes(searchText.toLowerCase()) ||
      model.provider.toLowerCase().includes(searchText.toLowerCase()) ||
      model.tags.some(tag => tag.toLowerCase().includes(searchText.toLowerCase()));

    const matchesFilter = filterType === 'all' || model.type === filterType;

    return matchesSearch && matchesFilter;
  });

  const columns = [
    {
      title: messages.models?.table?.name,
      dataIndex: 'name',
      key: 'name',
      align: 'center' as const,
      render: (text: string, record: ModelData) => (
        <div className={styles.nameCell}>
          <span className={styles.modelName}>{text}</span>
          <div>
            {record.tags.map(tag => (
              <Tag key={tag} color="blue" className={styles.tag}>
                {tag}
              </Tag>
            ))}
          </div>
        </div>
      ),
    },
    {
      title: messages.models?.table?.provider,
      dataIndex: 'provider',
      key: 'provider',
      align: 'center' as const,
      render: (text: string) => {
        let color = 'default';
        if (text === 'Ollama') color = 'green';
        if (text === 'OpenAI') color = 'blue';
        if (text === 'Anthropic') color = 'purple';
        if (text === 'DeepSeek') color = 'orange';

        return (
          <Tag color={color}>{text}</Tag>
        );
      }
    },
    {
      title: messages.models?.table?.type,
      dataIndex: 'type',
      key: 'type',
      align: 'center' as const,
      render: (text: string) => {
        return text === 'local' ?
          <Tag color="green">{messages.models?.types?.local}</Tag> :
          <Tag color="blue">{messages.models?.types?.remote}</Tag>;
      }
    },
    {
      title: messages.models?.table?.size,
      dataIndex: 'size',
      key: 'size',
      align: 'center' as const,
    },
    {
      title: messages.models?.table?.status,
      dataIndex: 'status',
      key: 'status',
      align: 'center' as const,
      render: (status: string) => {
        let color = 'green';
        let text = messages.models?.status?.available;

        if (status === 'downloading') {
          color = 'blue';
          text = messages.models?.status?.downloading;
        } else if (status === 'offline') {
          color = 'red';
          text = messages.models?.status?.offline;
        }

        return <Tag color={color} className={styles.statusTag}>{text}</Tag>;
      }
    },
    // {
    //   title: messages.models?.table?.actions,
    //   key: 'actions',
    //   render: (_: any, record: ModelData) => (
    //     <Space>
    //       <Tooltip title={messages.models?.actions?.view}>
    //         <Button
    //           type="text"
    //           icon={<EyeOutlined />}
    //           onClick={() => router.push(`/models/${record.id}`)}
    //         />
    //       </Tooltip>
    //       {record.type === 'remote' && (
    //         <Tooltip title={messages.models?.actions?.download}>
    //           <Button
    //             type="text"
    //             icon={<DownloadOutlined />}
    //             disabled={record.status === 'downloading'}
    //           />
    //         </Tooltip>
    //       )}
    //     </Space>
    //   ),
    // },
  ];

  return (
    <Card className={styles.tableCard}>
      <Table
        columns={columns}
        dataSource={filteredData}
        rowKey="id"
        loading={loading}
        pagination={{
          pageSize: 10,
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items`,
          position: ['bottomCenter'],
        }}
        className={styles.table}
      />
    </Card>
  );
}
