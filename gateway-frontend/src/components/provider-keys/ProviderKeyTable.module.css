.table {
  background: var(--color-background);
  border-radius: var(--radius-lg);
}

.table :global(.ant-table-thead > tr > th) {
  background: var(--color-background-secondary);
  border-bottom: 1px solid var(--color-border-light);
  font-weight: var(--font-weight-medium);
  color: var(--color-text-primary);
}

.table :global(.ant-table-tbody > tr > td) {
  border-bottom: 1px solid var(--color-border-light);
  padding: var(--spacing-md);
}

.table :global(.ant-table-tbody > tr:hover > td) {
  background: var(--color-background-hover);
}

.providerCell {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.providerIcon {
  width: 24px;
  height: 24px;
  border-radius: var(--radius-xs);
  object-fit: contain;
}

.providerName {
  font-weight: var(--font-weight-medium);
  color: var(--color-text-primary);
}

.keyNameCell {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.keyName {
  font-weight: var(--font-weight-medium);
  color: var(--color-text-primary);
}

.keyMask {
  font-family: var(--font-family-mono);
  font-size: var(--font-size-xs);
  color: var(--color-text-secondary);
  background: var(--color-background-secondary);
  padding: 2px var(--spacing-xs);
  border-radius: var(--radius-xs);
  width: fit-content;
}

.actionButton {
  border: none;
  background: transparent;
  color: var(--color-text-secondary);
  transition: var(--transition-fast);
  display: flex;
  align-items: center;
  justify-content: center;
  width: 28px;
  height: 28px;
  border-radius: var(--radius-sm);
}

.actionButton:hover {
  background: var(--color-background-hover);
  color: var(--color-primary);
}

.actionButton.dangerButton:hover {
  color: var(--color-error);
  background: var(--color-error-light);
}

.statusTag {
  border-radius: var(--radius-sm);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
  padding: 2px var(--spacing-xs);
  border: none;
}

.statusTag.active {
  background: var(--color-success-light);
  color: var(--color-success);
}

.statusTag.inactive {
  background: var(--color-warning-light);
  color: var(--color-warning);
}

.statusTag.error {
  background: var(--color-error-light);
  color: var(--color-error);
}

/* Responsive design */
@media (max-width: 768px) {
  .table :global(.ant-table-thead > tr > th),
  .table :global(.ant-table-tbody > tr > td) {
    padding: var(--spacing-sm);
  }
  
  .keyNameCell {
    min-width: 120px;
  }
  
  .keyMask {
    font-size: 10px;
  }
}
