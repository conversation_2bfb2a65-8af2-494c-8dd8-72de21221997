'use client';

import { Table, Button, Popconfirm, message, Tag, Space, Tooltip } from 'antd';
import { EditOutlined, DeleteOutlined, PlayCircleOutlined, PauseCircleOutlined, ApiOutlined } from '@ant-design/icons';
import { useLocaleContext } from '@/contexts/LocaleContext';
import { ProviderKey, deleteProviderKey, updateProv<PERSON><PERSON>ey, testProviderKey } from '@/api/providerKeys';
import { useState } from 'react';
import styles from './ProviderKeyTable.module.css';

interface ProviderKeyTableProps {
  loading: boolean;
  dataSource: ProviderKey[];
  total: number;
  page: number;
  pageSize: number;
  onPageChange: (page: number, size?: number) => void;
  onRefresh: () => void;
  onEdit?: (key: ProviderKey) => void;
}

export default function ProviderKeyTable({
  loading,
  dataSource,
  total,
  page,
  pageSize,
  onPageChange,
  onRefresh,
  onEdit
}: ProviderKeyTableProps) {
  const { messages } = useLocaleContext();
  const [actionLoading, setActionLoading] = useState<{ [key: string]: boolean }>({});

  const handleDelete = async (id: string) => {
    try {
      await deleteProviderKey(id);
      message.success(messages.providerKeys.table.deleteSuccess);
      onRefresh();
    } catch (error) {
      console.error('Error deleting provider key:', error);
      message.error(messages.providerKeys.table.deleteError);
    }
  };

  const handleStatusToggle = async (id: string, currentStatus: string, provider: string, region: string) => {
    const newStatus = currentStatus === 'active' ? 'inactive' : 'active';
    try {
      await updateProviderKey(id, { provider: provider.toLowerCase(), region: region.toLowerCase(), status: newStatus });
      message.success(
        newStatus === 'active' 
          ? messages.providerKeys.table.enableSuccess 
          : messages.providerKeys.table.disableSuccess
      );
      onRefresh();
    } catch (error) {
      console.error('Error updating provider key status:', error);
      message.error(messages.providerKeys.table.updateError);
    }
  };

  const handleTest = async (id: string) => {
    setActionLoading(prev => ({ ...prev, [`test-${id}`]: true }));
    try {
      const result = await testProviderKey(id);
      if (result.success) {
        message.success(messages.providerKeys.table.testSuccess);
      } else {
        message.error(result.message || messages.providerKeys.table.testError);
      }
      onRefresh();
    } catch (error) {
      console.error('Error testing provider key:', error);
      message.error(messages.providerKeys.table.testError);
    } finally {
      setActionLoading(prev => ({ ...prev, [`test-${id}`]: false }));
    }
  };

  const getStatusTag = (status: string) => {
    const statusConfig = {
      active: { color: 'success', text: 'Active' },
      inactive: { color: 'warning', text: 'Inactive' },
      error: { color: 'error', text: 'Error' }
    };
    
    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.inactive;
    return <Tag color={config.color}>{config.text}</Tag>;
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  const columns = [
    {
      title: messages.providerKeys.table.provider,
      dataIndex: 'keyType',
      key: 'keyType',
      render: (keyType: string) => (
        <div className={styles.providerCell}>
          <span className={styles.providerName}>{keyType}</span>
        </div>
      ),
    },
    {
      title: messages.providerKeys.table.keyName,
      dataIndex: 'name',
      key: 'name',
      render: (name: string, record: ProviderKey) => (
        <div className={styles.keyNameCell}>
          <div className={styles.keyName}>{name}</div>
          <div className={styles.keyMask}>{record.keyMask || `${record.keyType}_****${record.id.slice(-4)}`}</div>
        </div>
      ),
    },
    {
      title: messages.providerKeys.table.status,
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => getStatusTag(status),
    },
    {
      title: messages.providerKeys.table.created,
      dataIndex: 'createdAt',
      key: 'createdAt',
      render: (date: string) => formatDate(date),
    },
    {
      title: messages.providerKeys.table.lastUsed,
      dataIndex: 'lastUsed',
      key: 'lastUsed',
      render: (date: string | null) => date ? formatDate(date) : '-',
    },
    {
      title: messages.providerKeys.table.actions,
      key: 'actions',
      render: (_: any, record: ProviderKey) => (
        <Space size="small">
          
          <Tooltip title={record.status === 'active' ? messages.providerKeys.table.disable : messages.providerKeys.table.enable}>
            <Button
              type="text"
              icon={record.status === 'active' ? <PauseCircleOutlined /> : <PlayCircleOutlined />}
              size="small"
              onClick={() => handleStatusToggle(record.id, record.status, record.provider, record.region)}
              className={styles.actionButton}
            />
          </Tooltip>

          {onEdit && (
            <Tooltip title={messages.providerKeys.table.edit}>
              <Button
                type="text"
                icon={<EditOutlined />}
                size="small"
                onClick={() => onEdit(record)}
                className={styles.actionButton}
              />
            </Tooltip>
          )}

          <Popconfirm
            title={messages.providerKeys.table.deleteConfirmTitle}
            description={messages.providerKeys.table.deleteConfirmMessage}
            onConfirm={() => handleDelete(record.id)}
            okText={messages.providerKeys.table.deleteConfirm}
            cancelText={messages.providerKeys.table.deleteCancel}
            okButtonProps={{ danger: true }}
          >
            <Tooltip title={messages.providerKeys.table.delete}>
              <Button
                type="text"
                icon={<DeleteOutlined />}
                size="small"
                className={`${styles.actionButton} ${styles.dangerButton}`}
              />
            </Tooltip>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <Table
      columns={columns}
      dataSource={dataSource}
      loading={loading}
      rowKey="id"
      pagination={{
        current: page,
        pageSize: pageSize,
        total: total,
        showSizeChanger: true,
        showQuickJumper: true,
        showTotal: (total, range) => 
          `${range[0]}-${range[1]} of ${total} items`,
        onChange: onPageChange,
        onShowSizeChange: onPageChange,
      }}
      locale={{
        emptyText: messages.providerKeys.table.noData,
      }}
      className={styles.table}
    />
  );
}
