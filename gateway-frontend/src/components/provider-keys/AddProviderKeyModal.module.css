.modal {
  /* Custom modal styles */
}

.modal :global(.ant-modal-content) {
  border-radius: var(--radius-lg);
}

.modal :global(.ant-modal-header) {
  border-bottom: 1px solid var(--color-border-light);
  padding: var(--spacing-lg);
}

.modal :global(.ant-modal-body) {
  padding: var(--spacing-lg);
}

.modal :global(.ant-modal-footer) {
  border-top: 1px solid var(--color-border-light);
  padding: var(--spacing-md) var(--spacing-lg);
}

.steps {
  margin-bottom: var(--spacing-xl);
}

.stepContent {
  min-height: 300px;
}

.stepDescription {
  color: var(--color-text-secondary);
  margin-bottom: var(--spacing-lg);
  line-height: 1.5;
}

.providerGrid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: var(--spacing-md);
}

.providerCard {
  border: 1px solid var(--color-border-light);
  border-radius: var(--radius-md);
  padding: var(--spacing-md);
  cursor: pointer;
  transition: var(--transition-fast);
}

.providerCard:hover {
  border-color: var(--color-primary);
  box-shadow: var(--shadow-sm);
  transform: translateY(-2px);
}

.providerHeader {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-sm);
}

.providerIcon {
  width: 24px;
  height: 24px;
  border-radius: var(--radius-xs);
  object-fit: contain;
}

.providerName {
  font-size: var(--font-size-md);
  font-weight: var(--font-weight-medium);
  color: var(--color-text-primary);
  margin: 0;
}

.providerModels {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-xs);
}

.modelTag {
  background: var(--color-background-secondary);
  color: var(--color-text-secondary);
  padding: 2px var(--spacing-xs);
  border-radius: var(--radius-xs);
  font-size: var(--font-size-xs);
  border: none;
}

.testSection {
  margin: var(--spacing-lg) 0;
  padding: var(--spacing-lg);
  background: var(--color-background-secondary);
  border-radius: var(--radius-md);
  border: 1px solid var(--color-border-light);
}

.testButton {
  margin-bottom: var(--spacing-md);
}

.testResult {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: var(--radius-sm);
  font-size: var(--font-size-sm);
}

.testResult.success {
  background: var(--color-success-light);
  color: var(--color-success);
  border: 1px solid var(--color-success);
}

.testResult.error {
  background: var(--color-error-light);
  color: var(--color-error);
  border: 1px solid var(--color-error);
}

.testIcon {
  font-size: var(--font-size-md);
}

.encryptionInfo {
  margin-top: var(--spacing-lg);
  padding: var(--spacing-md);
  background: var(--color-background-secondary);
  border-radius: var(--radius-md);
  border: 1px solid var(--color-border-light);
}

.encryptionInfo :global(.ant-alert) {
  background: transparent;
  border: none;
  padding: 0;
}

.encryptionInfo :global(.ant-alert-message) {
  color: var(--color-primary);
  font-weight: var(--font-weight-medium);
  margin-bottom: var(--spacing-sm);
}

.encryptionInfo :global(.ant-alert-description) {
  color: var(--color-text-secondary);
}

.encryptionInfo ul {
  margin: var(--spacing-sm) 0;
  padding-left: var(--spacing-lg);
}

.encryptionInfo li {
  margin-bottom: var(--spacing-xs);
  font-family: var(--font-family-mono);
  font-size: var(--font-size-sm);
}

.encryptionInfo small {
  color: var(--color-success);
  font-weight: var(--font-weight-medium);
}

/* Form styles */
.stepContent :global(.ant-form-item-label > label) {
  font-weight: var(--font-weight-medium);
  color: var(--color-text-primary);
}

.stepContent :global(.ant-input),
.stepContent :global(.ant-input-password),
.stepContent :global(.ant-select-selector) {
  border-radius: var(--radius-md);
  border-color: var(--color-border-medium);
}

.stepContent :global(.ant-input:focus),
.stepContent :global(.ant-input-password:focus),
.stepContent :global(.ant-select-focused .ant-select-selector) {
  border-color: var(--color-primary);
  box-shadow: 0 0 0 2px rgba(109, 32, 245, 0.1);
}

/* Steps customization */
.steps :global(.ant-steps-item-process .ant-steps-item-icon) {
  background: var(--color-primary);
  border-color: var(--color-primary);
}

.steps :global(.ant-steps-item-finish .ant-steps-item-icon) {
  background: var(--color-success);
  border-color: var(--color-success);
}

.steps :global(.ant-steps-item-finish .ant-steps-item-icon .ant-steps-icon) {
  color: white;
}

.steps :global(.ant-steps-item-process .ant-steps-item-title) {
  color: var(--color-primary);
  font-weight: var(--font-weight-medium);
}

.steps :global(.ant-steps-item-finish .ant-steps-item-title) {
  color: var(--color-success);
}

/* Responsive design */
@media (max-width: 768px) {
  .providerGrid {
    grid-template-columns: 1fr;
  }
  
  .modal {
    width: 90% !important;
    max-width: none !important;
  }
  
  .stepContent {
    min-height: 250px;
  }
}
