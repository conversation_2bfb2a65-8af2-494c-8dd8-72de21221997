'use client';

import React, { useState } from 'react';
import { Modal, Steps, Button, Form, Input, Select, Card, message, Space, Alert } from 'antd';
import { CheckCircleOutlined, LoadingOutlined, LockOutlined } from '@ant-design/icons';
import { useLocaleContext } from '@/contexts/LocaleContext';
import { Provider, CreateProviderKeyRequest, createProviderKey, testConnection, getExecutorPublicKey, PublicKeyInfo } from '@/api/providerKeys';
import { encryptApiKey, validateEncryptedResult, getEncryptionSummary } from '@/utils/encryptApiKey';
import styles from './AddProviderKeyModal.module.css';

const { Option } = Select;

interface AddProviderKeyModalProps {
  open: boolean;
  onClose: () => void;
  onSuccess: () => void;
  providers: Provider[];
  preSelectedProvider?: Provider | null;
}

export default function AddProviderKeyModal({
  open,
  onClose,
  onSuccess,
  providers,
  preSelectedProvider
}: AddProviderKeyModalProps) {
  const { messages } = useLocaleContext();
  const [currentStep, setCurrentStep] = useState(0);
  const [form] = Form.useForm();
  const [selectedProvider, setSelectedProvider] = useState<Provider | null>(null);
  const [testing, setTesting] = useState(false);
  const [saving, setSaving] = useState(false);
  const [testResult, setTestResult] = useState<{ success: boolean; message: string; models?: string[] } | null>(null);
  const [encryptionInfo, setEncryptionInfo] = useState<any>(null);

  const handleClose = () => {
    setCurrentStep(preSelectedProvider ? 1 : 0);
    setSelectedProvider(preSelectedProvider || null);
    setTestResult(null);
    setEncryptionInfo(null);
    form.resetFields();
    if (preSelectedProvider) {
      form.setFieldsValue({ keyType: preSelectedProvider.name });
    }
    onClose();
  };

  // Handle pre-selected provider
  React.useEffect(() => {
    if (open && preSelectedProvider) {
      setSelectedProvider(preSelectedProvider);
      setCurrentStep(1);
      form.setFieldsValue({ keyType: preSelectedProvider.name });
    } else if (open && !preSelectedProvider) {
      setCurrentStep(0);
      setSelectedProvider(null);
    }
  }, [open, preSelectedProvider, form]);

  const handleProviderSelect = (provider: Provider) => {
    setSelectedProvider(provider);
    form.setFieldsValue({ keyType: provider.name });
    setCurrentStep(1);
  };

  const handleNext = async () => {
    if (currentStep === 1) {
      try {
        console.log('Before validation - form values:', form.getFieldsValue());
        await form.validateFields(['name', 'apiKey']);
        console.log('After validation - form values:', form.getFieldsValue());
        setCurrentStep(2);
      } catch (error) {
        console.error('Form validation failed:', error);
      }
    }
  };

  const handleBack = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleTest = async () => {
    try {
      const values = await form.validateFields(['apiKey', 'endpoint']);
      setTesting(true);
      setTestResult(null);
      setEncryptionInfo(null);

      // 1. 测试连接
      const result = await testConnection({
        keyType: selectedProvider!.name,
        apiKey: values.apiKey,
        endpoint: values.endpoint
      });

      setTestResult(result);
      if (result.success && result.availableModels) {
        form.setFieldsValue({ models: result.availableModels });
      }

      // 2. 如果测试成功，准备加密信息预览
      if (result.success) {
        try {
          const executorKeyInfo = await getExecutorPublicKey(selectedProvider!.name, 'asia');
          const encryptedResult = encryptApiKey(values.apiKey, executorKeyInfo.publicKey);
          const encryptionSummary = getEncryptionSummary(encryptedResult);
          setEncryptionInfo({
            ...encryptionSummary,
            keyId: executorKeyInfo.keyId,
            provider: executorKeyInfo.provider,
            region: executorKeyInfo.region
          });
        } catch (encryptError) {
          console.error('Encryption preview error:', encryptError);
          // 不影响测试结果，只是无法显示加密预览
        }
      }
    } catch (error) {
      console.error('Test connection error:', error);
      setTestResult({
        success: false,
        message: messages.providerKeys.addModal.testFailed
      });
    } finally {
      setTesting(false);
    }
  };

  const handleSave = async () => {
    try {
      // 验证所有必需字段
      const values = await form.validateFields(['name', 'apiKey']);
      setSaving(true);
      console.log('Form values:', values);
      console.log('All form values:', form.getFieldsValue());
      // 1. 获取executor公钥信息
      const executorKeyInfo = await getExecutorPublicKey(selectedProvider!.name, 'asia');

      // 2. 加密API密钥
      const encryptedResult = encryptApiKey(values.apiKey, executorKeyInfo.publicKey);

      // 3. 验证加密结果
      if (!validateEncryptedResult(encryptedResult)) {
        throw new Error('Encryption failed - invalid result');
      }

      // 4. 准备请求数据（不包含明文API密钥）
      const data: CreateProviderKeyRequest = {
        provider: selectedProvider!.name.toLowerCase(),
        region: executorKeyInfo.region,
        keyId: executorKeyInfo.keyId,
        encryptedKey: encryptedResult.encryptedKey,
        nonce: encryptedResult.nonce,
        tag: encryptedResult.tag,
        ephemeralPubKey: encryptedResult.ephemeralPubKey,
        name: values.name
      };

      // 5. 显示加密信息给用户（透明度）
      const encryptionSummary = getEncryptionSummary(encryptedResult);
      console.log('🔒 API Key Encryption Summary:', encryptionSummary);
      console.log('🔑 Executor Key Info:', executorKeyInfo);

      const response = await createProviderKey(data);
      console.log('✅ API Key created successfully:', response.data);
      message.success(messages.providerKeys.addModal.saveSuccess);
      handleClose();
      onSuccess();
    } catch (error) {
      console.error('Save provider key error:', error);
      message.error(messages.providerKeys.addModal.saveFailed);
    } finally {
      setSaving(false);
    }
  };

  const renderStepContent = () => {
    switch (currentStep) {
      case 0:
        return (
          <div className={styles.stepContent}>
            <p className={styles.stepDescription}>
              {messages.providerKeys.addModal.providerDescription}
            </p>
            <div className={styles.providerGrid}>
              {providers.map(provider => (
                <Card
                  key={provider.id}
                  className={styles.providerCard}
                  onClick={() => handleProviderSelect(provider)}
                  hoverable
                >
                  <div className={styles.providerHeader}>
                    {provider.iconUrl && (
                      <img 
                        src={provider.iconUrl} 
                        alt={provider.displayName}
                        className={styles.providerIcon}
                      />
                    )}
                    <h4 className={styles.providerName}>{provider.displayName}</h4>
                  </div>
                  <div className={styles.providerModels}>
                    {provider.supportedModels.slice(0, 3).map(model => (
                      <span key={model} className={styles.modelTag}>{model}</span>
                    ))}
                    {provider.supportedModels.length > 3 && (
                      <span className={styles.modelTag}>+{provider.supportedModels.length - 3} more</span>
                    )}
                  </div>
                </Card>
              ))}
            </div>
          </div>
        );

      case 1:
        return (
          <div className={styles.stepContent}>
            <p className={styles.stepDescription}>
              {messages.providerKeys.addModal.keyDescription}
            </p>
            <Form form={form} layout="vertical">
              <Form.Item
                name="name"
                label={messages.providerKeys.addModal.keyNameLabel}
                rules={[{ required: true, message: messages.providerKeys.addModal.keyNameRequired }]}
              >
                <Input
                  placeholder={messages.providerKeys.addModal.keyNamePlaceholder}
                  onChange={(e) => {
                    console.log('Name field changed:', e.target.value);
                    console.log('Current form values:', form.getFieldsValue());
                  }}
                />
              </Form.Item>

              <Form.Item
                name="apiKey"
                label={messages.providerKeys.addModal.apiKeyLabel}
                rules={[{ required: true, message: messages.providerKeys.addModal.apiKeyRequired }]}
              >
                <Input.Password
                  placeholder={messages.providerKeys.addModal.apiKeyPlaceholder}
                  onChange={(e) => {
                    console.log('API Key field changed:', e.target.value);
                    console.log('Current form values:', form.getFieldsValue());
                  }}
                />
              </Form.Item>

              <Form.Item
                name="endpoint"
                label={messages.providerKeys.addModal.endpointLabel}
              >
                <Input
                  placeholder={messages.providerKeys.addModal.endpointPlaceholder}
                  defaultValue={selectedProvider?.defaultEndpoint}
                />
              </Form.Item>

              {/* 调试信息 */}
              <div style={{ marginTop: '16px', padding: '8px', background: '#f5f5f5', borderRadius: '4px' }}>
                <small>Debug: Current form values: {JSON.stringify(form.getFieldsValue())}</small>
              </div>
            </Form>
          </div>
        );

      case 2:
        return (
          <div className={styles.stepContent}>
            <p className={styles.stepDescription}>
              {messages.providerKeys.addModal.testDescription}
            </p>

            <div className={styles.testSection}>
              <Button
                type="primary"
                onClick={handleTest}
                loading={testing}
                icon={testing ? <LoadingOutlined /> : undefined}
                className={styles.testButton}
              >
                {testing ? messages.providerKeys.addModal.testing : messages.providerKeys.addModal.test}
              </Button>

              {testResult && (
                <div className={`${styles.testResult} ${testResult.success ? styles.success : styles.error}`}>
                  <CheckCircleOutlined className={styles.testIcon} />
                  <span>{testResult.message}</span>
                </div>
              )}
            </div>

            {testResult?.success && (
              <>
                {testResult.models && (
                  <div style={{ marginTop: '16px' }}>
                    <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'medium' }}>
                      {messages.providerKeys.addModal.modelLabel}
                    </label>
                    <Select
                      mode="multiple"
                      placeholder={messages.providerKeys.addModal.modelPlaceholder}
                      options={testResult.models.map(model => ({ label: model, value: model }))}
                      style={{ width: '100%' }}
                      onChange={(value) => form.setFieldsValue({ models: value })}
                    />
                  </div>
                )}

                {encryptionInfo && (
                  <div className={styles.encryptionInfo}>
                    <Alert
                      message="🔒 Encryption Information"
                      description={
                        <div>
                          <p>Your API key will be encrypted before transmission:</p>
                          <ul>
                            <li>Target executor: <code>{encryptionInfo.keyId}</code></li>
                            <li>Provider: <code>{encryptionInfo.provider}</code></li>
                            <li>Region: <code>{encryptionInfo.region}</code></li>
                            <li>Encrypted key length: {encryptionInfo.encryptedKeyLength} characters</li>
                            <li>Ephemeral public key: {encryptionInfo.ephemeralPubKeyPreview}</li>
                            <li>Nonce length: {encryptionInfo.nonceLength} characters</li>
                            <li>Authentication tag length: {encryptionInfo.tagLength} characters</li>
                          </ul>
                          <p><small>✅ Your API key is encrypted using ChaCha20-Poly1305 with ECDH key exchange</small></p>
                        </div>
                      }
                      type="info"
                      showIcon
                      icon={<LockOutlined />}
                    />
                  </div>
                )}
              </>
            )}
          </div>
        );

      default:
        return null;
    }
  };

  const getFooterButtons = () => {
    const buttons = [];

    if (currentStep > 0) {
      buttons.push(
        <Button key="back" onClick={handleBack}>
          {messages.providerKeys.addModal.back}
        </Button>
      );
    }

    if (currentStep < 2) {
      buttons.push(
        <Button key="next" type="primary" onClick={handleNext}>
          {messages.providerKeys.addModal.next}
        </Button>
      );
    } else {
      buttons.push(
        <Button
          key="save"
          type="primary"
          onClick={handleSave}
          loading={saving}
          disabled={!testResult?.success}
        >
          {saving ? messages.providerKeys.addModal.saving : messages.providerKeys.addModal.save}
        </Button>
      );
    }

    return buttons;
  };

  const steps = [
    { title: messages.providerKeys.addModal.step1 },
    { title: messages.providerKeys.addModal.step2 },
    { title: messages.providerKeys.addModal.step3 },
  ];

  return (
    <Modal
      title={messages.providerKeys.addModal.title}
      open={open}
      onCancel={handleClose}
      footer={getFooterButtons()}
      width={720}
      className={styles.modal}
    >
      <Steps current={currentStep} items={steps} className={styles.steps} />
      {renderStepContent()}
    </Modal>
  );
}
