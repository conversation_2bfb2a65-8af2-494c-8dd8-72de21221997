'use client';

import React from 'react';
import styles from './gradientBorderButton.module.css';

interface GradientBorderButtonProps {
  children: React.ReactNode;
  onClick?: () => void;
  className?: string;
}

const GradientBorderButton: React.FC<GradientBorderButtonProps> = ({ 
  children, 
  onClick,
  className = ''
}) => {
  return (
    <button 
      className={`${styles.gradientBorderButton} ${className}`} 
      onClick={onClick}
    >
      <span className={styles.buttonText}>{children}</span>
    </button>
  );
};

export default GradientBorderButton;
