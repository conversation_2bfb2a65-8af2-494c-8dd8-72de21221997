.container {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 24px;
  animation: fadeIn 0.5s ease-out;
}

.walletButtons {
  display: flex;
  flex-direction: column;
  gap: 16px;
  width: 100%;
}

.walletButton {
  height: 56px;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  padding: 0 20px;
  gap: 12px;
  font-size: 16px;
  font-weight: 500;
  border-radius: 16px;
  background: linear-gradient(135deg, #9333EA 0%, #A855F7 100%);
  border: none;
  box-shadow: 0 8px 20px rgba(147, 51, 234, 0.25);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  color: white;
}

.walletButton::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    rgba(255, 255, 255, 0) 0%,
    rgba(255, 255, 255, 0.2) 50%,
    rgba(255, 255, 255, 0) 100%
  );
  transition: left 0.5s ease-in-out;
}

.walletButton:hover {
  background: linear-gradient(135deg, #A855F7 0%, #C084FC 100%) !important;
  transform: translateY(-2px);
  box-shadow: 0 12px 24px rgba(147, 51, 234, 0.35) !important;
}

.walletButton:hover::before {
  left: 100%;
}

.walletIcon {
  width: 28px;
  height: 28px;
  object-fit: contain;
  border-radius: 50%;
  background: white;
  padding: 4px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

.walletName {
  flex: 1;
  text-align: left;
}

.connectedInfo {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20px;
  background: rgba(147, 51, 234, 0.1);
  padding: 24px;
  border-radius: 16px;
  border: 1px solid rgba(147, 51, 234, 0.2);
  animation: slideUp 0.5s ease-out;
  backdrop-filter: blur(8px);
}

.addressContainer {
  display: flex;
  align-items: center;
  gap: 8px;
  background: rgba(255, 255, 255, 0.1);
  padding: 12px 16px;
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  width: 100%;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.addressLabel {
  color: rgba(255, 255, 255, 0.7);
  font-size: 14px;
  font-weight: 500;
}

.address {
  font-size: 16px;
  color: white;
  font-weight: 600;
  margin: 0;
  font-family: monospace;
  letter-spacing: 0.5px;
}

.walletActions {
  display: flex;
  width: 100%;
  gap: 12px;
}

.loginButton {
  flex: 1;
  height: 48px;
  border-radius: 12px;
  font-weight: 500;
  transition: all 0.3s ease;
  background: linear-gradient(135deg, #10B981 0%, #34D399 100%);
  border: none;
  box-shadow: 0 8px 16px rgba(16, 185, 129, 0.25);
}

.loginButton:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 20px rgba(16, 185, 129, 0.35) !important;
  background: linear-gradient(135deg, #059669 0%, #10B981 100%) !important;
}

.disconnectButton {
  flex: 1;
  height: 48px;
  border-radius: 12px;
  font-weight: 500;
  transition: all 0.3s ease;
  background: linear-gradient(135deg, #EF4444 0%, #F87171 100%);
  border: none;
  box-shadow: 0 8px 16px rgba(239, 68, 68, 0.25);
}

.disconnectButton:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 20px rgba(239, 68, 68, 0.35) !important;
  background: linear-gradient(135deg, #DC2626 0%, #EF4444 100%) !important;
}

.copyButton {
  background: transparent;
  border: none;
  color: rgba(255, 255, 255, 0.7);
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.copyButton:hover {
  color: white;
  background: rgba(255, 255, 255, 0.1);
}

.loadingContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  padding: 32px 0;
}

.loadingContainer p {
  color: rgba(255, 255, 255, 0.7);
  font-size: 16px;
  margin: 0;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.modalContent {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  padding: 16px 0;
}

.modalAddress {
  display: flex;
  align-items: center;
  gap: 8px;
  background: rgba(0, 0, 0, 0.05);
  padding: 12px 16px;
  border-radius: 8px;
  width: 100%;
  font-family: monospace;
  font-size: 14px;
}