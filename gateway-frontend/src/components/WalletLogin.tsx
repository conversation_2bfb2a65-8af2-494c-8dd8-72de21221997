'use client';

import { But<PERSON>, message, <PERSON>, Toolt<PERSON>, Modal } from 'antd';
import { useAccount, useConnect, useDisconnect, useSignMessage, useChainId } from 'wagmi';
import { useLocaleContext } from '@/contexts/LocaleContext';
import { useAuthStore } from '@/store/authStore';
import { useState, useEffect, useRef } from 'react';
import { useRouter } from 'next/navigation';
import {
  DisconnectOutlined,
  CopyOutlined,
  CheckOutlined,
  LoadingOutlined,
  LoginOutlined
} from '@ant-design/icons';
import styles from './walletLogin.module.css';
import * as userApi from '@/api/user';

// Helper function to generate EIP-4361 formatted message
const generateSignMessage = (address: string, chainId: number): string => {
  const domain = 'https://sightai.io';
  const uri = 'https://sightai.io';
  const issuedAt = new Date().toISOString();
  const nonce = Math.random().toString(36).substring(2, 15);
  const signMessage = `${domain} wants you to sign in with your Ethereum account: ${address}

Make sure that you trust this site and are aware of the security implications of signing this message.

URI: ${uri}
Version: 1
Chain ID: 1
Nonce: ${nonce}
Issued At: ${issuedAt}
`;
  return signMessage;
};

export default function WalletLogin() {
  const router = useRouter();
  const { messages } = useLocaleContext();
  const { address, isConnected } = useAccount();
  const { connect, connectors, isPending } = useConnect();
  const { disconnect } = useDisconnect();
  const { login, setAuthData } = useAuthStore();
  const chainId = useChainId();
  const { signMessageAsync } = useSignMessage();
  const [copied, setCopied] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [activeConnector, setActiveConnector] = useState<string | null>(null);
  const redirectTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const [isAuthenticating, setIsAuthenticating] = useState(false);
  const [showWalletModal, setShowWalletModal] = useState(false);

  // Reset copied state after 2 seconds
  useEffect(() => {
    if (copied) {
      const timer = setTimeout(() => {
        setCopied(false);
      }, 2000);
      return () => clearTimeout(timer);
    }
  }, [copied]);

  // Handle successful connection to show the modal
  useEffect(() => {
    if (isConnected && address) {
      // Update local state with wallet info
      const walletType = connectors.find(c => c.connected)?.name || 'Unknown';
      console.log('Connected wallet type:', walletType);
      login(address, walletType);

      // Show the wallet modal
      setShowWalletModal(true);
    } else {
      // Hide the modal when disconnected
      setShowWalletModal(false);
    }

    // Cleanup on unmount
    return () => {
      if (redirectTimeoutRef.current) {
        clearTimeout(redirectTimeoutRef.current);
      }
      message.destroy(); // Clear any pending messages
    };
  }, [isConnected, address, login, connectors]);

  // Function to handle the authentication process
  const handleAuthentication = async () => {
    if (isConnected && address && chainId) {
      try {
        console.log('Starting wallet authentication process');
        console.log('Connected wallet address:', address);
        console.log('Chain ID:', chainId);
        localStorage.setItem('walletAddress', address)
        // Clear any existing timeout
        if (redirectTimeoutRef.current) {
          clearTimeout(redirectTimeoutRef.current);
        }

        // Start authentication process
        setIsAuthenticating(true);

        // Generate the message to sign
        const signMessage = generateSignMessage(address, chainId);
        console.log('Generated sign message:', signMessage);

        // Request signature from wallet
        message.loading('Please sign the message in your wallet...', 0);
        console.log('Requesting wallet signature...');
        const signature = await signMessageAsync({ message: signMessage });
        console.log('Received signature:', signature);
        message.destroy(); // Clear the loading message

        // Call the login API with the signed message
        console.log('Calling login API with signed message');
        try {
          const response = await userApi.login(signMessage, signature);
          console.log('Login API response:', response);

          // 检查响应格式，支持直接返回或嵌套在data中的情况
          const accessToken = response.accessToken || (response.data && response.data.accessToken);
          const userId = response.userId || (response.data && response.data.userId);

          if (accessToken && userId) {
            // Store the authentication data
            setAuthData(accessToken, userId);

            // Show success message
            message.success(messages.login.loginSuccess);

            // Close the modal
            setShowWalletModal(false);

            // Redirect to homepage after a short delay
            redirectTimeoutRef.current = setTimeout(() => {
              router.push('/');
            }, 1500);
          } else {
            console.error('Invalid API response structure:', response);
            throw new Error('Invalid response from server. Missing accessToken or userId.');
          }
        } catch (apiError: any) {
          console.error('API call error:', apiError);
          // 显示更详细的错误信息
          if (apiError.response && apiError.response.data) {
            console.error('Error response data:', apiError.response.data);
          }
          throw apiError; // Re-throw to be caught by the outer catch block
        }
      } catch (error) {
        console.error('Authentication error:', error);
        message.error(
          typeof error === 'object' && error !== null && 'message' in error
            ? `Login failed: ${(error as Error).message}`
            : messages.login.loginFailed
        );
        // Don't disconnect wallet on authentication failure
      } finally {
        setIsAuthenticating(false);
      }
    }
  };

  const handleConnect = (connector: any) => {
    try {
      setIsLoading(true);
      setActiveConnector(connector.id);
      // Connect to wallet - no need to await as this is handled by the useConnect hook
      connect({ connector });
    } catch (error) {
      console.error('Connection error:', error);
      message.error(messages.login.loginFailed);
    } finally {
      // The loading state will be cleared when the connection is complete or fails
      // This is handled by the isPending state from useConnect
      setTimeout(() => {
        setIsLoading(false);
        setActiveConnector(null);
      }, 1000);
    }
  };

  const handleDisconnect = () => {
    disconnect();
    // 断开连接时不需要调用 logout，因为 AuthContext 会监听钱包状态
  };

  const copyAddress = () => {
    if (address) {
      navigator.clipboard.writeText(address);
      setCopied(true);
      message.success(messages.login.addressCopied);
    }
  };

  const antIcon = <LoadingOutlined style={{ fontSize: 24, color: 'white' }} spin />;
  console.log(connectors)
  return (
    <div className={styles.container}>
      {!isConnected ? (
        <div className={styles.walletButtons}>
          {isPending || isLoading ? (
            <div className={styles.loadingContainer}>
              <Spin indicator={antIcon} />
              <p>{messages.login.connecting}</p>
            </div>
          ) : (
            connectors.map((connector) => (
              <Button
                key={connector.id}
                type="primary"
                className={styles.walletButton}
                onClick={() => handleConnect(connector)}
                loading={activeConnector === connector.id}
                icon={
                  <img
                    src={connector.icon}
                    alt={connector.name}
                    className={styles.walletIcon}
                  />
                }
              >
                <span className={styles.walletName}>
                  {messages.login.connectWith} {connector.name}
                </span>
              </Button>
            ))
          )}
        </div>
      ) : (
        <div className={styles.connectedInfo}>
          {isAuthenticating ? (
            <div className={styles.loadingContainer}>
              <Spin indicator={antIcon} />
              <p>{messages.login.authenticating}</p>
            </div>
          ) : (
            <>
              <div className={styles.addressContainer}>
                <span className={styles.addressLabel}>{messages.login.connectedAddress}:</span>
                <span className={styles.address}>
                  {address?.slice(0, 6)}...{address?.slice(-4)}
                </span>
                <Tooltip title={copied ? 'Copied!' : 'Copy address'}>
                  <Button
                    type="text"
                    size="small"
                    className={styles.copyButton}
                    icon={copied ? <CheckOutlined /> : <CopyOutlined />}
                    onClick={copyAddress}
                  />
                </Tooltip>
              </div>
              <div className={styles.walletActions}>
                <Button
                  type="primary"
                  className={styles.loginButton}
                  onClick={handleAuthentication}
                  icon={<LoginOutlined />}
                >
                  {messages.login.signIn}
                </Button>
                <Button
                  type="primary"
                  danger
                  className={styles.disconnectButton}
                  onClick={handleDisconnect}
                  icon={<DisconnectOutlined />}
                >
                  {messages.login.disconnect}
                </Button>
              </div>
            </>
          )}
        </div>
      )}

      {/* Wallet Connection Modal */}
      <Modal
        title={messages.login.connectedAddress}
        open={showWalletModal}
        onCancel={() => setShowWalletModal(false)}
        footer={[
          <Button key="disconnect" danger onClick={handleDisconnect}>
            {messages.login.disconnect}
          </Button>,
          <Button key="login" type="primary" onClick={handleAuthentication}>
            {messages.login.signIn}
          </Button>,
        ]}
      >
        <div className={styles.modalContent}>
          <p>{messages.login.walletConnected}</p>
          <div className={styles.modalAddress}>
            <span>{address}</span>
            <Tooltip title={copied ? 'Copied!' : 'Copy address'}>
              <Button
                type="text"
                size="small"
                icon={copied ? <CheckOutlined /> : <CopyOutlined />}
                onClick={copyAddress}
              />
            </Tooltip>
          </div>
          <p>{messages.login.loginPrompt}</p>
        </div>
      </Modal>
    </div>
  );
}