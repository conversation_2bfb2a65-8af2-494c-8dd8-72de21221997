'use client';

import { useEffect } from 'react';
import { useLocaleContext } from '@/contexts/LocaleContext';
import { useUserProviderStore, UserProviderMode } from '@/store/userProviderStore';
import styles from './userProviderToggle.module.css';


interface UserProviderToggleProps {
  onChange?: (mode: UserProviderMode) => void;
  className?: string;
}

export default function UserProviderToggle({
  onChange,
  className
}: UserProviderToggleProps) {
  const { messages } = useLocaleContext();
  const { mode, setMode } = useUserProviderStore();

  const handleModeChange = (newMode: UserProviderMode) => {
    setMode(newMode);
    onChange?.(newMode);
  };

  // Notify parent component of initial mode
  useEffect(() => {
    onChange?.(mode);
  }, []);

  const toggleOptions = [
    {
      key: 'user' as UserProviderMode,
      label: messages.userProvider?.user || 'User',
      icon: '/user-icon.svg',
    },
    {
      key: 'provider' as UserProviderMode,
      label: messages.userProvider?.provider || 'Provider',
      icon: '/provider-icon.svg',
    },
  ];

  return (
    <div className={`${styles.toggleContainer} ${className || ''}`}>
      {toggleOptions.map((option) => (
        <div
          key={option.key}
          className={`${styles.toggleOption} ${
            mode === option.key ? styles.selected : ''
          }`}
          onClick={() => handleModeChange(option.key)}
        >
          <div className={styles.iconContainer}>
            <img
              src={option.icon}
              alt={option.label}
              className={styles.icon}
            />
          </div>
          <span className={styles.label}>{option.label}</span>
        </div>
      ))}
    </div>
  );
}
