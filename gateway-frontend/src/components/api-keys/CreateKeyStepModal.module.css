/* Modal styles are now in common.css */

.steps {
  margin-bottom: var(--spacing-xl);
}

.stepContent {
  min-height: 300px;
  margin-bottom: var(--spacing-xl);
}

.stepDescription {
  color: var(--color-text-secondary);
  margin-bottom: var(--spacing-base);
}

.input {
  width: 100%;
}

.channelList {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.channelCard {
  cursor: pointer;
  transition: all var(--transition-base);
  border: 1px solid var(--color-border);
}

.channelCard:hover {
  border-color: var(--color-primary);
  box-shadow: var(--shadow-sm);
}

.selectedCard {
  border-color: var(--color-primary);
  box-shadow: var(--shadow-sm);
  background-color: rgba(147, 51, 234, 0.05);
}

.cardContent {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.iconWrapper {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--radius-md);
  background: #F9FAFB;
}

.icon {
  width: 32px;
  height: 32px;
  object-fit: contain;
}

.title {
  margin: 0;
  font-size: var(--font-size-md);
  font-weight: var(--font-weight-medium);
  /* color: var(--color-foreground); */
}

.description {
  margin: var(--spacing-xs) 0 0;
  font-size: var(--font-size-base);
  color: var(--color-text-secondary);
}

.footer {
  display: flex;
  justify-content: flex-end;
}

.summaryContainer {
  background-color: #F9FAFB;
  border-radius: var(--radius-md);
  padding: var(--spacing-xl);
  margin-top: var(--spacing-base);
}

.summaryTitle {
  font-size: var(--font-size-lg);
  margin-bottom: var(--spacing-base);
  /* color: var(--color-foreground); */
}

.summaryItem {
  margin-bottom: var(--spacing-md);
  display: flex;
}

.summaryLabel {
  font-weight: var(--font-weight-medium);
  width: 120px;
  color: var(--color-text-secondary);
}

.summaryValue {
  /* color: var(--color-foreground); */
  font-weight: var(--font-weight-medium);
}

.summaryNote {
  margin-top: var(--spacing-xl);
  padding-top: var(--spacing-base);
  border-top: 1px solid var(--color-border);
  color: var(--color-text-secondary);
  font-size: var(--font-size-base);
}
