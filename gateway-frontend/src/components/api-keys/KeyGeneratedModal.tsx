'use client';

import { Mo<PERSON>, Button, message } from 'antd';
import { useLocaleContext } from '@/contexts/LocaleContext';
import styles from './KeyGeneratedModal.module.css';

interface KeyGeneratedModalProps {
  open: boolean;
  onClose: () => void;
  apiKey: string;
  example: string;
}

export default function KeyGeneratedModal({ 
  open, 
  onClose, 
  apiKey,
  example 
}: KeyGeneratedModalProps) {
  const { messages } = useLocaleContext();

  const handleCopy = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      message.success(messages.apiKeys.generatedModal.copied, 1);
    } catch (err) {
      message.error(messages.apiKeys.generatedModal.copyFailed, 1);
    }
  };

  return (
    <Modal
      title={messages.apiKeys.generatedModal.title}
      open={open}
      onCancel={onClose}
      footer={null}
      width={720}
      className={styles.modal}
    >
      <div className={styles.warning}>
        {messages.apiKeys.generatedModal.warning}
      </div>

      <div className={styles.keySection}>
        <div className={styles.keyDisplay}>
          <span className={styles.key}>{apiKey}</span>
          <Button type="primary" onClick={() => handleCopy(apiKey)}>
            {messages.apiKeys.generatedModal.copy}
          </Button>
        </div>
      </div>

      <div className={styles.exampleSection}>
        <pre className={styles.codeBlock}>
          <code>{example}</code>
          <Button 
            type="text" 
            className={styles.copyButton}
            onClick={() => handleCopy(example)}
          >
            {messages.apiKeys.generatedModal.copy}
          </Button>
        </pre>
      </div>

      <div className={styles.footer}>
        <a href="/docs" className={styles.docsLink}>
          {messages.apiKeys.generatedModal.learnMore}
        </a>
      </div>
    </Modal>
  );
} 