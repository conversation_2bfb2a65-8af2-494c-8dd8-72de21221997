/* Modal styles are now in common.css */

.warning {
  background: #FEF2F2;
  border: 1px solid #FEE2E2;
  padding: var(--spacing-md) var(--spacing-base);
  border-radius: var(--radius-md);
  color: #991B1B;
  margin-bottom: var(--spacing-xl);
}

.keySection {
  margin-bottom: var(--spacing-xl);
}

.keyDisplay {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  background: #F9FAFB;
  padding: var(--spacing-md) var(--spacing-base);
  border-radius: var(--radius-md);
  border: 1px solid var(--color-border);
}

.key {
  font-family: var(--font-family-mono);
  /* color: var(--color-foreground); */
  flex: 1;
}

.exampleSection {
  margin-bottom: var(--spacing-xl);
}

.codeBlock {
  position: relative;
  background: #F9FAFB;
  padding: var(--spacing-base);
  border-radius: var(--radius-md);
  border: 1px solid var(--color-border);
  margin: 0;
  font-family: var(--font-family-mono);
  font-size: var(--font-size-base);
  line-height: 1.5;
  overflow-x: auto;
}

.copyButton {
  position: absolute;
  top: var(--spacing-sm);
  right: var(--spacing-sm);
}

.footer {
  text-align: center;
}

.docsLink {
  color: var(--color-text-secondary);
  text-decoration: none;
  transition: color var(--transition-fast);
}

.docsLink:hover {
  color: var(--color-foreground);
  text-decoration: underline;
}