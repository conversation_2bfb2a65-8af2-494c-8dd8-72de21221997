.container {
  display: flex;
  gap: var(--spacing-base);
  margin-top: var(--spacing-base);
  overflow-x: hidden;
  overflow-y: hidden;
  padding-bottom: var(--spacing-xs);
  scroll-behavior: auto;
  -webkit-overflow-scrolling: touch;
  position: relative;
}

.container::-webkit-scrollbar {
  height: 6px;
}

.container::-webkit-scrollbar-track {
  background: var(--color-border);
  border-radius: 3px;
}

.container::-webkit-scrollbar-thumb {
  background: var(--color-text-secondary);
  border-radius: 3px;
}

.container::-webkit-scrollbar-thumb:hover {
  background: var(--color-text-primary);
}

.card {
  border-radius: var(--radius-md);
  border: 1px solid var(--color-border);
  background: var(--color-background);
  transition: all var(--transition-base);
  min-width: 250px;
  flex-shrink: 0;
}

.card:hover {
  box-shadow: var(--shadow-md);
}

.cardContent {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.iconWrapper {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--radius-md);
  background: #F9FAFB;
}

.icon {
  width: 32px;
  height: 32px;
  object-fit: contain;
}

.title {
  margin: 0;
  font-size: var(--font-size-md);
  font-weight: var(--font-weight-medium);
  /* color: var(--color-foreground); */
}

.description {
  margin: var(--spacing-xs) 0 0;
  font-size: var(--font-size-base);
  color: var(--color-text-secondary);
}

.loadingContainer {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
  width: 100%;
}

.noCategories {
  grid-column: 1 / -1;
  text-align: center;
  padding: var(--spacing-3xl);
  background: #f9f9f9;
  border-radius: var(--radius-md);
  color: var(--color-text-secondary);
}