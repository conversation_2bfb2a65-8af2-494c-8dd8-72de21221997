.container {
  padding: var(--spacing-base) 0;
}

.titleSection {
  margin-bottom: var(--spacing-md);
}

.title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-medium);
  margin: 0 0 var(--spacing-xs) 0;
  color: var(--color-text);
}

.description {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
  margin: 0;
}

.header {
  display: flex;
  justify-content: space-between;
  margin-bottom: var(--spacing-base);
}

.yearSelect {
  width: 100px;
}

.loadingContainer {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 300px;
}

.errorContainer {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 300px;
}

.errorText {
  color: var(--color-error);
  text-align: center;
}

.chartContainer {
  height: 300px;
  position: relative;
  margin-top: var(--spacing-lg);
  width: 100%;
}

/* Custom tooltip styles */
.customTooltip {
  background-color: rgba(255, 255, 255, 0.95);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-base);
  padding: var(--spacing-sm);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  font-size: var(--font-size-sm);
}

.tooltipLabel {
  margin: 0 0 var(--spacing-xs) 0;
  font-weight: var(--font-weight-medium);
  color: var(--color-text);
  border-bottom: 1px solid var(--color-border-light);
  padding-bottom: var(--spacing-xs);
}

/* Override Recharts default styles */
.chartContainer :global(.recharts-cartesian-grid-horizontal line),
.chartContainer :global(.recharts-cartesian-grid-vertical line) {
  stroke: var(--color-border-light);
  stroke-opacity: 0.5;
}

.chartContainer :global(.recharts-tooltip-cursor) {
  fill-opacity: 0.1;
}

.chartContainer :global(.recharts-bar-rectangle) {
  transition: opacity 0.3s;
}

.chartContainer :global(.recharts-bar-rectangle:hover) {
  opacity: 0.8;
}

.chartContainer :global(.recharts-text) {
  font-size: var(--font-size-xs);
  fill: var(--color-text-tertiary);
}
