'use client';

import { Radio, Spin, Select } from 'antd';
import { useState, useEffect } from 'react';
import { useLocaleContext } from '@/contexts/LocaleContext';
import { getOverallUsage, OverallUsageData } from '@/api/apiKeys';
import styles from './MonthlyUsageChart.module.css';
import dynamic from 'next/dynamic';
import {
  BarChart as RechartsBarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  TooltipProps
} from 'recharts';

// Dynamically import Recharts components with SSR disabled
const BarChart = dynamic(
  () => Promise.resolve(RechartsBarChart),
  { ssr: false }
);

const { Option } = Select;

interface MonthlyUsageChartProps {
  timeRange?: string;
}

export default function MonthlyUsageChart({ timeRange = '30' }: MonthlyUsageChartProps) {
  const { messages } = useLocaleContext();
  const [metric, setMetric] = useState<'requests' | 'tokens' | 'cost'>('requests');
  const [year, setYear] = useState<number>(new Date().getFullYear());
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [chartData, setChartData] = useState<OverallUsageData | null>(null);



  // 获取使用情况数据
  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);

        // 从API获取数据
        const data = await getOverallUsage(timeRange);
        setChartData(data);
        setError(null);
      } catch (err) {
        console.error('Error fetching usage data:', err);
        setError(err instanceof Error ? err.message : 'Failed to load usage data');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [timeRange]);

  const handleMetricChange = (e: any) => {
    setMetric(e.target.value);
  };

  const handleYearChange = (value: string) => {
    setYear(parseInt(value, 10));
  };

  const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];

  if (error) {
    return (
      <div className={styles.container}>
        <div className={styles.header}>
          <Select
            value={year.toString()}
            onChange={handleYearChange}
            className={styles.yearSelect}
          >
            <Option value={(new Date().getFullYear()).toString()}>{new Date().getFullYear()}</Option>
            <Option value={(new Date().getFullYear() - 1).toString()}>{new Date().getFullYear() - 1}</Option>
          </Select>
          <Radio.Group value={metric} onChange={handleMetricChange}>
            <Radio.Button value="requests">{messages.reports?.metrics?.requests || "Requests"}</Radio.Button>
            <Radio.Button value="tokens">{messages.reports?.metrics?.tokens || "Tokens"}</Radio.Button>
            <Radio.Button value="cost">{messages.reports?.metrics?.cost || "Cost"}</Radio.Button>
          </Radio.Group>
        </div>
        <div className={styles.errorContainer}>
          <p className={styles.errorText}>{error}</p>
        </div>
      </div>
    );
  }


  // Prepare data for Recharts
  const prepareChartData = () => {
    if (!chartData) return [];

    // Filter data for the selected year
    const filteredData = chartData.monthlyData.filter(item => item.year === year);

    // Create data array with all months (even if there's no data)
    return months.map(month => {
      const monthData = filteredData.find(item => item.month === month);
      return {
        month,
        requests: monthData ? monthData.requests : 0,
        tokens: monthData ? monthData.tokens : 0,
        cost: monthData ? monthData.cost : 0
      };
    });
  };

  // Format tooltip values
  const formatTooltipValue = (value: number, name: string) => {
    if (name === 'cost') {
      return [`$${value.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`, name];
    }
    return [value.toLocaleString(), name];
  };

  // Custom tooltip component
  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className={styles.customTooltip}>
          <p className={styles.tooltipLabel}>{`${label} ${year}`}</p>
          {payload.map((entry: any, index: number) => (
            <p key={index} style={{ color: entry.color }}>
              {entry.name === 'cost'
                ? `${messages.reports?.metrics?.cost || "Cost"}: $${entry.value.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`
                : `${entry.name === 'requests'
                    ? messages.reports?.metrics?.requests || "Requests"
                    : messages.reports?.metrics?.tokens || "Tokens"}: ${entry.value.toLocaleString()}`
              }
            </p>
          ))}
        </div>
      );
    }
    return null;
  };

  return (
    <div className={styles.container}>
      <div className={styles.titleSection}>
        <h3 className={styles.title}>{messages.reports?.monthlyUsage?.title || "Monthly Usage Chart"}</h3>
        <p className={styles.description}>{messages.reports?.monthlyUsage?.description || "View your monthly usage trends by requests, tokens, and cost."}</p>
      </div>

      <div className={styles.header}>
        <Select
          value={year.toString()}
          onChange={handleYearChange}
          className={styles.yearSelect}
        >
          <Option value={(new Date().getFullYear()).toString()}>{new Date().getFullYear()}</Option>
          <Option value={(new Date().getFullYear() - 1).toString()}>{new Date().getFullYear() - 1}</Option>
        </Select>
        <Radio.Group value={metric} onChange={handleMetricChange}>
          <Radio.Button value="requests">{messages.reports?.metrics?.requests || "Requests"}</Radio.Button>
          <Radio.Button value="tokens">{messages.reports?.metrics?.tokens || "Tokens"}</Radio.Button>
          <Radio.Button value="cost">{messages.reports?.metrics?.cost || "Cost"}</Radio.Button>
        </Radio.Group>
      </div>

      {loading ? (
        <div className={styles.loadingContainer}>
          <Spin />
        </div>
      ) : (
        <div className={styles.chartContainer}>
          {typeof window !== 'undefined' && (
            <ResponsiveContainer width="100%" height={300}>
              <BarChart
                data={prepareChartData()}
                margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
              >
                <CartesianGrid strokeDasharray="3 3" vertical={false} />
                <XAxis
                  dataKey="month"
                  axisLine={false}
                  tickLine={false}
                />
                <YAxis
                  axisLine={false}
                  tickLine={false}
                  orientation="right"
                  tickFormatter={(value) =>
                    metric === 'cost'
                      ? `$${value.toLocaleString(undefined, { minimumFractionDigits: 0, maximumFractionDigits: 0 })}`
                      : value.toLocaleString()
                  }
                />
                <Tooltip
                  content={<CustomTooltip />}
                  formatter={formatTooltipValue}
                />
                <Bar
                  dataKey={metric}
                  fill={metric === 'requests' ? '#1890ff' : metric === 'tokens' ? '#722ed1' : '#52c41a'}
                  radius={[4, 4, 0, 0]}
                  maxBarSize={50}
                />
              </BarChart>
            </ResponsiveContainer>
          )}
        </div>
      )}
    </div>
  );
}
