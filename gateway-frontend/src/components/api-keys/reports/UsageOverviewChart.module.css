.container {
  padding: var(--spacing-base) 0;
}

.header {
  display: flex;
  justify-content: flex-end;
  margin-bottom: var(--spacing-base);
}

.loadingContainer {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 300px;
}

.errorContainer {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 300px;
}

.errorText {
  color: var(--color-error);
  text-align: center;
}

.chartContainer {
  height: 300px;
  display: flex;
  position: relative;
  margin-top: var(--spacing-lg);
}

.yAxis {
  width: 60px;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  padding: 0;
  border-right: 1px solid var(--color-border-light);
}

.yAxisLabel {
  font-size: var(--font-size-xs);
  color: var(--color-text-tertiary);
  text-align: right;
  padding-right: var(--spacing-sm);
  height: 20px;
  line-height: 20px;
}

.chartBody {
  flex: 1;
  height: 100%;
  position: relative;
  border-bottom: 1px solid var(--color-border-light);
}

.chart {
  height: 100%;
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  padding: 0 var(--spacing-sm);
}

.barContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
  height: 100%;
  position: relative;
}

.barWrapper {
  display: flex;
  flex-direction: column;
  justify-content: flex-end; /* Ensure bars start from the bottom */
  align-items: center;
  height: 100%;
  width: 100%;
}

.bar {
  width: 80%;
  background-color: var(--color-info);
  border-radius: 2px 2px 0 0;
  transition: height var(--transition-base);
  min-height: 1px;
  margin-top: auto; /* Push bars to the bottom */
}

.xAxisLabel {
  position: absolute;
  bottom: -24px;
  color: var(--color-text-tertiary);
  font-size: var(--font-size-xs);
  text-align: center;
  width: 100%;
}
