'use client';

import { Table, Tag, Progress, Button, TableColumnProps } from 'antd';
import { EyeOutlined } from '@ant-design/icons';
import { useLocaleContext } from '@/contexts/LocaleContext';
import { useRouter } from 'next/navigation';
import styles from './ChannelUsageTable.module.css';
import { ChannelUsage } from '@/api/apiKeys';

interface ChannelData {
  key: string;
  channel: string;
  models: string[];
  requests: number;
  tokens: string;
  cost: string;
  quota: number;
}

interface ChannelUsageTableProps {
  data: ChannelUsage[];
  loading?: boolean;
}

export default function ChannelUsageTable({ data = [], loading = false }: ChannelUsageTableProps) {
  const { messages } = useLocaleContext();
  const router = useRouter();

  // 格式化数据为表格所需格式
  const tableData: ChannelData[] = data.reduce((acc: ChannelData[], item: ChannelUsage) => {
    // 查找是否已经有该provider的记录
    const existingIndex = acc.findIndex(record => record.channel === item.provider);

    if (existingIndex >= 0) {
      // 如果已存在，则更新该记录
      const existing = acc[existingIndex];

      // 添加模型（如果不存在）
      if (!existing.models.includes(item.model)) {
        existing.models.push(item.model);
      }

      // 更新请求数和token数
      existing.requests += item.requests;

      // 更新token数（需要先转换为数字）
      const existingTokens = parseFloat(existing.tokens.replace(/[^0-9.]/g, '')) *
                            (existing.tokens.includes('M') ? 1000000 : existing.tokens.includes('K') ? 1000 : 1);
      const newTokens = existingTokens + item.tokens;
      existing.tokens = formatNumber(newTokens);

      // 更新成本
      const existingCost = parseFloat(existing.cost.replace(/[^0-9.]/g, ''));
      existing.cost = `$${(existingCost + item.cost).toFixed(2)}`;

      return acc;
    } else {
      // 如果不存在，则创建新记录
      acc.push({
        key: item.provider,
        channel: item.provider,
        models: [item.model],
        requests: item.requests,
        tokens: formatNumber(item.tokens),
        cost: `$${item.cost.toFixed(2)}`,
        quota: Math.min(Math.round((item.requests / 100000) * 100), 100) // 假设配额是基于请求数的百分比
      });

      return acc;
    }
  }, []);

  // 格式化大数字为可读形式（如1.2M）
  function formatNumber(num: number): string {
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + 'M';
    } else if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'K';
    }
    return num.toString();
  }

  const columns: TableColumnProps<ChannelData>[] = [
    {
      title: messages.reports?.channels?.channel || 'Channel',
      dataIndex: 'channel',
      align: 'center',
      key: 'channel',
      render: (text: string) => {
        let color = 'blue';
        if (text === 'OpenAI') color = 'blue';
        if (text === 'DeepSeek') color = 'orange';
        if (text === 'Claude') color = 'purple';
        if (text === 'Ollama') color = 'green';

        return <Tag color={color}>{text}</Tag>;
      }
    },
    {
      title: messages.reports?.channels?.models || 'Models',
      dataIndex: 'models',
      key: 'models',
      align: 'center',
      render: (models: string[]) => (
        <div>
          {models.map(model => (
            <Tag key={model} className={styles.modelTag}>{model}</Tag>
          ))}
        </div>
      )
    },
    {
      title: messages.reports?.channels?.requests || 'Requests',
      dataIndex: 'requests',
      key: 'requests',
      align: 'center',
      render: (requests: number) => requests.toLocaleString()
    },
    {
      title: messages.reports?.channels?.tokens || 'Tokens',
      dataIndex: 'tokens',
      key: 'tokens',
      align: 'center',
    },
    {
      title: messages.reports?.channels?.cost || 'Cost',
      dataIndex: 'cost',
      key: 'cost',
      align: 'center',
    },
    // {
    //   title: messages.reports?.channels?.quota || 'Quota Usage',
    //   dataIndex: 'quota',
    //   key: 'quota',
    //   render: (quota: number) => (
    //     <Progress
    //       percent={quota}
    //       size="small"
    //       status={quota > 90 ? 'exception' : quota > 70 ? 'normal' : 'success'}
    //     />
    //   )
    // },
    {
      title: messages.reports?.channels?.actions || 'Actions',
      key: 'actions',
      align: 'center',
      render: (_: any, record: ChannelData) => (
        <Button
          type="text"
          icon={<EyeOutlined />}
          onClick={() => router.push(`/api-keys?channel=${record.channel.toLowerCase()}`)}
        >
          {messages.reports?.channels?.viewKeys || 'View Keys'}
        </Button>
      ),
    },
  ];

  return (
    <Table
      columns={columns}
      dataSource={tableData}
      className={styles.table}
      pagination={false}
      loading={loading}
      locale={{
        emptyText: 'No channel usage data available'
      }}
    />
  );
}
