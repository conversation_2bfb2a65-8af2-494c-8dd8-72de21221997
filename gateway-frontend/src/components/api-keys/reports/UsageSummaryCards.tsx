'use client';

import { Card, Row, Col, Statistic, Spin } from 'antd';
import { ApiOutlined, ThunderboltOutlined, ClockCircleOutlined, DollarOutlined, ArrowUpOutlined, ArrowDownOutlined } from '@ant-design/icons';
import { useLocaleContext } from '@/contexts/LocaleContext';
import styles from './UsageSummaryCards.module.css';

interface UsageSummaryCardsProps {
  data?: {
    totalRequests: string;
    totalTokens: string;
    avgResponseTime: string;
    costThisMonth: string;
  };
  loading?: boolean;
  error?: string | null;
}

export default function UsageSummaryCards({ data, loading = false, error = null }: UsageSummaryCardsProps) {
  const { messages } = useLocaleContext();

  // 使用传入的数据或默认值
  const summaryData = data || {
    totalRequests: '0',
    totalTokens: '0',
    avgResponseTime: '0ms',
    costThisMonth: '$0.00'
  };

  // 计算请求变化率的图标
  const getRequestsIcon = () => {
    const requestsValue = parseInt(summaryData.totalRequests.replace(/,/g, ''), 10);
    if (requestsValue > 1000) {
      return <ApiOutlined className={`${styles.iconRequests} ${styles.increasing}`} />;
    } else if (requestsValue < 0) {
      return <ApiOutlined className={`${styles.iconRequests} ${styles.decreasing}`} />;
    } else {
      return <ApiOutlined className={styles.iconRequests} />;
    }
  };

  // 计算成本变化率的图标
  const getCostIcon = () => {
    const costValue = parseFloat(summaryData.costThisMonth.replace(/[^0-9.-]+/g, ''));
    if (costValue > 100) {
      return <DollarOutlined className={`${styles.iconCost} ${styles.increasing}`} />;
    } else if (costValue < 0) {
      return <DollarOutlined className={`${styles.iconCost} ${styles.decreasing}`} />;
    } else {
      return <DollarOutlined className={styles.iconCost} />;
    }
  };

  if (loading) {
    return (
      <div className={styles.loadingContainer}>
        <Spin />
      </div>
    );
  }

  if (error) {
    return (
      <div className={styles.errorContainer}>
        <p className={styles.errorText}>{error}</p>
      </div>
    );
  }

  return (
    <div className={styles.cardsContainer}>
      <Row gutter={[16, 16]}>
        <Col xs={24} sm={12} md={6}>
          <Card className={styles.card}>
            <Statistic
              title={messages.reports?.stats?.requests || "Total Requests"}
              value={summaryData.totalRequests}
              prefix={getRequestsIcon()}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card className={styles.card}>
            <Statistic
              title={messages.reports?.stats?.tokens || "Total Tokens"}
              value={summaryData.totalTokens}
              prefix={<ThunderboltOutlined className={styles.iconTokens} />}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card className={styles.card}>
            <Statistic
              title={messages.reports?.stats?.responseTime || "Avg Response Time"}
              value={summaryData.avgResponseTime}
              prefix={<ClockCircleOutlined className={styles.iconTime} />}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card className={styles.card}>
            <Statistic
              title={messages.reports?.stats?.cost || "Cost This Month"}
              value={summaryData.costThisMonth}
              prefix={getCostIcon()}
            />
          </Card>
        </Col>
      </Row>
    </div>
  );
}
