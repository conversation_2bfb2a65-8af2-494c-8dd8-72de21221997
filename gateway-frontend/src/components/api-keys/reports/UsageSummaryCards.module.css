.cardsContainer {
  margin-bottom: var(--spacing-xl);
}

.card {
  height: 100%;
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-sm);
}

.iconRequests {
  color: var(--color-info);
  margin-right: var(--spacing-sm);
}

.iconTokens {
  color: #722ed1;
  margin-right: var(--spacing-sm);
}

.iconTime {
  color: #13c2c2;
  margin-right: var(--spacing-sm);
}

.iconCost {
  color: var(--color-success);
  margin-right: var(--spacing-sm);
}

.increasing {
  color: var(--color-success);
}

.decreasing {
  color: var(--color-error);
}

.loadingContainer {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
  margin-bottom: var(--spacing-xl);
}

.errorContainer {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
  margin-bottom: var(--spacing-xl);
}

.errorText {
  color: var(--color-error);
  text-align: center;
}
