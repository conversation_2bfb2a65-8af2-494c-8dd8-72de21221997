'use client';

import { Radio, Spin } from 'antd';
import { useState, useEffect } from 'react';
import { useLocaleContext } from '@/contexts/LocaleContext';
import { getOverallUsage, OverallUsageData } from '@/api/apiKeys';
import styles from './UsageOverviewChart.module.css';

// 注意：在实际项目中，您需要引入一个图表库，如 Recharts, Chart.js 或 Ant Design Charts
// 这里我们使用一个简化的模拟图表

interface UsageOverviewChartProps {
  timeRange?: string;
}

export default function UsageOverviewChart({ timeRange = '30' }: UsageOverviewChartProps) {
  const { messages } = useLocaleContext();
  const [metric, setMetric] = useState<'requests' | 'tokens' | 'cost'>('requests');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [chartData, setChartData] = useState<OverallUsageData | null>(null);



  // 获取使用情况数据
  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);

        // 从API获取数据
        const data = await getOverallUsage(timeRange);
        setChartData(data);
        setError(null);
      } catch (err) {
        console.error('Error fetching usage data:', err);
        setError(err instanceof Error ? err.message : 'Failed to load usage data');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [timeRange]);

  const handleMetricChange = (e: any) => {
    setMetric(e.target.value);
  };

  // 格式化日期
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return `${date.getMonth() + 1}/${date.getDate()}`;
  };

  if (error) {
    return (
      <div className={styles.container}>
        <div className={styles.header}>
          <Radio.Group value={metric} onChange={handleMetricChange}>
            <Radio.Button value="requests">{messages.reports?.metrics?.requests || "Requests"}</Radio.Button>
            <Radio.Button value="tokens">{messages.reports?.metrics?.tokens || "Tokens"}</Radio.Button>
            <Radio.Button value="cost">{messages.reports?.metrics?.cost || "Cost"}</Radio.Button>
          </Radio.Group>
        </div>
        <div className={styles.errorContainer}>
          <p className={styles.errorText}>{error}</p>
        </div>
      </div>
    );
  }

  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <Radio.Group value={metric} onChange={handleMetricChange}>
          <Radio.Button value="requests">{messages.reports?.metrics?.requests || "Requests"}</Radio.Button>
          <Radio.Button value="tokens">{messages.reports?.metrics?.tokens || "Tokens"}</Radio.Button>
          <Radio.Button value="cost">{messages.reports?.metrics?.cost || "Cost"}</Radio.Button>
        </Radio.Group>
      </div>

      {loading ? (
        <div className={styles.loadingContainer}>
          <Spin />
        </div>
      ) : (
        <div className={styles.chartContainer}>
          {/* Y轴 */}
          <div className={styles.yAxis}>
            {chartData && (() => {
              // 计算数据的最大值和平均值
              const values = chartData.dailyData.map(d => d[metric]);
              const maxValue = Math.max(...values);
              const avgValue = values.reduce((sum, val) => sum + val, 0) / values.length;

              // 设置Y轴的最大值为最大值的1.1倍，确保最高的柱子不会完全占满
              const yAxisMax = maxValue * 1.1;

              // 如果最大值远大于平均值，使用平方根刻度来平衡显示
              const useSquareRootScale = maxValue > avgValue * 5;

              const steps = 5;
              return Array.from({ length: steps + 1 }).map((_, i) => {
                let value;
                if (useSquareRootScale) {
                  // 使用平方根刻度，使小值更明显
                  const sqrtMax = Math.sqrt(yAxisMax);
                  const stepValue = sqrtMax * (1 - i / steps);
                  value = Math.pow(stepValue, 2);
                } else {
                  // 使用线性刻度
                  value = yAxisMax * (1 - i / steps);
                }

                return (
                  <div key={i} className={styles.yAxisLabel}>
                    {metric === 'cost'
                      ? `$${value.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`
                      : value.toLocaleString(undefined, { maximumFractionDigits: 0 })}
                  </div>
                );
              });
            })()}
          </div>

          {/* 图表主体 */}
          <div className={styles.chartBody}>
            <div className={styles.chart}>
              {chartData && (() => {
                // 计算数据的统计信息
                const values = chartData.dailyData.map(d => d[metric]);
                const maxValue = Math.max(...values);
                const avgValue = values.reduce((sum, val) => sum + val, 0) / values.length;

                // 设置Y轴的最大值为最大值的1.1倍，确保最高的柱子不会完全占满
                const yAxisMax = maxValue * 1.1;

                // 如果最大值远大于平均值，使用平方根刻度来平衡显示
                const useSquareRootScale = maxValue > avgValue * 5;

                return chartData.dailyData.map((item, index) => {
                  const value = item[metric];
                  let height;

                  if (useSquareRootScale) {
                    // 使用平方根刻度计算高度
                    const sqrtValue = Math.sqrt(value);
                    const sqrtMax = Math.sqrt(yAxisMax);
                    height = (sqrtValue / sqrtMax) * 100;
                  } else {
                    // 使用线性刻度计算高度
                    height = (value / yAxisMax) * 100;
                  }

                  // 确保即使是很小的值也有最小高度，便于查看
                  const minHeight = value > 0 ? 2 : 0;
                  height = Math.max(height, minHeight);

                  return (
                    <div key={index} className={styles.barContainer}>
                      <div className={styles.barWrapper}>
                        <div
                          className={styles.bar}
                          style={{
                            height: `${height}%`,
                            backgroundColor: metric === 'requests' ? '#1890ff' : metric === 'tokens' ? '#722ed1' : '#52c41a'
                          }}
                          title={`${formatDate(item.date)}: ${value.toLocaleString()}`}
                        />
                      </div>
                      {/* 只在特定间隔显示日期标签 */}
                      {index % 5 === 0 && (
                        <div className={styles.xAxisLabel}>{formatDate(item.date)}</div>
                      )}
                    </div>
                  );
                });
              })()}
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
