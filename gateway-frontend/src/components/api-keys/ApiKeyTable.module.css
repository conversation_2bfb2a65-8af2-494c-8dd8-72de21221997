.table {
  margin-top: var(--spacing-base);
}

.table :global(.ant-table) {
  background: var(--color-background);
  border-radius: var(--radius-md);
  border: 1px solid var(--color-border);
}

.secretKeyCell {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.secretKeyCell span {
  font-family: var(--font-family-mono);
}

.actions {
  display: flex;
  gap: var(--spacing-sm);
}

.tableHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-base);
}

.tableTitle {
  margin: 0;
  font-size: var(--font-size-md);
  font-weight: var(--font-weight-medium);
}

.statusActive {
  color: var(--color-success);
  font-weight: var(--font-weight-medium);
}

.statusInactive {
  color: var(--color-error);
  font-weight: var(--font-weight-medium);
}

.disabled<PERSON>ey {
  color: var(--color-success);
}