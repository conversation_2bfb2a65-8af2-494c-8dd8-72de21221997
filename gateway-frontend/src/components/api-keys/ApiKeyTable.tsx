'use client';

import { Table, Button, Tooltip, Modal, message, TableColumnProps } from 'antd';
import { CopyOutlined, DeleteOutlined, EyeOutlined, StopOutlined, ReloadOutlined } from '@ant-design/icons';
import { useLocaleContext } from '@/contexts/LocaleContext';
import styles from './ApiKeyTable.module.css';
import { useRouter } from 'next/navigation';
import { ApiKey, deleteApiKey, updateApiKey } from '@/api/apiKeys';
import { format } from 'date-fns';

interface ApiKeyTableProps {
  loading?: boolean;
  dataSource?: ApiKey[];
  total?: number;
  page?: number;
  pageSize?: number;
  onPageChange?: (page: number, pageSize?: number) => void;
  onRefresh?: () => void;
}

export default function ApiKeyTable({
  loading = false,
  dataSource = [],
  total = 0,
  page = 1,
  pageSize = 10,
  onPageChange,
  onRefresh
}: ApiKeyTableProps) {
  const { messages } = useLocaleContext();
  const router = useRouter();

  // Format date for display
  const formatDate = (dateString: string) => {
    try {
      return format(new Date(dateString), 'MM-dd-yyyy');
    } catch (error) {
      return dateString;
    }
  };

  // Handle key deletion
  const handleDelete = (id: string) => {
    Modal.confirm({
      title: messages.apiKeys.table.deleteConfirmTitle || 'Delete API Key',
      content: messages.apiKeys.table.deleteConfirmMessage || 'Are you sure you want to delete this API key? This action cannot be undone.',
      okText: messages.apiKeys.table.deleteConfirm || 'Delete',
      okType: 'danger',
      cancelText: messages.apiKeys.table.deleteCancel || 'Cancel',
      onOk: async () => {
        try {
          await deleteApiKey(id);
          message.success(messages.apiKeys.table.deleteSuccess || 'API key deleted successfully');
          if (onRefresh) onRefresh();
        } catch (error) {
          console.error('Error deleting API key:', error);
          message.error(messages.apiKeys.table.deleteError || 'Failed to delete API key');
        }
      }
    });
  };

  // Handle key status toggle
  const handleToggleStatus = async (id: string, currentStatus: string) => {
    try {
      const newStatus = currentStatus === 'active' ? 'inactive' : 'active';
      await updateApiKey(id, { status: newStatus as any });
      message.success(
        newStatus === 'active'
          ? (messages.apiKeys.table.enableSuccess || 'API key enabled successfully')
          : (messages.apiKeys.table.disableSuccess || 'API key disabled successfully')
      );
      if (onRefresh) onRefresh();
    } catch (error) {
      console.error('Error updating API key status:', error);
      message.error(messages.apiKeys.table.updateError || 'Failed to update API key');
    }
  };

  const columns: TableColumnProps<ApiKey>[] = [
    {
      title: messages.apiKeys.table.name,
      key: 'name',
      align: 'center',
      render: (row: ApiKey) => `${row.name || 'Unnamed Key'}`,
    },
    {
      title: messages.apiKeys.table.type,
      key: 'type',
      align: 'center',
      render: (row: ApiKey) => `${row.type || 'Unnamed Type'}`,
    },
    {
      title: messages.apiKeys.table.secretKey,
      key: 'secretKey',
      align: 'center',
      render: (row: ApiKey) => (
        <div className={styles.secretKeyCell}>
          <span>{row.keyMask}</span>
          <Tooltip title={messages.apiKeys.table.copy}>
            <Button
              type="text"
              icon={<CopyOutlined />}
              onClick={() => {
                navigator.clipboard.writeText(row.keyMask);
                message.success(messages.apiKeys.table.copied || 'Copied to clipboard');
              }}
            />
          </Tooltip>
        </div>
      ),
    },
    {
      title: messages.apiKeys.table.created,
      key: 'created',
      align: 'center',
      render: (row: ApiKey) => formatDate(row.createdAt),
    },
    {
      title: messages.apiKeys.table.lastUsed,
      key: 'lastUsed',
      align: 'center',
      render: (row: ApiKey) => row.lastUsed ? formatDate(row.lastUsed) : '-',
    },
    {
      title: messages.apiKeys.table.createdBy,
      key: 'createdBy',
      align: 'center',
      render: (row: ApiKey) => row.createdBy || '-',
    },
    {
      title: messages.apiKeys.table.status,
      key: 'status',
      align: 'center',
      render: (row: ApiKey) => (
        <span className={row.status === 'active' ? styles.statusActive : styles.statusInactive}>
          {row.status}
        </span>
      ),
    },
    {
      title: messages.apiKeys.table.actions,
      key: 'actions',
      align: 'center',
      render: (row: ApiKey) => (
        <div className={styles.actions}>
          <Tooltip title={messages.apiKeys.table.view}>
            <Button
              type="text"
              icon={<EyeOutlined />}
              onClick={() => {
                router.push(`/api-keys/${row.id}`);
              }}
            />
          </Tooltip>
          <Tooltip title={row.status === 'active' ? messages.apiKeys.table.disable : messages.apiKeys.table.enable}>
            <Button
              type="text"
              icon={<StopOutlined />}
              onClick={() => handleToggleStatus(row.id, row.status)}
              className={row.status === 'active' ? '' : styles.disabledKey}
            />
          </Tooltip>
          <Tooltip title={messages.apiKeys.table.delete}>
            <Button
              type="text"
              danger
              icon={<DeleteOutlined />}
              onClick={() => handleDelete(row.id)}
            />
          </Tooltip>
        </div>
      ),
    },
  ];

  return (
    <div>
      <div className={styles.tableHeader}>
        <h3 className={styles.tableTitle}>{messages.apiKeys.table.title || 'My API Keys'}</h3>
        <Button
          icon={<ReloadOutlined />}
          onClick={onRefresh}
          loading={loading}
        >
          {messages.apiKeys.table.refresh || 'Refresh'}
        </Button>
      </div>
      <Table
        columns={columns}
        dataSource={dataSource.map(item => ({ ...item, key: item.id }))}
        pagination={{
          current: page,
          pageSize: pageSize,
          total: total,
          onChange: onPageChange
        }}
        loading={loading}
        className={styles.table}
        locale={{
          emptyText: messages.apiKeys.table.noData || 'No API keys found'
        }}
      />
    </div>
  );
}