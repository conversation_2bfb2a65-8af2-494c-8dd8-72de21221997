'use client';

import { Card, Spin } from 'antd';
import Image from 'next/image';
import { useEffect, useRef } from 'react';
import styles from './ChannelCards.module.css';
import { useLocaleContext } from '@/contexts/LocaleContext';
import { ApiCategory } from '@/api/apiKeys';
import Image1 from '../../../public/openai.png';
import Image2 from '../../../public/deepseek.png';
import Image3 from '../../../public/claude.png';
import Image4 from '../../../public/ollama.png';

// Default icons for known providers
const providerIcons: Record<string, string> = {
  'OpenAI': Image1.src,
  'DeepSeek': Image2.src,
  'Claude': Image3.src,
  'Anthropic': Image3.src,
  'Ollama': Image4.src,
};

interface ChannelCardsProps {
  categories?: ApiCategory[];
}

export default function ChannelCards({ categories }: ChannelCardsProps) {
  const { messages } = useLocaleContext();
  const containerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (!categories || categories.length === 0) return;

    const container = containerRef.current;
    if (!container) return;

    let scrollPosition = 0;
    let animationId: number;
    let isHovered = false;
    const scrollSpeed = 0.5; // 滚动速度，像素/帧
    const cardWidth = 250 + 16; // 卡片宽度 + gap
    const totalWidth = categories.length * cardWidth;

    const autoScroll = () => {
      if (!isHovered) {
        scrollPosition += scrollSpeed;

        // 当滚动到一半时重置位置，创建无缝循环效果
        if (scrollPosition >= totalWidth) {
          scrollPosition = 0;
        }

        container.scrollLeft = scrollPosition;
      }

      animationId = requestAnimationFrame(autoScroll);
    };

    // 开始自动滚动
    animationId = requestAnimationFrame(autoScroll);

    // 鼠标悬停时暂停滚动
    const handleMouseEnter = () => {
      isHovered = true;
    };

    const handleMouseLeave = () => {
      isHovered = false;
    };

    container.addEventListener('mouseenter', handleMouseEnter);
    container.addEventListener('mouseleave', handleMouseLeave);

    return () => {
      cancelAnimationFrame(animationId);
      container.removeEventListener('mouseenter', handleMouseEnter);
      container.removeEventListener('mouseleave', handleMouseLeave);
    };
  }, [categories]);

  if (!categories || categories.length === 0) {
    return (
      <div className={styles.loadingContainer}>
        <Spin />
      </div>
    );
  }

  // 复制数组以创建无缝循环效果
  const duplicatedCategories = [...categories, ...categories];

  return (
    <div ref={containerRef} className={styles.container}>
      {duplicatedCategories.map((category, index) => {
        // Get icon from category or use default based on provider
        const iconUrl = category.iconUrl ||
                       providerIcons[category.provider] ||
                       providerIcons.Default;

        return (
          <Card key={`${category.id}-${index}`} className={styles.card}>
            <div className={styles.cardContent}>
              <div className={styles.iconWrapper}>
                <Image
                  src={iconUrl}
                  alt={category.provider}
                  width={32}
                  height={32}
                  className={styles.icon}
                />
              </div>
              <div>
                <h4 className={styles.title}>{category.provider}</h4>
                <p className={styles.description}>
                  {category.description ||
                   messages.apiKeys.createModal.channelDesc[category.name as keyof typeof messages.apiKeys.createModal.channelDesc] ||
                   `${category.provider} ${category.category} API`}
                </p>
              </div>
            </div>
          </Card>
        );
      })}
    </div>
  );
}