.toggleContainer {
  display: flex;
  background: #FAFAFA;
  border-radius: 16px;
  padding: 0 8px;
  gap: 0;
  width: fit-content;
  height: auto;
  align-items: center;
}

.toggleOption {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 4px;
  padding: 12px 0 16px;
  cursor: pointer;
  transition: all 0.2s ease;
  border-radius: 12px;
  min-width: 80px;
  position: relative;
}

.toggleOption:hover {
  background: rgba(255, 255, 255, 0.5);
}

.toggleOption.selected {
  background: transparent;
}

.iconContainer {
  width: 64px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 100px;
  position: relative;
  transition: all 0.2s ease;
}

.toggleOption.selected .iconContainer {
  background: linear-gradient(90deg, rgba(255, 255, 255, 0.4) 0%, rgba(255, 255, 255, 0.04) 100%);
  border: 1px solid;
  border-image: linear-gradient(135deg, #D4D4D4 0%, #FFFFFF 9.69%, #FFFFFF 89.15%, #D4D4D4 100%) 1;
  box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.09);
}

.icon {
  width: 24px;
  height: 24px;
  object-fit: contain;
  stroke: #1E1E1E;
  stroke-width: 2px;
}

.label {
  font-family: 'Roboto', sans-serif;
  font-size: 12px;
  line-height: 16px;
  letter-spacing: 0.5px;
  text-align: center;
  transition: all 0.2s ease;
  color: #49454F;
}

.toggleOption.selected .label {
  font-weight: 600;
  color: #1D1B20;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .toggleContainer {
    width: 100%;
    justify-content: center;
  }
  
  .toggleOption {
    flex: 1;
    max-width: 120px;
  }
}
