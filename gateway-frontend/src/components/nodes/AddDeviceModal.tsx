import React from 'react';
import { Modal } from 'antd';
import CreateDeviceWizard from './CreateDeviceWizard';
import { Device } from '@/types/node';

interface AddDeviceModalProps {
  visible: boolean;
  onCancel: () => void;
  onSuccess: () => void;
  resumeDevice?: Device | null;
  mode?: 'create' | 'resume';
}

const AddDeviceModal: React.FC<AddDeviceModalProps> = ({
  visible,
  onCancel,
  onSuccess,
  resumeDevice = null,
  mode = 'create'
}) => {
  return (
    <Modal
      title={mode === 'resume' ? 'Resume Device Connection' : 'Add New Device'}
      open={visible}
      onCancel={onCancel}
      footer={null}
      width={900}
      destroyOnClose
    >
      <CreateDeviceWizard
        onCancel={onCancel}
        onSuccess={onSuccess}
        resumeDevice={resumeDevice}
        mode={mode}
      />
    </Modal>
  );
};

export default AddDeviceModal;
