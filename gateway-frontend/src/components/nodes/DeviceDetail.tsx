import React, { useState, useEffect } from 'react';
import { Card, Descriptions, Tag, Button, Space, Statistic, Row, Col, Progress, Spin, message, Divider, Typography, Tabs, Table, Timeline, Empty } from 'antd';
import { ReloadOutlined, DesktopOutlined, DollarOutlined, HistoryOutlined, CodeOutlined, AppstoreOutlined } from '@ant-design/icons';
import { nodeApi } from '@/api/node';
import { Device, DeviceStatusChange, Task, TaskStatus } from '@/types/node';
import { formatDistanceToNow, format } from 'date-fns';
import { useLocaleContext } from '@/contexts/LocaleContext';
import ModelTable from '@/components/models/ModelTable';
import styles from './DeviceDetail.module.css';

const { Title, Text } = Typography;

// Status color mapping
const statusColors = {
  waiting: 'orange',
  'in-progress': 'blue',
  connected: 'green',
  disconnected: 'red',
  failed: 'red',
};

// Status text mapping will use internationalization messages inside the component

// Task status color mapping
const taskStatusColors = {
  pending: 'orange',
  running: 'blue',
  completed: 'green',
  failed: 'red',
  cancelled: 'gray',
};

// Task status text mapping will use internationalization messages inside the component

interface DeviceDetailProps {
  deviceId: string;
}

const DeviceDetail: React.FC<DeviceDetailProps> = ({ deviceId }) => {
  const [device, setDevice] = useState<Device | null>(null);
  const [statusHistory, setStatusHistory] = useState<DeviceStatusChange[]>([]);
  const [tasks, setTasks] = useState<Task[]>([]);
  const [loading, setLoading] = useState(true);
  const [historyLoading, setHistoryLoading] = useState(false);
  const [tasksLoading, setTasksLoading] = useState(false);
  const [activeTab, setActiveTab] = useState('info');
  const [modelRefreshTrigger, setModelRefreshTrigger] = useState(0);
  const { messages } = useLocaleContext();

  // Load device details
  const loadDeviceDetails = async () => {
    setLoading(true);
    try {
      const response = await nodeApi.getDeviceDetails(encodeURIComponent(deviceId));
      setDevice(response.data);
    } catch (error) {
      message.error(messages.nodes.deviceDetail.loading);
    } finally {
      setLoading(false);
    }
  };

  // Load status history
  const loadStatusHistory = async () => {
    if (!deviceId) return;
    setHistoryLoading(true);
    try {
      const response = await nodeApi.getDeviceStatusHistory(deviceId);
      setStatusHistory(response.data);
    } catch (error) {
      message.error(messages.nodes.deviceDetail.statusHistory.loading);
    } finally {
      setHistoryLoading(false);
    }
  };

  // Load task list
  const loadTasks = async () => {
    if (!deviceId) return;
    setTasksLoading(true);
    try {
      const response = await nodeApi.getDeviceTasks(deviceId);
      setTasks(response.data.data);
    } catch (error) {
      message.error(messages.nodes.deviceDetail.taskList.loading);
    } finally {
      setTasksLoading(false);
    }
  };

  // Initial load
  useEffect(() => {
    if (deviceId) {
      loadDeviceDetails();
    }
  }, [deviceId]);

  // Load corresponding data when switching tabs
  useEffect(() => {
    if (activeTab === 'history' && statusHistory.length === 0) {
      loadStatusHistory();
    } else if (activeTab === 'tasks' && tasks.length === 0) {
      loadTasks();
    } else if (activeTab === 'models') {
      // 模型数据会在 ModelTable 组件内部加载
    }
  }, [activeTab]);

  if (loading) {
    return (
      <div style={{ textAlign: 'center', padding: '50px 0' }}>
        <Spin size="large" />
        <div style={{ marginTop: 16 }}>{messages.nodes.deviceDetail.loading}</div>
      </div>
    );
  }

  if (!device) {
    return (
      <Card>
        <div style={{ textAlign: 'center', padding: '50px 0' }}>
          <Title level={4}>{messages.nodes.deviceDetail.deviceNotFound}</Title>
          <Text type="secondary">{messages.nodes.deviceDetail.deviceNotFoundDesc?.replace('{deviceId}', deviceId)}</Text>
        </div>
      </Card>
    );
  }

  // Render status history
  const renderStatusHistory = () => {
    if (historyLoading) {
      return <Spin tip={messages.nodes.deviceDetail.statusHistory.loading} />;
    }

    if (statusHistory.length === 0) {
      return <Empty description={messages.nodes.deviceDetail.statusHistory.noHistory} />;
    }

    return (
      <Timeline mode="left">
        {statusHistory.map((item) => (
          <Timeline.Item
            key={item.id}
            color={statusColors[item.to_status] || 'blue'}
            label={format(new Date(item.change_time), 'yyyy-MM-dd HH:mm:ss')}
          >
            <>
              {messages.nodes.deviceDetail.statusHistory.statusChanged
                ? (
                  <>
                    {messages.nodes.deviceDetail.statusHistory.statusChanged.split('{from}')[0]}
                    <Tag color={statusColors[item.from_status]}>{messages.nodes.management.status[item.from_status]}</Tag>
                    {messages.nodes.deviceDetail.statusHistory.statusChanged.split('{from}')[1].split('{to}')[0]}
                    <Tag color={statusColors[item.to_status]}>{messages.nodes.management.status[item.to_status]}</Tag>
                    {messages.nodes.deviceDetail.statusHistory.statusChanged.split('{to}')[1]}
                  </>
                ) : (
                  <>
                    Status from <Tag color={statusColors[item.from_status]}>{messages.nodes.management.status[item.from_status]}</Tag>
                    to <Tag color={statusColors[item.to_status]}>{messages.nodes.management.status[item.to_status]}</Tag>
                  </>
                )
              }
            </>
          </Timeline.Item>
        ))}
      </Timeline>
    );
  };

  // Render task list
  const renderTasks = () => {
    if (tasksLoading) {
      return <Spin tip={messages.nodes.deviceDetail.taskList.loading} />;
    }

    const columns = [
      {
        title: messages.nodes.deviceDetail.taskList.columns.taskId,
        dataIndex: 'id',
        key: 'id',
        render: (text: string) => text.substring(0, 8),
      },
      {
        title: messages.nodes.deviceDetail.taskList.columns.model,
        dataIndex: 'model',
        key: 'model',
      },
      {
        title: messages.nodes.deviceDetail.taskList.columns.status,
        dataIndex: 'status',
        key: 'status',
        render: (status: TaskStatus) => (
          <Tag color={taskStatusColors[status] || 'default'}>
            {messages.nodes.deviceDetail.taskList.status[status]}
          </Tag>
        ),
      },
      {
        title: messages.nodes.deviceDetail.taskList.columns.createdAt,
        dataIndex: 'created_at',
        key: 'created_at',
        render: (text: string) => format(new Date(text), 'yyyy-MM-dd HH:mm:ss'),
      },
      {
        title: messages.nodes.deviceDetail.taskList.columns.duration,
        dataIndex: 'total_duration',
        key: 'total_duration',
        render: (value: number) => value ? `${(value / 1000000000).toFixed(2)}${messages.nodes.deviceDetail.seconds}` : '-',
      },
    ];

    return (
      <Table
        columns={columns}
        dataSource={tasks}
        rowKey="id"
        pagination={{ pageSize: 5 }}
      />
    );
  };

  return (
    <div>
      <Card
        title={
          <Space>
            <DesktopOutlined className={styles.deviceIcon}/>
            {device.name || `${messages.nodes.deviceDetail.infoItems.deviceId} ${device.id ? device.id.substring(0, 8) : ''}`}
          </Space>
        }
        extra={
          <Space>
            <Tag color={statusColors[device.status] || 'default'}>
              {messages.nodes.management.status[device.status]}
            </Tag>
            <Button icon={<ReloadOutlined />} onClick={loadDeviceDetails}>
              {messages.nodes.management.table.refresh}
            </Button>
          </Space>
        }
      >
        <Tabs
          activeKey={activeTab}
          onChange={setActiveTab}
          items={[
            {
              key: 'info',
              label: (
                <span>
                  <DesktopOutlined className={styles.deviceIcon}/>
                  {messages.nodes.deviceDetail.tabs.deviceInfo}
                </span>
              ),
              children: (
                <>
                  <Row gutter={[16, 16]}>
                    <Col span={6}>
                      <Statistic
                        title={messages.nodes.deviceDetail.stats.cpuUsage}
                        suffix="%"
                        prefix={
                          <Progress
                            type="dashboard"
                            percent={device.cpu_usage_percent || 0}
                            size={80}
                            status={device.cpu_usage_percent && device.cpu_usage_percent > 90 ? 'exception' : 'normal'}
                          />
                        }
                      />
                    </Col>
                    <Col span={6}>
                      <Statistic
                        title={messages.nodes.deviceDetail.stats.memoryUsage}
                        value={device.ram_usage_percent || (device.ram_available ? Math.round((1 - device.ram_available / device.ram_total!) * 100) : 0)}
                        suffix="%"
                        prefix={
                          <Progress
                            type="dashboard"
                            percent={device.ram_usage_percent || (device.ram_available ? Math.round((1 - device.ram_available / device.ram_total!) * 100) : 0)}
                            size={80}
                            status={device.ram_usage_percent && device.ram_usage_percent > 90 ? 'exception' : 'normal'}
                          />
                        }
                      />
                    </Col>
                    <Col span={6}>
                      {device.gpu_model ? (
                        <Statistic
                          title={messages.nodes.deviceDetail.stats.gpuUsage}
                          value={device.gpu_usage_percent || 0}
                          suffix="%"
                          prefix={
                            <Progress
                              type="dashboard"
                              percent={device.gpu_usage_percent || 0}
                              size={80}
                              status={device.gpu_usage_percent && device.gpu_usage_percent > 90 ? 'exception' : 'normal'}
                            />
                          }
                        />
                      ) : (
                        <Statistic
                          title={messages.nodes.deviceDetail.stats.gpuTemp || "GPU Temperature"}
                          value={device.gpu_temperature || 0}
                          suffix="°C"
                          prefix={
                            <Progress
                              type="dashboard"
                              percent={device.gpu_temperature ? Math.min(device.gpu_temperature / 100 * 100, 100) : 0}
                              size={80}
                              status={device.gpu_temperature && device.gpu_temperature > 80 ? 'exception' : 'normal'}
                            />
                          }
                        />
                      )}
                    </Col>
                    <Col span={6}>
                      <Statistic
                        title={messages.nodes.deviceDetail.stats.totalEarnings}
                        value={device.total_earnings || 0}
                        precision={2}
                        suffix="USDT"
                        prefix={<DollarOutlined />}
                      />
                    </Col>
                  </Row>

                  <Row gutter={[16, 16]} style={{ marginTop: '16px' }}>
                    <Col span={6}>
                      {device.gpu_temperature && (
                        <Statistic
                          title={messages.nodes.deviceDetail.stats.gpuTemp || "GPU Temperature"}
                          value={device.gpu_temperature || 0}
                          suffix="°C"
                          prefix={
                            <Progress
                              type="circle"
                              percent={device.gpu_temperature ? Math.min(device.gpu_temperature / 100 * 100, 100) : 0}
                              size={80}
                              status={device.gpu_temperature && device.gpu_temperature > 80 ? 'exception' : 'normal'}
                            />
                          }
                        />
                      )}
                    </Col>
                  </Row>

                  <Divider />
                  <Descriptions title={messages.nodes.deviceDetail.basicInfo} bordered column={2}>
                    <Descriptions.Item label={messages.nodes.deviceDetail.infoItems.deviceId}>{device.id}</Descriptions.Item>
                    <Descriptions.Item label={messages.nodes.deviceDetail.infoItems.deviceType}>{device.device_type || messages.nodes.deviceDetail.unknown}</Descriptions.Item>
                    <Descriptions.Item label={messages.nodes.deviceDetail.infoItems.createdAt}>{format(new Date(device.created_at), 'yyyy-MM-dd HH:mm:ss')}</Descriptions.Item>
                    <Descriptions.Item label={messages.nodes.deviceDetail.infoItems.lastActivity}>{device.last_ping ? formatDistanceToNow(new Date(device.last_ping), { addSuffix: true }) : messages.nodes.deviceDetail.unknown}</Descriptions.Item>
                    <Descriptions.Item label={messages.nodes.deviceDetail.infoItems.ownerAddress}>{device.owner_address || messages.nodes.deviceDetail.unknown}</Descriptions.Item>
                    <Descriptions.Item label={messages.nodes.deviceDetail.infoItems.rewardAddress}>{device.reward_address || messages.nodes.deviceDetail.unknown}</Descriptions.Item>
                    <Descriptions.Item label={messages.nodes.deviceDetail.infoItems.currentModel}>{device.current_model || messages.nodes.deviceDetail.none}</Descriptions.Item>
                    <Descriptions.Item label={messages.nodes.deviceDetail.infoItems.uptime}>{device.uptime_seconds ? `${Math.floor(device.uptime_seconds / 3600)}${messages.nodes.deviceDetail.hours}${Math.floor((device.uptime_seconds % 3600) / 60)}${messages.nodes.deviceDetail.minutes}` : messages.nodes.deviceDetail.unknown}</Descriptions.Item>
                  </Descriptions>

                  <Divider />

                  <Descriptions title={messages.nodes.deviceDetail.hardwareInfo} bordered column={2}>
                    <Descriptions.Item label={messages.nodes.deviceDetail.infoItems.cpuModel}>{device.cpu_model || messages.nodes.deviceDetail.unknown}</Descriptions.Item>
                    <Descriptions.Item label={messages.nodes.deviceDetail.infoItems.cpuCores}>{device.cpu_cores || messages.nodes.deviceDetail.unknown}</Descriptions.Item>
                    <Descriptions.Item label={messages.nodes.deviceDetail.infoItems.cpuThreads}>{device.cpu_threads || messages.nodes.deviceDetail.unknown}</Descriptions.Item>
                    <Descriptions.Item label={messages.nodes.deviceDetail.infoItems.totalMemory}>{device.ram_total ? `${(device.ram_total / 1024).toFixed(2)} GB` : messages.nodes.deviceDetail.unknown}</Descriptions.Item>
                    <Descriptions.Item label={messages.nodes.deviceDetail.infoItems.gpuModel}>{device.gpu_model || messages.nodes.deviceDetail.unknown}</Descriptions.Item>
                     <Descriptions.Item label={messages.nodes.deviceDetail.infoItems.gpuCount}>{device.gpu_count || messages.nodes.deviceDetail.unknown}</Descriptions.Item>
                     <Descriptions.Item label={messages.nodes.deviceDetail.infoItems.gpuMemory}>{device.gpu_memory ? `${(device.gpu_memory / 1024).toFixed(2)} GB` : messages.nodes.deviceDetail.unknown}</Descriptions.Item>
                     <Descriptions.Item label={messages.nodes.deviceDetail.infoItems.diskTotal}>{device.disk_total ? `${(device.disk_total / 1024).toFixed(2)} GB` : messages.nodes.deviceDetail.unknown}</Descriptions.Item>
                    <Descriptions.Item label={messages.nodes.deviceDetail.infoItems.osInfo}>{device.os_info || messages.nodes.deviceDetail.unknown}</Descriptions.Item>
                    <Descriptions.Item label={messages.nodes.deviceDetail.infoItems.ipAddress}>{device.ip_address || messages.nodes.deviceDetail.unknown}</Descriptions.Item>
                  </Descriptions>
                </>
              ),
            },
            {
              key: 'history',
              label: (
                <span>
                  <HistoryOutlined className={styles.deviceIcon}/>
                  {messages.nodes.deviceDetail.tabs.statusHistory}
                </span>
              ),
              children: renderStatusHistory(),
            },
            {
              key: 'tasks',
              label: (
                <span>
                  <CodeOutlined className={styles.deviceIcon}/>
                  {messages.nodes.deviceDetail.tabs.taskList}
                </span>
              ),
              children: renderTasks(),
            },
            {
              key: 'models',
              label: (
                <span>
                  <AppstoreOutlined className={styles.deviceIcon}/>
                  {messages.nodes.deviceDetail.tabs.models}
                </span>
              ),
              children: (
                <div style={{ padding: '16px 0' }}>
                  <ModelTable
                    searchText=""
                    filterType="all"
                    deviceId={deviceId}
                    refreshTrigger={modelRefreshTrigger}
                  />
                </div>
              ),
            },
          ]}
        />

        <Divider />

        <Space>
          {activeTab === 'history' && (
            <Button icon={<ReloadOutlined />} onClick={loadStatusHistory}>
              {messages.nodes.deviceDetail.refreshStatusHistory}
            </Button>
          )}
          {activeTab === 'tasks' && (
            <Button icon={<ReloadOutlined />} onClick={loadTasks}>
              {messages.nodes.deviceDetail.refreshTaskList}
            </Button>
          )}
          {activeTab === 'models' && (
            <Button icon={<ReloadOutlined />} onClick={() => {
              // 通过增加 refreshTrigger 的值来触发 ModelTable 重新获取数据
              setModelRefreshTrigger(prev => prev + 1);
            }}>
              {messages.nodes.deviceDetail.refreshModels}
            </Button>
          )}
        </Space>
      </Card>
    </div>
  );
};

export default DeviceDetail;
