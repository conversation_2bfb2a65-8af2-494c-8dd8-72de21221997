.deviceId {
  font-family: var(--font-family-mono);
  color: var(--color-foreground);
}

.deviceName {
  color: var(--color-foreground);
  font-weight: var(--font-weight-medium);
}

.deviceCell {
  display: flex;
  align-items: center;
}

.deviceIcon {
  width: 20px;
  height: 20px;
  margin-right: var(--spacing-sm);
}

.unknownDevice {
  color: var(--color-text-tertiary);
  display: flex;
  align-items: center;
}

.statusTag {
  composes: status-tag from global;
}

.modelTag {
  display: flex;
  align-items: center;
}

.earningsCell {
  text-align: right;
  font-weight: var(--font-weight-medium);
}