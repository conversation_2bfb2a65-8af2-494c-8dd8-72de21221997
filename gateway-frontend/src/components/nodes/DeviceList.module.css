/* DeviceList 组件样式 */
.deviceListCard {
  border-radius: 16px;
  border: none;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

/* 统一分页样式 */
.deviceTable .ant-pagination {
  margin: 24px 0 0 0;
  padding: 20px 0;
  border-top: 1px solid #f1f5f9;
}

.deviceTable .ant-pagination-item {
  border-radius: 6px;
  border: 1px solid #e5e7eb;
  margin: 0 4px;
  transition: all 0.2s ease;
}
.searchInput:focus {
  border-color: #6366f1;
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}
.deviceTable .ant-pagination-item:hover {
  border-color: #6366f1;
}

.deviceTable .ant-pagination-item-active {
  background: #6366f1;
  border-color: #6366f1;
}

.deviceTable .ant-pagination-item-active a {
  color: white;
}

.deviceTable .ant-pagination-prev,
.deviceTable .ant-pagination-next {
  border-radius: 6px;
  border: 1px solid #e5e7eb;
  transition: all 0.2s ease;
}

.deviceTable .ant-pagination-prev:hover,
.deviceTable .ant-pagination-next:hover {
  border-color: #6366f1;
}

.deviceTable .ant-pagination-options {
  margin-left: 16px;
}

.deviceTable .ant-pagination-options .ant-select {
  margin-right: 8px;
}

.deviceTable .ant-pagination-total-text {
  color: #6b7280;
  font-size: 14px;
}

/* 表格样式优化 */
.deviceTable .ant-table-thead > tr > th {
  background: #f8fafc;
  border-bottom: 1px solid #e5e7eb;
  font-weight: 600;
  color: #374151;
  font-size: 13px;
}

.deviceTable .ant-table-tbody > tr:hover > td {
  background: #f8fafc;
}

/* 搜索区域样式 */
.searchSection {
  margin-bottom: 16px;
}

.searchSection .ant-input {
  border-radius: 8px;
  border: 1px solid #e5e7eb;
}

.searchSection .ant-input:focus {
  border-color: #6366f1;
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.searchSection .ant-select .ant-select-selector {
  border-radius: 8px;
  border: 1px solid #e5e7eb;
}

.searchSection .ant-btn {
  border-radius: 8px;
  border: 1px solid #e5e7eb;
}

.searchSection .ant-btn:hover {
  border-color: #6366f1;
  color: #6366f1;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .deviceTable .ant-table-thead > tr > th,
  .deviceTable .ant-table-tbody > tr > td {
    padding: 12px 8px;
    font-size: 12px;
  }
}
