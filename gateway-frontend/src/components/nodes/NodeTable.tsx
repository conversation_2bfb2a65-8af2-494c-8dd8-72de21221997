import React, { useState } from 'react';
import { Table, Tag, Tooltip, Typography } from 'antd';
import { DesktopOutlined } from '@ant-design/icons';
import { useLocaleContext } from '@/contexts/LocaleContext';
import styles from './NodeTable.module.css';
import Image from '../../public/nvidia.svg';

const { Text } = Typography;

interface NodeData {
  node_id: string;
  task_name: string;
  device_type: string;
  gpu_type?: string;
  status: string;
  current_ai_model: string;
  connected_gateway: string;
  total_earnings: number;
  pending_earnings: number;
}

interface NodeTableProps {
  searchText?: string;
  filterStatus?: string;
}

const getDeviceIcon = (type: string) => {
  switch (type.toLowerCase()) {
    case 'nvidia':
      return <img src={Image.src} alt="NVIDIA" className={styles.deviceIcon} />;
    case 'cpu':
      return <DesktopOutlined className={styles.deviceIcon} />;
    default:
      return <DesktopOutlined className={styles.deviceIcon} />;
  }
};

const NodeTable: React.FC<NodeTableProps> = ({ searchText = '', filterStatus = 'all' }) => {
  const { messages } = useLocaleContext();

  const columns = [
    {
      title: messages.nodes.management.table.deviceId,
      dataIndex: 'node_id',
      key: 'node_id',
      width: 120,
      ellipsis: true,
      render: (id: string) => (
        <Tooltip title={id}>
          <span className={styles.deviceId}>{id}</span>
        </Tooltip>
      ),
    },
    {
      title: messages.nodes.management.table.deviceName,
      dataIndex: 'task_name',
      key: 'task_name',
      width: 120,
      render: (name: string) => (
        <span className={styles.deviceName}>
          {name || 'Unnamed Device'}
        </span>
      ),
    },
    {
      title: messages.nodes.management.table.deviceType,
      dataIndex: 'device_type',
      key: 'device_type',
      width: 200,
      render: (type: string | null, record: NodeData) => {
        if (!type && !record.gpu_type) return (
          <span className={styles.unknownDevice}>
            <DesktopOutlined style={{ marginRight: 8 }} /> Unknown
          </span>
        );
        const deviceType = type || '';
        const gpuType = record.gpu_type ? ` (${record.gpu_type})` : '';
        const displayType = `${deviceType}${gpuType}`;
        const isGpu = (deviceType + gpuType).toLowerCase().includes('gpu') ||
                     (deviceType + gpuType).toLowerCase().includes('nvidia');
        return (
          <div className={styles.deviceCell}>
            {getDeviceIcon(isGpu ? 'nvidia' : deviceType)}
            <span style={{ marginLeft: '8px' }}>{displayType || 'Unknown'}</span>
          </div>
        );
      },
    },
    {
      title: messages.nodes.management.table.status,
      dataIndex: 'status',
      key: 'status',
      width: 120,
      render: (status: string) => {
        const statusMap: Record<string, { color: string; text: string }> = {
          online: { color: '#52c41a', text: messages.nodes.management.status.online },
          offline: { color: '#d9d9d9', text: messages.nodes.management.status.offline },
          busy: { color: '#faad14', text: messages.nodes.management.status.busy },
          error: { color: '#ff4d4f', text: messages.nodes.management.status.error },
        };
        const statusInfo = statusMap[status.toLowerCase()] || { color: '#faad14', text: status };
        return (
          <Tag color={statusInfo.color} className={styles.statusTag}>
            {statusInfo.text.toUpperCase()}
          </Tag>
        );
      },
    },
    {
      title: messages.nodes.management.table.currentModel,
      dataIndex: 'current_ai_model',
      key: 'current_ai_model',
      width: 150,
      render: (model: string) => (
        <div className={styles.modelTag}>
          {model ? (
            <Tag color="#177ddc" style={{ margin: 0 }}>
              {model}
            </Tag>
          ) : <span style={{ color: 'rgba(0, 0, 0, 0.45)' }}>-</span>}
        </div>
      ),
    },
    {
      title: messages.nodes.management.table.connectedGateway,
      dataIndex: 'connected_gateway',
      key: 'connected_gateway',
      width: 150,
      render: (gateway: string) => gateway ? (
        <Tooltip title={gateway}>
          <Tag color="#722ed1" style={{ margin: 0 }}>
            {gateway.length > 20 ? gateway.substring(0, 20) + '...' : gateway}
          </Tag>
        </Tooltip>
      ) : <span style={{ color: 'rgba(0, 0, 0, 0.45)' }}>-</span>,
    },
    {
      title: messages.nodes.management.table.totalEarnings,
      dataIndex: 'total_earnings',
      key: 'total_earnings',
      width: 120,
      render: (value: number) => (
        <div className={styles.earningsCell}>
          {value ? (
            <Text style={{ color: '#52c41a', fontWeight: 'bold' }}>
              {value.toFixed(1)}
            </Text>
          ) : '0.0'}
        </div>
      ),
    },
    {
      title: messages.nodes.management.table.pendingEarnings,
      dataIndex: 'pending_earnings',
      key: 'pending_earnings',
      width: 120,
      render: (value: number) => (
        <div className={styles.earningsCell}>
          {value ? (
            <Text style={{ color: '#faad14', fontWeight: 'bold' }}>
              {value.toFixed(1)}
            </Text>
          ) : '0.0'}
        </div>
      ),
    },
  ];

  // Data source
  const [data, setData] = useState<NodeData[]>([]);

  // Filter data
  const filteredData = data.filter(node => {
    const matchesSearch = searchText === '' ||
      node.node_id.toLowerCase().includes(searchText.toLowerCase()) ||
      node.task_name.toLowerCase().includes(searchText.toLowerCase()) ||
      node.device_type.toLowerCase().includes(searchText.toLowerCase());

    const matchesStatus = filterStatus === 'all' || node.status === filterStatus;

    return matchesSearch && matchesStatus;
  });

  return (
    <Table
      columns={columns}
      dataSource={filteredData}
      rowKey="node_id"
      pagination={{
        pageSize: 10,
        showSizeChanger: true,
        showQuickJumper: true,
      }}
      scroll={{ x: 1200 }}
    />
  );
};

export default NodeTable;