'use client';

import { useState, useEffect } from 'react';
import { Modal, Typography, Button, message, Input, App } from 'antd';
import { CopyOutlined, SyncOutlined } from '@ant-design/icons';
import { useLocaleContext } from '@/contexts/LocaleContext';
import styles from './WaitingConnectionModal.module.css';
import sharedStyles from './shared.module.css';

const { Text, Paragraph } = Typography;

interface WaitingConnectionModalProps {
  open: boolean;
  onClose: () => void;
  nodeId: string;
  command: string;
  onConnectionSuccess: () => void;
}

export default function WaitingConnectionModal({
  open,
  onClose,
  nodeId,
  command,
  onConnectionSuccess,
}: WaitingConnectionModalProps) {
  const { messages } = useLocaleContext();
  const { message: messageApi } = App.useApp();
  const [copying, setCopying] = useState(false);
  const [connectionStatus, setConnectionStatus] = useState<'waiting' | 'connected' | 'failed'>('waiting');
  const [step, setStep] = useState(0);

  const handleCopy = async () => {
    try {
      setCopying(true);
      await navigator.clipboard.writeText(command);
      messageApi.success(messages.nodes.management.createModal.copied);
    } catch (error) {
      messageApi.error(messages.nodes.management.createModal.copyFailed);
    } finally {
      setCopying(false);
    }
  };

  useEffect(() => {
    if (!open) return;

    // TODO: Replace with actual connection status check
    // Example:
    // const checkConnectionStatus = async () => {
    //   try {
    //     const response = await checkNodeConnectionStatus(nodeId);
    //     if (response.status === 'connected') {
    //       setConnectionStatus('connected');
    //       onConnectionSuccess();
    //     } else if (response.status === 'failed') {
    //       setConnectionStatus('failed');
    //     } else {
    //       setConnectionStatus('waiting');
    //       setTimeout(checkConnectionStatus, 3000);
    //     }
    //   } catch (error) {
    //     setConnectionStatus('failed');
    //   }
    // };
    //
    // checkConnectionStatus();

    // Default to waiting state
    setConnectionStatus('waiting');
  }, [open, nodeId, onConnectionSuccess]);

  const handleRetry = () => {
    setStep(0);
    setConnectionStatus('waiting');
  };

  return (
    <Modal
      title={messages.nodes.management.createModal.commandTitle}
      open={open}
      onCancel={onClose}
      footer={null}
      width={600}
      className={sharedStyles.modal}
    >
      <div className={styles.content}>
        <Paragraph className={styles.description}>
          {messages.nodes.management.createModal.commandDescription}
        </Paragraph>

        <div className={styles.commandBox}>
          <Input.TextArea
            value={command}
            readOnly
            autoSize={{ minRows: 2, maxRows: 6 }}
            className={styles.command}
            bordered={false}
          />
          <Button
            type="text"
            icon={<CopyOutlined />}
            onClick={handleCopy}
            loading={copying}
            className={styles.copyButton}
          />
        </div>

        {connectionStatus === 'waiting' && (
          <div className={styles.waitingStatus}>
            <SyncOutlined spin className={styles.spinIcon} />
            <Text>{messages.nodes.management.createModal.waitingDescription}</Text>
          </div>
        )}

        {connectionStatus === 'connected' && (
          <div className={styles.successStatus}>
            <Text type="success">{messages.nodes.management.createModal.connected}</Text>
          </div>
        )}

        {connectionStatus === 'failed' && (
          <div className={styles.failedStatus}>
            <Text type="danger">{messages.nodes.management.createModal.connectionFailed}</Text>
            <Button
              onClick={handleRetry}
              className={`${styles.retryButton} ${sharedStyles.primaryButton}`}
            >
              {messages.nodes.management.createModal.retry}
            </Button>
          </div>
        )}
      </div>
    </Modal>
  );
}