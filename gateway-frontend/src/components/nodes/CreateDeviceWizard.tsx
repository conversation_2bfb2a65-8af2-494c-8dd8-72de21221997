import React, { useState, useEffect, useRef } from 'react';
import {
  Form,
  Input,
  Button,
  Radio,
  Typography,
  Alert,
  Tabs,
  message as antMessage,
} from 'antd';
import {
  LoadingOutlined,
  DownloadOutlined,
  AppleOutlined,
  WindowsOutlined,
  DesktopOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined
} from '@ant-design/icons';
import { nodeApi } from '@/api/node';
import { useAccount } from 'wagmi';
import { useLocaleContext } from '@/contexts/LocaleContext';
import { useAuthStore } from '@/store/authStore';
import { Device } from '@/types/node';
import styles from './CreateDeviceWizard.module.css';
import { useRouter } from 'next/navigation';
import svg2 from '../../../public/SVG (2).png';
import svg from '../../../public/SVG.png';
import svg1 from '../../../public/SVG (1).png';


const { Title, Text, Paragraph } = Typography;

interface CreateDeviceWizardProps {
  onCancel: () => void;
  onSuccess: () => void;
  resumeDevice?: Device | null; // 恢复设备信息
  mode?: 'create' | 'resume'; // 模式：创建新设备或恢复现有设备
  deviceId?: string; // 设备ID，用于恢复模式
}

interface ConnectInfo {
  task_id: string;
  one_time_code: string;
  gateway_address: string;
}

interface DeviceFormData {
  deviceName: string;
  deviceType: string;
}

const CreateDeviceWizard: React.FC<CreateDeviceWizardProps> = ({
  onCancel,
  onSuccess,
  resumeDevice = null,
  mode = 'create',
  deviceId
}) => {
  const [form] = Form.useForm();
  const { address } = useAccount();
  const { messages } = useLocaleContext();
  const { accessToken } = useAuthStore();
  const router = useRouter();

  // 步骤状态
  const [currentStep, setCurrentStep] = useState(mode === 'resume' ? 1 : 0);
  const [loading, setLoading] = useState(false);
  const [deviceInfo, setDeviceInfo] = useState<Device | null>(resumeDevice);
  const [formData, setFormData] = useState<DeviceFormData>({
    deviceName: resumeDevice?.task_name || resumeDevice?.name || '',
    deviceType: resumeDevice?.device_type || 'macos-apple-silicon'
  });
  const [selectedDeviceType, setSelectedDeviceType] = useState<string>(resumeDevice?.device_type || 'macos-apple-silicon');
  useEffect(() => {
    form.setFieldsValue({
      deviceType: resumeDevice?.device_type || 'macos-apple-silicon'
    });
  }, []);
  // 连接信息
  const [connectInfo, setConnectInfo] = useState<ConnectInfo | null>(null);
  const [connectionStatus, setConnectionStatus] = useState<'waiting' | 'connected' | 'failed' | 'timeout'>('waiting');
  const [elapsedTime, setElapsedTime] = useState(0);
  const [maxWaitTime] = useState(60);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);

  // 设备类型选项
  const deviceTypes = [
    {
      key: 'macos-apple-silicon',
      label: messages.nodes.createWizard.step2.deviceTypes.macosAppleSilicon.label,
      icon: <img style={{ width: 24 }} src={svg2.src} alt="Apple" />,
      description: messages.nodes.createWizard.step2.deviceTypes.macosAppleSilicon.description
    },
    {
      key: 'macos-intel',
      label: messages.nodes.createWizard.step2.deviceTypes.macosIntel.label,
      icon: <img style={{ width: 24 }} src={svg2.src} alt="Apple" />,
      description: messages.nodes.createWizard.step2.deviceTypes.macosIntel.description
    },
    {
      key: 'linux',
      label: messages.nodes.createWizard.step2.deviceTypes.linux.label,
      icon: <img style={{ width: 24 }} src={svg.src} alt="Linux" />,
      description: messages.nodes.createWizard.step2.deviceTypes.linux.description
    },
    {
      key: 'windows',
      label: messages.nodes.createWizard.step2.deviceTypes.windows.label,
      icon: <img style={{ width: 24 }} src={svg1.src} alt="Windows" />,
      description: messages.nodes.createWizard.step2.deviceTypes.windows.description
    }
  ];

  // 步骤配置
  const steps = [
    {
      title: messages.nodes.createWizard.steps.step1.title,
      description: messages.nodes.createWizard.steps.step1.description
    },
    {
      title: messages.nodes.createWizard.steps.step2.title,
      description: messages.nodes.createWizard.steps.step2.description
    },
    {
      title: messages.nodes.createWizard.steps.step3.title,
      description: messages.nodes.createWizard.steps.step3.description
    }
  ];

  // 处理表单提交：设备名称和类型
  const handleFormSubmit = async () => {
    try {
      const values = await form.validateFields(['deviceName', 'deviceType']);
      setFormData(values);
      setSelectedDeviceType(values.deviceType);

      // 创建连接任务
      setLoading(true);
      await createConnectTask(values.deviceName, values.deviceType);
      setCurrentStep(1);
    } catch (error: any) {
      if (error.errorFields) {
        // 表单验证失败
        return;
      }
      antMessage.error('创建连接任务失败，请重试');
    } finally {
      setLoading(false);
    }
  };

  // 创建连接任务
  const createConnectTask = async (deviceName: string, deviceType: string) => {
    try {
      // 检查用户是否已认证
      if (!accessToken) {
        antMessage.error('Please login first');
        return;
      }

      // 调用真实的API接口
      const response:any = await nodeApi.createConnectTask({
        task_name: deviceName,
        signature: `create-task:${deviceName}:${address}:${Date.now()}`, // 简单的签名格式
        device_type: deviceType,
        gpu_type: undefined // 可选字段
      });

      // 处理API响应
      if (response.success && response.data) {
        setConnectInfo({
          task_id: response.data.task_id,
          one_time_code: response.data.one_time_code,
          gateway_address: response.data.gateway_address
        });
        antMessage.success('Device registered successfully!');
      }
    } catch (error: any) {
      console.error('Failed to create connect task:', error);
      antMessage.error(error.message || 'Failed to create connect task');
      throw error; // 重新抛出错误，让调用方处理
    }
  };

  // 根据设备类型获取可执行文件名
  const getExecutableName = (deviceType: string) => {
    switch (deviceType) {
      case 'macos-apple-silicon':
        return './sightai-macos-arm64';
      case 'macos-intel':
        return './sightai-macos-x86_64';
      case 'linux':
        return './sightai-linux-x86_64';
      case 'windows':
        return './sightai-win-x86_64.exe';
      default:
        return './sightai-linux-x86_64';
    }
  };

  // 复制到剪贴板
  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text).then(
      () => {
        antMessage.success(messages.nodes.createWizard.step3.copySuccess);
      },
      () => {
        antMessage.error(messages.nodes.createWizard.step3.copyFailed);
      }
    );
  };

  // 检查连接状态
  const checkConnectionStatus = async () => {
    if (!connectInfo || !connectInfo.task_id) return;

    try {
      const response = await nodeApi.checkConnectTaskStatus(connectInfo.task_id);

      if (response.success && response.data) {
        const taskStatus = response.data.status;

        if (taskStatus === 'connected') {
          setConnectionStatus('connected');
          if (intervalRef.current) {
            clearInterval(intervalRef.current);
            intervalRef.current = null;
          }
          antMessage.success(messages.nodes.createWizard.messages.deviceConnected);
          router.push(`/nodes/device/detail?id=${encodeURIComponent(response.data.device_id)}`);
        } else if (taskStatus === 'failed') {
          setConnectionStatus('failed');
          if (intervalRef.current) {
            clearInterval(intervalRef.current);
            intervalRef.current = null;
          }
          antMessage.error(messages.nodes.createWizard.messages.deviceConnectionFailed);
        }
      }
    } catch (error) {
      // 处理错误
      setConnectionStatus('failed');
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
    }
  };

  // 重试连接
  const handleRetry = async () => {
    try {
      // 重置状态
      setConnectionStatus('waiting');
      setElapsedTime(0);

      // 清除现有定时器
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }

      // 如果是恢复模式，需要重新创建连接任务并更新设备状态
      if (mode === 'resume' && (deviceInfo || deviceId)) {
        const deviceName = formData.deviceName || deviceInfo?.task_name || deviceInfo?.name || '';
        const deviceType = formData.deviceType || deviceInfo?.device_type || '';

        if (deviceName && deviceType) {
          await createReconnectTask(deviceName, deviceType);
        }
      }

      // 重新开始计时和状态检查
      startConnectionTimer();
    } catch (error) {
      console.error('Error during retry:', error);
      antMessage.error('Failed to retry connection');
    }
  };

  // 开始连接计时器
  const startConnectionTimer = () => {
    // 立即检查一次状态
    checkConnectionStatus();

    // 设置定时器
    intervalRef.current = setInterval(() => {
      setElapsedTime(prev => {
        const newTime = prev + 0.5;

        if (newTime >= maxWaitTime) {
          if (intervalRef.current) {
            clearInterval(intervalRef.current);
            intervalRef.current = null;
          }
          setConnectionStatus('timeout');
          antMessage.warning(messages.nodes.createWizard.messages.connectionTimeout);
          return maxWaitTime;
        }

        checkConnectionStatus();
        return newTime;
      });
    }, 5000);
  };

  // 启动连接状态检查
  useEffect(() => {
    if (currentStep === 1 && connectInfo && connectInfo.task_id) {
      checkConnectionStatus();
      // setConnectionStatus('waiting');
      setElapsedTime(0);

      // 使用新的计时器函数
      startConnectionTimer();

      return () => {
        if (intervalRef.current) {
          clearInterval(intervalRef.current);
          intervalRef.current = null;
        }
      };
    }
  }, [currentStep, connectInfo]);

  // 获取设备信息并创建重连任务
  const fetchDeviceInfo = async (deviceId: string) => {
    try {
      setLoading(true);
      const response = await nodeApi.getDeviceDetails(encodeURIComponent(deviceId));
      if (response.success && response.data) {
        const device = response.data;
        setDeviceInfo(device);
        console.log(response)
        // 设置表单数据
        const deviceName = device.task_name || device.name || '';
        const deviceType = device.device_type || '';

        form.setFieldsValue({
          deviceName: deviceName,
          deviceType: deviceType
        });

        setFormData({
          deviceName: deviceName,
          deviceType: deviceType
        });

        setSelectedDeviceType(deviceType);

        // 为设备创建新的连接任务
        await createReconnectTask(deviceName, deviceType);

        // 设置为第二步
        setCurrentStep(1);
      }
    } catch (error) {
      antMessage.error('Failed to load device information');
      console.error('Error fetching device info:', error);
    } finally {
      setLoading(false);
    }
  };

  // 创建重连任务
  const createReconnectTask = async (deviceName: string, deviceType: string) => {
    try {
      if (!deviceInfo?.id && !deviceId) {
        throw new Error('Device ID is required for reconnection');
      }

      const targetDeviceId = deviceInfo?.id || deviceId || '';
      const response = await nodeApi.reconnectDevice(targetDeviceId, deviceName, deviceType);
      // @ts-ignore
      const gatewayCommandUrl = process.env.NEXT_PUBLIC_GATEWAY_COMMAND_URL || connectInfo.gateway_address;

      if (response.success && response.data) {
        // 设置真实的连接信息
        setConnectInfo({
          task_id: response.data.rows[0].id,
          one_time_code: response.data.rows[0].one_time_code,
          gateway_address: gatewayCommandUrl
        });
        antMessage.success('Reconnection task created successfully!');
      }
    } catch (error) {
      antMessage.error('Failed to create reconnection task');
      console.error('Error creating reconnect task:', error);
      throw error;
    }
  };

  // 恢复模式初始化
  useEffect(() => {
    if (mode === 'resume') {
      if (resumeDevice) {
        // 如果已经有设备信息，直接使用并创建重连任务
        setDeviceInfo(resumeDevice);

        const deviceName = resumeDevice.task_name || resumeDevice.name || '';
        const deviceType = resumeDevice.device_type || '';

        // 设置表单数据
        form.setFieldsValue({
          deviceName: deviceName,
          deviceType: deviceType
        });

        setFormData({
          deviceName: deviceName,
          deviceType: deviceType
        });

        setSelectedDeviceType(deviceType);

        // 创建重连任务
        createReconnectTask(deviceName, deviceType).then(() => {
          // 设置为第二步
          setCurrentStep(1);
        }).catch((error) => {
          console.error('Failed to create reconnect task:', error);
        });
      } else if (deviceId) {
        // 如果只有设备ID，需要获取设备信息
        fetchDeviceInfo(deviceId);
      }
    }
  }, [mode, resumeDevice, deviceId, form]);
  const statusColors = {
    'in-progress': 'blue',
    connected: 'green',
    disconnected: 'red',
    failed: 'red',
    waiting: 'orange'
  };
  const statusBackgroundColors = {
    'in-progress': 'rgba(0, 0, 0, 0.05)',
    connected: 'rgba(0, 128, 0, 0.1)',
    disconnected: 'rgba(255, 0, 0, 0.1)',
    failed: 'rgba(255, 0, 0, 0.1)',
    waiting: 'rgba(255, 165, 0, 0.1)'
  };
  const walletAddress = address || localStorage.getItem('walletAddress') || '';
      // @ts-ignore
  const gatewayCommandUrl = process.env.NEXT_PUBLIC_GATEWAY_COMMAND_URL || connectInfo.gateway_address;
  const serverBasePath = process.env.NEXT_PUBLIC_SERVER_BASE_PATH || '/';

  return (
    <div className={styles.wizard}>


      <div className={styles.content}>
        <Form form={form} layout="vertical" className={styles.form}>
          {/* 合并的步骤：设备名称和类型选择 */}
          {currentStep === 0 && (
            <div className={styles.stepContent}>
              <div className={styles.header}>
                <Title level={2}>
                  {mode === 'resume' ? 'Resume device connection' : 'Create new device'}
                </Title>
                <Paragraph type="secondary">
                  {mode === 'resume'
                    ? 'Resume the connection process for your existing device.'
                    : 'Note: Each machine can be registered as a single device. Registering again on the same machine will replace the previous registration.'
                  }
                </Paragraph>
                {mode === 'create' && (
                  <Paragraph type="secondary">
                    Required fields are marked with an asterisk (*).
                  </Paragraph>
                )}

                <div style={{ marginTop: 16, marginBottom: 24 }}>
                  <Paragraph strong>Please install Ollama to use this feature.</Paragraph>
                  <Button
                    type="default"
                    icon={<DownloadOutlined />}
                    href="https://ollama.ai"
                    target="_blank"
                    style={{
                      background: '#000',
                      color: '#fff',
                      border: 'none',
                      borderRadius: '6px'
                    }}
                  >
                    Download Ollama here
                  </Button>
                </div>
              </div>
              <div className={styles.step1Section}>
                <Title level={4} className={styles.stepTitle}>Step 1: Inter your device name</Title>
                <Paragraph strong className={styles.fieldLabel}>
                  Device name *
                </Paragraph>

                <Form.Item
                  name="deviceName"
                  rules={[
                    { required: true, message: 'Please enter device name' },
                    { pattern: /^[a-zA-Z0-9]+$/, message: 'Input must consist of letters (A-Z, a-z) and digits (0-9) only.' }
                  ]}
                  className={styles.formItem}
                >
                  <Input
                    placeholder="Input must consist of letters (A-Z, a-z) and digits (0-9) only."
                    size="large"
                    className={styles.deviceNameInput}
                  />
                </Form.Item>
              </div>

              <div className={styles.step2Section}>
                <Title level={4} className={styles.stepTitle}>Step 2: Select your device type *</Title>

                <Form.Item
                  name="deviceType"
                  rules={[{ required: true, message: 'Please select device type' }]}
                  className={styles.formItem}
                >
                  <Radio.Group className={styles.deviceTypeGroup}>
                    {deviceTypes.map(type => (
                      <Radio
                        key={type.key}
                        value={type.key}
                        className={styles.deviceTypeOption}
                      >
                        <div className={styles.deviceTypeContent}>
                          {type.icon}
                          <span className={styles.deviceTypeLabel}>{type.label}</span>
                        </div>
                      </Radio>
                    ))}
                  </Radio.Group>
                </Form.Item>
              </div>

              <div className={styles.stepActions}>
                <Button
                  onClick={() => {
                    router.replace('/management/nodes')
                  }}
                  loading={loading}
                  style={{
                    background: '#000',
                    color: '#000',
                    // border: 'none',
                    borderRadius: '20px',
                    marginRight: '10px',
                    backgroundColor: '#fff',
                    borderColor: '#000',
                  }}
                >
                  Cancel
                </Button>
                <Button
                  onClick={handleFormSubmit}
                  loading={loading}
                  style={{
                    background: '#000',
                    color: '#fff',
                    border: 'none',
                    borderRadius: '20px',
                  }}
                >
                  Register Device
                </Button>
              </div>
            </div>
          )}

          {/* 第二步：连接详情 */}
          {currentStep === 1 && connectInfo && (
            <div className={styles.connectionDetailContent}>
              <Title level={2} className={styles.connectionDetailTitle}>Device connection detail</Title>
              <Paragraph className={styles.connectionDetailNote}>
                Note: If your previous device registration session was interrupted, you can resume it from the "Resume" action in the Node Management list.
              </Paragraph>

              {/* 成功提示 */}
              <Alert
                message="Device created successfully"
                description="Click 'Create' to connect your device. You will need to run a command on your device to complete the connection."
                type="success"
                showIcon
                className={styles.successAlert}
              />

              {/* 连接状态 */}
              <div className={styles.connectionStatusSection}>
                <Title level={4} className={styles.sectionTitle}>Connection Status</Title>

                {/* 测试按钮*/}
                {/*<div style={{ marginBottom: '16px', display: 'flex', gap: '8px' }}>*/}
                {/*  <Button size="small" onClick={() => setConnectionStatus('waiting')}>Test Waiting</Button>*/}
                {/*  <Button size="small" onClick={() => setConnectionStatus('failed')}>Test Failed</Button>*/}
                {/*  <Button size="small" onClick={() => setConnectionStatus('timeout')}>Test Timeout</Button>*/}
                {/*  <Button size="small" onClick={() => setConnectionStatus('connected')}>Test Connected</Button>*/}
                {/*</div>*/}

                <div style={{display: 'flex', alignItems: 'center', gap: '16px'}}>
                  <div className={styles.statusCard} style={{

                    // @ts-ignore
                    background: statusBackgroundColors[connectionStatus],
                    // @ts-ignore
                    color: statusColors[connectionStatus],
                  }}>
                    {connectionStatus === 'waiting' &&
                        <div className={styles.statusIndicator}>
                          <LoadingOutlined style={{ fontSize: 16, color: '#faad14' }} spin />
                          <Text className={styles.statusText}>Waiting for device connection...</Text>
                        </div>}
                    {connectionStatus === 'connected' &&
                        <div className={styles.statusIndicator}>
                          <CheckCircleOutlined style={{ fontSize: 16, color: '#52c41a' }} />
                          <Text className={styles.statusText}>Device connected successfully!</Text>
                        </div>}
                    {connectionStatus === 'failed' &&
                        <div className={styles.failedStatusContainer}>
                          <div className={styles.failedStatusContent}>
                            <CloseCircleOutlined style={{ fontSize: 16, color: '#f5222d' }} />
                            <div className={styles.failedStatusText}>
                              <Text strong style={{ color: '#f5222d' }}>Connection failed</Text>
                              <Text type="secondary" style={{ fontSize: '12px', marginLeft: '8px', display: 'flex', alignItems: 'center', lineHeight: '12px' }}>
                                click "Restart" to re-connect
                              </Text>
                            </div>
                          </div>
                        </div>
                    }
                    {connectionStatus === 'timeout' &&
                        <div className={styles.failedStatusContainer}>
                          <div className={styles.failedStatusContent}>
                            <CloseCircleOutlined style={{ fontSize: 16, color: '#faad14' }} />
                            <div className={styles.failedStatusText}>
                              <Text strong style={{ color: '#faad14' }}>Connection timeout</Text>
                              <Text type="secondary" style={{ fontSize: '12px', marginLeft: '8px', display: 'flex', alignItems: 'center', lineHeight: '12px' }}>
                                click "Restart" to re-connect
                              </Text>
                            </div>
                          </div>
                        </div>
                    }

                  </div>
                   
                  {(connectionStatus === 'failed' || connectionStatus === 'timeout' || 
                   // @ts-ignore
                  connectionStatus === 'disconnected') &&
                      <Button
                      onClick={handleRetry}
                      className={styles.retryButton}
                      icon={<LoadingOutlined style={{display: 'none'}}/>}
                  >Restart
                  </Button>}
                </div>
              </div>

              {/* Tab 切换 */}
              <Tabs
                defaultActiveKey="gui"
                className={styles.deployTabs}
                items={[
                  {
                    key: 'gui',
                    label: (
                      <span className={styles.tabLabel}>
                        <DesktopOutlined />
                        Deploy with GUI
                      </span>
                    ),
                    children: (
                      <div className={styles.tabContent}>
                        {/* Connection Information */}
                        <div className={styles.connectionInfoSection}>
                          <Title level={4} className={styles.sectionTitle}>Connection Information</Title>

                          <div className={styles.infoRow}>
                            <Text className={styles.infoLabel}>One-time Code:</Text>
                            <div className={styles.infoValue}>
                              <Text code className={styles.codeText}>{connectInfo.one_time_code}</Text>
                              <Button
                                size="small"
                                onClick={() => copyToClipboard(connectInfo.one_time_code)}
                                className={styles.copyButton}
                              >
                                Copy
                              </Button>
                            </div>
                          </div>

                          <div className={styles.infoRow}>
                            <Text className={styles.infoLabel}>Gateway Address:</Text>
                            <div className={styles.infoValue}>
                              <Text code className={styles.codeText}>{gatewayCommandUrl}</Text>
                              <Button
                                size="small"
                                onClick={() => copyToClipboard(gatewayCommandUrl)}
                                className={styles.copyButton}
                              >
                                Copy
                              </Button>
                            </div>
                          </div>

                           <div className={styles.infoRow}>
                            <Text className={styles.infoLabel}>Reward Address:</Text>
                            <div className={styles.infoValue}>
                              <Text code className={styles.codeText}>{walletAddress}</Text>
                              <Button
                                size="small"
                                onClick={() => copyToClipboard(walletAddress)}
                                className={styles.copyButton}
                              >
                                Copy
                              </Button>
                            </div>
                          </div>
                          
                        </div>

                        {/* Note */}
                        <Alert
                          message="Note"
                          description="Please ensure your device has downloaded and started the program correctly before running the registration command. After successful registration, the device will automatically connect to the gateway."
                          type="warning"
                          showIcon
                          className={styles.noteAlert}
                        />
                      </div>
                    )
                  },
                  {
                    key: 'cli',
                    label: (
                      <span className={styles.tabLabel}>
                        Deploy with command line
                      </span>
                    ),
                    children: (
                      <div className={styles.tabContent}>
                        {/* Connection Command Section */}
                        <div className={styles.commandSection}>
                          <Title level={4} className={styles.sectionTitle}>Connection Command</Title>
                          <Paragraph className={styles.commandDescription}>
                            Please make sure you have correctly executed the connection command
                          </Paragraph>

                          {/* Step 1: Start Program */}
                          <div className={styles.commandStep}>
                            <Title level={5} className={styles.stepTitle}>Step 1: Start Program</Title>
                            <div className={styles.commandBox}>
                              <Text code className={styles.commandText}>
                                {getExecutableName(selectedDeviceType)} start -d
                              </Text>
                              <Button
                                size="small"
                                onClick={() => copyToClipboard(`${getExecutableName(selectedDeviceType)} start -d`)}
                                className={styles.copyButton}
                              >
                                Copy
                              </Button>
                            </div>
                          </div>

                          {/* Step 2: View Logs (Optional) */}
                          <div className={styles.commandStep}>
                            <Title level={5} className={styles.stepTitle}>Step 2: View Logs (Optional)</Title>
                            <div className={styles.commandBox}>
                              <Text code className={styles.commandText}>
                                {getExecutableName(selectedDeviceType)} logs
                              </Text>
                              <Button
                                size="small"
                                onClick={() => copyToClipboard(`${getExecutableName(selectedDeviceType)} logs`)}
                                className={styles.copyButton}
                              >
                                Copy
                              </Button>
                            </div>
                          </div>

                          {/* Step 3: Register to Gateway */}
                          <div className={styles.commandStep}>
                            <Title level={5} className={styles.stepTitle}>Step 3: Register to Gateway</Title>
                            <div className={styles.commandBox}>
                              <Text code className={styles.commandText}>
                                {`${getExecutableName(selectedDeviceType)} register --base-path "${serverBasePath}" --code "${connectInfo?.one_time_code}" --gateway "${gatewayCommandUrl}" --reward "${walletAddress}" `}
                              </Text>
                              <Button
                                size="small"
                                onClick={() => copyToClipboard(`${getExecutableName(selectedDeviceType)} register --base-path "${serverBasePath}" --code "${connectInfo?.one_time_code}" --gateway "${gatewayCommandUrl}" --reward "${walletAddress}" `)}
                                className={styles.copyButton}
                              >
                                Copy
                              </Button>
                            </div>
                          </div>

                          {/* Step 4: Report Models */}
                          <div className={styles.commandStep}>
                            <Title level={5} className={styles.stepTitle}>Step 4: Report Models</Title>
                            <div className={styles.commandBox}>
                              <Text code className={styles.commandText}>
                                {getExecutableName(selectedDeviceType)} models report
                              </Text>
                              <Button
                                size="small"
                                onClick={() => copyToClipboard(`${getExecutableName(selectedDeviceType)} models report`)}
                                className={styles.copyButton}
                              >
                                Copy
                              </Button>
                            </div>
                          </div>

                          {/* Step 5: Refresh Device List */}
                          <div className={styles.commandStep}>
                            <Title level={5} className={styles.stepTitle}>Step 5: Refresh Device List</Title>
                            <Paragraph className={styles.refreshDescription}>
                              Refresh device list to check connection status
                            </Paragraph>
                          </div>

                          {/* Note */}
                          <Alert
                            message="Note"
                            description="Please ensure your device has downloaded and started the program correctly before running the registration command. After successful registration, the device will automatically connect to the gateway."
                            type="warning"
                            showIcon
                            className={styles.noteAlert}
                          />
                        </div>
                      </div>
                    ),
                  }
                ]}
              />

              {/* Finished Button */}
              <div className={styles.finishedButtonContainer}>
                <Button
                  onClick={onCancel}
                  className={styles.finishedButton}
                  style={{
                    background: '#000',
                    color: '#fff',
                    border: 'none',
                    borderRadius: '20px',
                  }}
                >
                  Finished
                </Button>
              </div>
            </div>
          )}
        </Form>
      </div>
    </div>
  );
};

export default CreateDeviceWizard;
