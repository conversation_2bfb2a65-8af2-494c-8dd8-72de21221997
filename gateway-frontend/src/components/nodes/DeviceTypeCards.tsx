'use client';

import { Card, Typography } from 'antd';
import { useLocaleContext } from '@/contexts/LocaleContext';
import styles from './DeviceTypeCards.module.css';
import Image from 'next/image';
import Image1 from '../../public/nvidia.svg';
import Image2 from '../../public/cpu.svg';
import Image3 from '../../public/apple.svg';
import Image4 from '../../public/cloud.svg';

const { Text } = Typography;

interface DeviceType {
  id: string;
  name: string;
  description: string;
  icon: string;
}

export default function DeviceTypeCards() {
  const { messages } = useLocaleContext();

  // 设备类型数据
  const deviceTypes: DeviceType[] = [
    {
      id: 'gpu',
      name: 'GPU',
      description: messages.nodes?.deviceTypes?.gpu || 'NVIDIA GPU devices for high-performance AI processing',
      icon: Image1.src
    },
    {
      id: 'cpu',
      name: 'CPU',
      description: messages.nodes?.deviceTypes?.cpu || 'CPU-based devices for general-purpose computing',
      icon: Image2.src
    },
    {
      id: 'mac',
      name: 'Apple Silicon',
      description: messages.nodes?.deviceTypes?.mac || 'M1/M2/M3 devices optimized for AI workloads',
      icon: Image3.src
    },
    {
      id: 'cloud',
      name: 'Cloud',
      description: messages.nodes?.deviceTypes?.cloud || 'Cloud-based virtual machines for scalable deployment',
      icon: Image4.src
    }
  ];

  return (
    <div className={styles.container}>
      {deviceTypes.map(type => (
        <Card key={type.id} className={styles.card}>
          <div className={styles.cardContent}>
            <div className={styles.iconContainer}>
              <Image
                src={type.icon}
                alt={type.name}
                width={32}
                height={32}
                className={styles.icon}
              />
            </div>
            <div className={styles.textContent}>
              <div className={styles.title}>{type.name}</div>
              <Text type="secondary" className={styles.description}>
                {type.description}
              </Text>
            </div>
          </div>
        </Card>
      ))}
    </div>
  );
}
