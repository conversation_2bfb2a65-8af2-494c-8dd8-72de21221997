
.content {
  padding: var(--spacing-xl) 0;
}

.description {
  margin-bottom: var(--spacing-xl);
  color: var(--color-text-tertiary);
}

.commandBox {
  position: relative;
  background: #fafafa;
  border-radius: var(--radius-sm);
  margin-bottom: var(--spacing-xl);
  padding: var(--spacing-base);
}

.command {
  font-family: var(--font-family-mono);
  font-size: var(--font-size-base);
  background: transparent;
  resize: none;
}

.command :global(.ant-input) {
  background: transparent;
  border: none;
  color: var(--color-foreground);
  cursor: default;
  padding: 0;
  line-height: 1.5;
}

.command :global(.ant-input:hover),
.command :global(.ant-input:focus) {
  border: none;
  box-shadow: none;
}

.copyButton {
  position: absolute;
  top: var(--spacing-sm);
  right: var(--spacing-sm);
  color: var(--color-text-tertiary);
  transition: color var(--transition-base);
}

.copyButton:hover {
  color: var(--color-info);
}

.waitingStatus,
.successStatus,
.failedStatus {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-sm);
  margin-top: var(--spacing-base);
}

.spinIcon {
  color: var(--color-info);
}

.retryButton {
  margin-left: var(--spacing-base);
}