.wizard {
  min-width: 800px;
  margin: 0 auto;
  padding: 0px;
  background: #fff;
  border-radius: 8px;
}

.header {
  margin-bottom: 48px;
}

.header h2 {
  margin-bottom: 16px;
  color: #000;
  font-size: 32px;
  font-weight: 600;
}

.header .ant-typography {
  margin-bottom: 8px;
  color: #666;
  font-size: 14px;
}

.content {
  min-height: 400px;
}

.form {
  max-width: 100%;
}

.stepContent {
  position: relative;
  min-height: 500px;
}

.step1Section {
  margin-bottom: 48px;
}

.step2Section {
  margin-bottom: 80px;
}

.stepTitle {
  margin-bottom: 16px !important;
  color: #000 !important;
  font-size: 20px !important;
  font-weight: 600 !important;
}

.fieldLabel {
  margin-bottom: 8px !important;
  color: #000 !important;
  font-size: 14px !important;
}

.formItem {
  margin-bottom: 0 !important;
}

.deviceNameInput {
  height: 48px;
  border-radius: 6px;
  border: 1px solid #d9d9d9;
  font-size: 14px;
}

.stepContent h4 {
  margin-bottom: 16px;
  color: #262626;
}

.stepActions {
  position: absolute;
  bottom: -50px;
  right: 0;
  display: flex;
  justify-content: flex-end;
}

.stepActions .ant-btn {
  min-width: 100px;
}

/* 设备类型选择样式 */
.deviceTypeGroup {
  width: 100%;
  display: flex;
  flex-direction: column;
}

.deviceTypeGroup .ant-radio-wrapper {
  display: flex;
  align-items: center;
  width: 100%;
  margin-bottom: 16px;
  padding: 0;
  border: none;
  background: transparent;
}

.deviceTypeGroup .ant-radio-wrapper:last-child {
  margin-bottom: 0;
}

.deviceTypeOption {
  cursor: pointer;
  margin: 10px 0
}

.deviceTypeContent {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-left: 8px;
}

.deviceTypeLabel {
  font-size: 16px;
  font-weight: 400;
  color: #000;
}

/* 连接状态样式 */
.connectionStatus {
  margin-bottom: 24px;
}

.statusHeader {
  display: flex;
  align-items: flex-start;
  gap: 16px;
}

.statusIcon {
  flex-shrink: 0;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.statusText {
  flex: 1;
}

.statusText .ant-typography {
  margin-bottom: 4px;
}

.connectionInfo {
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
}

.connectionInfo:last-child {
  border-bottom: none;
}

.connectionInfo .ant-typography {
  margin-bottom: 8px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .wizard {
    margin: 16px;
    padding: 16px;
  }
  
  .form {
    max-width: 100%;
  }
  
  .stepActions {
    flex-direction: column;
    gap: 12px;
  }
  
  .stepActions .ant-btn {
    width: 100%;
  }
  
  .deviceTypeContent {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .statusHeader {
    flex-direction: column;
    gap: 12px;
  }
}

/* 动画效果 */
.stepContent {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 自定义按钮样式 */
.registerButton {
  background: #000 !important;
  border-color: #000 !important;
  color: #fff !important;
  min-width: 140px;
  height: 40px;
  border-radius: 6px;
  font-weight: 500;
  font-size: 14px;
}

.registerButton:hover {
  background: #333 !important;
  border-color: #333 !important;
  color: #fff !important;
}

.registerButton:focus {
  background: #000 !important;
  border-color: #000 !important;
  color: #fff !important;
}

.stepActions .ant-btn-primary {
  background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
  border: none;
  box-shadow: 0 2px 4px rgba(24, 144, 255, 0.3);
}

.stepActions .ant-btn-primary:hover {
  background: linear-gradient(135deg, #40a9ff 0%, #1890ff 100%);
  box-shadow: 0 4px 8px rgba(24, 144, 255, 0.4);
}

/* 进度条样式 */
.connectionStatus .ant-progress-line {
  margin-top: 16px;
}

.connectionStatus .ant-progress-bg {
  background: linear-gradient(90deg, #1890ff 0%, #52c41a 100%);
}

/* 连接详情页面样式 */
.connectionDetailContent {
  position: relative;
  min-height: 600px;
  padding-bottom: 80px;
}

.connectionDetailTitle {
  margin-bottom: 16px !important;
  color: #000 !important;
  font-size: 32px !important;
  font-weight: 600 !important;
}

.connectionDetailNote {
  margin-bottom: 24px !important;
  color: #666 !important;
  font-size: 14px !important;
}

.successAlert {
  margin-bottom: 32px !important;
  border-radius: 8px !important;
}

.connectionStatusSection {
  margin-bottom: 32px;
}

.sectionTitle {
  margin-bottom: 16px !important;
  color: #000 !important;
  font-size: 20px !important;
  font-weight: 600 !important;
}

.statusCard {
  padding: 16px;
  background: #fffbe6;
  /*border: 1px solid #ffe58f;*/
  border-radius: 8px;
  flex: 1;
}

.statusIndicator {
  display: flex;
  align-items: center;
  gap: 12px;
}

.statusText {
  /*color: #d48806;*/
  /*font-size: 14px;*/
}

.deployTabs {
  margin-bottom: 40px;
}

.deployTabs .ant-tabs-tab {
  padding: 12px 24px !important;
}

.deployTabs .ant-tabs-tab-active {
  background: #f0f0f0;
  border-radius: 8px 8px 0 0;
}

.tabLabel {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  font-weight: 500;
}

.tabContent {
  padding: 24px 0;
}

.connectionInfoSection {
  margin-bottom: 24px;
}

.infoRow {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
  padding: 12px 0;
}

.infoLabel {
  min-width: 140px;
  color: #000;
  font-weight: 500;
}

.infoValue {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
}

.codeText {
  background: #f5f5f5 !important;
  border: 1px solid #d9d9d9 !important;
  padding: 4px 8px !important;
  border-radius: 4px !important;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace !important;
}

.copyButton {
  height: 28px;
  font-size: 12px;
  border-radius: 4px;
}

.noteAlert {
  border-radius: 8px !important;
}

.finishedButtonContainer {
  position: absolute;
  bottom: 0;
  right: 0;
}

.finishedButton {
  background: #000 !important;
  border-color: #000 !important;
  color: #fff !important;
  min-width: 100px;
  height: 40px;
  border-radius: 6px;
  font-weight: 500;
  font-size: 14px;
}

.finishedButton:hover {
  background: #333 !important;
  border-color: #333 !important;
  color: #fff !important;
}

.finishedButton:focus {
  background: #000 !important;
  border-color: #000 !important;
  color: #fff !important;
}

/* CLI Tab 样式 */
.commandSection {
  width: 100%;
}

.commandDescription {
  margin-bottom: 24px !important;
  color: #666 !important;
  font-size: 14px !important;
}

.commandStep {
  margin-bottom: 24px;
}

.stepTitle {
  margin-bottom: 12px !important;
  color: #000 !important;
  font-size: 16px !important;
  font-weight: 600 !important;
}

.commandBox {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 16px;
  background: #f5f5f5;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
}

.commandText {
  flex: 1;
  background: transparent !important;
  border: none !important;
  padding: 0 !important;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace !important;
  font-size: 13px !important;
  line-height: 1.5 !important;
  color: #000 !important;
  word-break: break-all;
  white-space: pre-wrap;
}

.refreshDescription {
  color: #999 !important;
  font-size: 14px !important;
  margin: 0 !important;
}

/* 连接失败状态样式 */
.failedStatusContainer {
  display: flex;
  align-items: center;
  gap: 12px;
}

.failedStatusContent {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
}

.failedStatusText {
  display: flex;
  align-items: center;
}

/* 重试按钮样式 */
.retryButton {
  background: #000 !important;
  border-color: #000 !important;
  color: #fff !important;
  border-radius: 6px;
  font-size: 14px;
  height: 40px;
  padding: 0 16px;
  display: flex;
  align-items: center;
  gap: 4px;
}

.retryButton:hover {
  background: #333 !important;
  border-color: #333 !important;
  color: #fff !important;
}

.retryButton:focus {
  background: #000 !important;
  border-color: #000 !important;
  color: #fff !important;
}
