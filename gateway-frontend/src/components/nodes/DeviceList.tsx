import React, { useState, useEffect, useImperativeHandle, forwardRef } from 'react';
import { Table, Tag, Space, Button, Input, Select, Card, message, Checkbox } from 'antd';
import { SearchOutlined, ReloadOutlined, PlusOutlined, SyncOutlined, RedoOutlined } from '@ant-design/icons';
import { nodeApi } from '@/api/node';
import { Device, DeviceStatus } from '@/types/node';
// import { formatDistanceToNow } from 'date-fns';
import { useRouter } from 'next/navigation';
import { useLocaleContext } from '@/contexts/LocaleContext';
import { useAuthStore } from '@/store/authStore';
import styles from './DeviceList.module.css';
import { ColumnProps } from 'antd/es/table';

const { Option } = Select;

// Status color mapping
export const statusColors = {
  waiting: 'orange',
  'in-progress': 'blue',
  connected: 'green',
  disconnected: 'red',
  failed: 'red',
};

interface DeviceListProps {
  onAddDevice?: () => void;
  onResumeDevice?: (deviceId: string) => void; // 恢复设备回调，传递设备ID
  ref?: React.Ref<{ loadDevices: () => Promise<void> }>;
  defaultOnlyMyDevices?: boolean; // New prop to default to user's devices only
}

const DeviceList: React.ForwardRefRenderFunction<{ loadDevices: () => Promise<void> }, DeviceListProps> = ({ onAddDevice, onResumeDevice, defaultOnlyMyDevices = true }, ref) => {
  const router = useRouter();
  const { messages } = useLocaleContext();
  const { isLoggedIn } = useAuthStore();
  const [devices, setDevices] = useState<Device[]>([]);
  const [loading, setLoading] = useState(false);
  const [total, setTotal] = useState(0);
  const [current, setCurrent] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [searchText, setSearchText] = useState('');
  const [statusFilter, setStatusFilter] = useState<string | undefined>('connected');
  const [onlyMyDevices, setOnlyMyDevices] = useState(defaultOnlyMyDevices && isLoggedIn);

  // Load device list
  const loadDevices = async () => {
    setLoading(true);
    try {
      const response = await nodeApi.getDevices({
        current,
        pageSize,
        status: statusFilter,
        search: searchText,
        onlyMyDevices: onlyMyDevices && isLoggedIn,
      });

      // Handle different response formats
      if (response.success && response.data) {
        // New unified response format
        setDevices(response.data.data || []);
        setTotal(response.data.total || 0);
      } else if (response.data) {
        // Old response format
        setDevices(response.data || []);
        setTotal(response.total || 0);
      } else {
        // Direct data return
        setDevices(response.data || []);
        setTotal(response.total || 0);
      }
    } catch (error) {
      message.error(messages.nodes.management.loadFailed);
    } finally {
      setLoading(false);
    }
  };

  // Expose loadDevices method to parent component
  useImperativeHandle(ref, () => ({
    loadDevices
  }));

  // Initial load and reload when parameters change
  useEffect(() => {
    loadDevices();
  }, [current, pageSize, statusFilter, onlyMyDevices]);

  // Handle search
  const handleSearch = () => {
    setCurrent(1); // Reset to first page
    loadDevices();
  };

  // Handle view device details
  const handleViewDevice = (deviceId: string) => {
    router.push(`/nodes/device/detail?id=${encodeURIComponent(deviceId)}`);
  };

  // Table column definitions
  const columns: ColumnProps<Device>[] = [
    {
      title: messages.nodes.management.table.deviceName,
      dataIndex: 'task_name',
      align: 'center',
      key: 'task_name',
      render: (text: string, record: Device) => text || (record.id || record.node_id || messages.nodes.deviceDetail.unknown).substring(0, 8),
    },
    {
      title: messages.nodes.management.table.deviceType,
      dataIndex: 'device_type',
      key: 'device_type',
      align: 'center',
      render: (text: string) => text || messages.nodes.deviceDetail.unknown,
    },
    {
      title: messages.nodes.management.table.gpuModel,
      dataIndex: 'gpu_model',
      key: 'gpu_model',
      align: 'center',
      render: (text: string) => text || messages.nodes.deviceDetail.unknown,
    },
    {
      title: messages.nodes.management.table.status,
      dataIndex: 'status',
      key: 'status',
      align: 'center',
      onCell: () => ({
        onClick: (e) => {
          e.stopPropagation();
        },
      }),
      render: (status: DeviceStatus, record: Device) => (
        <span style={{ display: 'flex', alignItems: 'center', gap: 8, cursor: 'pointer', flexDirection: 'column' }}>
          <div style={{ display: 'flex', alignItems: 'center', gap: 8, cursor: 'pointer', flexDirection: 'row' }}>
            <Tag color={statusColors[status] || 'default'}>
              {messages.nodes.management.status[status]}
            </Tag>
            {(status === 'disconnected' || status === 'failed') && (
              <RedoOutlined
                onClick={(e) => {
                  e.stopPropagation(); // 防止触发行点击事件
                  if (onResumeDevice) {
                    // @ts-ignore
                    onResumeDevice(record.device_id);
                  }
                }}
                style={{
                  color: 'red',
                  cursor: 'pointer'
                }}
              />
            )}
          </div>
          {(status === 'disconnected' || status === 'failed') && (
            <span style={{
              color: '#777777',
              fontSize: 12,
            }}>
              click to resume connection process
            </span>
          )}
        </span>
      ),
    },
    {
      title: messages.nodes.management.table.currentModel,
      dataIndex: 'current_model',
      key: 'current_model',
      align: 'center',
      render: (text: string) => text || messages.nodes.deviceDetail.none,
    },
    {
      title: messages.nodes.management.table.lastActivity,
      dataIndex: 'last_ping',
      key: 'last_ping',
      align: 'center',
      render: (text: string) => (text ? new Date(text).toLocaleString() : messages.nodes.deviceDetail.unknown),
    },
    {
      title: messages.nodes.management.table.totalEarnings,
      dataIndex: 'total_earnings',
      key: 'total_earnings',
      align: 'center',
      render: (value: number) => (value ? `${value.toFixed(2)} USDT` : '0.00 USDT'),
    },
    // {
    //   title: messages.nodes.management.table.actions,
    //   key: 'action',
    //   align: 'center',
    //   render: (_: any, record: Device) => (
    //     <Space size="middle">
    //       <Button  style={{color: '#a01ec1'}} onClick={() => handleViewDevice(record.id || record.device_id || '')}>
    //         {messages.nodes.management.table.view}
    //       </Button>
    //     </Space>
    //   ),
    // },
  ];

  return (
    <Card
      title={messages.nodes.management.table.deviceList}
      className={styles.deviceListCard}
      extra={
        <Button type="primary" icon={<PlusOutlined />} className='button' onClick={onAddDevice}>
          {messages.nodes.management.table.addDevice}
        </Button>
      }
    >
      <Space className={styles.searchSection}>
        <Input
          placeholder={messages.nodes.management.table.searchPlaceholder}
          value={searchText}
          onChange={(e) => setSearchText(e.target.value)}
          onPressEnter={handleSearch}
          style={{ width: 200 }}
          prefix={<SearchOutlined />}
        />
        <Select
          placeholder={messages.nodes.management.table.statusFilter}
          style={{ width: 120 }}
          allowClear
          value={statusFilter}
          onChange={(value) => setStatusFilter(value)}
        >
          <Option value="waiting">{messages.nodes.management.status.waiting}</Option>
          <Option value="in-progress">{messages.nodes.management.status["in-progress"]}</Option>
          <Option value="connected">{messages.nodes.management.status.connected}</Option>
          <Option value="disconnected">{messages.nodes.management.status.disconnected}</Option>
          <Option value="failed">{messages.nodes.management.status.failed}</Option>
        </Select>
        {isLoggedIn && (
          <Checkbox
            checked={onlyMyDevices}
            onChange={(e) => setOnlyMyDevices(e.target.checked)}
          >
            {messages.nodes.management.table.showMyDevicesOnly}
          </Checkbox>
        )}
        <Button icon={<SearchOutlined />} onClick={handleSearch}>
          {messages.nodes.management.table.search || "Search"}
        </Button>
        <Button icon={<ReloadOutlined />} onClick={loadDevices}>
          {messages.nodes.management.table.refresh || "Refresh"}
        </Button>
      </Space>

      <Table
        columns={columns}
        dataSource={devices}
        rowKey={(record) =>
              // @ts-ignore
          record.device_id}
        loading={loading}
        className={styles.deviceTable}
        onRow={(record) => ({
          onClick: () => {
            if (record.status === 'connected') {
              // @ts-ignore
              handleViewDevice(record.device_id)
            } else {
              if (onResumeDevice) {
                // @ts-ignore
                onResumeDevice(record.device_id );
              }
            }
          },
        })}
        components={{
          body: {
              // @ts-ignore
            row: (props) => {
              const { record } = props;
              return (
                <tr {...props} style={{ cursor: 'pointer' }} />
              );
            },
          },
        }}
        pagination={{
          position: ['bottomCenter'],
          current,
          pageSize,
          total,
          onChange: (page) => setCurrent(page),
          onShowSizeChange: (_, size) => setPageSize(size),
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total) => messages.nodes.management.table.totalRecords?.replace('{total}', total.toString()) || `Total ${total} records`,
        }}
      />
    </Card>
  );
};

export default forwardRef(DeviceList);
