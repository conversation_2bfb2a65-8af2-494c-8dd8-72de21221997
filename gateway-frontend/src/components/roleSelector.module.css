@import url('https://fonts.googleapis.com/css2?family=Bruno+Ace&display=swap');

.roleSelectorContainer {
  padding: 0;
  margin-bottom: 0;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
  width: 100%;
}

.logo {
  width: 25px;
  height: auto;
}

.selectorLabel {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
  font-weight: var(--font-weight-semibold);
  margin-bottom: var(--spacing-xs);
  letter-spacing: 0.02em;
}

.selectorLabelText {
  color: #c2e5fe;
  font-family: "Bruno Ace", sans-serif;
  font-weight: var(--font-weight-normal);
  font-size: var(--font-size-md);
  line-height: 100%;
  letter-spacing: 0%;
}

.customRoleSelector {
  width: 100%;
  border-radius: var(--radius-xl);
  height: 44px;
  padding: 0 var(--spacing-base);
  display: flex;
  align-items: center;
  justify-content: space-between;
  border: 1px solid var(--color-border);
  box-shadow: var(--shadow-sm);
  transition: all var(--transition-fast);
  background: #f5f7fa;
  cursor: pointer;
}

.customRoleSelector:hover {
  border-color: #d9d9d9;
  box-shadow: var(--shadow-md);
}

.selectedRoleDisplay {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.selectedRoleIcon {
  width: 20px;
  height: 20px;
  border-radius: var(--radius-md);
  object-fit: cover;
}

.selectedRoleText {
    font-family: "Bruno Ace", sans-serif;
  font-weight: var(--font-weight-normal);
  font-size: var(--font-size-md);
  line-height: 100%;
  letter-spacing: 0%;
}

.dropdownArrow {
  font-size: var(--font-size-xs);
  color: var(--color-text-secondary);
}

.roleOption {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-xs) 0;
}

.roleIcon {
  width: 32px;
  height: 32px;
  border-radius: var(--radius-md);
  object-fit: cover;
}

/* Style for the selected role in the dropdown */
.roleSelect :global(.ant-select-selection-item) .roleOption {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.roleIconSmall {
  width: 24px;
  height: 24px;
  border-radius: var(--radius-sm);
  object-fit: cover;
}

.collapsedSelector {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f5f7fa;
  border-radius: var(--radius-lg);
  cursor: pointer;
  transition: background var(--transition-fast);
  margin: 0 auto;
}

.collapsedSelector:hover {
  background: #e6edff;
}

.roleDropdownOverlay {
  border-radius: var(--radius-md);
  overflow: hidden;
  box-shadow: var(--shadow-lg);
}

.roleDropdownItem {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-sm) var(--spacing-md);
  transition: background-color var(--transition-fast);
}

.roleDropdownIcon {
  width: 200px;
  border-radius: var(--radius-md);
  object-fit: cover;
}

/* Style for Ant Design dropdown items */
.roleDropdownOverlay :global(.ant-dropdown-menu-item:hover) {
  background-color: #f5f7fa !important;
}

.roleDropdownOverlay :global(.ant-dropdown-menu-item-selected) {
  background-color: #f0f4ff !important;
  font-weight: var(--font-weight-semibold);
}
