'use client';

import { useState, useEffect, useRef } from 'react';
import { Tooltip, Dropdown } from 'antd';
import { DownOutlined } from '@ant-design/icons';
import { useRouter, usePathname } from 'next/navigation';
import styles from './roleSelector.module.css';
import { useLocaleContext } from '@/contexts/LocaleContext';
import Image9 from '../../public/Frame 348.png';
import Image10 from '../../public/Frame 345 (1).png';
import Image13 from '../../public/Frame 346.png';
import Image11 from '../../public/Frame 345.png';
import Image12 from '../../public/Frame 347.png';
import SIGHT from '../../public/SIGHT AI Brand Kit Vector 1.png';

interface RoleSelectorProps {
  collapsed?: boolean;
}

export default function RoleSelector({ collapsed = false }: RoleSelectorProps) {
  const router = useRouter();
  const { messages } = useLocaleContext();
  const pathname = usePathname();

  // Define path to role mapping
  const pathToRoleMap = {
    '/docs': '0',
    '/earnings': '3',
    '/api-keys': '2',
    '/models': '2',
    '/nodes': '3',
    '/management': '1'
  };

  // Determine the current role based on the path
  const getCurrentRole = () => {
    const path = pathname || '';

    // Check if the current path matches any of the defined paths
    for (const [pathPrefix, roleValue] of Object.entries(pathToRoleMap)) {
      if (path.startsWith(pathPrefix)) {
        return roleValue;
      }
    }

    // Default to Developer role if no match is found
    return '0';
  };

  // Initialize selected role based on current path
  const [selectedRole, setSelectedRole] = useState<string>(getCurrentRole());

  // Update selected role when path changes
  useEffect(() => {
    setSelectedRole(getCurrentRole());
  }, [pathname]);

  // Role options based on the 9-grid cards
  const roleOptions = [
    { value: '0', label: messages.roles?.role1, image: Image9.src },
    { value: '1', label: messages.roles?.role2, image: Image11.src },
    { value: '2', label: messages.roles?.role3, image: Image13.src },
    { value: '3', label: messages.roles?.role4, image: Image12.src },
    { value: '4', label: messages.roles?.role5, image: Image10.src },
  ];

  const handleRoleChange = (value: string) => {
    setSelectedRole(value);

    // Navigate based on role selection
    switch (value) {
      case '0':
        // Developer role
        router.push('/docs');
        break;
      case '1':
        // Node Operator role
        router.push('/management/dashboard');
        break;
      case '2':
        // API User role
        router.push('/api-keys');
        break;
      case '3':
        // Device Manager role
        router.push('/earnings');
        break;
      case '4':
        // Administrator role
        router.push('/');
        break;
      default:
        break;
    }
  };

  // Add state for dropdown visibility
  const [dropdownVisible, setDropdownVisible] = useState(false);

  // Get the currently selected role
  const currentRole = roleOptions.find(role => role.value === selectedRole);

  // Create dropdown menu items
  const dropdownItems = {
    items: roleOptions.map(role => ({
      key: role.value,
      label: (
        <div className={styles.roleDropdownItem}>
          <img src={role.image} alt={role.label} className={styles.roleDropdownIcon} />
        </div>
      ),
    })),
    onClick: ({ key }: { key: string }) => handleRoleChange(key),
  };

  if (collapsed) {
    return (
      <div className={styles.roleSelectorContainer}>
        <Tooltip title={messages.roles?.switchRole || "Switch Role"} placement="right">
          <div className={styles.collapsedSelector}>
            <img
              src={currentRole?.image}
              alt={currentRole?.label}
              className={styles.roleIconSmall}
            />
          </div>
        </Tooltip>
      </div>
    );
  }

  return (
    <div className={styles.roleSelectorContainer}>
      <Dropdown
        menu={dropdownItems}
        trigger={['click']}
        open={dropdownVisible}
        onOpenChange={setDropdownVisible}
        placement="bottomLeft"
        overlayClassName={styles.roleDropdownOverlay}
      >
        <div className={styles.customRoleSelector} onClick={() => setDropdownVisible(!dropdownVisible)}>
          <div className={styles.selectedRoleDisplay}>
            <img src={SIGHT.src} className={styles.selectedRoleIcon} />

            <span className={styles.selectedRoleText}>SIGHT · <span className={styles.selectorLabelText}>{currentRole?.label}</span></span>
          </div>
          <DownOutlined className={styles.dropdownArrow} />
        </div>
      </Dropdown>
    </div>
  );
}
