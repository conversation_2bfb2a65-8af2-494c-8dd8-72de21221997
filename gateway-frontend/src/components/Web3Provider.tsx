'use client';

import { useEffect, useState } from 'react';
import { initWeb3Modal } from '@/config/web3';
import { message } from 'antd';

export default function Web3Provider({
  children,
}: {
  children: React.ReactNode;
}) {
  const [isInitialized, setIsInitialized] = useState(false);

  useEffect(() => {
    const initializeWeb3 = async () => {
      try {
        await initWeb3Modal();
        setIsInitialized(true);
      } catch (error) {
        console.error('Failed to initialize Web3Modal:', error);
        message.error('Failed to initialize Web3Modal');
      }
    };

    if (!isInitialized) {
      initializeWeb3();
    }
  }, [isInitialized]);

  if (!isInitialized) {
    return null;
  }

  return <>{children}</>;
} 