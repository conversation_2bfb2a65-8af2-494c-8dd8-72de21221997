.gradientBorderButton {
  position: relative;
  background: transparent;
  border: none;
  z-index: 1;
  font-weight: 300;
  padding: 0 12px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  border-radius: 50px;
  font-size: 13px;
  cursor: pointer;
}

.gradientBorderButton:hover {
  transform: translateY(-1px);
}

.gradientBorderButton::before {
  content: "";
  position: absolute;
  inset: 0;
  border-radius: 50px;
  padding: 1px; /* Border width */
  background: linear-gradient(119.29deg, #6D20F5 0%, #E7337A 100%);
  -webkit-mask:
    linear-gradient(#fff 0 0) content-box,
    linear-gradient(#fff 0 0);
  mask:
    linear-gradient(#fff 0 0) content-box,
    linear-gradient(#fff 0 0);
  -webkit-mask-composite: xor;
  mask-composite: exclude;
  z-index: -1;
}

.gradientBorderButton:hover::before {
  background: linear-gradient(119.29deg, #7D30FF 0%, #F7438A 100%);
}

.buttonText {
  color: black;
  font-weight: 300;
}
