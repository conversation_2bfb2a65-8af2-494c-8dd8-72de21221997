
.root {
  min-height: 100vh;
  background: #fff;
  display: flex;
  flex-direction: column;
}

.SiderCollapsed {
  display: flex;
  width: 100%;
  justify-content: space-between;
  align-items: center;
  padding: 20px 30px;
  transition: all var(--transition-base);
  cursor: pointer;
  border-bottom: none;
  background-color: #fafafa;
  height: 80px;
}

/* Collapsed state of SiderCollapsed */
:global(.ant-layout-sider-collapsed) .SiderCollapsed {
  padding: 20px 0;
  justify-content: center;
  align-items: center;
  text-align: center;
}

.SiderCollapsed img {
  width: 24px;
  height: 24px;
  color: var(--color-text-primary);
}

.menuIcon {
  transition: transform var(--transition-base);
}

.menuIcon:hover {
  transform: scale(1.1);
}

.SiderCollapsed span {
  font-family: var(--font-family-base);
  font-weight: var(--font-weight-bold);
  font-size: var(--font-size-2xl);
  line-height: 100%;
  letter-spacing: 0.05em;
}

.mainLayout {
  display: flex;
  flex: 1;
  flex-direction: row;
}

.sider {
  background: #fff !important;
  /* border-right: 1px solid var(--color-border); */
  box-shadow: none;
  padding-top: 0;
  display: flex;
  flex-direction: column;
  align-items: stretch;
  height: calc(100vh - var(--header-height));
  /* Subtract header height */
  overflow-y: auto;
  /* border-top: 1px solid var(--color-border-light); */
  transition: all var(--transition-base);
}

.siderHeader {
  padding: 20px 24px 16px;
  display: flex;
  justify-content: center;
  align-items: center;
  border-bottom: 1px solid #f0f0f0;
}

/* Expanded sidebar */
.sider:not(:global(.ant-layout-sider-collapsed)) {
  /* width: 300px !important; */
  /* min-width: 300px !important; */
  /* max-width: 300px !important; */
}

/* Collapsed sidebar */
.sider:global(.ant-layout-sider-collapsed) {
  width: 80px !important;
  min-width: 80px !important;
  max-width: 80px !important;
}

.logoRow {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 0 var(--spacing-2xl) var(--spacing-2xl) var(--spacing-2xl);
}

.logoDot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: #2a4ae4;
  display: inline-block;
}

.logoText {
  font-size: var(--font-size-md);
  font-weight: var(--font-weight-bold);
  color: var(--color-foreground);
  letter-spacing: 0.04em;
}

.logo {
  width: 150px;
}

.menu {
  background: transparent !important;
  border-inline-end: none !important;
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  width: 100%;
  padding: 0 !important;
  margin-top: var(--spacing-lg);
}

/* Collapsed menu */
:global(.ant-layout-sider-collapsed) .menu {
  width: 100%;
  overflow: hidden;
  padding: 0;
  margin: 0;
}

:global(.ant-layout-sider-collapsed) .menu :global(.ant-menu-inline) {
  width: 100%;
}

.menu :global(.ant-menu-item-group-title) {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
  font-weight: var(--font-weight-semibold);
  margin: 18px 0 8px 30px;
  letter-spacing: 0.02em;
  display: none; /* Hide group titles as per the image */
}

.menu :global(.ant-menu-item) {
  border-radius: 0;
  margin: 0 0 5px 0;
  height: 56px;
  display: flex;
  align-items: center;
  position: relative;
  transition: all var(--transition-base);
  padding-left: 30px !important;
  border-left: 4px solid transparent;
  color: var(--color-text-primary);
  font-size: var(--font-size-md);
  font-weight: var(--font-weight-normal);
}

/* Menu item icon styles */
.menuIcon {
  font-size: var(--font-size-lg) !important;
  margin-right: var(--spacing-sm);
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
}

.menuLabel {
  font-size: var(--font-size-md);
}

/* Collapsed menu items */
:global(.ant-layout-sider-collapsed) .menu :global(.ant-menu-item) {
  padding: 0 !important;
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 0;
}

/* Collapsed menu item icons */
:global(.ant-layout-sider-collapsed) .menuIcon {
  margin: 0 !important;
  font-size: var(--font-size-lg) !important;
  display: flex;
  justify-content: center;
  align-items: center;
}

/* Hide menu item text in collapsed state */
:global(.ant-layout-sider-collapsed) .menuLabel {
  display: none !important;
  width: 0;
  height: 0;
  overflow: hidden;
  opacity: 0;
  position: absolute;
  pointer-events: none;
}

:global(.ant-layout-sider-collapsed) .menu :global(.ant-menu-title-content) {
  display: inline-block;
  width: 0;
  height: 0;
  overflow: hidden;
  opacity: 0;
  position: absolute;
  pointer-events: none;
}

.menu :global(.ant-menu-item-selected) {
  background: transparent !important;
  font-weight: var(--font-weight-medium);
  box-shadow: none;
  /* border-left: 4px solid var(--color-primary); */
}

/* Selected menu item in collapsed state */
:global(.ant-layout-sider-collapsed) .menu :global(.ant-menu-item-selected) {
  border-left: none;
  border-left-width: 0;
  position: relative;
}

:global(.ant-layout-sider-collapsed) .menu :global(.ant-menu-item-selected)::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  height: 70%;
  width: 4px;
  border-radius: 0 2px 2px 0;
}

.menu :global(.ant-menu-item:hover) {
  background: var(--color-background-light) !important;
  box-shadow: none;
}

.header {
  background: rgba(255, 255, 255, 0.85);
  box-shadow: var(--shadow-sm);
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: var(--header-height);
  padding: 0 var(--spacing-2xl);
  border-bottom: 1px solid var(--color-border);
}

.headerLeft {
  display: flex;
  align-items: center;
  gap: var(--spacing-lg);
}

.backBtn {
  background: #f5f7fa !important;
  border-radius: var(--radius-lg) !important;
  color: #2a4ae4 !important;
  border: none;
  box-shadow: none;
  font-size: var(--font-size-xl) !important;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: var(--spacing-sm);
  transition: background var(--transition-fast);
}

.backBtn:hover {
  background: #e6edff !important;
}

.pageTitle {
  font-size: var(--font-size-md);
  font-weight: var(--font-weight-bold);
  color: var(--color-foreground);
  letter-spacing: 0.01em;
}

.headerRight {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.username {
  font-size: var(--font-size-md);
  color: var(--color-text-primary);
  font-weight: var(--font-weight-medium);
  margin-left: var(--spacing-xs);
}

.content {
  /* margin: var(--spacing-3xl) var(--spacing-3xl) 0 var(--spacing-3xl); */
  padding: var(--spacing-3xl);
  background: #fff;
  /* border-radius: var(--radius-2xl); */
  min-height: 400px;
  /* box-shadow: var(--shadow-lg); */
  overflow-y: auto;
  height: calc(100vh - var(--header-height) - 80px);
  /* Subtract header height and margin */
}

.langBtn {
  composes: btn-language from global;
  margin-right: var(--spacing-sm);
}

.langBtn :global(.anticon-down) {
  font-size: var(--font-size-xs);
  margin-left: var(--spacing-xs);
}

.userInfo {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-md);
  cursor: pointer;
  transition: background-color var(--transition-fast);
}

.userInfo:hover {
  background-color: var(--color-background-medium);
}

.username {
  color: var(--color-text-primary);
  font-size: var(--font-size-base);
  height: 28px;
  line-height: 28px;
  font-weight: var(--font-weight-medium);
}

.userDropdownIcon {
  font-size: var(--font-size-xs);
  color: var(--color-text-secondary);
  margin-left: var(--spacing-xs);
}