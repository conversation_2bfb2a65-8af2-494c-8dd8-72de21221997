'use client';

import { Button, Avatar, Dropdown, MenuProps, Tooltip } from 'antd';
import {
  UserOutlined,
  LogoutOutlined,
  GlobalOutlined,
  DownOutlined,
  ArrowLeftOutlined,
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  ReloadOutlined
} from '@ant-design/icons';
import { useRefresh } from '@/contexts/RefreshContext';
import GradientBorderButton from './GradientBorderButton';
import { useRouter } from 'next/navigation';
import { useLocaleContext } from '@/contexts/LocaleContext';
import { useAuthStore } from '@/store/authStore';
import { useDisconnect } from 'wagmi';
import styles from './header.module.css';
import Image from '../../public/White 1.png';
import RoleSelector from './RoleSelector';
import AddDeviceModal from './nodes/AddDeviceModal';
import { useState } from 'react';

interface HeaderProps {
  isHomePage?: boolean;
  collapsed?: boolean;
  setCollapsed?: (collapsed: boolean) => void;
  pageTitle?: string;
}

export default function Header({ isHomePage = false, collapsed, setCollapsed, pageTitle }: HeaderProps) {
  const router = useRouter();
  const { locale, setLocale, messages } = useLocaleContext();
  const { logout, walletAddress, isLoggedIn, accessToken } = useAuthStore();
  const { disconnect } = useDisconnect();
  const { refreshPage, isRefreshing } = useRefresh();

  const handleLanguageChange = ({ key }: { key: string }) => {
    setLocale(key as 'zh' | 'en');
  };

  const handleLogin = () => {
    router.push('/login');
  };

  const handleSignup = () => {
    router.push('/login');
  };

  const handleLogout = () => {
    // Disconnect wallet
    disconnect();
    // Clear login state
    logout();
    // Redirect to login page if not on home page
    if (!isHomePage) {
      router.push('/login');
    }
  };

  const langMenu = {
    items: [
      { key: 'zh', label: '中文' },
      { key: 'en', label: 'English' },
    ],
    onClick: handleLanguageChange,
  };

  const userMenu = {
    items: [
      {
        key: 'profile',
        icon: <UserOutlined />,
        label: messages.header?.profile || messages.profile?.title || 'Profile',
      },
      {
        key: 'logout',
        icon: <LogoutOutlined />,
        label: messages.home.logout,
        danger: true,
      },
    ],
    onClick: ({ key }: { key: string }) => {
      if (key === 'logout') {
        handleLogout();
      } else if (key === 'profile') {
        router.push('/profile');
      }
    },
  };
  console.log(isLoggedIn, walletAddress, accessToken)
  // Home page header
  if (isHomePage) {
    return (
      <div className={styles.topNav}>
        <div className={styles.logoContainer}>
          <img src={Image.src} alt="logo" className={styles.logo} />
        </div>
        <div className={styles.navBtns}>
          <Dropdown menu={langMenu} placement="bottomRight" className={styles.langDropdown} overlayStyle={{ zIndex: 9999999 }}>
            <Button type="text" className={styles.langBtn}>
              <GlobalOutlined />
              <span>{locale === 'zh' ? '中文' : 'English'}</span>
              <DownOutlined style={{ fontSize: '10px' }} />
            </Button>
          </Dropdown>
          {(isLoggedIn && walletAddress && accessToken) && <GradientBorderButton
            onClick={() => {
              router.push('/nodes')
            }}
          >
            {messages.home.create}
          </GradientBorderButton>}
          {(isLoggedIn && walletAddress && accessToken) ? (
            <Dropdown
              menu={userMenu}
              placement="bottomRight"
              trigger={['click']}
              mouseEnterDelay={0.5}
              overlayStyle={{ zIndex: 99999999 }}
            >
              <div className={styles.userInfo}>
                <Avatar
                  icon={<UserOutlined />}
                  size={28}
                  style={{ background: '#a259ff' }}
                />
                <span className={styles.username}>
                  {walletAddress
                    ? `${walletAddress.slice(0, 6)}...${walletAddress.slice(-4)}`
                    : ''}
                </span>
              </div>
            </Dropdown>
          ) : (
            <>
              <Button className={styles.connectWalletBtn} onClick={handleLogin}>
                {messages.login.connectWallet}
              </Button>
            </>
          )}
        </div>
      </div>
    );
  }

  const [addDeviceModalVisible, setAddDeviceModalVisible] = useState(false);
  // 处理添加设备成功
  const handleAddDeviceSuccess = () => {
    // 刷新设备列表
    setAddDeviceModalVisible(false);
    // // 调用设备列表的刷新方法
    // if (deviceListRef.current) {
    //   deviceListRef.current.loadDevices();
    // }
  };
  // Client layout header
  return (
    <div className={styles.header}>
      <div className={styles.headerLeft}>
        <div className={styles.roleSelectorWrapper}>
          <RoleSelector />
        </div>
        {/* {setCollapsed && (
          <Button
            type="text"
            icon={collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
            onClick={() => setCollapsed(!collapsed)}
            className={styles.collapseBtn}
          />
        )} */}
        {/* <Button
          type="text"
          icon={<ArrowLeftOutlined />}
          className={styles.backBtn}
          onClick={() => router.back()}
        /> */}
        {/* <span className={styles.pageTitle}>{pageTitle}</span> */}
      </div>
      <div className={styles.headerRight}>
        <Tooltip title={messages.common?.refresh || 'Refresh Page'}>
          <Button
            icon={<ReloadOutlined />}
            className={styles.refreshBtn}
            onClick={refreshPage}
            loading={isRefreshing}
          />
        </Tooltip>
        <Dropdown menu={langMenu} placement="bottomRight">
          <Button
            icon={<GlobalOutlined />}
            className={styles.langBtn}
          >
            {locale === 'zh' ? '中文' : 'EN'}
            <DownOutlined />
          </Button>
        </Dropdown>
        {(isLoggedIn && walletAddress && accessToken) && <GradientBorderButton
          onClick={() => {
            // setAddDeviceModalVisible(true)
            router.push('/nodes/create')
          }}
        >
          {messages.home.create}
        </GradientBorderButton>}
        {(isLoggedIn && walletAddress && accessToken) ? <Dropdown menu={userMenu} placement="bottomRight">
          <div className={styles.userInfo}>
            <Avatar
              icon={<UserOutlined />}
              size={28}
              style={{ background: '#a259ff' }}
            />
            <span className={styles.username}>
              {walletAddress
                ? `${walletAddress.slice(0, 6)}...${walletAddress.slice(-4)}`
                : messages.header.user}
            </span>
            <DownOutlined className={styles.userDropdownIcon} />
          </div>
        </Dropdown> : <Button className={styles.connectWalletBtn} onClick={handleLogin}>
          {messages.login.connectWallet}
        </Button>}
      </div>

      <AddDeviceModal
        visible={addDeviceModalVisible}
        onCancel={() => setAddDeviceModalVisible(false)}
        onSuccess={handleAddDeviceSuccess}
      />
    </div>
  );
}
