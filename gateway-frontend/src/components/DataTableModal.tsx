import { Modal, Table } from 'antd';
import { TableProps } from 'antd/es/table';
import { useState, useEffect } from 'react';

interface DataTableModalProps<T> {
  title: string;
  open: boolean;
  onClose: () => void;
  columns: TableProps<T>['columns'];
  dataSource: T[];
  total?: number;
  pageSize?: number;
  loading?: boolean;
  onPageChange?: (page: number, pageSize: number) => void;
  serverPagination?: boolean;
}

export function DataTableModal<T extends object>({
  title,
  open,
  onClose,
  columns,
  dataSource,
  total,
  pageSize = 10,
  loading = false,
  onPageChange,
  serverPagination = false,
}: DataTableModalProps<T>) {
  const [currentPage, setCurrentPage] = useState(1);
  const [currentPageSize, setCurrentPageSize] = useState(pageSize);

  // Reset pagination when modal opens
  useEffect(() => {
    if (open) {
      setCurrentPage(1);
      setCurrentPageSize(pageSize);
    }
  }, [open, pageSize]);

  // Handle pagination change
  const handlePaginationChange = (page: number, size: number) => {
    setCurrentPage(page);
    setCurrentPageSize(size);

    if (serverPagination && onPageChange) {
      onPageChange(page, size);
    }
  };

  return (
    <Modal
      title={title}
      open={open}
      onCancel={onClose}
      width={1200}
      footer={null}
    >
      <Table
        columns={columns}
        dataSource={dataSource}
        loading={loading}
        pagination={
          serverPagination
            ? {
                current: currentPage,
                pageSize: currentPageSize,
                total: total || 0,
                showSizeChanger: true,
                showTotal: (total) => `Total ${total} items`,
                onChange: handlePaginationChange,
                onShowSizeChange: handlePaginationChange,
              }
            : {
                pageSize: currentPageSize,
                showSizeChanger: true,
                showTotal: (total) => `Total ${total} items`,
                onChange: handlePaginationChange,
                onShowSizeChange: handlePaginationChange,
              }
        }
        scroll={{ x: 'max-content' }}
      />
    </Modal>
  );
}