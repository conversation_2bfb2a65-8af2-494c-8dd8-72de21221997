'use client';

import React from 'react';
import styles from './clientLayout.module.css';
import { useRouter } from 'next/navigation';
import Collapsed from '../../public/dock_to_right.png';

interface SiderCollapsedProps {
  collapsed?: boolean;
  setCollapsed?: (collapsed: boolean) => void;
}

const SiderCollapsed: React.FC<SiderCollapsedProps> = ({ collapsed = false, setCollapsed }) => {
  const router = useRouter();

  const handleTitleClick = () => {
    router.push('/');
  };

  const handleIconClick = (e: React.MouseEvent) => {
    e.stopPropagation(); // 阻止事件冒泡，避免触发整个组件的点击事件
    if (setCollapsed) {
      setCollapsed(!collapsed);
    }
  };

  return (
    <div className={styles.SiderCollapsed}>
      {!collapsed && <span onClick={handleTitleClick}>
        {collapsed ? 'S' : 'SIGHT'}
      </span>}
      <img
        src={Collapsed.src}
        alt="menu"
        style={{ cursor: 'pointer', transform: collapsed ? 'rotate(180deg)' : 'none' }}
        onClick={handleIconClick}
        className={styles.menuIcon}
      />
    </div>
  );
};

export default SiderCollapsed;
