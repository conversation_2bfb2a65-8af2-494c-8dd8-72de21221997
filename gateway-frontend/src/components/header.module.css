/* Common header styles */
.header, .topNav {
  background: rgba(255,255,255,0.85);
  /* box-shadow: var(--shadow-sm); */
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: var(--header-height);
  padding: 0 var(--spacing-2xl);
  /* border-bottom: 1px solid var(--color-border); */
}

/* Home page specific header styles */
.topNav {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  z-index: var(--z-index-fixed);
}

.logoContainer {
  display: flex;
  align-items: center;
}

.logo {
  height: 36px;
}

.headerLogo {
  height: 36px;
  margin-right: var(--spacing-lg);
  cursor: pointer;
}

.roleSelectorWrapper {
  margin-right: var(--spacing-lg);
  min-width: 250px;
  max-width: 300px;
  display: flex;
  align-items: center;
  height: 44px;
}

.navBtns {
  display: flex;
  gap: var(--spacing-md);
  align-items: center;
}

/* Language dropdown */
.langDropdown {
  margin-right: var(--spacing-sm);
}



/* Use common language button styles */
.langBtn {
  composes: btn-language from global;
  margin-right: var(--spacing-sm);
}

.langBtn :global(.anticon-down) {
  font-size: var(--font-size-xs);
  margin-left: var(--spacing-xs);
}

/* Login/Signup buttons */
.loginBtn {
  composes: btn from global;
  position: relative;
  padding: 0 var(--spacing-md);
  border: none;
  color: white;
  font-weight: var(--font-weight-medium);
  height: 36px;
  background: linear-gradient(119.29deg, #6D20F5 0%, #E7337A 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: transform var(--transition-fast), box-shadow var(--transition-fast);
}

.loginBtn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(109, 32, 245, 0.2);
  background: linear-gradient(119.29deg, #7D30FF 0%, #F7438A 100%);
}

.signupBtn {
  composes: btn from global;
  position: relative;
  background: transparent !important;
  color: black !important;
  border: none !important;
  z-index: 1;
  font-weight: var(--font-weight-medium);
  padding: 0 var(--spacing-md);
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: transform var(--transition-fast), box-shadow var(--transition-fast);
  border-radius: 50px;
  font-size: 16px;
}

.signupBtn:hover,
.signupBtn:focus,
.signupBtn:active {
  transform: translateY(-1px);
  color: black !important;
  background: transparent !important;
}

.signupBtn span,
.signupBtn:hover span,
.signupBtn:focus span,
.signupBtn:active span,
button.signupBtn span,
button.signupBtn:hover span,
button.signupBtn:focus span,
button.signupBtn:active span {
  color: black !important;
}

.signupBtn::before {
  content: "";
  position: absolute;
  inset: 0;
  border-radius: 50px;
  padding: 1px; /* Border width */
  background: linear-gradient(119.29deg, #6D20F5 0%, #E7337A 100%);
  -webkit-mask:
    linear-gradient(#fff 0 0) content-box,
    linear-gradient(#fff 0 0);
  mask:
    linear-gradient(#fff 0 0) content-box,
    linear-gradient(#fff 0 0);
  -webkit-mask-composite: xor;
  mask-composite: exclude;
  z-index: -1;
}

.signupBtn:hover::before {
  background: linear-gradient(119.29deg, #7D30FF 0%, #F7438A 100%);
}

/* Client layout specific header styles */
.headerLeft {
  display: flex;
  align-items: center;
  gap: var(--spacing-lg);
}

.collapseBtn {
  background: transparent;
  border: none;
  font-size: var(--font-size-lg);
  padding: 0;
  margin-right: var(--spacing-sm);
}

.backBtn {
  background: #f5f7fa !important;
  border-radius: var(--radius-lg) !important;
  color: #2a4ae4 !important;
  border: none;
  box-shadow: none;
  font-size: var(--font-size-xl) !important;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: var(--spacing-sm);
  transition: background var(--transition-fast);
}

.backBtn:hover {
  background: #e6edff !important;
}

.pageTitle {
  font-size: var(--font-size-md);
  font-weight: var(--font-weight-bold);
  letter-spacing: 0.01em;
}

.headerRight {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

/* Refresh button */
.refreshBtn {
  background: transparent;
  border: none;
  font-size: var(--font-size-lg);
  padding: 0;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--color-text-secondary);
  transition: all var(--transition-fast);
}

.refreshBtn:hover {
  color: var(--color-primary);
  background: var(--color-background-medium);
}

/* Connect Wallet Button */
.connectWalletBtn {
  composes: btn from global;
  position: relative;
  /* padding: 10px 30px; */
  border-radius: 50px;
  border: none;
  color: white !important;
  font-size: 12px;
  font-weight: 300;
  cursor: pointer;
  background: linear-gradient(119.29deg, #6D20F5 0%, #E7337A 100%);
  transition: transform var(--transition-fast), box-shadow var(--transition-fast);
}

.connectWalletBtn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(109, 32, 245, 0.2);
  color: white !important;
  background: linear-gradient(119.29deg, #7D30FF 0%, #F7438A 100%);
}

/* User info dropdown */
.userInfo {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-md);
  cursor: pointer;
  transition: background-color var(--transition-fast);
}

.userInfo:hover {
  background-color: var(--color-background-medium);
}

.username {
  color: var(--color-text-primary);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
}

.userDropdownIcon {
  font-size: var(--font-size-xs);
  color: var(--color-text-secondary);
  margin-left: var(--spacing-xs);
}
