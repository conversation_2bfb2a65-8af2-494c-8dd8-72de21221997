:root {
  --background: #ffffff;
  /* Colors */
  --color-primary: #9333EA;
  --color-primary-hover: #A855F7;
  --color-primary-light: #a259ff;
  --color-primary-light-hover: #b57aff;
  --color-primary-gradient: linear-gradient(119.29deg, #6D20F5 0%, #E7337A 100%);

  --color-background: #ffffff;
  --color-foreground: #171717;
  --color-background-dark: #0a0a0a;
  --color-foreground-dark: #ededed;

  --color-text-primary: #333333;
  --color-text-secondary: #666666;
  --color-text-tertiary: rgba(0, 0, 0, 0.45);
  --color-text-light: #ffffff;

  --color-border: #ececec;
  --color-border-light: rgba(0, 0, 0, 0.06);
  --color-border-medium: rgba(0, 0, 0, 0.15);
  --color-border-dark: rgba(0, 0, 0, 0.3);

  --color-background-hover: rgba(0, 0, 0, 0.02);
  --color-background-light: rgba(0, 0, 0, 0.03);
  --color-background-medium: rgba(0, 0, 0, 0.05);
  --color-background-dark: rgba(0, 0, 0, 0.06);

  --color-shadow-light: rgba(0, 0, 0, 0.03);
  --color-shadow-medium: rgba(0, 0, 0, 0.08);
  --color-shadow-dark: rgba(0, 0, 0, 0.15);
  --color-shadow-primary: rgba(162, 89, 255, 0.08);
  --color-shadow-primary-hover: rgba(162, 89, 255, 0.15);

  /* Status Colors */
  --color-success: #52c41a;
  --color-warning: #faad14;
  --color-error: #f5222d;
  --color-info: #1890ff;

  /* Status Tag Colors */
  --color-status-online: #52c41a;
  --color-status-offline: #f5222d;
  --color-status-busy: #faad14;
  --color-status-waiting: #faad14;
  --color-status-in-progress: #1890ff;
  --color-status-connected: #52c41a;
  --color-status-disconnected: #f5222d;
  --color-status-failed: #f5222d;

  /* Typography */
  --font-family-base: var(--font-geist-sans), 'Inter', 'Helvetica', Arial, sans-serif;
  --font-family-mono: var(--font-geist-mono), monospace;
  --font-family-bruno-ace: var(--font-bruno-ace), 'Bruno Ace', cursive;

  --font-size-xs: 12px;
  --font-size-sm: 13px;
  --font-size-base: 14px;
  --font-size-md: 16px;
  --font-size-lg: 18px;
  --font-size-xl: 20px;
  --font-size-2xl: 24px;

  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;

  /* Spacing */
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 12px;
  --spacing-base: 16px;
  --spacing-lg: 20px;
  --spacing-xl: 24px;
  --spacing-2xl: 32px;
  --spacing-3xl: 48px;
  --spacing-4xl: 64px;

  /* Border Radius */
  --radius-sm: 4px;
  --radius-md: 8px;
  --radius-lg: 10px;
  --radius-xl: 16px;
  --radius-2xl: 24px;

  /* Transitions */
  --transition-fast: 0.2s ease;
  --transition-base: 0.3s ease;
  --transition-slow: 0.5s ease;
  --transition-bounce: 0.18s cubic-bezier(.4, 2, .6, 1);

  /* Z-index */
  --z-index-dropdown: 1000;
  --z-index-sticky: 1020;
  --z-index-fixed: 1030;
  --z-index-modal-backdrop: 1040;
  --z-index-modal: 1050;
  --z-index-popover: 1060;
  --z-index-tooltip: 1070;

  /* Shadows */
  --shadow-sm: 0 2px 8px var(--color-shadow-light);
  --shadow-md: 0 4px 12px var(--color-shadow-medium);
  --shadow-lg: 0 8px 24px var(--color-shadow-dark);
  --shadow-primary: 0 2px 8px var(--color-shadow-primary);
  --shadow-primary-hover: 0 4px 12px var(--color-shadow-primary-hover);

  /* Layout */
  --header-height: 64px;
}

@media (prefers-color-scheme: dark) {
  :root {
    --color-background: var(--color-background-dark);
    --color-foreground: var(--color-foreground-dark);
  }
}
