/* Import variables */
@import './variables.css';

/* <PERSON><PERSON> Styles */
.btn {
  border-radius: var(--radius-xl);
  font-weight: var(--font-weight-medium);
  height: 32px;
  padding: 0 var(--spacing-md);
  transition: var(--transition-fast);
}

.btn-primary {
  background: var(--color-primary-light);
  color: var(--color-text-light);
  border: none;
  box-shadow: var(--shadow-primary);
}

.btn-primary:hover {
  background: var(--color-primary-light-hover);
  transform: translateY(-1px);
  box-shadow: var(--shadow-primary-hover);
}

.btn-secondary {
  border-color: var(--color-border-medium);
  color: var(--color-text-primary);
}

.btn-secondary:hover {
  border-color: var(--color-border-dark);
  color: var(--color-foreground);
  background: var(--color-background-hover);
}

.btn-language {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  color: var(--color-text-primary) !important;
  background: var(--color-background-light) !important;
  border: 1px solid var(--color-border-light) !important;
  border-radius: var(--radius-xl) !important;
  padding: var(--spacing-xs) var(--spacing-md) !important;
  height: 32px;
  transition: var(--transition-fast);
  font-size: var(--font-size-sm);
}

/* Card Styles */
.card {
  border-radius: var(--radius-2xl);
  transition: transform var(--transition-bounce), box-shadow var(--transition-base);
  cursor: pointer;
}

.card:hover {
  transform: translateY(-8px) scale(1.04);
}

/* Layout Utilities */
.flex {
  display: flex;
}

.flex-col {
  flex-direction: column;
}

.items-center {
  align-items: center;
}

.justify-center {
  justify-content: center;
}

.justify-between {
  justify-content: space-between;
}

.justify-end {
  justify-content: flex-end;
}

.gap-xs {
  gap: var(--spacing-xs);
}

.gap-sm {
  gap: var(--spacing-sm);
}

.gap-md {
  gap: var(--spacing-md);
}

.gap-base {
  gap: var(--spacing-base);
}

.gap-lg {
  gap: var(--spacing-lg);
}

/* Spacing Utilities */
.m-0 {
  margin: 0;
}

.mt-xs {
  margin-top: var(--spacing-xs);
}

.mt-sm {
  margin-top: var(--spacing-sm);
}

.mt-md {
  margin-top: var(--spacing-md);
}

.mt-base {
  margin-top: var(--spacing-base);
}

.mr-xs {
  margin-right: var(--spacing-xs);
}

.mr-sm {
  margin-right: var(--spacing-sm);
}

.mr-md {
  margin-right: var(--spacing-md);
}

.mr-lg {
  margin-right: var(--spacing-lg);
}

.mb-xs {
  margin-bottom: var(--spacing-xs);
}

.mb-sm {
  margin-bottom: var(--spacing-sm);
}

.mb-md {
  margin-bottom: var(--spacing-md);
}

.mb-base {
  margin-bottom: var(--spacing-base);
}

/* Typography Utilities */
.text-xs {
  font-size: var(--font-size-xs);
}

.text-sm {
  font-size: var(--font-size-sm);
}

.text-base {
  font-size: var(--font-size-base);
}

.text-md {
  font-size: var(--font-size-md);
}

.text-lg {
  font-size: var(--font-size-lg);
}

.text-xl {
  font-size: var(--font-size-xl);
}

.text-2xl {
  font-size: var(--font-size-2xl);
}

.font-normal {
  font-weight: var(--font-weight-normal);
}

.font-medium {
  font-weight: var(--font-weight-medium);
}

.font-semibold {
  font-weight: var(--font-weight-semibold);
}

.font-bold {
  font-weight: var(--font-weight-bold);
}

.text-primary {
  color: var(--color-text-primary);
}

.text-secondary {
  color: var(--color-text-secondary);
}

.text-tertiary {
  color: var(--color-text-tertiary);
}

.text-light {
  color: var(--color-text-light);
}

.font-mono {
  font-family: var(--font-family-mono);
}

/* Status Tag Styles */
.status-tag {
  min-width: 80px;
  text-align: center;
  border-radius: var(--radius-lg);
  padding: 0 var(--spacing-sm);
}

.status-online {
  color: var(--color-status-online);
  background-color: rgba(82, 196, 26, 0.1);
  border-color: rgba(82, 196, 26, 0.2);
}

.status-offline {
  color: var(--color-status-offline);
  background-color: rgba(245, 34, 45, 0.1);
  border-color: rgba(245, 34, 45, 0.2);
}

.status-busy {
  color: var(--color-status-busy);
  background-color: rgba(250, 173, 20, 0.1);
  border-color: rgba(250, 173, 20, 0.2);
}

.status-waiting {
  color: var(--color-status-waiting);
  background-color: rgba(250, 173, 20, 0.1);
  border-color: rgba(250, 173, 20, 0.2);
}

.status-in-progress {
  color: var(--color-status-in-progress);
  background-color: rgba(24, 144, 255, 0.1);
  border-color: rgba(24, 144, 255, 0.2);
}

/* Modal Styles */
.modal :global(.ant-modal-body) {
  padding: var(--spacing-xl);
}

.modal :global(.ant-modal-content) {
  padding: var(--spacing-xl);
}

/* Ant Design Button Override */
.ant-btn-primary:not([disabled]):not(.ant-btn-dangerous) {
  border-width: 0;
 
  > span {
    position: relative;
  }
 
  &::before {
    content: '';
    background: var(--color-primary-gradient);
    position: absolute;
    inset: 0;
    opacity: 1;
    transition: var(--transition-base);
    border-radius: inherit;
  }
 
  &:hover::before {
    opacity: 0;
  }
}
