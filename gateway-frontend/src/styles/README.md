# CSS Organization Guide

This document outlines the CSS organization and best practices for the Gateway Frontend project.

## Directory Structure

```
src/
├── styles/
│   ├── variables.css    # CSS variables for colors, spacing, etc.
│   ├── common.css       # Common utility classes and shared styles
│   └── README.md        # This documentation file
├── app/
│   ├── globals.css      # Global styles and CSS reset
│   └── [component].module.css  # Page-specific styles
└── components/
    └── [component]/
        └── [Component].module.css  # Component-specific styles
```

## CSS Architecture

We follow a modular CSS approach with CSS Modules for component-specific styles and global utility classes for common patterns.

### 1. Variables (variables.css)

All design tokens are defined as CSS variables in `variables.css`:

- Colors
- Typography
- Spacing
- Border radius
- Z-index
- Shadows
- Transitions

Example usage:
```css
.myElement {
  color: var(--color-primary);
  padding: var(--spacing-md);
  border-radius: var(--radius-sm);
}
```

### 2. Common Styles (common.css)

Common utility classes and shared component styles:

- Button styles
- Card styles
- Layout utilities
- Typography utilities
- Status tags

Example usage:
```css
/* In your component CSS */
.myButton {
  composes: btn btn-primary from global;
}
```

### 3. Global Styles (globals.css)

Global styles and CSS reset:

- CSS reset
- Base element styles
- Imports for variables and common styles

### 4. Component Styles (*.module.css)

Component-specific styles using CSS Modules:

- Scoped to the component
- Can compose global styles
- Should use variables for consistency

Example:
```css
.container {
  padding: var(--spacing-lg);
}

.title {
  font-size: var(--font-size-xl);
  color: var(--color-text-primary);
}

.button {
  composes: btn btn-primary from global;
}
```

## Best Practices

1. **Use CSS variables** for all design tokens
2. **Avoid hardcoded values** for colors, spacing, etc.
3. **Use composition** with `composes` for shared styles
4. **Keep component styles focused** on the component's needs
5. **Use semantic class names** that describe the purpose, not the appearance
6. **Avoid deep nesting** of selectors
7. **Use utility classes** from common.css for repeated patterns
8. **Document complex styles** with comments

## Naming Conventions

- Use kebab-case for global classes: `.btn-primary`
- Use camelCase for component classes: `.cardContainer`
- Use descriptive, semantic names: `.errorMessage` not `.redText`
- Prefix state classes: `.isActive`, `.hasError`

## Media Queries

Media queries should be defined at the component level, using the variables for breakpoints:

```css
@media (max-width: var(--breakpoint-md)) {
  .container {
    flex-direction: column;
  }
}
```

## Dark Mode

Dark mode is supported through CSS variables with alternate values:

```css
@media (prefers-color-scheme: dark) {
  :root {
    --color-background: var(--color-background-dark);
    --color-foreground: var(--color-foreground-dark);
  }
}
```
