import { http } from 'wagmi';
import { mainnet, sepolia } from 'wagmi/chains';
import { createConfig } from 'wagmi';
import { QueryClient } from '@tanstack/react-query';

const projectId = process.env.NEXT_PUBLIC_WALLET_CONNECT_PROJECT_ID || '';

export const metadata = {
  name: 'Sight AI Gateway',
  description: 'Sight AI Gateway Web3 Integration',
  url: 'https://sight.ai',
  icons: ['https://sight.ai/icon.png']
};

export const config = createConfig({
  chains: [mainnet, sepolia],
  transports: {
    [mainnet.id]: http(),
    [sepolia.id]: http(),
  },
});

export const queryClient = new QueryClient();

// 在客户端初始化 Web3Modal
export const initWeb3Modal = () => {
  if (typeof window !== 'undefined') {
    const { createWeb3Modal, defaultWagmiConfig } = require('@web3modal/wagmi/react');
    
    const wagmiConfig = defaultWagmiConfig({
      chains: [mainnet, sepolia],
      projectId,
      metadata,
      enableWalletConnect: true,
      enableInjected: true,
      enableEIP6963: true,
      enableCoinbase: true,
      enableRainbow: true,
      enableTrust: true,
      enableLedger: true,
      enableZerion: true,
    });

    createWeb3Modal({
      wagmiConfig,
      projectId,
      chains: [mainnet, sepolia],
      themeMode: 'light',
      themeVariables: {
        '--w3m-font-family': 'var(--font-geist-sans)',
        '--w3m-accent-color': '#1890ff',
        '--w3m-background-color': '#ffffff',
        '--w3m-text-color': '#1a1a1a',
        '--w3m-border-radius': '8px',
        '--w3m-button-border-radius': '8px',
        '--w3m-button-height': '48px',
        '--w3m-button-font-size': '16px',
        '--w3m-button-font-weight': '500',
        '--w3m-button-letter-spacing': '0.5px',
        '--w3m-button-text-transform': 'none',
        '--w3m-button-padding': '0 24px',
      },
      includeWalletIds: [
        'c57ca95b47569778a828d19178114f4db188b89b763c899ba0be274e97267d96', // MetaMask
        'fd20dc426fb37566d803205b19bbc1d4096b248ac04548e3cfb6b3a38bd033aa', // Coinbase
        '4622a2b2d6af1c9844944291e5e7351a6aa24cd7b23099efac1b2fd875da31a0', // Trust
        '225affb176778569276e484e1b92637ad061b01e13a048b35a9d280c3b58970f', // Rainbow
        '19177a98252e07ddfc9af2083ba8e07ef627cb6103467ffebb3f8f4205fd7927', // Ledger
        'fd20dc426fb37566d803205b19bbc1d4096b248ac04548e3cfb6b3a38bd033aa', // Zerion
      ],
    });
  }
}; 