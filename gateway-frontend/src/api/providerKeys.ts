import { apiRequest } from "@/utils/api";

// Types
export interface ProviderKey {
  id: string;
  name: string;
  keyType: string;
  status: 'active' | 'inactive' | 'error';
  createdAt: string;
  updatedAt?: string;
  lastUsed?: string | null;
  // Additional fields for display
  provider: string;
  keyMask?: string;
  region: string;
  endpoint?: string;
  models?: string[];
}

export interface Provider {
  id: string;
  name: string;
  displayName: string;
  iconUrl?: string;
  defaultEndpoint?: string;
  supportedModels: string[];
  authType: 'bearer' | 'api_key';
  isActive: boolean;
}

export interface CreateProviderKeyRequest {
  provider: string;        // 提供商名称 (openai, claude, etc.)
  region?: string;         // 区域名称 (asia, us, europe, etc.)
  keyId: string;           // executor key ID
  // 加密相关字段
  encryptedKey: string;    // 加密后的API密钥
  nonce: string;           // 加密使用的nonce
  tag: string;             // 加密标签
  ephemeralPubKey: string; // 临时公钥
  name: string;             // Key name provided by user
}

export interface UpdateProviderKeyRequest {
  provider: string;        // 提供商名称 (openai, claude, etc.)
  region?: string;         // 区域名称 (asia, us, europe, etc.)
  status?: 'active' | 'inactive';
}

export interface TestConnectionRequest {
  keyType: string;
  apiKey: string;
  endpoint?: string;
}

export interface TestConnectionResponse {
  success: boolean;
  message: string;
  availableModels?: string[];
  responseTime?: number;
}

export interface QueryParams {
  page?: number;
  pageSize?: number;
  status?: 'active' | 'inactive' | 'error';
  keyType?: string;
  search?: string;
}

// 新接口类型定义
export interface PublicKeyInfo {
  keyId: string;
  publicKey: string;
  provider: string;
  region: string;
  status: 'active' | 'inactive';
  registeredAt: string;
  lastHeartbeat: string;
}

export interface GetPublicKeysParams {
  provider: string;
  region?: string;
  status?: 'active' | 'inactive' | 'all';
}

export interface GetPublicKeysResponse {
  success: boolean;
  data: {
    keys: PublicKeyInfo[];
    totalCount: number;
  };
}

export interface CreateEncryptedKeyResponse {
  success: boolean;
  data: {
    uuid: string;
    provider: string;
    region: string;
    keyId: string;
    status: string;
    createdAt: string;
  };
}

// API Functions
/**
 * Get all third-party keys
 * @param params Query parameters
 */
export const getProviderKeys = async (params?: QueryParams): Promise<{ data: {items: ProviderKey[]}; total: number; page: number; pageSize: number }> => {
  const response = await apiRequest({
    url: 'third-party/third-party-keys',
    method: 'GET',
    query: params
  });

  // Transform the response to match our interface
  const keys = Array.isArray(response.data.items) ? response.data.items : [];
  return {
    data: keys.map((key: any) => ({
      ...key,
      provider: key.keyType, // Map keyType to provider for display
      keyMask: key.keyMask || `${key.keyType}_****${key.id.slice(-4)}` // Generate mask if not provided
    })),
    total: keys.length,
    page: params?.page || 1,
    pageSize: params?.pageSize || 10
  };
};

/**
 * Get available providers (hardcoded for now since backend doesn't have this endpoint)
 */
export const getProviders = async (): Promise<Provider[]> => {
  // Return hardcoded providers since backend doesn't have this endpoint yet
  return Promise.resolve([
    {
      id: 'openai',
      name: 'OpenAI',
      displayName: 'OpenAI',
      iconUrl: '/openai.png',
      defaultEndpoint: 'https://api.openai.com/v1',
      supportedModels: ['gpt-4', 'gpt-3.5-turbo', 'gpt-4-turbo'],
      authType: 'bearer',
      isActive: true
    }
  ]);
};

/**
 * Create a new encrypted API key
 * @param data The encrypted provider key data
 */
export const createProviderKey = async (data: CreateProviderKeyRequest): Promise<CreateEncryptedKeyResponse> => {
  const response = await apiRequest({
    url: 'third-party/encrypted',
    method: 'POST',
    body: data
  });
  return response;
};

/**
 * Update a third-party key (not implemented in backend yet)
 * @param id The provider key ID
 * @param data The update data
 */
export const updateProviderKey = async (id: string, data: UpdateProviderKeyRequest): Promise<CreateEncryptedKeyResponse> => {
    const response = await apiRequest({
    url: `third-party/encrypted/${data.provider}/${data.region}/${id}`,
    method: 'PUT',
    body: {
      status: data.status
    }
  });
  return response;
};

/**
 * Delete a third-party key
 * @param id The provider key ID
 */
export const deleteProviderKey = async (id: string): Promise<void> => {
  await apiRequest({
    url: `third-party/${id}`,
    method: 'DELETE'
  });
};

/**
 * Test connection for a provider key (not implemented in backend yet)
 * @param data The test connection data
 */
export const testConnection = async (data: TestConnectionRequest): Promise<TestConnectionResponse> => {
  // Mock implementation since backend doesn't have this endpoint yet
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve({
        success: true,
        message: 'Connection test successful (mocked)',
        availableModels: ['gpt-4', 'gpt-3.5-turbo'],
        responseTime: 150
      });
    }, 1000);
  });
};

/**
 * Test existing provider key connection (not implemented in backend yet)
 * @param id The provider key ID
 */
export const testProviderKey = async (id: string): Promise<TestConnectionResponse> => {
  // Mock implementation since backend doesn't have this endpoint yet
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve({
        success: true,
        message: 'Connection test successful (mocked)',
        responseTime: 120
      });
    }, 1000);
  });
};

/**
 * Get provider key detail (not implemented in backend yet)
 * @param id The provider key ID
 */
export const getProviderKeyDetail = async (id: string): Promise<ProviderKey> => {
  // Mock implementation since backend doesn't have this endpoint yet
  throw new Error('Get detail functionality not yet implemented in backend');
};

/**
 * Get public keys for encryption
 * @param params Query parameters for public keys
 * @returns Public keys response
 */
export const getPublicKeys = async (params: GetPublicKeysParams): Promise<GetPublicKeysResponse> => {
  const response = await apiRequest({
    url: 'public-keys',
    method: 'GET',
    query: params
  });
  return response;
};

/**
 * Get executor public key for specific provider and region
 * @param provider Provider name (openai, claude, etc.)
 * @param region Region name (asia, us, europe, etc.)
 * @returns The executor public key info
 */
export const getExecutorPublicKey = async (provider: string, region: string = 'asia'): Promise<PublicKeyInfo> => {
  try {
    const response = await getPublicKeys({
      provider: provider.toLowerCase(),
      region,
      status: 'active'
    });

    if (response.success && response.data.keys.length > 0) {
      // Return the first active key
      return response.data.keys[0];
    } else {
      throw new Error('No active public keys found');
    }
  } catch (error) {
    // Mock implementation for development/testing
    console.warn('Using mock executor public key for development');
    // Generate a mock keypair and return the public key
    const nacl = await import('tweetnacl');
    const mockKeypair = nacl.box.keyPair();
    return {
      keyId: `executor-${provider}-${region}-mock`,
      publicKey: Buffer.from(mockKeypair.publicKey).toString('base64'),
      provider: provider.toLowerCase(),
      region,
      status: 'active',
      registeredAt: new Date().toISOString(),
      lastHeartbeat: new Date().toISOString()
    };
  }
};
