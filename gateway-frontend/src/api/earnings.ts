import { apiRequest } from "@/utils/api";

export interface StatsData {
  earnings: number;
  tasks: number;
  nodes: number;
}

export interface ChartDataPoint {
  date: string;
  value: number;
}

export interface ServerStatusData {
  server: string;
  apiLoad: number;
  wsStatus: string;
  taskTime: number;
  health: string;
}

export interface TaskQueueData {
  id: string;
  model: string;
  status: string;
  queue: number | string;
  device: string | null;
  ptime: number | string;
  earn: number | string;
  tokens?: number;
  created_at?: string;
  updated_at?: string;
}

export interface NodePerformanceData {
  id: string;
  type: string;
  tasks: number;
  mem: number;
  rate: number;
  earn: number;
  status?: string;
  last_ping?: string;
}

export interface EarningsData {
  id: string;
  block_rewards: number;
  job_rewards: number;
  total_rewards: number;
  date: string;
  created_at: string;
  device_name: string | null;
  model: string | null;
}

export interface QueryParams {
  page?: number;
  pageSize?: number;
  status?: string;
  search?: string;
  timeRange?: string;
}

/**
 * Get overall earnings statistics
 * @param timeRange Optional time range in days (e.g., "7", "30", "90", "365")
 */
export const getEarningsStats = async (timeRange?: string): Promise<{ data: StatsData }> => {
  return apiRequest({
    url: 'earnings/stats',
    method: 'GET',
    query: { timeRange }
  });
};

/**
 * Get earnings chart data
 * @param days Number of days to get data for
 */
export const getEarningsChartData = async (days: number = 15): Promise<{ data: ChartDataPoint[] }> => {
  return apiRequest({ url: 'earnings/chart', method: 'GET', query: { days } });
};

/**
 * Get server status information
 * @param timeRange Optional time range in days (e.g., "7", "30", "90", "365")
 */
export const getServerStatus = async (timeRange?: string): Promise<{ data: ServerStatusData[] }> => {
  return apiRequest({
    url: 'earnings/server-status',
    method: 'GET',
    query: { timeRange }
  });
};

/**
 * Get task queue data
 * @param params Query parameters including pagination, filters, and time range
 */
export const getTaskQueue = async (params?: QueryParams): Promise<{ data: { data: TaskQueueData[]; total: number } }> => {
  return apiRequest({
    url: 'earnings/task-queue',
    method: 'GET',
    query: {
      page: params?.page,
      pageSize: params?.pageSize,
      status: params?.status,
      search: params?.search,
      timeRange: params?.timeRange
    }
  });
};

/**
 * Get node performance data
 * @param params Query parameters including pagination, filters, and time range
 */
export const getNodePerformance = async (params?: QueryParams): Promise<{ data: { data: NodePerformanceData[]; total: number } }> => {
  return apiRequest({
    url: 'earnings/node-performance',
    method: 'GET',
    query: {
      page: params?.page,
      pageSize: params?.pageSize,
      status: params?.status,
      search: params?.search,
      timeRange: params?.timeRange
    }
  });
};

/**
 * Get earnings data
 * @param params Query parameters including pagination and time range
 */
export const getEarnings = async (params?: QueryParams): Promise<{ data: { data: EarningsData[]; total: number } }> => {
  return apiRequest({
    url: 'earnings',
    method: 'GET',
    query: {
      page: params?.page,
      pageSize: params?.pageSize,
      timeRange: params?.timeRange
    }
  });
};

/**
 * Get device earnings data
 * @param deviceId The ID of the device
 * @param params Query parameters including pagination, status, and time range
 */
export const getDeviceEarnings = async (deviceId: string, params?: QueryParams): Promise<{ data: { data: EarningsData[]; total: number } }> => {
  return apiRequest({
    url: `node/devices/${deviceId}/earnings`,
    method: 'GET',
    query: {
      page: params?.page,
      pageSize: params?.pageSize,
      status: params?.status,
      timeRange: params?.timeRange
    }
  });
};