import { apiRequest } from "@/utils/api";
import axios from "axios";

/**
 * Login with wallet signature
 * @param signMessage The signed message
 * @param signature The signature
 * @returns The login response with accessToken and userId
 */
export const login = async (signMessage: string, signature: string) => {
    // First try using the apiRequest utility
    return await apiRequest({
        url: 'auth/sign-in',
        method: 'POST',
        body: {
            message: signMessage,
            signature: signature,
        }
    });
}