import { apiRequest } from "@/utils/api";

// 接口定义
interface ConnectTask {
  task_name: string;
  signature: string;
  device_type?: string;
  gpu_type?: string;
}

interface ConnectTaskResponseData {
  task_id: string;
  one_time_code: string;
  gateway_address: string;
}

interface ConnectTaskResponse {
  data?: ConnectTaskResponseData;
  task_id?: string;
  one_time_code?: string;
  gateway_address?: string;
}

interface DeviceListParams {
  current?: number;
  pageSize?: number;
  status?: string;
  search?: string;
  onlyMyDevices?: boolean;
}

interface DeviceMetrics {
  cpuUsagePercent?: number;
  ramUsagePercent?: number;
  gpuUsagePercent?: number;
  gpuTemperature?: number;
  networkInKbps?: number;
  networkOutKbps?: number;
  currentAiModel?: string;
}

// 创建设备连接任务
export const createConnectTask = async (task: ConnectTask): Promise<ConnectTaskResponse> => {
  return apiRequest({
    url: 'node/connect-task',
    method: 'POST',
    body: {
      task_name: task.task_name,
      signature: task.signature,
      device_type: task.device_type,
      gpu_type: task.gpu_type
    }
  });
}

// 获取连接任务列表
export const getConnectTasks = (params: DeviceListParams = {}) => {
  return apiRequest({
    url: 'node/connect-task-list',
    method: 'GET',
    query: {
      page: params.current || 1,
      pageSize: params.pageSize || 10,
      status: params.status,
      search: params.search ? params.search.trim() : undefined
    }
  });
};

// 获取设备列表
export const getDevices = (params: DeviceListParams = {}) => {
  return apiRequest({
    url: 'node/devices',
    method: 'GET',
    query: {
      page: params.current || 1,
      pageSize: params.pageSize || 10,
      status: params.status,
      search: params.search ? params.search.trim() : undefined,
      onlyMyDevices: params.onlyMyDevices
    }
  });
};

// 获取设备详情
export const getDeviceDetails = async (deviceId: string) => {
  return apiRequest({
    url: `node/devices/detail?id=${deviceId}`,
    method: 'GET',
  });
};



// 获取设备状态历史
export const getDeviceStatusHistory = async (deviceId: string, days: number = 7) => {
  return apiRequest({
    url: `node/devices/status-history?id=${encodeURIComponent(deviceId)}`,
    method: 'GET',
    query: {
      days
    }
  });
};

// 获取设备任务列表
export const getDeviceTasks = async (deviceId: string, params: DeviceListParams = {}) => {
  return apiRequest({
    url: `node/devices/tasks?id=${encodeURIComponent(deviceId)}`,
    method: 'GET',
    query: {
      page: params.current || 1,
      pageSize: params.pageSize || 10,
      status: params.status
    }
  });
};

// 兼容旧接口
export const getAllDevices = async (query: {code: string}) => {
  return apiRequest({ url: 'node/device-history', method: 'GET', query });
}

export const disconnect = async (data: {code: string}) => {
  return apiRequest({ url: 'node/disconnect', method: 'POST', body: data });
}

export const checkConnection = async (code: string) => {
  return apiRequest({ url: 'node/check-connection', method: 'GET', query: { code } });
}

// 检查连接任务状态
export const checkConnectTaskStatus = async (taskId: string) => {
  return apiRequest({
    url: `node/connect-task/${taskId}/status`,
    method: 'GET'
  });
};

export const getModels = async () => {
  return apiRequest({
    url: 'node/models',
    method: 'GET'
  });
};

// 获取设备支持的模型列表
export const getDeviceModels = async (deviceId: string) => {
  return apiRequest({
    url: `node/devices/models?id=${encodeURIComponent(deviceId)}`,
    method: 'GET'
  });
};

// 重连设备 - 为现有设备创建新的连接任务
export const reconnectDevice = async (deviceId: string, deviceName: string, deviceType: string) => {
  return apiRequest({
    url: 'node/devices/reconnect',
    method: 'POST',
    body: {
      device_id: deviceId,
      device_name: deviceName,
      device_type: deviceType
    }
  });
};

// 导出所有API函数
export const nodeApi = {
  createConnectTask,
  getConnectTasks,
  getDevices,
  getDeviceDetails,
  getDeviceStatusHistory,
  getDeviceTasks,
  getAllDevices,
  disconnect,
  checkConnection,
  checkConnectTaskStatus,
  getModels,
  getDeviceModels,
  reconnectDevice
};
