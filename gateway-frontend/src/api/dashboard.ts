import { apiRequest } from "@/utils/api";

export interface DashboardStats {
  earnings: number;
  tasks: number;
  nodes: number;
}

export interface ChartDataPoint {
  date: string;
  value: number;
}

export interface ChatHeatmapDataPoint {
  date: string; // YYYY-MM-DD format
  count: number; // 聊天数量
}

export interface TaskHistoryItem {
  id: string;
  model: string;
  status: string;
  queue: number | string;
  device: string | null;
  ptime: number | string;
  earn: number | string;
  tokens?: number;
  created_at?: string;
  updated_at?: string;
}

export interface NodePerformanceItem {
  id: string;
  type: string;
  tasks: number;
  mem: number;
  rate: number;
  earn: number;
  status?: string;
  last_ping?: string;
  name?: string;
}

export interface QueryParams {
  page?: number;
  pageSize?: number;
  status?: string;
  search?: string;
  timeRange?: string;
}

/**
 * Get dashboard statistics for the current user
 * @param timeRange Optional time range in days (e.g., "7", "30", "90", "365")
 * @param year Optional year filter
 * @param month Optional month filter (1-12)
 */
export const getDashboardStats = async (timeRange?: string, year?: number, month?: number): Promise<{ data: DashboardStats }> => {
  return apiRequest({
    url: 'earnings/stats',
    method: 'GET',
    query: { timeRange, year, month }
  });
};

/**
 * Get user's task history (chat task records)
 * @param params Query parameters including pagination, filters, and time range
 */
export const getUserTaskHistory = async (params?: QueryParams): Promise<{ data: { data: TaskHistoryItem[]; total: number } }> => {
  return apiRequest({
    url: 'earnings/task-queue',
    method: 'GET',
    query: {
      page: params?.page,
      pageSize: params?.pageSize,
      status: params?.status,
      search: params?.search,
      timeRange: params?.timeRange
    }
  });
};

/**
 * Get user's node performance data
 * @param params Query parameters including pagination, filters, and time range
 */
export const getUserNodePerformance = async (params?: QueryParams): Promise<{ data: { data: NodePerformanceItem[]; total: number } }> => {
  return apiRequest({
    url: 'earnings/node-performance',
    method: 'GET',
    query: {
      page: params?.page,
      pageSize: params?.pageSize,
      status: params?.status,
      search: params?.search,
      timeRange: params?.timeRange
    }
  });
};

/**
 * Get user's earnings data for charts
 * @param params Query parameters including pagination and time range
 */
export const getUserEarnings = async (params?: QueryParams): Promise<{ data: { data: any[]; total: number } }> => {
  return apiRequest({
    url: 'earnings',
    method: 'GET',
    query: {
      page: params?.page,
      pageSize: params?.pageSize,
      timeRange: params?.timeRange
    }
  });
};

/**
 * Get user's devices/nodes data
 * @param params Query parameters including pagination, filters
 */
export const getUserDevices = async (params?: QueryParams & { onlyMyDevices?: boolean }): Promise<{ data: { data: any[]; total: number } }> => {
  return apiRequest({
    url: 'node/devices',
    method: 'GET',
    query: {
      page: params?.page,
      pageSize: params?.pageSize,
      status: params?.status,
      search: params?.search,
      onlyMyDevices: 'true' // Always filter to user's devices
    }
  });
};

/**
 * Get revenue chart data for dashboard
 * @param params Date range parameters
 */
export const getRevenueChartData = async (params?: {
  startDate?: string;
  endDate?: string;
  timeRange?: string;
  year?: number;
  month?: number;
}): Promise<{ data: ChartDataPoint[] }> => {
  return apiRequest({
    url: 'earnings/dashboard/revenue-chart',
    method: 'GET',
    query: {
      startDate: params?.startDate,
      endDate: params?.endDate,
      timeRange: params?.timeRange,
      year: params?.year,
      month: params?.month
    }
  });
};

/**
 * Get request count chart data for dashboard
 * @param params Date range parameters
 */
export const getRequestCountChartData = async (params?: {
  startDate?: string;
  endDate?: string;
  timeRange?: string;
  year?: number;
  month?: number;
}): Promise<{ data: ChartDataPoint[] }> => {
  return apiRequest({
    url: 'earnings/dashboard/request-count-chart',
    method: 'GET',
    query: {
      startDate: params?.startDate,
      endDate: params?.endDate,
      timeRange: params?.timeRange,
      year: params?.year,
      month: params?.month
    }
  });
};

/**
 * Get revenue breakdown chart data for dashboard
 * @param params Date range parameters
 */
export const getRevenueBreakdownData = async (params?: {
  startDate?: string;
  endDate?: string;
  timeRange?: string;
}): Promise<{ data: Array<{ date: string; blockRewards: number; jobRewards: number; totalRewards: number }> }> => {
  return apiRequest({
    url: 'earnings/dashboard/revenue-breakdown',
    method: 'GET',
    query: {
      startDate: params?.startDate,
      endDate: params?.endDate,
      timeRange: params?.timeRange
    }
  });
};

/**
 * Get chat heatmap data for dashboard calendar
 * @param params Date range parameters
 */
export const getChatHeatmapData = async (params?: {
  startDate?: string;
  endDate?: string;
  timeRange?: string;
  year?: number;
  month?: number;
}): Promise<{ data: ChatHeatmapDataPoint[] }> => {
  return apiRequest({
    url: 'earnings/dashboard/chat-heatmap',
    method: 'GET',
    query: {
      startDate: params?.startDate,
      endDate: params?.endDate,
      timeRange: params?.timeRange,
      year: params?.year,
      month: params?.month
    }
  });
};

// Export all dashboard API functions
export const dashboardApi = {
  getDashboardStats,
  getUserTaskHistory,
  getUserNodePerformance,
  getUserEarnings,
  getUserDevices,
  getRevenueChartData,
  getRequestCountChartData,
  getRevenueBreakdownData,
  getChatHeatmapData
};
