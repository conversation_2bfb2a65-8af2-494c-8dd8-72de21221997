import { z } from 'zod';
import axios from 'axios';

/**
 * @typedef {Object} ApiRequestOptions
 * @property {string} url - Request URL.
 * @property {string} [method='GET'] - Request method (GET, POST, PUT, DELETE, etc.).
 * @property {any} [body] - Request body.
 */
const ApiRequestOptionsSchema = z.object({
  url: z.string(),
  method: z.string().default('GET'),
  body: z.any().optional(),
  query:  z.any().optional(),
});

/**
 * @typedef {Object} ApiResponse
 * @property {any} data - Response data.
 */
const ApiResponseSchema = z.any();

/**
 * Send API request.
 *
 * This function uses axios to send requests and Zod for parameter validation.
 *
 * @param {ApiRequestOptions} options - Request options.
 * @returns {Promise<ApiResponse>} - Promise containing response data.
 *
 * @example
 * // Send GET request
 * apiRequest({ url: 'https://example.com/api/users' })
 *   .then(response => console.log(response.data))
 *   .catch(error => console.error(error));
 *
 * @example
 * // Send POST request
 * apiRequest({ url: 'https://example.com/api/users', method: 'POST', body: { name: 'John Doe' } })
 *   .then(response => console.log(response.data))
 *   .catch(error => console.error(error));
 */
export const apiRequest = async (options: z.infer<typeof ApiRequestOptionsSchema>) => {
  const validatedOptions = ApiRequestOptionsSchema.parse(options);

  // Get the base URL from environment variables or use a default
  const baseUrl = process.env.NEXT_PUBLIC_GATEWAY_URL;

  // Build request headers
  const headers: Record<string, string> = {
    'Content-Type': 'application/json',
  };

  // Add Authorization header only if accessToken exists
  const accessToken = typeof window !== 'undefined' ? window.localStorage.getItem('accessToken') : null;
  if (accessToken) {
    headers['Authorization'] = `Bearer ${accessToken}`;
  }

  try {
    const response = await axios({
      url: `${baseUrl}/${validatedOptions.url}`,
      method: validatedOptions.method,
      headers,
      data: validatedOptions.body,
      params: options.query
    });
    return ApiResponseSchema.parse(response.data);
  } catch (error: any) {
    if (error.response) {
      // Server returned an error status code
      if(error?.response?.data?.statusCode === 401) {
        localStorage.clear();
        window.location.href = '/login';
      }
      if (error.response.data && error.response.data.message) {
        throw new Error(`API request failed: ${error.response.data.message}`);
      }
    }

    throw new Error(`API request failed: ${error.message}`);
  }
};
