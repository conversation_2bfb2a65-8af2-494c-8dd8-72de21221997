/**
 * Frontend Encryption Utilities for SightAI
 * 
 * This file implements the same encryption logic as FrontEnd-encryptApiKey.ts
 * but for use in the Gateway backend for testing and validation purposes.
 * 
 * Based on the design document:
 * - Uses X25519 for key exchange
 * - Uses ChaCha20-Poly1305 for symmetric encryption
 * - Compatible with frontend encryption implementation
 */

import * as nacl from 'tweetnacl';
import { chacha20poly1305 } from '@noble/ciphers/chacha';

export interface EncryptionResult {
  encryptedKey: string;      // base64(ciphertext) 加密后的Provider API KEY
  nonce: string;             // base64(nonce) 随机数，ChaCha20-Poly1305要求解密用
  tag: string;               // base64(tag) ChaCha20-Poly1305要求解密用
  ephemeralPubKey: string;   // base64(X25519 公钥)
}

export interface DecryptionData {
  encryptedKey: string;      // base64 encoded ciphertext
  nonce: string;             // base64 encoded nonce
  tag: string;               // base64 encoded authentication tag
  ephemeralPubKey: string;   // base64 encoded ephemeral public key
}

/**
 * Encrypt API key using X25519 + ChaCha20-Poly1305
 * This matches the frontend encryption logic in FrontEnd-encryptApiKey.ts
 */
export function encryptApiKey(apiKey: string, executorPublicKey: string): EncryptionResult {
  try {
    // Step 1: Generate ephemeral key pair
    const ephemeralKeyPair = nacl.box.keyPair();
    
    // Step 2: Derive shared secret using X25519 ECDH
    const executorPubKeyBuffer = Buffer.from(executorPublicKey, 'base64');
    if (executorPubKeyBuffer.length !== 32) {
      throw new Error('Invalid executor public key length');
    }
    
    const sharedSecret = nacl.scalarMult(ephemeralKeyPair.secretKey, executorPubKeyBuffer);
    
    // Step 3: Generate random nonce for ChaCha20-Poly1305
    const nonce = nacl.randomBytes(12); // ChaCha20-Poly1305 uses 12-byte nonce
    
    // Step 4: Encrypt using ChaCha20-Poly1305
    const aead = chacha20poly1305(sharedSecret, nonce);
    const plaintext = new TextEncoder().encode(apiKey);
    const encrypted = aead.encrypt(plaintext);
    
    // Split encrypted data into ciphertext and tag
    const ciphertext = encrypted.slice(0, -16); // All but last 16 bytes
    const tag = encrypted.slice(-16); // Last 16 bytes
    
    // Step 5: Return base64 encoded results
    return {
      encryptedKey: Buffer.from(ciphertext).toString('base64'),
      nonce: Buffer.from(nonce).toString('base64'),
      tag: Buffer.from(tag).toString('base64'),
      ephemeralPubKey: Buffer.from(ephemeralKeyPair.publicKey).toString('base64'),
    };
  } catch (error) {
    throw new Error(`Encryption failed: ${error instanceof Error ? error.message : String(error)}`);
  }
}

/**
 * Decrypt API key using X25519 + ChaCha20-Poly1305
 * This is for testing purposes and matches the Executor decryption logic
 */
export function decryptApiKey(
  encryptionData: DecryptionData,
  executorPrivateKey: string,
): string {
  try {
    // Step 1: Decode base64 data
    const ciphertext = Buffer.from(encryptionData.encryptedKey, 'base64');
    const nonce = Buffer.from(encryptionData.nonce, 'base64');
    const tag = Buffer.from(encryptionData.tag, 'base64');
    const ephemeralPubKey = Buffer.from(encryptionData.ephemeralPubKey, 'base64');
    const executorPrivKey = Buffer.from(executorPrivateKey, 'base64');
    
    // Validate lengths
    if (nonce.length !== 12) {
      throw new Error('Invalid nonce length');
    }
    if (tag.length !== 16) {
      throw new Error('Invalid tag length');
    }
    if (ephemeralPubKey.length !== 32) {
      throw new Error('Invalid ephemeral public key length');
    }
    if (executorPrivKey.length !== 32) {
      throw new Error('Invalid executor private key length');
    }
    
    // Step 2: Derive shared secret using X25519 ECDH
    const sharedSecret = nacl.scalarMult(executorPrivKey, ephemeralPubKey);
    
    // Step 3: Decrypt using ChaCha20-Poly1305
    const aead = chacha20poly1305(sharedSecret, nonce);
    const encryptedData = new Uint8Array(ciphertext.length + tag.length);
    encryptedData.set(ciphertext);
    encryptedData.set(tag, ciphertext.length);
    
    const decrypted = aead.decrypt(encryptedData);
    
    // Step 4: Convert back to string
    return new TextDecoder().decode(decrypted);
  } catch (error) {
    throw new Error(`Decryption failed: ${error instanceof Error ? error.message : String(error)}`);
  }
}

/**
 * Generate a new X25519 key pair
 * Useful for testing and key generation
 */
export function generateKeyPair(): {
  publicKey: string;  // base64 encoded
  privateKey: string; // base64 encoded
} {
  const keyPair = nacl.box.keyPair();
  
  return {
    publicKey: Buffer.from(keyPair.publicKey).toString('base64'),
    privateKey: Buffer.from(keyPair.secretKey).toString('base64'),
  };
}

/**
 * Validate base64 encoded key
 */
export function validateKey(key: string, expectedLength: number): boolean {
  try {
    const buffer = Buffer.from(key, 'base64');
    return buffer.length === expectedLength;
  } catch {
    return false;
  }
}

/**
 * Validate encryption data structure
 */
export function validateEncryptionData(data: DecryptionData): {
  valid: boolean;
  errors: string[];
} {
  const errors: string[] = [];
  
  // Check required fields
  if (!data.encryptedKey) errors.push('Missing encryptedKey');
  if (!data.nonce) errors.push('Missing nonce');
  if (!data.tag) errors.push('Missing tag');
  if (!data.ephemeralPubKey) errors.push('Missing ephemeralPubKey');
  
  // Validate base64 encoding and lengths
  if (data.nonce && !validateKey(data.nonce, 12)) {
    errors.push('Invalid nonce: must be 12 bytes base64 encoded');
  }
  if (data.tag && !validateKey(data.tag, 16)) {
    errors.push('Invalid tag: must be 16 bytes base64 encoded');
  }
  if (data.ephemeralPubKey && !validateKey(data.ephemeralPubKey, 32)) {
    errors.push('Invalid ephemeralPubKey: must be 32 bytes base64 encoded');
  }
  
  // Validate encryptedKey is valid base64 (length can vary)
  if (data.encryptedKey) {
    try {
      Buffer.from(data.encryptedKey, 'base64');
    } catch {
      errors.push('Invalid encryptedKey: must be valid base64');
    }
  }
  
  return {
    valid: errors.length === 0,
    errors,
  };
}

/**
 * Test encryption/decryption round trip
 * Useful for validating the implementation
 */
export function testEncryptionRoundTrip(apiKey: string): {
  success: boolean;
  error?: string;
  encryptionResult?: EncryptionResult;
  decryptedKey?: string;
} {
  try {
    // Generate test key pair
    const keyPair = generateKeyPair();
    
    // Encrypt
    const encryptionResult = encryptApiKey(apiKey, keyPair.publicKey);
    
    // Decrypt
    const decryptedKey = decryptApiKey(encryptionResult, keyPair.privateKey);
    
    // Verify
    if (decryptedKey !== apiKey) {
      return {
        success: false,
        error: 'Decrypted key does not match original',
      };
    }
    
    return {
      success: true,
      encryptionResult,
      decryptedKey,
    };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : String(error),
    };
  }
}

/**
 * Create encryption data compatible with frontend format
 * This ensures compatibility with FrontEnd-encryptApiKey.ts
 */
export function createCompatibleEncryptionData(
  apiKey: string,
  executorPublicKey: string,
): {
  provider: string;
  region: string;
  keyId: string;
  encryptedKey: string;
  nonce: string;
  tag: string;
  ephemeralPubKey: string;
} {
  const encryptionResult = encryptApiKey(apiKey, executorPublicKey);
  
  return {
    provider: 'openai', // Default, should be provided by caller
    region: 'asia',     // Default, should be provided by caller
    keyId: 'test-key',  // Default, should be provided by caller
    ...encryptionResult,
  };
}

/**
 * Validate encrypted result (alias for validateEncryptionData for backward compatibility)
 */
export function validateEncryptedResult(result: EncryptionResult): boolean {
  const validation = validateEncryptionData({
    encryptedKey: result.encryptedKey,
    nonce: result.nonce,
    tag: result.tag,
    ephemeralPubKey: result.ephemeralPubKey
  });
  return validation.valid;
}

/**
 * Get encryption summary for display purposes
 */
export function getEncryptionSummary(result: EncryptionResult) {
  return {
    encryptedKeyLength: result.encryptedKey.length,
    nonceLength: result.nonce.length,
    tagLength: result.tag.length,
    ephemeralPubKeyLength: result.ephemeralPubKey.length,
    ephemeralPubKeyPreview: `${result.ephemeralPubKey.slice(0, 8)}...${result.ephemeralPubKey.slice(-8)}`,
  };
}