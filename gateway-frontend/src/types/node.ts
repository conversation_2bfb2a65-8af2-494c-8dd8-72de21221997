// 设备类型
export type DeviceType = 'gpu' | 'cpu' | 'mac' | 'cloud';

// 设备状态 - 与后端保持一致
export type DeviceStatus = 'waiting' | 'in-progress' | 'connected' | 'disconnected' | 'failed';

// 任务状态 - 与后端保持一致
export type TaskStatus = 'pending' | 'running' | 'completed' | 'failed' | 'cancelled';

// 设备信息
export interface Device {
  id?: string;
  node_id?: string; // 添加 node_id 字段，与后端返回的数据匹配
  name?: string;
  task_name?: string; // 添加 task_name 字段，与后端返回的数据匹配
  user_id?: string;
  owner_address?: string;
  reward_address?: string;
  status: DeviceStatus;
  device_type?: string;
  gpu_type?: string; // 添加 gpu_type 字段，与后端返回的数据匹配
  cpu_model?: string;
  cpu_cores?: number;
  cpu_threads?: number;
  cpu_usage_percent?: number;
  ram_total?: number;
  ram_available?: number;
  ram_usage_percent?: number; // 添加内存使用率字段
  gpu_model?: string;
  gpu_count?: number;
  gpu_memory?: number;
  gpu_usage_percent?: number; // 添加GPU使用率字段
  gpu_temperature?: number;
  disk_total?: number;
  disk_available?: number;
  ip_address?: string;
  last_ping?: string | number; // 可能是字符串或时间戳
  latency?: number;
  uptime_seconds?: number;
  last_boot?: string;
  os_info?: string;
  created_at: string;
  updated_at: string;
  last_error?: string;
  firmware_version?: string;
  software_version?: string;
  last_maintenance_at?: string;
  next_maintenance_at?: string;
  health_score?: number;
  tags?: string[];
  current_model?: string;
  total_earnings?: number;
  pending_earnings?: number;
  network_in_kbps?: number; // 添加网络入站流量字段
  network_out_kbps?: number; // 添加网络出站流量字段
}

// 连接任务
export interface ConnectTask {
  id: string;
  task_name: string;
  user_id: string;
  owner_address: string;
  reward_address?: string;
  signature: string;
  node_id: string;
  one_time_code: string;
  gateway_address?: string;
  device_type?: string;
  gpu_type?: string;
  created_at: string;
  updated_at: string;
  status: DeviceStatus;
}

// 创建连接任务请求
export interface CreateConnectTaskRequest {
  task_name: string;
  signature: string;
  device_type?: string;
  gpu_type?: string;
}

// 创建连接任务响应
export interface CreateConnectTaskResponse {
  task_id: string;
  one_time_code: string;
  gateway_address: string;
}

// 设备心跳请求
export interface DeviceHeartbeatRequest {
  node_id: string;
  status: DeviceStatus;
  cpu_usage_percent?: number;
  ram_usage_percent?: number;
  gpu_usage_percent?: number;
  gpu_temperature?: number;
  network_in_kbps?: number;
  network_out_kbps?: number;
  uptime_seconds?: number;
  model?: string;
}

// 设备指标
export interface DeviceMetrics {
  id: string;
  device_id: string;
  timestamp: string;
  cpu_usage_percent?: number;
  ram_usage_percent?: number;
  gpu_usage_percent?: number;
  gpu_temperature?: number;
  network_in_kbps?: number;
  network_out_kbps?: number;
  date: string;
}

// 设备列表请求
export interface DeviceListRequest {
  page: number;
  pageSize: number;
  status?: string;
  search?: string;
  onlyMyDevices?: boolean;
}

// 设备列表响应
export interface DeviceListResponse {
  data: Device[];
  total: number;
  page: number;
  pageSize: number;
}

// 设备连接请求
export interface DeviceConnectionRequest {
  one_time_code: string;
  device_info: {
    cpu_model?: string;
    cpu_cores?: number;
    cpu_threads?: number;
    ram_total?: number;
    gpu_model?: string;
    gpu_count?: number;
    gpu_memory?: number;
    disk_total?: number;
    os_info?: string;
  };
}

// 设备连接响应
export interface DeviceConnectionResponse {
  node_id: string;
  status: DeviceStatus;
}

// 设备状态变更
export interface DeviceStatusChange {
  id: string;
  device_id: string;
  from_status: DeviceStatus;
  to_status: DeviceStatus;
  change_time: string;
  date: string;
  created_at: string;
}

// 任务
export interface Task {
  id: string;
  device_id: string;
  model: string;
  created_at: string;
  status: TaskStatus;
  total_duration?: number;
  load_duration?: number;
  prompt_eval_count?: number;
  prompt_eval_duration?: number;
  eval_count?: number;
  eval_duration?: number;
  updated_at: string;
}
