'use client';

import { useState } from 'react';
import { Button, Typography, Input, Select } from 'antd';
import { PlusOutlined, SearchOutlined } from '@ant-design/icons';
import { useLocaleContext } from '@/contexts/LocaleContext';
import ModelTable from '@/components/models/ModelTable';
import styles from './page.module.css';

const { Text } = Typography;
const { Option } = Select;

export default function ModelsPage() {
  const { messages } = useLocaleContext();
  const [searchText, setSearchText] = useState('');
  const [filterType, setFilterType] = useState('all');

  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <div>
          <Text type="secondary">
            {messages.models?.description}
          </Text>
        </div>
      </div>

      <div className={styles.filterSection}>
        <Input
          placeholder={messages.models?.search}
          prefix={<SearchOutlined />}
          value={searchText}
          onChange={(e) => setSearchText(e.target.value)}
          className={styles.searchInput}
        />
        {/* <Select
          defaultValue="all"
          className={styles.filterSelect}
          onChange={(value) => setFilterType(value)}
        >
          <Option value="all">{messages.models?.filters?.all}</Option>
          <Option value="local">{messages.models?.filters?.local}</Option>
          <Option value="remote">{messages.models?.filters?.remote}</Option>
        </Select> */}
      </div>

      <div className={styles.section}>
        <ModelTable searchText={searchText} filterType={filterType} />
      </div>
    </div>
  );
}
