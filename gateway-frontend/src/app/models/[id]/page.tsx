'use client';

import { useParams } from 'next/navigation';
import { Typography, Spin, Result, Tabs, Card, Tag, Descriptions, Button, Space, Divider } from 'antd';
import { DownloadOutlined, CodeOutlined, ApiOutlined, HistoryOutlined } from '@ant-design/icons';
import { useLocaleContext } from '@/contexts/LocaleContext';
import styles from './page.module.css';
import { useEffect, useState } from 'react';
import ModelUsageChart from './ModelUsageChart';
import ModelCodeExample from './ModelCodeExample';

const { Title, Text, Paragraph } = Typography;
const { TabPane } = Tabs;

interface ModelDetail {
  id: string;
  name: string;
  provider: string;
  type: string;
  size: string;
  status: 'available' | 'downloading' | 'offline';
  tags: string[];
  description: string;
  parameters: string;
  context: string;
  license: string;
  lastUpdated: string;
  capabilities: string[];
}

export default function ModelDetailPage() {
  const { id } = useParams();
  const { messages } = useLocaleContext();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [modelDetail, setModelDetail] = useState<ModelDetail | null>(null);

  useEffect(() => {
    const fetchModelDetail = async () => {
      try {
        setLoading(true);

        // TODO: Replace with actual API call
        // Example:
        // const response = await getModelDetail(id as string);
        // setModelDetail(response.data);

        setError(null);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to load model details');
      } finally {
        setLoading(false);
      }
    };

    fetchModelDetail();
  }, [id]);

  if (loading) {
    return (
      <div className={styles.loadingContainer}>
        <Spin size="large" />
      </div>
    );
  }

  if (error || !modelDetail) {
    return (
      <Result
        status="error"
        title="Failed to load model details"
        subTitle={error}
      />
    );
  }

  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <div>
          <Title level={3}>{modelDetail.name}</Title>
          <Space size={8} wrap>
            <Tag color={modelDetail.provider === 'Ollama' ? 'green' : 'blue'}>
              {modelDetail.provider}
            </Tag>
            <Tag color={modelDetail.type === 'local' ? 'green' : 'blue'}>
              {modelDetail.type === 'local' ?
                messages.models?.types?.local :
                messages.models?.types?.remote}
            </Tag>
            {modelDetail.tags.map(tag => (
              <Tag key={tag} color="purple">{tag}</Tag>
            ))}
          </Space>
        </div>
        {modelDetail.type === 'remote' && (
          <Button
            type="primary"
            icon={<DownloadOutlined />}
          >
            {messages.models?.actions?.download}
          </Button>
        )}
      </div>

      <Paragraph className={styles.description}>
        {modelDetail.description}
      </Paragraph>

      <Card className={styles.infoCard}>
        <Descriptions column={{ xs: 1, sm: 2, md: 3, lg: 4 }} bordered>
          <Descriptions.Item label={messages.models?.detail?.parameters}>
            {modelDetail.parameters}
          </Descriptions.Item>
          <Descriptions.Item label={messages.models?.detail?.context}>
            {modelDetail.context}
          </Descriptions.Item>
          <Descriptions.Item label={messages.models?.detail?.size}>
            {modelDetail.size}
          </Descriptions.Item>
          <Descriptions.Item label={messages.models?.detail?.license}>
            {modelDetail.license}
          </Descriptions.Item>
          <Descriptions.Item label={messages.models?.detail?.updated}>
            {modelDetail.lastUpdated}
          </Descriptions.Item>
          <Descriptions.Item label={messages.models?.detail?.status}>
            <Tag color="green">
              {messages.models?.status?.available}
            </Tag>
          </Descriptions.Item>
        </Descriptions>
      </Card>

      <Divider />

      <Tabs defaultActiveKey="usage" className={styles.tabs}>
        <TabPane
          tab={
            <span>
              <HistoryOutlined />
              {messages.models?.tabs?.usage}
            </span>
          }
          key="usage"
        >
          <ModelUsageChart />
        </TabPane>
        <TabPane
          tab={
            <span>
              <CodeOutlined />
              {messages.models?.tabs?.examples}
            </span>
          }
          key="examples"
        >
          <ModelCodeExample modelName={modelDetail.name} provider={modelDetail.provider} />
        </TabPane>
        <TabPane
          tab={
            <span>
              <ApiOutlined />
              {messages.models?.tabs?.api}
            </span>
          }
          key="api"
        >
          <div className={styles.apiSection}>
            <Title level={4}>{messages.models?.api?.title}</Title>
            <Paragraph>
              {messages.models?.api?.description}
            </Paragraph>

            <Card className={styles.endpointCard}>
              <Text strong>POST /v1/completions</Text>
              <Paragraph type="secondary" className={styles.endpointDescription}>
                {messages.models?.api?.completions}
              </Paragraph>
            </Card>

            <Card className={styles.endpointCard}>
              <Text strong>POST /v1/chat/completions</Text>
              <Paragraph type="secondary" className={styles.endpointDescription}>
                {messages.models?.api?.chat}
              </Paragraph>
            </Card>

            <Card className={styles.endpointCard}>
              <Text strong>POST /v1/embeddings</Text>
              <Paragraph type="secondary" className={styles.endpointDescription}>
                {messages.models?.api?.embeddings}
              </Paragraph>
            </Card>
          </div>
        </TabPane>
      </Tabs>
    </div>
  );
}
