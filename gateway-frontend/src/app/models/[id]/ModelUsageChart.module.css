.container {
  margin-top: var(--spacing-base);
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-base);
}

.title {
  margin: 0;
  font-size: var(--font-size-lg);
}

.chartCard {
  margin-bottom: var(--spacing-xl);
  height: 300px;
}

.loadingContainer {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 250px;
}

.chartContainer {
  height: 250px;
  display: flex;
  flex-direction: column;
}

.chart {
  flex: 1;
  display: flex;
  align-items: flex-end;
  padding-bottom: 20px;
}

.bar {
  background: linear-gradient(to top, var(--color-info), #69c0ff);
  margin: 0 1px;
  border-radius: 3px 3px 0 0;
  transition: height var(--transition-base);
}

.xAxis {
  height: 20px;
  display: flex;
  justify-content: space-between;
  color: var(--color-text-tertiary);
  font-size: var(--font-size-xs);
}

.statsRow {
  display: flex;
  gap: var(--spacing-base);
}

.statCard {
  flex: 1;
  text-align: center;
}

.statTitle {
  color: var(--color-text-tertiary);
  font-size: var(--font-size-base);
  margin-bottom: var(--spacing-sm);
}

.statValue {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-semibold);
  margin-bottom: var(--spacing-sm);
}

.statChange {
  color: var(--color-success);
  font-size: var(--font-size-base);
}
