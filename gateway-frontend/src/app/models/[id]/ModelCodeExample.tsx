'use client';

import { Ta<PERSON>, <PERSON><PERSON>, message } from 'antd';
import { CopyOutlined } from '@ant-design/icons';
import { useLocaleContext } from '@/contexts/LocaleContext';
import styles from './ModelCodeExample.module.css';

const { TabPane } = Tabs;

interface ModelCodeExampleProps {
  modelName: string;
  provider: string;
}

export default function ModelCodeExample({ modelName, provider }: ModelCodeExampleProps) {
  const { messages } = useLocaleContext();

  const handleCopy = async (code: string) => {
    try {
      await navigator.clipboard.writeText(code);
      message.success(messages.models?.examples?.copied);
    } catch (err) {
      message.error(messages.models?.examples?.copyFailed);
    }
  };

  // Generate different code examples based on provider and model name
  const getCodeExamples = () => {
    if (provider === 'Ollama') {
      return {
        curl: `curl -X POST http://localhost:11434/api/generate \\
  -d '{
    "model": "${modelName}",
    "prompt": "Why is the sky blue?",
    "stream": false
  }'`,
        python: `import requests

response = requests.post(
    'http://localhost:11434/api/generate',
    json={
        'model': '${modelName}',
        'prompt': 'Why is the sky blue?',
        'stream': False
    }
)

print(response.json()['response'])`,
        javascript: `const response = await fetch('http://localhost:11434/api/generate', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
  },
  body: JSON.stringify({
    model: '${modelName}',
    prompt: 'Why is the sky blue?',
    stream: false
  })
});

const data = await response.json();
console.log(data.response);`
      };
    } else if (provider === 'OpenAI') {
      return {
        curl: `curl https://api.openai.com/v1/chat/completions \\
  -H "Content-Type: application/json" \\
  -H "Authorization: Bearer $OPENAI_API_KEY" \\
  -d '{
    "model": "${modelName}",
    "messages": [
      {"role": "system", "content": "You are a helpful assistant."},
      {"role": "user", "content": "Why is the sky blue?"}
    ]
  }'`,
        python: `from openai import OpenAI

client = OpenAI()

response = client.chat.completions.create(
    model="${modelName}",
    messages=[
        {"role": "system", "content": "You are a helpful assistant."},
        {"role": "user", "content": "Why is the sky blue?"}
    ]
)

print(response.choices[0].message.content)`,
        javascript: `import OpenAI from 'openai';

const openai = new OpenAI();

const response = await openai.chat.completions.create({
  model: '${modelName}',
  messages: [
    { role: 'system', content: 'You are a helpful assistant.' },
    { role: 'user', content: 'Why is the sky blue?' }
  ]
});

console.log(response.choices[0].message.content);`
      };
    } else {
      // Default examples
      return {
        curl: `curl -X POST https://api.example.com/v1/completions \\
  -H "Content-Type: application/json" \\
  -H "Authorization: Bearer $API_KEY" \\
  -d '{
    "model": "${modelName}",
    "prompt": "Why is the sky blue?"
  }'`,
        python: `import requests

response = requests.post(
    'https://api.example.com/v1/completions',
    headers={
        'Authorization': 'Bearer ' + API_KEY,
        'Content-Type': 'application/json'
    },
    json={
        'model': '${modelName}',
        'prompt': 'Why is the sky blue?'
    }
)

print(response.json()['choices'][0]['text'])`,
        javascript: `const response = await fetch('https://api.example.com/v1/completions', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': 'Bearer ' + API_KEY
  },
  body: JSON.stringify({
    model: '${modelName}',
    prompt: 'Why is the sky blue?'
  })
});

const data = await response.json();
console.log(data.choices[0].text);`
      };
    }
  };

  const codeExamples = getCodeExamples();

  return (
    <div className={styles.container}>
      <Tabs defaultActiveKey="curl">
        <TabPane tab="cURL" key="curl">
          <div className={styles.codeBlock}>
            <pre>{codeExamples.curl}</pre>
            <Button
              type="text"
              icon={<CopyOutlined />}
              className={styles.copyButton}
              onClick={() => handleCopy(codeExamples.curl)}
            />
          </div>
        </TabPane>
        <TabPane tab="Python" key="python">
          <div className={styles.codeBlock}>
            <pre>{codeExamples.python}</pre>
            <Button
              type="text"
              icon={<CopyOutlined />}
              className={styles.copyButton}
              onClick={() => handleCopy(codeExamples.python)}
            />
          </div>
        </TabPane>
        <TabPane tab="JavaScript" key="javascript">
          <div className={styles.codeBlock}>
            <pre>{codeExamples.javascript}</pre>
            <Button
              type="text"
              icon={<CopyOutlined />}
              className={styles.copyButton}
              onClick={() => handleCopy(codeExamples.javascript)}
            />
          </div>
        </TabPane>
      </Tabs>
    </div>
  );
}
