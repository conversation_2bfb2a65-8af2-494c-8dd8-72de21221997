.container {
  padding: var(--spacing-xl);
}

.loadingContainer {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400px;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--spacing-base);
}

.description {
  font-size: var(--font-size-md);
  margin-bottom: var(--spacing-xl);
}

.infoCard {
  margin-bottom: var(--spacing-2xl);
}

.tabs {
  margin-top: var(--spacing-base);
}

.apiSection {
  padding: var(--spacing-base) 0;
}

.endpointCard {
  margin-bottom: var(--spacing-base);
  border-left: 4px solid var(--color-info);
}

.endpointDescription {
  margin-top: var(--spacing-sm);
  margin-bottom: 0;
}
