'use client';

import { Card, Radio, Spin } from 'antd';
import { useState } from 'react';
import { useLocaleContext } from '@/contexts/LocaleContext';
import styles from './ModelUsageChart.module.css';

// Note: In a real project, you should use a chart library like Recharts, Chart.js or Ant Design Charts
// This is a simplified chart implementation

export default function ModelUsageChart() {
  const { messages } = useLocaleContext();
  const [timeRange, setTimeRange] = useState('7d');
  const [loading, setLoading] = useState(false);

  // TODO: Replace with actual API data
  const [chartData, setChartData] = useState({
    '7d': Array(7).fill(0),
    '30d': Array(30).fill(0),
    '90d': Array(90).fill(0)
  });

  const handleRangeChange = (e: any) => {
    setTimeRange(e.target.value);
    setLoading(true);
    // TODO: Fetch data based on time range
    setTimeout(() => setLoading(false), 500);
  };

  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <h3 className={styles.title}>{messages.models?.usage?.title || "Model Usage"}</h3>
        <Radio.Group value={timeRange} onChange={handleRangeChange}>
          <Radio.Button value="7d">7 {messages.models?.usage?.days || "Days"}</Radio.Button>
          <Radio.Button value="30d">30 {messages.models?.usage?.days || "Days"}</Radio.Button>
          <Radio.Button value="90d">90 {messages.models?.usage?.days || "Days"}</Radio.Button>
        </Radio.Group>
      </div>

      <Card className={styles.chartCard}>
        {loading ? (
          <div className={styles.loadingContainer}>
            <Spin />
          </div>
        ) : (
          <div className={styles.chartContainer}>
            {/* This is a simplified chart visualization */}
            <div className={styles.chart}>
              {chartData[timeRange as keyof typeof chartData].map((value, index) => (
                <div
                  key={index}
                  className={styles.bar}
                  style={{ height: `${value}%`, width: `${100 / chartData[timeRange as keyof typeof chartData].length}%` }}
                />
              ))}
            </div>
            <div className={styles.xAxis}>
              {timeRange === '7d' && (
                <>
                  <span>Mon</span>
                  <span>Tue</span>
                  <span>Wed</span>
                  <span>Thu</span>
                  <span>Fri</span>
                  <span>Sat</span>
                  <span>Sun</span>
                </>
              )}
              {timeRange === '30d' && (
                <>
                  <span>Week 1</span>
                  <span>Week 2</span>
                  <span>Week 3</span>
                  <span>Week 4</span>
                </>
              )}
              {timeRange === '90d' && (
                <>
                  <span>Month 1</span>
                  <span>Month 2</span>
                  <span>Month 3</span>
                </>
              )}
            </div>
          </div>
        )}
      </Card>

      <div className={styles.statsRow}>
        <Card className={styles.statCard}>
          <div className={styles.statTitle}>{messages.models?.usage?.requests || "Total Requests"}</div>
          <div className={styles.statValue}>12,450</div>
          <div className={styles.statChange}>+15% {messages.models?.usage?.fromPrevious || "from previous period"}</div>
        </Card>
        <Card className={styles.statCard}>
          <div className={styles.statTitle}>{messages.models?.usage?.tokens || "Total Tokens"}</div>
          <div className={styles.statValue}>1.2M</div>
          <div className={styles.statChange}>+8% {messages.models?.usage?.fromPrevious || "from previous period"}</div>
        </Card>
        <Card className={styles.statCard}>
          <div className={styles.statTitle}>{messages.models?.usage?.avgLatency || "Average Latency"}</div>
          <div className={styles.statValue}>245ms</div>
          <div className={styles.statChange}>-5% {messages.models?.usage?.fromPrevious || "from previous period"}</div>
        </Card>
      </div>
    </div>
  );
}
