'use client';

import type { <PERSON><PERSON><PERSON> } from "next";
import { Inter } from "next/font/google";
import { <PERSON><PERSON><PERSON> } from "next/font/google";
import { <PERSON>_Ace } from "next/font/google";
import "./globals.css";
import ClientLayout from "@/components/ClientLayout";
import { LocaleProvider } from "@/contexts/LocaleContext";
import { RefreshProvider } from "@/contexts/RefreshContext";
// 使用 Zustand 替代 AuthContext
import Providers from "@/components/Providers";
import { useRouter, usePathname } from 'next/navigation';
import { WagmiProvider } from 'wagmi';
import { QueryClientProvider } from '@tanstack/react-query';
import { config, queryClient } from '@/config/web3';
import Web3Provider from '@/components/Web3Provider';
import { ConfigProvider, theme } from 'antd';

const inter = Inter({
  subsets: ["latin"],
  variable: "--font-inter",
});

const aldrich = Aldrich({
  weight: "400",
  subsets: ["latin"],
  variable: "--font-aldrich",
});

const brunoAce = Bruno_Ace({
  weight: "400",
  subsets: ["latin"],
  variable: "--font-bruno-ace",
});

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const pathname = usePathname();
  return (
    <html lang="en">
      <head>
        <link
          rel="stylesheet"
          href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css"
          integrity="sha512-1ycn6IcaQQ40/MKBW2W4Rhis/DbILU74C1vSrLJxCq57o941Ym01SwNsOMqvEBFlcgUa6xLiPY/NS5R+E6ztJQ=="
          crossOrigin="anonymous"
          referrerPolicy="no-referrer"
        />
        {/* Load environment variables for browser */}
        <script src="/env.js" />
      </head>
      <body className={`${inter.variable} ${aldrich.variable} ${brunoAce.variable} antialiased`}>
        <ConfigProvider
          theme={{
            token: {
              "colorSuccess": "#4cff24",
              "colorWarning": "#f5b94e",
              "colorError": "#df3587",
              "colorLink": "#000000",
              "colorTextBase": "#000000",
              "colorPrimaryText": "#000000",
              "colorPrimaryTextActive": "#000000",
              "colorPrimary": "#6d20f5",
              "colorInfo": "#6d20f5",
              "borderRadius": 16,
              "wireframe": false
            },
            components: {
              Button: {
              },
            },
          }}
        >
          <LocaleProvider>
            <RefreshProvider>
              <QueryClientProvider client={queryClient}>
                <WagmiProvider config={config}>
                  <Web3Provider>
                    <Providers>
                      {pathname === '/login' ? children : <ClientLayout>{children}</ClientLayout>}
                    </Providers>
                  </Web3Provider>
                </WagmiProvider>
              </QueryClientProvider>
            </RefreshProvider>
          </LocaleProvider>
        </ConfigProvider>
      </body>
    </html>
  );
}
