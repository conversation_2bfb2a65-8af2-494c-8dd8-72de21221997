.container {
  padding: 24px;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
}

.header > div {
  max-width: 800px;
}

.titleRow {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.backButton {
  padding: 4px 8px;
  height: auto;
}

.backButton:hover {
  background: rgba(0, 0, 0, 0.06);
}

.createButton {
  background: #1890ff;
  border-color: #1890ff;
}

.createButton:hover {
  background: #40a9ff !important;
  border-color: #40a9ff !important;
}

.section {
  margin-bottom: 32px;
}

.filterSection {
  display: flex;
  gap: 16px;
  margin-bottom: 24px;
}

.searchInput {
  max-width: 320px;
}

.filterSelect {
  min-width: 160px;
}