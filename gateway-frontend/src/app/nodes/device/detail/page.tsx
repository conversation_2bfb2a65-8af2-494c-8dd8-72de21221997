'use client';

import { useEffect, useState } from 'react';
import { Button, Breadcrumb } from 'antd';
import { ArrowLeftOutlined } from '@ant-design/icons';
import { useRouter } from 'next/navigation';
import DeviceDetail from '@/components/nodes/DeviceDetail';
import styles from './page.module.css';
import { useLocaleContext } from '@/contexts/LocaleContext';

export default function DeviceDetailPage() {
  const router = useRouter();
  const [deviceId, setDeviceId] = useState<string>('');
  const { messages } = useLocaleContext();

  useEffect(() => {
      const queryString = window.location.search;
      const urlParams = new URLSearchParams(queryString);
      const id: string = urlParams.get('id') as string;
      setDeviceId(id);
  }, []);

  return (
    <div className={styles.container}>
      <div className={styles.header}>
        {/* <Breadcrumb
          items={[
            { title: messages.nodes?.deviceDetail?.breadcrumb?.home || '首页', href: '/' },
            { title: messages.nodes?.deviceDetail?.breadcrumb?.deviceManagement || '设备管理', href: '/nodes' },
            { title: messages.nodes?.deviceDetail?.breadcrumb?.deviceDetail || '设备详情' },
          ]}
        /> */}
        <Button
          icon={<ArrowLeftOutlined />}
          onClick={() => router.replace('/management/nodes')}
          className={styles.backButton}
        >
          {messages.nodes?.deviceDetail?.backToList || '返回设备列表'}
        </Button>
      </div>

      <div className={styles.content}>
        <DeviceDetail deviceId={deviceId} />
      </div>
    </div>
  );
}
