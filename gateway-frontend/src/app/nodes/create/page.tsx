'use client';

import React, {useEffect, useState} from 'react';
import { useRouter } from 'next/navigation';
import CreateDeviceWizard from '@/components/nodes/CreateDeviceWizard';
import { Breadcrumb } from 'antd';
import { HomeOutlined, DesktopOutlined } from '@ant-design/icons';
import styles from './page.module.css';

const CreateDevicePage: React.FC = () => {
  const router = useRouter();
  const handleCancel = () => {
    router.replace('/management/nodes');
  };
  const [deviceId, setDeviceId] = useState<string>('');
  const [mode, setMode] = useState<string>('create');
  useEffect(() => {
    const queryString = window.location.search;
    const urlParams = new URLSearchParams(queryString);
    const id: string = urlParams.get('deviceId') as string;
    const mode: string = urlParams.get('mode') as string;
    setDeviceId(id);
    setMode(mode)
  }, []);
  const handleSuccess = () => {
    router.replace('/management/nodes');
  };

  return (
    <div className={styles.container}>
      <div className={styles.content}>
        <CreateDeviceWizard
          onCancel={handleCancel}
          deviceId={deviceId}
          mode={mode as 'create' | 'resume'}
          onSuccess={handleSuccess}
        />
      </div>
    </div>
  );
};

export default CreateDevicePage;
