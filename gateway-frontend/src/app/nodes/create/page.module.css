.container {
  padding: 24px;
}

.breadcrumb {
  margin-bottom: 24px;
}

.breadcrumb .ant-breadcrumb {
  background: #fff;
  padding: 16px 24px;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.content {
  display: flex;
  justify-content: center;
  align-items: flex-start;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .container {
    padding: 16px;
  }
  
  .breadcrumb .ant-breadcrumb {
    padding: 12px 16px;
  }
  
  .content {
    min-height: calc(100vh - 100px);
  }
}
