'use client';

import { useState, useEffect } from 'react';
import { Table, Card, Tag, Button, Input, message } from 'antd';
import {
  SearchOutlined,
  ReloadOutlined
} from '@ant-design/icons';
import { getUserTaskHistory } from '@/api/dashboard';
import styles from './page.module.css';
import { ColumnProps } from 'antd/es/table';

interface RequestRecord {
  id: string;
  status: string;
  model: string;
  tokens?: number;
  earn: number | string;
  created_at?: string;
  updated_at?: string;
}

export default function HistoryPage() {
  const [loading, setLoading] = useState(false);
  const [requestData, setRequestData] = useState<RequestRecord[]>([]);
  const [total, setTotal] = useState(0);
  const [current, setCurrent] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [searchText, setSearchText] = useState('');

  const loadHistory = async () => {
    setLoading(true);
    try {
      const response = await getUserTaskHistory({
        page: current,
        pageSize: pageSize,
        search: searchText || undefined
      });

      setRequestData(response.data.data || []);
      setTotal(response.data.total || 0);
    } catch (error) {
      console.error('Error loading task history:', error);
      message.error('Failed to load task history');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadHistory();
  }, [current, pageSize]);

  const handleSearch = () => {
    setCurrent(1);
    loadHistory();
  };

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'completed':
      case 'done':
        return 'green';
      case 'running':
      case 'in-progress':
        return 'purple';
      case 'pending':
        return 'orange';
      case 'failed':
        return 'red';
      default:
        return 'default';
    }
  };

  const columns: ColumnProps<RequestRecord>[] = [
    {
      title: 'Task ID',
      dataIndex: 'id',
      key: 'id',
      align: 'center',
      width: 120,
      render: (text: string) => (
        <span className={styles.requestId}>{text.substring(0, 8)}...</span>
      ),
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      width: 120,
      align: 'center',
      render: (status: string) => (
        // <Tag
        //   color={getStatusColor(status)}
        //   className={styles.statusTag}
        // >
        //   {status}
        // </Tag>
        <div className={styles.status}>
          {status.toLowerCase() === 'completed' && (
            <svg width="167" height="34" viewBox="0 0 167 34" fill="none" xmlns="http://www.w3.org/2000/svg">
              <rect width="167" height="34" rx="17" fill="#D0D0D0" fill-opacity="0.25" />
              <g filter="url(#filter1_f_179697_5885)">
                <circle cx="59" cy="17" r="5.5" fill="url(#paint0_radial_179697_5885)" />
              </g>
              <path d="M79.1719 22H76.7969L76.8125 20.7734H79.1719C79.9844 20.7734 80.6615 20.6042 81.2031 20.2656C81.7448 19.9219 82.151 19.4427 82.4219 18.8281C82.6979 18.2083 82.8359 17.4844 82.8359 16.6562V15.9609C82.8359 15.3099 82.7578 14.7318 82.6016 14.2266C82.4453 13.7161 82.2161 13.2865 81.9141 12.9375C81.612 12.5833 81.2422 12.3151 80.8047 12.1328C80.3724 11.9505 79.875 11.8594 79.3125 11.8594H76.75V10.625H79.3125C80.0573 10.625 80.737 10.75 81.3516 11C81.9661 11.2448 82.4948 11.6016 82.9375 12.0703C83.3854 12.5339 83.7292 13.0964 83.9688 13.7578C84.2083 14.4141 84.3281 15.1536 84.3281 15.9766V16.6562C84.3281 17.4792 84.2083 18.2214 83.9688 18.8828C83.7292 19.5391 83.3828 20.099 82.9297 20.5625C82.4818 21.026 81.9401 21.3828 81.3047 21.6328C80.6745 21.8776 79.9635 22 79.1719 22ZM77.6016 10.625V22H76.0938V10.625H77.6016ZM86.4922 17.8672V17.6875C86.4922 17.0781 86.5807 16.513 86.7578 15.9922C86.9349 15.4661 87.1901 15.0104 87.5234 14.625C87.8568 14.2344 88.2604 13.9323 88.7344 13.7188C89.2083 13.5 89.7396 13.3906 90.3281 13.3906C90.9219 13.3906 91.4557 13.5 91.9297 13.7188C92.4089 13.9323 92.8151 14.2344 93.1484 14.625C93.487 15.0104 93.7448 15.4661 93.9219 15.9922C94.099 16.513 94.1875 17.0781 94.1875 17.6875V17.8672C94.1875 18.4766 94.099 19.0417 93.9219 19.5625C93.7448 20.0833 93.487 20.5391 93.1484 20.9297C92.8151 21.3151 92.4115 21.6172 91.9375 21.8359C91.4688 22.0495 90.9375 22.1562 90.3438 22.1562C89.75 22.1562 89.2161 22.0495 88.7422 21.8359C88.2682 21.6172 87.862 21.3151 87.5234 20.9297C87.1901 20.5391 86.9349 20.0833 86.7578 19.5625C86.5807 19.0417 86.4922 18.4766 86.4922 17.8672ZM87.9375 17.6875V17.8672C87.9375 18.2891 87.987 18.6875 88.0859 19.0625C88.1849 19.4323 88.3333 19.7604 88.5312 20.0469C88.7344 20.3333 88.987 20.5599 89.2891 20.7266C89.5911 20.888 89.9427 20.9688 90.3438 20.9688C90.7396 20.9688 91.0859 20.888 91.3828 20.7266C91.6849 20.5599 91.9349 20.3333 92.1328 20.0469C92.3307 19.7604 92.4792 19.4323 92.5781 19.0625C92.6823 18.6875 92.7344 18.2891 92.7344 17.8672V17.6875C92.7344 17.2708 92.6823 16.8776 92.5781 16.5078C92.4792 16.1328 92.3281 15.8021 92.125 15.5156C91.9271 15.224 91.6771 14.9948 91.375 14.8281C91.0781 14.6615 90.7292 14.5781 90.3281 14.5781C89.9323 14.5781 89.5833 14.6615 89.2812 14.8281C88.9844 14.9948 88.7344 15.224 88.5312 15.5156C88.3333 15.8021 88.1849 16.1328 88.0859 16.5078C87.987 16.8776 87.9375 17.2708 87.9375 17.6875ZM97.9453 15.3516V22H96.5V13.5469H97.8672L97.9453 15.3516ZM97.6016 17.4531L97 17.4297C97.0052 16.8516 97.0911 16.3177 97.2578 15.8281C97.4245 15.3333 97.6589 14.9036 97.9609 14.5391C98.263 14.1745 98.6224 13.8932 99.0391 13.6953C99.4609 13.4922 99.9271 13.3906 100.438 13.3906C100.854 13.3906 101.229 13.4479 101.562 13.5625C101.896 13.6719 102.18 13.849 102.414 14.0938C102.654 14.3385 102.836 14.6562 102.961 15.0469C103.086 15.4323 103.148 15.9036 103.148 16.4609V22H101.695V16.4453C101.695 16.0026 101.63 15.6484 101.5 15.3828C101.37 15.112 101.18 14.9167 100.93 14.7969C100.68 14.6719 100.372 14.6094 100.008 14.6094C99.6484 14.6094 99.3203 14.6849 99.0234 14.8359C98.7318 14.987 98.4792 15.1953 98.2656 15.4609C98.0573 15.7266 97.8932 16.0312 97.7734 16.375C97.6589 16.7135 97.6016 17.0729 97.6016 17.4531ZM109.352 22.1562C108.763 22.1562 108.229 22.0573 107.75 21.8594C107.276 21.6562 106.867 21.3724 106.523 21.0078C106.185 20.6432 105.924 20.2109 105.742 19.7109C105.56 19.2109 105.469 18.6641 105.469 18.0703V17.7422C105.469 17.0547 105.57 16.4427 105.773 15.9062C105.977 15.3646 106.253 14.9062 106.602 14.5312C106.951 14.1562 107.346 13.8724 107.789 13.6797C108.232 13.487 108.69 13.3906 109.164 13.3906C109.768 13.3906 110.289 13.4948 110.727 13.7031C111.169 13.9115 111.531 14.2031 111.812 14.5781C112.094 14.9479 112.302 15.3854 112.438 15.8906C112.573 16.3906 112.641 16.9375 112.641 17.5312V18.1797H106.328V17H111.195V16.8906C111.174 16.5156 111.096 16.151 110.961 15.7969C110.831 15.4427 110.622 15.151 110.336 14.9219C110.049 14.6927 109.659 14.5781 109.164 14.5781C108.836 14.5781 108.534 14.6484 108.258 14.7891C107.982 14.9245 107.745 15.1276 107.547 15.3984C107.349 15.6693 107.195 16 107.086 16.3906C106.977 16.7812 106.922 17.2318 106.922 17.7422V18.0703C106.922 18.4714 106.977 18.849 107.086 19.2031C107.201 19.5521 107.365 19.8594 107.578 20.125C107.797 20.3906 108.06 20.599 108.367 20.75C108.68 20.901 109.034 20.9766 109.43 20.9766C109.94 20.9766 110.372 20.8724 110.727 20.6641C111.081 20.4557 111.391 20.1771 111.656 19.8281L112.531 20.5234C112.349 20.7995 112.117 21.0625 111.836 21.3125C111.555 21.5625 111.208 21.7656 110.797 21.9219C110.391 22.0781 109.909 22.1562 109.352 22.1562Z" fill="black" />
              <defs>
                <filter id="filter1_f_179697_5885" x="51.5" y="9.5" width="15" height="15" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
                  <feFlood flood-opacity="0" result="BackgroundImageFix" />
                  <feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape" />
                  <feGaussianBlur stdDeviation="1" result="effect1_foregroundBlur_179697_5885" />
                </filter>
                <radialGradient id="paint0_radial_179697_5885" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(59 17) rotate(90) scale(5.5)">
                  <stop stop-color="#2FFF00" />
                  <stop offset="1" stop-color="#BCFFAD" />
                </radialGradient>
              </defs>
            </svg>

          )}
          {status.toLowerCase() === 'failed' && (
            <svg width="167" height="34" viewBox="0 0 167 34" fill="none" xmlns="http://www.w3.org/2000/svg">
              <rect width="167" height="34" rx="17" fill="#D0D0D0" fill-opacity="0.25" />
              <g filter="url(#filter1_f_179697_5885)">
                <circle cx="59" cy="17" r="5.5" fill="url(#paint0_radial_179697_5885)" />
              </g>
              <path d="M79.1719 22H76.7969L76.8125 20.7734H79.1719C79.9844 20.7734 80.6615 20.6042 81.2031 20.2656C81.7448 19.9219 82.151 19.4427 82.4219 18.8281C82.6979 18.2083 82.8359 17.4844 82.8359 16.6562V15.9609C82.8359 15.3099 82.7578 14.7318 82.6016 14.2266C82.4453 13.7161 82.2161 13.2865 81.9141 12.9375C81.612 12.5833 81.2422 12.3151 80.8047 12.1328C80.3724 11.9505 79.875 11.8594 79.3125 11.8594H76.75V10.625H79.3125C80.0573 10.625 80.737 10.75 81.3516 11C81.9661 11.2448 82.4948 11.6016 82.9375 12.0703C83.3854 12.5339 83.7292 13.0964 83.9688 13.7578C84.2083 14.4141 84.3281 15.1536 84.3281 15.9766V16.6562C84.3281 17.4792 84.2083 18.2214 83.9688 18.8828C83.7292 19.5391 83.3828 20.099 82.9297 20.5625C82.4818 21.026 81.9401 21.3828 81.3047 21.6328C80.6745 21.8776 79.9635 22 79.1719 22ZM77.6016 10.625V22H76.0938V10.625H77.6016ZM86.4922 17.8672V17.6875C86.4922 17.0781 86.5807 16.513 86.7578 15.9922C86.9349 15.4661 87.1901 15.0104 87.5234 14.625C87.8568 14.2344 88.2604 13.9323 88.7344 13.7188C89.2083 13.5 89.7396 13.3906 90.3281 13.3906C90.9219 13.3906 91.4557 13.5 91.9297 13.7188C92.4089 13.9323 92.8151 14.2344 93.1484 14.625C93.487 15.0104 93.7448 15.4661 93.9219 15.9922C94.099 16.513 94.1875 17.0781 94.1875 17.6875V17.8672C94.1875 18.4766 94.099 19.0417 93.9219 19.5625C93.7448 20.0833 93.487 20.5391 93.1484 20.9297C92.8151 21.3151 92.4115 21.6172 91.9375 21.8359C91.4688 22.0495 90.9375 22.1562 90.3438 22.1562C89.75 22.1562 89.2161 22.0495 88.7422 21.8359C88.2682 21.6172 87.862 21.3151 87.5234 20.9297C87.1901 20.5391 86.9349 20.0833 86.7578 19.5625C86.5807 19.0417 86.4922 18.4766 86.4922 17.8672ZM87.9375 17.6875V17.8672C87.9375 18.2891 87.987 18.6875 88.0859 19.0625C88.1849 19.4323 88.3333 19.7604 88.5312 20.0469C88.7344 20.3333 88.987 20.5599 89.2891 20.7266C89.5911 20.888 89.9427 20.9688 90.3438 20.9688C90.7396 20.9688 91.0859 20.888 91.3828 20.7266C91.6849 20.5599 91.9349 20.3333 92.1328 20.0469C92.3307 19.7604 92.4792 19.4323 92.5781 19.0625C92.6823 18.6875 92.7344 18.2891 92.7344 17.8672V17.6875C92.7344 17.2708 92.6823 16.8776 92.5781 16.5078C92.4792 16.1328 92.3281 15.8021 92.125 15.5156C91.9271 15.224 91.6771 14.9948 91.375 14.8281C91.0781 14.6615 90.7292 14.5781 90.3281 14.5781C89.9323 14.5781 89.5833 14.6615 89.2812 14.8281C88.9844 14.9948 88.7344 15.224 88.5312 15.5156C88.3333 15.8021 88.1849 16.1328 88.0859 16.5078C87.987 16.8776 87.9375 17.2708 87.9375 17.6875ZM97.9453 15.3516V22H96.5V13.5469H97.8672L97.9453 15.3516ZM97.6016 17.4531L97 17.4297C97.0052 16.8516 97.0911 16.3177 97.2578 15.8281C97.4245 15.3333 97.6589 14.9036 97.9609 14.5391C98.263 14.1745 98.6224 13.8932 99.0391 13.6953C99.4609 13.4922 99.9271 13.3906 100.438 13.3906C100.854 13.3906 101.229 13.4479 101.562 13.5625C101.896 13.6719 102.18 13.849 102.414 14.0938C102.654 14.3385 102.836 14.6562 102.961 15.0469C103.086 15.4323 103.148 15.9036 103.148 16.4609V22H101.695V16.4453C101.695 16.0026 101.63 15.6484 101.5 15.3828C101.37 15.112 101.18 14.9167 100.93 14.7969C100.68 14.6719 100.372 14.6094 100.008 14.6094C99.6484 14.6094 99.3203 14.6849 99.0234 14.8359C98.7318 14.987 98.4792 15.1953 98.2656 15.4609C98.0573 15.7266 97.8932 16.0312 97.7734 16.375C97.6589 16.7135 97.6016 17.0729 97.6016 17.4531ZM109.352 22.1562C108.763 22.1562 108.229 22.0573 107.75 21.8594C107.276 21.6562 106.867 21.3724 106.523 21.0078C106.185 20.6432 105.924 20.2109 105.742 19.7109C105.56 19.2109 105.469 18.6641 105.469 18.0703V17.7422C105.469 17.0547 105.57 16.4427 105.773 15.9062C105.977 15.3646 106.253 14.9062 106.602 14.5312C106.951 14.1562 107.346 13.8724 107.789 13.6797C108.232 13.487 108.69 13.3906 109.164 13.3906C109.768 13.3906 110.289 13.4948 110.727 13.7031C111.169 13.9115 111.531 14.2031 111.812 14.5781C112.094 14.9479 112.302 15.3854 112.438 15.8906C112.573 16.3906 112.641 16.9375 112.641 17.5312V18.1797H106.328V17H111.195V16.8906C111.174 16.5156 111.096 16.151 110.961 15.7969C110.831 15.4427 110.622 15.151 110.336 14.9219C110.049 14.6927 109.659 14.5781 109.164 14.5781C108.836 14.5781 108.534 14.6484 108.258 14.7891C107.982 14.9245 107.745 15.1276 107.547 15.3984C107.349 15.6693 107.195 16 107.086 16.3906C106.977 16.7812 106.922 17.2318 106.922 17.7422V18.0703C106.922 18.4714 106.977 18.849 107.086 19.2031C107.201 19.5521 107.365 19.8594 107.578 20.125C107.797 20.3906 108.06 20.599 108.367 20.75C108.68 20.901 109.034 20.9766 109.43 20.9766C109.94 20.9766 110.372 20.8724 110.727 20.6641C111.081 20.4557 111.391 20.1771 111.656 19.8281L112.531 20.5234C112.349 20.7995 112.117 21.0625 111.836 21.3125C111.555 21.5625 111.208 21.7656 110.797 21.9219C110.391 22.0781 109.909 22.1562 109.352 22.1562Z" fill="black" />
              <defs>
                <filter id="filter1_f_179697_5885" x="51.5" y="9.5" width="15" height="15" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
                  <feFlood flood-opacity="0" result="BackgroundImageFix" />
                  <feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape" />
                  <feGaussianBlur stdDeviation="1" result="effect1_foregroundBlur_179697_5885" />
                </filter>
                <radialGradient id="paint0_radial_179697_5885" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(59 17) rotate(90) scale(5.5)">
                  <stop stop-color="#FF4D4F" />
                  <stop offset="1" stop-color="#FF4D4F" />
                </radialGradient>
              </defs>
            </svg>
          )}
          {status.toLowerCase() === 'pending' && (
            <svg width="167" height="34" viewBox="0 0 167 34" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M17 1H150C158.837 1 166 8.16344 166 17C166 25.8366 158.837 33 150 33H17C8.16345 33 1 25.8366 1 17C1 8.16344 8.16344 1 17 1Z" fill="black" />
              <path d="M17 1H150C158.837 1 166 8.16344 166 17C166 25.8366 158.837 33 150 33H17C8.16345 33 1 25.8366 1 17C1 8.16344 8.16344 1 17 1Z" stroke="url(#paint0_linear_180325_11049)" stroke-width="2" />
              <path d="M48.6777 11.468H50.0518V21.51H48.6777V11.468ZM52.1025 14.1887H53.2715V15.2278C53.6178 14.7994 53.9847 14.4918 54.3721 14.3049C54.7594 14.1181 55.1901 14.0247 55.6641 14.0247C56.7031 14.0247 57.4049 14.387 57.7695 15.1116C57.9701 15.5081 58.0703 16.0754 58.0703 16.8137V21.51H56.8193V16.8958C56.8193 16.4491 56.7533 16.0891 56.6211 15.8157C56.4023 15.3599 56.0059 15.1321 55.4316 15.1321C55.14 15.1321 54.9007 15.1617 54.7139 15.2209C54.3766 15.3212 54.0804 15.5217 53.8252 15.8225C53.6201 16.064 53.4857 16.3147 53.4219 16.5745C53.3626 16.8297 53.333 17.1965 53.333 17.675V21.51H52.1025V14.1887ZM59.5742 16.9778H63.0059V18.2424H59.5742V16.9778ZM64.8516 11.468H69.3701C70.2633 11.468 70.9834 11.7209 71.5303 12.2268C72.0771 12.7281 72.3506 13.4345 72.3506 14.3459C72.3506 15.1298 72.1068 15.8134 71.6191 16.3967C71.1315 16.9755 70.3818 17.2649 69.3701 17.2649H66.2119V21.51H64.8516V11.468ZM70.9766 14.3528C70.9766 13.6145 70.7031 13.1132 70.1562 12.8489C69.8555 12.7076 69.443 12.637 68.9189 12.637H66.2119V16.1165H68.9189C69.5296 16.1165 70.0241 15.9866 70.4023 15.7268C70.7852 15.467 70.9766 15.009 70.9766 14.3528ZM73.9297 14.1887H75.0986V15.4534C75.1943 15.2073 75.429 14.9088 75.8027 14.5579C76.1764 14.2024 76.6071 14.0247 77.0947 14.0247C77.1175 14.0247 77.1562 14.0269 77.2109 14.0315C77.2656 14.0361 77.359 14.0452 77.4912 14.0588V15.3577C77.4183 15.344 77.3499 15.3349 77.2861 15.3303C77.2269 15.3258 77.1608 15.3235 77.0879 15.3235C76.4681 15.3235 75.9919 15.524 75.6592 15.925C75.3265 16.3215 75.1602 16.7795 75.1602 17.2991V21.51H73.9297V14.1887ZM81.4629 20.717C82.2786 20.717 82.8369 20.4094 83.1377 19.7942C83.443 19.1744 83.5957 18.4862 83.5957 17.7297C83.5957 17.0461 83.4863 16.4902 83.2676 16.0618C82.9212 15.3873 82.3242 15.05 81.4766 15.05C80.7246 15.05 80.1777 15.3372 79.8359 15.9114C79.4941 16.4856 79.3232 17.1783 79.3232 17.9895C79.3232 18.7688 79.4941 19.4182 79.8359 19.9377C80.1777 20.4573 80.7201 20.717 81.4629 20.717ZM81.5107 13.9768C82.4541 13.9768 83.2516 14.2913 83.9033 14.9202C84.555 15.5491 84.8809 16.4742 84.8809 17.6956C84.8809 18.8759 84.5938 19.8512 84.0195 20.6213C83.4453 21.3915 82.5544 21.7766 81.3467 21.7766C80.3395 21.7766 79.5397 21.4371 78.9473 20.7581C78.3548 20.0745 78.0586 19.1584 78.0586 18.01C78.0586 16.7795 78.3708 15.7997 78.9951 15.0706C79.6195 14.3414 80.458 13.9768 81.5107 13.9768ZM88.9346 14.0588C89.5088 14.0588 90.0101 14.2001 90.4385 14.4827C90.6709 14.6422 90.9079 14.8746 91.1494 15.1799V14.2571H92.2842V20.9153C92.2842 21.845 92.1475 22.5787 91.874 23.1165C91.3636 24.1099 90.3997 24.6067 88.9824 24.6067C88.194 24.6067 87.5309 24.429 86.9932 24.0735C86.4554 23.7226 86.1546 23.1711 86.0908 22.4192H87.3418C87.401 22.7473 87.5195 23.0002 87.6973 23.178C87.9753 23.4514 88.4128 23.5881 89.0098 23.5881C89.9531 23.5881 90.5706 23.2555 90.8623 22.5901C91.0355 22.1982 91.1152 21.4986 91.1016 20.4915C90.8555 20.8652 90.5592 21.1431 90.2129 21.3254C89.8665 21.5077 89.4085 21.5989 88.8389 21.5989C88.0459 21.5989 87.3509 21.3186 86.7539 20.7581C86.1615 20.193 85.8652 19.261 85.8652 17.9622C85.8652 16.7362 86.1637 15.7792 86.7607 15.0911C87.3623 14.4029 88.0869 14.0588 88.9346 14.0588ZM91.1494 17.8186C91.1494 16.9117 90.9626 16.2395 90.5889 15.802C90.2152 15.3645 89.7389 15.1458 89.1602 15.1458C88.2943 15.1458 87.7018 15.5514 87.3828 16.3625C87.2142 16.7955 87.1299 17.3629 87.1299 18.0647C87.1299 18.8896 87.2962 19.5185 87.6289 19.9514C87.9661 20.3798 88.4173 20.594 88.9824 20.594C89.8665 20.594 90.4886 20.1952 90.8486 19.3977C91.0492 18.9465 91.1494 18.4202 91.1494 17.8186ZM94.1777 14.1887H95.3467V15.4534C95.4424 15.2073 95.6771 14.9088 96.0508 14.5579C96.4245 14.2024 96.8551 14.0247 97.3428 14.0247C97.3656 14.0247 97.4043 14.0269 97.459 14.0315C97.5137 14.0361 97.6071 14.0452 97.7393 14.0588V15.3577C97.6663 15.344 97.598 15.3349 97.5342 15.3303C97.4749 15.3258 97.4089 15.3235 97.3359 15.3235C96.7161 15.3235 96.2399 15.524 95.9072 15.925C95.5745 16.3215 95.4082 16.7795 95.4082 17.2991V21.51H94.1777V14.1887ZM101.854 14.0247C102.374 14.0247 102.878 14.1477 103.365 14.3938C103.853 14.6353 104.224 14.9498 104.479 15.3372C104.726 15.7063 104.89 16.137 104.972 16.6292C105.045 16.9664 105.081 17.5042 105.081 18.2424H99.7148C99.7376 18.9853 99.9131 19.5823 100.241 20.0334C100.569 20.4801 101.077 20.7034 101.766 20.7034C102.408 20.7034 102.921 20.4915 103.304 20.0676C103.522 19.8215 103.677 19.5367 103.769 19.2131H104.979C104.947 19.482 104.84 19.7828 104.657 20.1155C104.479 20.4436 104.279 20.7125 104.056 20.9221C103.682 21.2867 103.219 21.5328 102.668 21.6604C102.372 21.7333 102.037 21.7698 101.663 21.7698C100.752 21.7698 99.9792 21.4394 99.3457 20.7786C98.7122 20.1132 98.3955 19.1835 98.3955 17.9895C98.3955 16.8137 98.7145 15.859 99.3525 15.1252C99.9906 14.3915 100.825 14.0247 101.854 14.0247ZM103.816 17.2649C103.766 16.7317 103.65 16.3056 103.468 15.9866C103.131 15.3941 102.568 15.0979 101.779 15.0979C101.214 15.0979 100.74 15.303 100.357 15.7131C99.9746 16.1187 99.7718 16.636 99.749 17.2649H103.816ZM107.33 19.2131C107.367 19.6233 107.469 19.9377 107.638 20.1565C107.948 20.553 108.485 20.7512 109.251 20.7512C109.707 20.7512 110.108 20.6532 110.454 20.4573C110.8 20.2568 110.974 19.9491 110.974 19.5344C110.974 19.22 110.835 18.9807 110.557 18.8167C110.379 18.7164 110.028 18.6002 109.504 18.468L108.526 18.2219C107.902 18.067 107.442 17.8938 107.146 17.7024C106.617 17.3697 106.353 16.9094 106.353 16.3215C106.353 15.6288 106.601 15.0683 107.098 14.6399C107.599 14.2115 108.271 13.9973 109.114 13.9973C110.217 13.9973 111.012 14.3209 111.5 14.968C111.805 15.3782 111.953 15.8202 111.944 16.2942H110.782C110.759 16.0162 110.661 15.7633 110.488 15.5354C110.206 15.2118 109.716 15.05 109.019 15.05C108.554 15.05 108.201 15.1389 107.959 15.3167C107.722 15.4944 107.604 15.7291 107.604 16.0208C107.604 16.3398 107.761 16.595 108.075 16.7864C108.257 16.9003 108.526 17.0006 108.882 17.0872L109.695 17.2854C110.579 17.4996 111.172 17.7069 111.473 17.9075C111.951 18.2219 112.19 18.7164 112.19 19.3909C112.19 20.0426 111.942 20.6054 111.445 21.0793C110.953 21.5533 110.201 21.7903 109.189 21.7903C108.1 21.7903 107.328 21.5442 106.872 21.052C106.421 20.5553 106.179 19.9423 106.147 19.2131H107.33ZM114.33 19.2131C114.367 19.6233 114.469 19.9377 114.638 20.1565C114.948 20.553 115.485 20.7512 116.251 20.7512C116.707 20.7512 117.108 20.6532 117.454 20.4573C117.8 20.2568 117.974 19.9491 117.974 19.5344C117.974 19.22 117.835 18.9807 117.557 18.8167C117.379 18.7164 117.028 18.6002 116.504 18.468L115.526 18.2219C114.902 18.067 114.442 17.8938 114.146 17.7024C113.617 17.3697 113.353 16.9094 113.353 16.3215C113.353 15.6288 113.601 15.0683 114.098 14.6399C114.599 14.2115 115.271 13.9973 116.114 13.9973C117.217 13.9973 118.012 14.3209 118.5 14.968C118.805 15.3782 118.953 15.8202 118.944 16.2942H117.782C117.759 16.0162 117.661 15.7633 117.488 15.5354C117.206 15.2118 116.716 15.05 116.019 15.05C115.554 15.05 115.201 15.1389 114.959 15.3167C114.722 15.4944 114.604 15.7291 114.604 16.0208C114.604 16.3398 114.761 16.595 115.075 16.7864C115.257 16.9003 115.526 17.0006 115.882 17.0872L116.695 17.2854C117.579 17.4996 118.172 17.7069 118.473 17.9075C118.951 18.2219 119.19 18.7164 119.19 19.3909C119.19 20.0426 118.942 20.6054 118.445 21.0793C117.953 21.5533 117.201 21.7903 116.189 21.7903C115.1 21.7903 114.328 21.5442 113.872 21.052C113.421 20.5553 113.179 19.9423 113.147 19.2131H114.33Z" fill="white" />
              <defs>
                <linearGradient id="paint0_linear_180325_11049" x1="0" y1="17" x2="167" y2="17" gradientUnits="userSpaceOnUse">
                  <stop stop-color="#6D20F5" />
                  <stop offset="1" stop-color="#E7337A" />
                </linearGradient>
              </defs>
            </svg>
          )}
          {status.toLowerCase() === 'running' && (
            <svg width="167" height="34" viewBox="0 0 167 34" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M17 1H150C158.837 1 166 8.16344 166 17C166 25.8366 158.837 33 150 33H17C8.16345 33 1 25.8366 1 17C1 8.16344 8.16344 1 17 1Z" fill="black" />
              <path d="M17 1H150C158.837 1 166 8.16344 166 17C166 25.8366 158.837 33 150 33H17C8.16345 33 1 25.8366 1 17C1 8.16344 8.16344 1 17 1Z" stroke="url(#paint0_linear_180325_11049)" stroke-width="2" />
              <path d="M48.6777 11.468H50.0518V21.51H48.6777V11.468ZM52.1025 14.1887H53.2715V15.2278C53.6178 14.7994 53.9847 14.4918 54.3721 14.3049C54.7594 14.1181 55.1901 14.0247 55.6641 14.0247C56.7031 14.0247 57.4049 14.387 57.7695 15.1116C57.9701 15.5081 58.0703 16.0754 58.0703 16.8137V21.51H56.8193V16.8958C56.8193 16.4491 56.7533 16.0891 56.6211 15.8157C56.4023 15.3599 56.0059 15.1321 55.4316 15.1321C55.14 15.1321 54.9007 15.1617 54.7139 15.2209C54.3766 15.3212 54.0804 15.5217 53.8252 15.8225C53.6201 16.064 53.4857 16.3147 53.4219 16.5745C53.3626 16.8297 53.333 17.1965 53.333 17.675V21.51H52.1025V14.1887ZM59.5742 16.9778H63.0059V18.2424H59.5742V16.9778ZM64.8516 11.468H69.3701C70.2633 11.468 70.9834 11.7209 71.5303 12.2268C72.0771 12.7281 72.3506 13.4345 72.3506 14.3459C72.3506 15.1298 72.1068 15.8134 71.6191 16.3967C71.1315 16.9755 70.3818 17.2649 69.3701 17.2649H66.2119V21.51H64.8516V11.468ZM70.9766 14.3528C70.9766 13.6145 70.7031 13.1132 70.1562 12.8489C69.8555 12.7076 69.443 12.637 68.9189 12.637H66.2119V16.1165H68.9189C69.5296 16.1165 70.0241 15.9866 70.4023 15.7268C70.7852 15.467 70.9766 15.009 70.9766 14.3528ZM73.9297 14.1887H75.0986V15.4534C75.1943 15.2073 75.429 14.9088 75.8027 14.5579C76.1764 14.2024 76.6071 14.0247 77.0947 14.0247C77.1175 14.0247 77.1562 14.0269 77.2109 14.0315C77.2656 14.0361 77.359 14.0452 77.4912 14.0588V15.3577C77.4183 15.344 77.3499 15.3349 77.2861 15.3303C77.2269 15.3258 77.1608 15.3235 77.0879 15.3235C76.4681 15.3235 75.9919 15.524 75.6592 15.925C75.3265 16.3215 75.1602 16.7795 75.1602 17.2991V21.51H73.9297V14.1887ZM81.4629 20.717C82.2786 20.717 82.8369 20.4094 83.1377 19.7942C83.443 19.1744 83.5957 18.4862 83.5957 17.7297C83.5957 17.0461 83.4863 16.4902 83.2676 16.0618C82.9212 15.3873 82.3242 15.05 81.4766 15.05C80.7246 15.05 80.1777 15.3372 79.8359 15.9114C79.4941 16.4856 79.3232 17.1783 79.3232 17.9895C79.3232 18.7688 79.4941 19.4182 79.8359 19.9377C80.1777 20.4573 80.7201 20.717 81.4629 20.717ZM81.5107 13.9768C82.4541 13.9768 83.2516 14.2913 83.9033 14.9202C84.555 15.5491 84.8809 16.4742 84.8809 17.6956C84.8809 18.8759 84.5938 19.8512 84.0195 20.6213C83.4453 21.3915 82.5544 21.7766 81.3467 21.7766C80.3395 21.7766 79.5397 21.4371 78.9473 20.7581C78.3548 20.0745 78.0586 19.1584 78.0586 18.01C78.0586 16.7795 78.3708 15.7997 78.9951 15.0706C79.6195 14.3414 80.458 13.9768 81.5107 13.9768ZM88.9346 14.0588C89.5088 14.0588 90.0101 14.2001 90.4385 14.4827C90.6709 14.6422 90.9079 14.8746 91.1494 15.1799V14.2571H92.2842V20.9153C92.2842 21.845 92.1475 22.5787 91.874 23.1165C91.3636 24.1099 90.3997 24.6067 88.9824 24.6067C88.194 24.6067 87.5309 24.429 86.9932 24.0735C86.4554 23.7226 86.1546 23.1711 86.0908 22.4192H87.3418C87.401 22.7473 87.5195 23.0002 87.6973 23.178C87.9753 23.4514 88.4128 23.5881 89.0098 23.5881C89.9531 23.5881 90.5706 23.2555 90.8623 22.5901C91.0355 22.1982 91.1152 21.4986 91.1016 20.4915C90.8555 20.8652 90.5592 21.1431 90.2129 21.3254C89.8665 21.5077 89.4085 21.5989 88.8389 21.5989C88.0459 21.5989 87.3509 21.3186 86.7539 20.7581C86.1615 20.193 85.8652 19.261 85.8652 17.9622C85.8652 16.7362 86.1637 15.7792 86.7607 15.0911C87.3623 14.4029 88.0869 14.0588 88.9346 14.0588ZM91.1494 17.8186C91.1494 16.9117 90.9626 16.2395 90.5889 15.802C90.2152 15.3645 89.7389 15.1458 89.1602 15.1458C88.2943 15.1458 87.7018 15.5514 87.3828 16.3625C87.2142 16.7955 87.1299 17.3629 87.1299 18.0647C87.1299 18.8896 87.2962 19.5185 87.6289 19.9514C87.9661 20.3798 88.4173 20.594 88.9824 20.594C89.8665 20.594 90.4886 20.1952 90.8486 19.3977C91.0492 18.9465 91.1494 18.4202 91.1494 17.8186ZM94.1777 14.1887H95.3467V15.4534C95.4424 15.2073 95.6771 14.9088 96.0508 14.5579C96.4245 14.2024 96.8551 14.0247 97.3428 14.0247C97.3656 14.0247 97.4043 14.0269 97.459 14.0315C97.5137 14.0361 97.6071 14.0452 97.7393 14.0588V15.3577C97.6663 15.344 97.598 15.3349 97.5342 15.3303C97.4749 15.3258 97.4089 15.3235 97.3359 15.3235C96.7161 15.3235 96.2399 15.524 95.9072 15.925C95.5745 16.3215 95.4082 16.7795 95.4082 17.2991V21.51H94.1777V14.1887ZM101.854 14.0247C102.374 14.0247 102.878 14.1477 103.365 14.3938C103.853 14.6353 104.224 14.9498 104.479 15.3372C104.726 15.7063 104.89 16.137 104.972 16.6292C105.045 16.9664 105.081 17.5042 105.081 18.2424H99.7148C99.7376 18.9853 99.9131 19.5823 100.241 20.0334C100.569 20.4801 101.077 20.7034 101.766 20.7034C102.408 20.7034 102.921 20.4915 103.304 20.0676C103.522 19.8215 103.677 19.5367 103.769 19.2131H104.979C104.947 19.482 104.84 19.7828 104.657 20.1155C104.479 20.4436 104.279 20.7125 104.056 20.9221C103.682 21.2867 103.219 21.5328 102.668 21.6604C102.372 21.7333 102.037 21.7698 101.663 21.7698C100.752 21.7698 99.9792 21.4394 99.3457 20.7786C98.7122 20.1132 98.3955 19.1835 98.3955 17.9895C98.3955 16.8137 98.7145 15.859 99.3525 15.1252C99.9906 14.3915 100.825 14.0247 101.854 14.0247ZM103.816 17.2649C103.766 16.7317 103.65 16.3056 103.468 15.9866C103.131 15.3941 102.568 15.0979 101.779 15.0979C101.214 15.0979 100.74 15.303 100.357 15.7131C99.9746 16.1187 99.7718 16.636 99.749 17.2649H103.816ZM107.33 19.2131C107.367 19.6233 107.469 19.9377 107.638 20.1565C107.948 20.553 108.485 20.7512 109.251 20.7512C109.707 20.7512 110.108 20.6532 110.454 20.4573C110.8 20.2568 110.974 19.9491 110.974 19.5344C110.974 19.22 110.835 18.9807 110.557 18.8167C110.379 18.7164 110.028 18.6002 109.504 18.468L108.526 18.2219C107.902 18.067 107.442 17.8938 107.146 17.7024C106.617 17.3697 106.353 16.9094 106.353 16.3215C106.353 15.6288 106.601 15.0683 107.098 14.6399C107.599 14.2115 108.271 13.9973 109.114 13.9973C110.217 13.9973 111.012 14.3209 111.5 14.968C111.805 15.3782 111.953 15.8202 111.944 16.2942H110.782C110.759 16.0162 110.661 15.7633 110.488 15.5354C110.206 15.2118 109.716 15.05 109.019 15.05C108.554 15.05 108.201 15.1389 107.959 15.3167C107.722 15.4944 107.604 15.7291 107.604 16.0208C107.604 16.3398 107.761 16.595 108.075 16.7864C108.257 16.9003 108.526 17.0006 108.882 17.0872L109.695 17.2854C110.579 17.4996 111.172 17.7069 111.473 17.9075C111.951 18.2219 112.19 18.7164 112.19 19.3909C112.19 20.0426 111.942 20.6054 111.445 21.0793C110.953 21.5533 110.201 21.7903 109.189 21.7903C108.1 21.7903 107.328 21.5442 106.872 21.052C106.421 20.5553 106.179 19.9423 106.147 19.2131H107.33ZM114.33 19.2131C114.367 19.6233 114.469 19.9377 114.638 20.1565C114.948 20.553 115.485 20.7512 116.251 20.7512C116.707 20.7512 117.108 20.6532 117.454 20.4573C117.8 20.2568 117.974 19.9491 117.974 19.5344C117.974 19.22 117.835 18.9807 117.557 18.8167C117.379 18.7164 117.028 18.6002 116.504 18.468L115.526 18.2219C114.902 18.067 114.442 17.8938 114.146 17.7024C113.617 17.3697 113.353 16.9094 113.353 16.3215C113.353 15.6288 113.601 15.0683 114.098 14.6399C114.599 14.2115 115.271 13.9973 116.114 13.9973C117.217 13.9973 118.012 14.3209 118.5 14.968C118.805 15.3782 118.953 15.8202 118.944 16.2942H117.782C117.759 16.0162 117.661 15.7633 117.488 15.5354C117.206 15.2118 116.716 15.05 116.019 15.05C115.554 15.05 115.201 15.1389 114.959 15.3167C114.722 15.4944 114.604 15.7291 114.604 16.0208C114.604 16.3398 114.761 16.595 115.075 16.7864C115.257 16.9003 115.526 17.0006 115.882 17.0872L116.695 17.2854C117.579 17.4996 118.172 17.7069 118.473 17.9075C118.951 18.2219 119.19 18.7164 119.19 19.3909C119.19 20.0426 118.942 20.6054 118.445 21.0793C117.953 21.5533 117.201 21.7903 116.189 21.7903C115.1 21.7903 114.328 21.5442 113.872 21.052C113.421 20.5553 113.179 19.9423 113.147 19.2131H114.33Z" fill="white" />
              <defs>
                <linearGradient id="paint0_linear_180325_11049" x1="0" y1="17" x2="167" y2="17" gradientUnits="userSpaceOnUse">
                  <stop stop-color="#6D20F5" />
                  <stop offset="1" stop-color="#E7337A" />
                </linearGradient>
              </defs>
            </svg>
          )}
        </div>
      ),
    },
    {
      title: 'Model',
      dataIndex: 'model',
      align: 'center',
      key: 'model',
      width: 150,
    },
    {
      title: 'Token Usage',
      dataIndex: 'tokens',
      align: 'center',
      key: 'tokens',
      width: 120,
      render: (tokens: number) => tokens || '-',
    },
    {
      title: 'Reward',
      dataIndex: 'earn',
      align: 'center',
      key: 'earn',
      width: 100,
      render: (earn: number | string) => (
        <span className={styles.reward}>{Number(earn) || 0}</span>
      ),
    },
    {
      title: 'Created Time',
      align: 'center',
      dataIndex: 'created_at',
      key: 'created_at',
      width: 160,
      render: (created_at?: string) => {
        if (!created_at) return '-';
        const date = new Date(created_at);
        return (
          <div className={styles.timeColumn}>
            <div className={styles.time}>{date.toLocaleTimeString()}</div>
            <div className={styles.date}>{date.toLocaleDateString()}</div>
          </div>
        );
      },
    },
  ];

  return (
   <Card
      title={'History'}
      className={styles.ListCard}
    >
      {/* 搜索栏 */}
      <div className={styles.searchSection}>
        <Input
          placeholder="Search requests..."
          value={searchText}
          onChange={(e) => setSearchText(e.target.value)}
          onPressEnter={handleSearch}
          className={styles.searchInput}
          prefix={<SearchOutlined />}
        />
        <Button
          icon={<ReloadOutlined />}
          onClick={loadHistory}
          className={styles.refreshButton}
        >
          Refresh
        </Button>
      </div>

      {/* 表格 */}
        <Table
          columns={columns}
          dataSource={requestData}
          rowKey="id"
          loading={loading}
          className={styles.table}
          size="middle"
          pagination={{
            position: ['bottomCenter'],
            current,
            pageSize,
            total,
            onChange: (page) => setCurrent(page),
            onShowSizeChange: (_, size) => setPageSize(size),
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `Total ${total} records`,
          }}
        />
    </Card>
  );
}
