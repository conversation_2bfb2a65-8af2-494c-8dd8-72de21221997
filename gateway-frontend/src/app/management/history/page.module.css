.container {
}

/* 搜索区域 */
.searchSection {
  display: flex;
  gap: 16px;
  margin-bottom: 24px;
  align-items: center;
}

.searchInput {
  width: 300px;
  border: 1px solid #e5e7eb;
  background: white;
}

.searchInput:focus {
  border-color: #6366f1;
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}
.ListCard {
  border-radius: 16px;
  border: none;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}
.refreshButton {
  border: 1px solid #e5e7eb;
  background: white;
  color: #6b7280;
  font-weight: 500;
}

.refreshButton:hover {
  border-color: #6366f1;
  color: #6366f1;
}

/* 表格卡片 */
.tableCard {
  background: white;
  border-radius: 16px;
  border: none;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  padding: 0;
}

/* 表格样式 */
.table {
  border-radius: 0;
}

.table .ant-table-thead > tr > th {
  background: #f8fafc;
  border-bottom: 1px solid #e5e7eb;
  font-weight: 600;
  color: #374151;
  font-size: 13px;
  padding: 16px 20px;
  text-align: center;
}

.table .ant-table-tbody > tr > td {
  padding: 16px 20px;
  border-bottom: 1px solid #f1f5f9;
  text-align: center;
  font-size: 14px;
}

.table .ant-table-tbody > tr:hover > td {
  background: #f8fafc;
}

.table .ant-table-tbody > tr:last-child > td {
  border-bottom: none;
}

/* 表格列样式 */
.requestId {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
  color: #6b7280;
  background: #f3f4f6;
  padding: 4px 8px;
  border-radius: 4px;
}

.statusTag {
  border-radius: 20px;
  font-size: 11px;
  font-weight: 500;
  padding: 2px 12px;
  border: none;
}
.status {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}
.reward {
  font-weight: 600;
  color: #059669;
}

.timeColumn {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2px;
}

.time {
  font-weight: 500;
  color: #374151;
  font-size: 13px;
}

.date {
  font-size: 11px;
  color: #9ca3af;
}

/* 表格内置分页样式 */
.table .ant-pagination {
  margin: 24px 0 0 0;
  padding: 20px 0;
  border-top: 1px solid #f1f5f9;
}

.table .ant-pagination-item {
  border-radius: 6px;
  border: 1px solid #e5e7eb;
  margin: 0 4px;
  transition: all 0.2s ease;
}

.table .ant-pagination-item:hover {
  border-color: #6366f1;
}

.table .ant-pagination-item-active {
  background: #6366f1;
  border-color: #6366f1;
}

.table .ant-pagination-item-active a {
  color: white;
}

.table .ant-pagination-prev,
.table .ant-pagination-next {
  border-radius: 6px;
  border: 1px solid #e5e7eb;
  transition: all 0.2s ease;
}

.table .ant-pagination-prev:hover,
.table .ant-pagination-next:hover {
  border-color: #6366f1;
}

.table .ant-pagination-options {
  margin-left: 16px;
}

.table .ant-pagination-options .ant-select {
  margin-right: 8px;
}

.table .ant-pagination-total-text {
  color: #6b7280;
  font-size: 14px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .container {
    padding: 16px;
  }

  .searchSection {
    flex-direction: column;
    align-items: stretch;
  }

  .searchInput {
    width: 100%;
  }

  .table .ant-table-thead > tr > th,
  .table .ant-table-tbody > tr > td {
    padding: 12px 8px;
    font-size: 12px;
  }

  .requestId {
    font-size: 10px;
    padding: 2px 4px;
  }
}
