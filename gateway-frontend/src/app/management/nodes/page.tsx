'use client';

import { useState, useRef } from 'react';
import { Typography } from 'antd';

import { useLocaleContext } from '@/contexts/LocaleContext';
import DeviceList from '@/components/nodes/DeviceList';
import AddDeviceModal from '@/components/nodes/AddDeviceModal';
import styles from './page.module.css';
import {useRouter} from "next/navigation";

const { Text } = Typography;

export default function NodeManagementPage() {
  const { messages } = useLocaleContext();
  const [addDeviceModalVisible, setAddDeviceModalVisible] = useState(false);
  const deviceListRef = useRef<{ loadDevices: () => Promise<void> } | null>(null);
  const router = useRouter();
  // 处理添加设备成功
  const handleAddDeviceSuccess = () => {
    // 刷新设备列表
    setAddDeviceModalVisible(false);
    // 调用设备列表的刷新方法
    if (deviceListRef.current) {
      deviceListRef.current.loadDevices();
    }
  };

  return (
    <div className={styles.container}>
      {/* <div className={styles.header}>
        <div>
          <Text type="secondary">
            {messages.nodes.management?.description || "Manage and monitor your connected devices"}
          </Text>
        </div>
      </div> */}

      <div className={styles.section}>
        <DeviceList
          ref={deviceListRef}
          onAddDevice={() => {
            // setResumeDevice(null);
            // setAddDeviceModalVisible(true);
              router.push('/nodes/create');
          }}
          defaultOnlyMyDevices={true}
          onResumeDevice={
              (id) => router.push('/nodes/create?mode=resume&deviceId='+ encodeURIComponent(id))
          }
        />
      </div>

      <AddDeviceModal
        visible={addDeviceModalVisible}
        onCancel={() => setAddDeviceModalVisible(false)}
        onSuccess={handleAddDeviceSuccess}
      />
    </div>
  );
}
