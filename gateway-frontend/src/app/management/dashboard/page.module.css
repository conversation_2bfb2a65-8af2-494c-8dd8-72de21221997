.container {
}

.loadingContainer {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

/* 布局区域 */
.topSection {
  margin-bottom: 48px;
}

.earningsSection {
  margin-bottom: 24px;
}

.uptimeSection {
  margin-bottom: 24px;
}

.chartSection {
  margin-bottom: 32px;
}

.chartsRow {
  margin-bottom: 40px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .container {
    padding: 16px;
  }

  .topSection {
    margin-bottom: 32px;
  }
}

/* 动画效果 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.earningsSection,
.uptimeSection,
.chartSection {
  animation: fadeInUp 0.6s ease-out;
}

.earningsSection {
  animation-delay: 0.1s;
}

.uptimeSection {
  animation-delay: 0.2s;
}

.chartSection:nth-of-type(1) {
  animation-delay: 0.3s;
}

.chartSection:nth-of-type(2) {
  animation-delay: 0.4s;
}
