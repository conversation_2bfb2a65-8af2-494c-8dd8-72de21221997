'use client';

import { Card, Spin } from 'antd';
import { Column } from '@ant-design/charts';
import styles from './ChartCard.module.css';

interface RevenueBreakdownData {
  date: string;
  blockRewards: number;
  jobRewards: number;
  totalRewards: number;
}

interface RevenueBreakdownChartProps {
  title: string;
  data: RevenueBreakdownData[];
  loading?: boolean;
}

export default function RevenueBreakdownChart({
  title,
  data,
  loading = false
}: RevenueBreakdownChartProps) {
  // Transform data for stacked column chart
  const chartData = data.length > 0 ? data.flatMap(item => [
    {
      date: item.date,
      type: 'Block Rewards',
      value: item.blockRewards
    },
    {
      date: item.date,
      type: 'Job Rewards',
      value: item.jobRewards
    }
  ]) : [
    // 默认空数据以显示基本图表结构
    {
      date: 'No Data',
      type: 'Block Rewards',
      value: 0
    },
    {
      date: 'No Data',
      type: 'Job Rewards',
      value: 0
    }
  ];

  const config = {
    data: chartData,
    xField: 'date',
    yField: 'value',
    seriesField: 'type',
    isStack: true,
    color: data.length > 0 ? ['#6366f1', '#8b5cf6'] : ['#d1d5db', '#e5e7eb'],
    columnStyle: {
      radius: [2, 2, 0, 0],
    },
    legend: {
      position: 'top' as const,
      itemName: {
        style: {
          fontSize: 12,
          fill: '#6b7280',
        },
      },
    },
    xAxis: {
      label: {
        style: {
          fontSize: 12,
          fill: '#6b7280',
        },
      },
    },
    yAxis: {
      label: {
        style: {
          fontSize: 12,
          fill: '#6b7280',
        },
      },
      grid: {
        line: {
          style: {
            stroke: '#e5e7eb',
            lineWidth: 1,
          },
        },
      },
    },
    tooltip: {
      formatter: (datum: any) => {
        return {
          name: datum.type,
          value: data.length > 0 ? `$${datum.value.toFixed(2)}` : 'No data',
        };
      },
    },
    // 当没有数据时显示空状态提示
    annotations: data.length === 0 ? [
      {
        type: 'text',
        position: ['50%', '50%'],
        content: 'No data available',
        style: {
          fontSize: 14,
          fill: '#9ca3af',
          textAlign: 'center',
        },
      },
    ] : [],
  };

  return (
    <Card className={styles.chartCard}>
      <div className={styles.chartContainer}>
        <div className={styles.chartHeader}>
          <h3>{title}</h3>
        </div>
        <div className={styles.chart}>
          {loading ? (
            <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: 300 }}>
              <Spin size="large" />
            </div>
          ) : (
            <Column {...config} height={300} />
          )}
        </div>
      </div>
    </Card>
  );
}
