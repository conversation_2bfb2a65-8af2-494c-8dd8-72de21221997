'use client';

import { <PERSON>, Select, Button } from 'antd';
import styles from './UptimeCalendar.module.css';

const { Option } = Select;

interface TaskData {
  date: string; // YYYY-MM-DD format
  count: number; // 任务数量
}

interface UptimeCalendarProps {
  selectedYear: number;
  selectedMonth: string;
  viewMode: string;
  onYearChange: (year: number) => void;
  onMonthChange: (month: string) => void;
  onViewModeChange: (mode: string) => void;
  // Props for time range filtering
  onTimeRangeChange?: (timeRange: string) => void;
  currentTimeRange?: string;
  // Props for task data heatmap
  chatData?: TaskData[]; // Keep the prop name for backward compatibility
}

export default function UptimeCalendar({
  selectedYear,
  selectedMonth,
  viewMode,
  onYearChange,
  onMonthChange,
  onViewModeChange,
  onTimeRangeChange,
  currentTimeRange = '30',
  chatData = []
}: UptimeCalendarProps) {
  // 根据任务数量获取热力图颜色强度
  const getHeatmapIntensity = (count: number): string => {
    if (count === 0) return '#f3f4f6'; // 浅灰色 - 无数据
    if (count <= 5) return '#d1d5db'; // 灰色 - 低活跃度 (1-5 tasks)
    if (count <= 15) return '#9ca3af'; // 中灰色 - 中等活跃度 (6-15 tasks)
    if (count <= 30) return '#6b7280'; // 深灰色 - 高活跃度 (16-30 tasks)
    return '#374151'; // 黑色 - 极高活跃度 (30+ tasks)
  };

  // 获取指定日期的任务数量
  const getTaskCountForDate = (year: number, month: number, day: number): number => {
    const dateStr = `${year}-${String(month + 1).padStart(2, '0')}-${String(day).padStart(2, '0')}`;
    const taskItem = chatData.find(item => item.date === dateStr);
    return taskItem ? taskItem.count : 0;
  };

  // 获取指定月份的任务数量（聚合该月所有数据）
  const getTaskCountForMonth = (year: number, month: number): number => {
    const monthStr = `${year}-${String(month).padStart(2, '0')}`;
    return chatData
      .filter(item => item.date.startsWith(monthStr))
      .reduce((total, item) => total + item.count, 0);
  };

  // 生成热力图数据的函数
  const generateHeatmapData = () => {
    if (viewMode === 'month') {
      // 月视图：显示选中月份的天数据 (最多31天)
      const daysInMonth = new Date(selectedYear, getMonthIndex(selectedMonth) + 1, 0).getDate();
      return Array.from({ length: daysInMonth }, (_, i) => {
        // 模拟每天的活跃度数据
        const day = i + 1;
        if ([5, 6, 12, 13, 19, 20, 26, 27].includes(day)) return 'high';
        if ([3, 4, 10, 11, 17, 18, 24, 25].includes(day)) return 'medium';
        if ([1, 2, 8, 9, 15, 16, 22, 23].includes(day)) return 'low';
        return 'empty';
      });
    } else {
      // 年视图：显示12个月的数据
      return Array.from({ length: 12 }, (_, i) => {
        // 模拟每月的活跃度数据
        if ([2, 5, 8, 11].includes(i)) return 'high'; // Mar, Jun, Sep, Dec
        if ([1, 4, 7, 10].includes(i)) return 'medium'; // Feb, May, Aug, Nov
        if ([0, 3, 6, 9].includes(i)) return 'low'; // Jan, Apr, Jul, Oct
        return 'empty';
      });
    }
  };

  // 获取月份索引
  const getMonthIndex = (monthName: string) => {
    const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
    return months.indexOf(monthName);
  };

  // 获取年份列表
  const getYearOptions = () => {
    if (viewMode === 'year') {
      // 年视图时，显示年份范围
      return Array.from({ length: 10 }, (_, i) => 2020 + i);
    } else {
      // 月视图时，显示具体年份
      return [2022, 2023, 2024, 2025];
    }
  };

  const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
  const years = Array.from({ length: 10 }, (_, i) => 2020 + i); // 2020-2029

  // 处理视图模式切换
  const handleViewModeChange = (mode: string) => {
    onViewModeChange(mode);
    // 根据视图模式触发不同的时间范围
    if (onTimeRangeChange) {
      if (mode === 'month') {
        onTimeRangeChange('30'); // 月视图使用30天
      } else {
        onTimeRangeChange('365'); // 年视图使用365天
      }
    }
  };

  // 处理网格项点击
  const handleGridItemClick = (item: string) => {
    if (viewMode === 'month') {
      // 月视图：点击选择月份
      onMonthChange(item);
      // 触发数据更新，使用30天作为默认时间范围
      if (onTimeRangeChange) {
        onTimeRangeChange('30');
      }
    } else {
      // 年视图：点击选择年份
      onYearChange(parseInt(item));
      // 触发数据更新，使用365天作为默认时间范围
      if (onTimeRangeChange) {
        onTimeRangeChange('365');
      }
    }
  };

  // 获取当前显示的网格数据
  const getGridData = () => {
    if (viewMode === 'month') {
      return months;
    } else {
      return years.map(year => year.toString());
    }
  };

  // 检查项目是否被选中
  const isItemActive = (item: string) => {
    if (viewMode === 'month') {
      return item === selectedMonth;
    } else {
      return parseInt(item) === selectedYear;
    }
  };



  return (
    <Card className={styles.uptimeCard}>
      <div className={styles.uptimeContent}>
        {/* 左侧：时间选择器 */}
        <div className={styles.uptimeLeft}>
          <div className={styles.uptimeControls}>
            <Select
              value={selectedYear}
              onChange={(value) => {
                onYearChange(value);
                // 年份变化时触发数据更新
                if (onTimeRangeChange) {
                  onTimeRangeChange(viewMode === 'month' ? '30' : '365');
                }
              }}
              size="small"
              style={{ width: '75px', marginBottom: 8 }}
              bordered={false}
            >
              {getYearOptions().map(year => (
                <Option key={year} value={year}>{year}</Option>
              ))}
            </Select>
            <div className={styles.timeButtons}>
              <Button
                size="small"
                type={viewMode === 'month' ? 'primary' : 'default'}
                className={styles.timeButton}
                onClick={() => handleViewModeChange('month')}
              >
                Month
              </Button>
              <Button
                size="small"
                type={viewMode === 'year' ? 'primary' : 'default'}
                className={styles.timeButton}
                onClick={() => handleViewModeChange('year')}
              >
                Year
              </Button>
            </div>
          </div>

          <div className={styles.monthGrid}>
            {getGridData().map((item) => (
              <div
                key={item}
                className={`${styles.monthItem} ${isItemActive(item) ? styles.active : ''}`}
                onClick={() => handleGridItemClick(item)}
              >
                {item}
              </div>
            ))}
          </div>
        </div>

        {/* 右侧：动态热力点图 */}
        <div className={styles.uptimeRight}>
          <div className={styles.heatmapContainer}>
            <div className={styles.heatmapGrid} data-view={viewMode}>
              {generateHeatmapData().map((level, i) => {
                let taskCount = 0;
                let tooltipText = '';

                if (viewMode === 'month') {
                  const day = i + 1;
                  taskCount = getTaskCountForDate(selectedYear, getMonthIndex(selectedMonth), day);
                  // Format tooltip with proper English date format
                  const monthNames = ['January', 'February', 'March', 'April', 'May', 'June',
                                    'July', 'August', 'September', 'October', 'November', 'December'];
                  const monthName = monthNames[getMonthIndex(selectedMonth)];
                  tooltipText = `${taskCount} task${taskCount !== 1 ? 's' : ''} on ${monthName} ${day}, ${selectedYear}`;
                } else {
                  // 年视图显示月度任务数据
                  const monthNames = ['January', 'February', 'March', 'April', 'May', 'June',
                                    'July', 'August', 'September', 'October', 'November', 'December'];
                  const monthName = monthNames[i];
                  // 获取该月的任务数据（从真实数据中聚合）
                  const monthTaskCount = getTaskCountForMonth(selectedYear, i + 1);
                  taskCount = monthTaskCount;
                  tooltipText = `${monthTaskCount} task${monthTaskCount !== 1 ? 's' : ''} in ${monthName} ${selectedYear}`;
                }

                return (
                  <div
                    key={i}
                    className={styles.heatmapCell}
                    style={{
                      backgroundColor: getHeatmapIntensity(taskCount)
                    }}
                    title={tooltipText}
                    data-count={taskCount}
                  />
                );
              })}
            </div>
          </div>
        </div>
      </div>
    </Card>
  );
}
