/* Earnings Card - 优化版本 */
.earningsCard {
  background: linear-gradient(135deg, #a855f7 0%, #ec4899 50%, #f59e0b 100%);
  border: none;
  border-radius: 20px;
  color: white;
  min-height: 140px;
  box-shadow: 0 8px 32px rgba(168, 85, 247, 0.25);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.earningsCard::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0) 100%);
  pointer-events: none;
}

.earningsCard:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 40px rgba(168, 85, 247, 0.35);
}

.earningsContent {
  padding: 24px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  height: 100%;
  position: relative;
  z-index: 1;
}

.earningsLabel {
  font-size: 13px;
  font-weight: 600;
  margin-bottom: 12px;
  opacity: 0.95;
  letter-spacing: 1px;
  text-transform: uppercase;
}

.earningsValue {
  font-size: 42px;
  font-weight: 800;
  margin-bottom: 0;
  line-height: 1;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}
