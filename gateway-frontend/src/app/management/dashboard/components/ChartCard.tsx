'use client';

import { <PERSON>, <PERSON><PERSON>, Spin } from 'antd';
import ReactECharts from 'echarts-for-react';
import styles from './ChartCard.module.css';

interface ChartData {
  date: string;
  value: number;
}

interface ChartCardProps {
  title: string;
  data: ChartData[];
  timeRange: string;
  onTimeRangeChange: (range: string) => void;
  loading?: boolean;
  showTimeRangeControls?: boolean;
  viewMode?: string; // 'month' | 'year'
  selectedYear?: number;
  selectedMonth?: string;
}

export default function ChartCard({
  title,
  data,
  timeRange,
  onTimeRangeChange,
  loading = false,
  showTimeRangeControls = true,
  viewMode,
  selectedYear,
  selectedMonth
}: ChartCardProps) {
  // 根据视图模式生成完整的时间序列数据
  const generateCompleteTimeSeriesData = () => {
    if (viewMode === 'year' && selectedYear) {
      // 年视图：生成完整的12个月数据
      return Array.from({ length: 12 }, (_, i) => ({
        date: `${selectedYear}-${String(i + 1).padStart(2, '0')}-01`,
        value: 0
      }));
    } else if (viewMode === 'month' && selectedYear && selectedMonth) {
      // 月视图：生成该月的所有天数数据
      // 支持短月份名称（Jan, Feb等）和完整月份名称（January, February等）
      const shortMonths = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
      const fullMonths = ['January', 'February', 'March', 'April', 'May', 'June',
                         'July', 'August', 'September', 'October', 'November', 'December'];
      
      let monthIndex = shortMonths.indexOf(selectedMonth) + 1;
      if (monthIndex === 0) {
        monthIndex = fullMonths.indexOf(selectedMonth) + 1;
      }
      
      if (monthIndex === 0) {
        // 如果月份名称无法识别，默认使用当前月份
        monthIndex = new Date().getMonth() + 1;
      }
      
      const daysInMonth = new Date(selectedYear, monthIndex, 0).getDate();

      // 生成该月所有天数的数据
      return Array.from({ length: daysInMonth }, (_, i) => ({
        date: `${selectedYear}-${String(monthIndex).padStart(2, '0')}-${String(i + 1).padStart(2, '0')}`,
        value: 0
      }));
    } else {
      // 默认：显示最近30天的数据
      return Array.from({ length: 30 }, (_, i) => {
        const date = new Date();
        date.setDate(date.getDate() - (29 - i));
        return {
          date: date.toISOString().split('T')[0],
          value: 0
        };
      });
    }
  };

  // 合并真实数据和完整时间序列
  const mergeDataWithTimeSeries = () => {
    const completeTimeSeries = generateCompleteTimeSeriesData();

    if (!data || data.length === 0) {
      // 没有真实数据，返回完整的0值时间序列
      console.log('No real data, using complete time series with zero values');
      return completeTimeSeries;
    }

    // 有真实数据，合并到完整时间序列中
    const dataMap = new Map(data.map(item => [item.date, Number(item.value) || 0]));

    return completeTimeSeries.map(timePoint => ({
      date: timePoint.date,
      value: dataMap.get(timePoint.date) || 0
    }));
  };

  const chartData = mergeDataWithTimeSeries();

  // 添加调试日志
  console.log('ChartCard Debug:', {
    title,
    viewMode,
    selectedYear,
    selectedMonth,
    originalData: data,
    originalDataLength: data?.length || 0,
    chartData: chartData,
    chartDataLength: chartData.length,
    hasValidData: chartData.some(item => item.value > 0)
  });

  // 确保数据格式正确并预处理X轴标签
  const validatedData = chartData.map(item => {
    const originalDate = String(item.date);
    let displayDate = originalDate;

    // 根据viewMode格式化显示的日期
    const match = originalDate.match(/(\d{4})-(\d{2})-(\d{2})/);
    if (match) {
      const year = match[1];
      const month = match[2];
      const day = match[3];

      if (viewMode === 'year') {
        // 年视图：显示 YY-MM
        displayDate = `${year.slice(-2)}-${month}`;
      } else if (viewMode === 'month') {
        // 月视图：显示 MM-DD
        displayDate = `${month}-${day}`;
      } else {
        // 默认：显示 MM-DD
        displayDate = `${month}-${day}`;
      }
    }

    return {
      date: displayDate, // 使用格式化后的日期作为显示
      originalDate: originalDate, // 保留原始日期用于tooltip
      value: Number(item.value) || 0
    };
  });

  // 检查是否所有数据都是0
  const hasRealData = validatedData.some(item => item.value > 0);
  // 检查最大值，用于设置Y轴范围
  const maxValue = Math.max(...validatedData.map(item => item.value));

  console.log('Validated data for chart:', validatedData);
  console.log('Has real data:', hasRealData);
  console.log('Max value:', maxValue);

  // ECharts配置
  const option = {
    grid: {
      left: '3%',
      right: '4%',
      bottom: validatedData.length > 15 ? '15%' : '8%', // 数据多时为倾斜标签留更多空间
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: validatedData.map(item => item.date),
      axisLabel: {
        fontSize: validatedData.length > 20 ? 10 : 12, // 数据多时使用更小字体
        color: '#6b7280',
        rotate: validatedData.length > 15 ? 45 : 0, // 数据多时倾斜显示
        interval: 0, // 显示所有标签
        margin: 8, // 增加标签与轴的距离
        // 自定义格式化函数，确保标签不会太长
        formatter: (value: string) => {
          if (validatedData.length > 25) {
            // 数据点很多时，只显示部分字符
            return value.length > 5 ? value.substring(0, 5) : value;
          }
          return value;
        }
      },
      axisLine: {
        lineStyle: {
          color: '#d1d5db'
        }
      },
      axisTick: {
        lineStyle: {
          color: '#d1d5db'
        }
      }
    },
    yAxis: {
      type: 'value',
      min: 0,
      max: maxValue === 0 ? 1 : undefined, // 当所有数据都是0时，设置最大值为1
      axisLabel: {
        fontSize: 12,
        color: '#6b7280',
        formatter: (value: number) => {
          // Format Y-axis labels based on chart type
          if (title.toLowerCase().includes('earning') || title.toLowerCase().includes('revenue')) {
            return `$${value.toLocaleString()}`;
          }
          return value.toLocaleString();
        }
      },
      axisLine: {
        lineStyle: {
          color: '#d1d5db'
        }
      },
      splitLine: {
        lineStyle: {
          color: '#f3f4f6'
        }
      }
    },
    series: [
      {
        data: validatedData.map(item => item.value),
        type: 'line',
        smooth: true,
        lineStyle: {
          color: hasRealData ? '#6b7280' : '#d1d5db',
          width: 2
        },
        itemStyle: {
          color: hasRealData ? '#6b7280' : '#d1d5db'
        },
        symbol: 'circle',
        symbolSize: hasRealData ? 4 : 2,
        areaStyle: null // 不填充区域
      }
    ],
    tooltip: {
      trigger: 'axis',
      backgroundColor: 'rgba(255, 255, 255, 0.9)',
      borderColor: '#d1d5db',
      borderWidth: 1,
      textStyle: {
        color: '#374151'
      },
      formatter: (params: any) => {
        const data = params[0];
        const value = data.value || 0;
        const formattedValue = title.toLowerCase().includes('earning') || title.toLowerCase().includes('revenue')
          ? `$${value.toLocaleString()}`
          : value.toLocaleString();
        return `${data.name}<br/>${title}: ${formattedValue}`;
      }
    }
  };

  return (
    <Card className={styles.chartCard}>
      <div className={styles.chartContainer}>
        <div className={styles.chartHeader}>
          <h3>{title}</h3>
          {showTimeRangeControls && (
            <div className={styles.chartControls}>
              <Button
                size="small"
                type={timeRange === 'daily' ? 'primary' : 'default'}
                onClick={() => onTimeRangeChange('daily')}
              >
                daily
              </Button>
              <Button
                size="small"
                type={timeRange === 'weekly' ? 'primary' : 'default'}
                onClick={() => onTimeRangeChange('weekly')}
              >
                weekly
              </Button>
              <Button
                size="small"
                type={timeRange === 'monthly' ? 'primary' : 'default'}
                onClick={() => onTimeRangeChange('monthly')}
              >
                monthly
              </Button>
            </div>
          )}
        </div>
        <div className={styles.chart}>
          {loading ? (
            <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: 300 }}>
              <Spin size="large" />
            </div>
          ) : (
            <div style={{ width: '100%', height: 300, position: 'relative' }}>
              <ReactECharts option={option} style={{ height: '100%', width: '100%' }} />
              {!hasRealData && (
                <div style={{
                  position: 'absolute',
                  top: '50%',
                  left: '50%',
                  transform: 'translate(-50%, -50%)',
                  color: '#9ca3af',
                  fontSize: '14px',
                  pointerEvents: 'none',
                  zIndex: 10
                }}>
                  No data available for selected period
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </Card>
  );
}
