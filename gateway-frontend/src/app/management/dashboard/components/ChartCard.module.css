/* Chart Components - 优化版本 */
.chartCard {
  min-height: 320px;
  background: white;
  border-radius: 20px;
  border: none;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  overflow: hidden;
}

.chartCard:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

.chartContainer {
  padding: 24px;
}

.chartHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #f1f5f9;
}

.chartHeader h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
}

.chartControls {
  display: flex;
  gap: 4px;
  background: #f8fafc;
  padding: 4px;
  border-radius: 8px;
}

.chartControls button {
  font-size: 12px;
  font-weight: 500;
  height: 28px;
  padding: 0 12px;
  border-radius: 6px;
  border: none;
  transition: all 0.2s ease;
}

.chartControls button:hover {
  transform: translateY(-1px);
}

.chart {
  width: 100%;
  overflow-x: auto;
}
