'use client';

import { Card } from 'antd';
import styles from './EarningsCard.module.css';

interface EarningsCardProps {
  earnings: number;
}

export default function EarningsCard({ earnings }: EarningsCardProps) {
  return (
    <Card className={styles.earningsCard}>
      <div className={styles.earningsContent}>
        <div className={styles.earningsLabel}>
          SIGHT TOKENS
        </div>
        <div className={styles.earningsValue}>
          ${earnings.toFixed(1)}
        </div>
      </div>
    </Card>
  );
}
