/* Uptime Card - 优化版本 */
.uptimeCard {
  background: white;
  border-radius: 20px;
  border: none;
  min-height: 160px;
  padding: 24px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
}

.uptimeCard:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

.uptimeContent {
  display: flex;
  gap: 20px;
  align-items: flex-start;
  height: 100%;
}

.uptimeLeft {
  flex: 0 0 auto;
  /* width: 35%; */
  min-width: 160px;
}

.uptimeRight {
  flex: 1;
  display: flex;
  justify-content: flex-start;
  align-items: flex-start;
  padding-left: 20px;
}

.uptimeControls {
  display: flex;
  flex-direction: row;
  gap: 8px;
  width: 100%;
  margin-bottom: 20px;
}

.timeButtons {
  display: flex;
  gap: 4px;
  background: #f8fafc;
  padding: 4px;
  border-radius: 8px;
}

.timeButton {
  font-size: 12px;
  font-weight: 500;
  height: 28px;
  padding: 0 12px;
  border-radius: 6px;
  border: none;
  transition: all 0.2s ease;
}

.timeButton:hover {
  transform: translateY(-1px);
}

.monthGrid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 8px;
  max-width: 190px;
  width: 100%;
}

.monthItem {
  padding: 8px 12px;
  text-align: center;
  font-size: 11px;
  font-weight: 500;
  border-radius: 6px;
  cursor: pointer;
  background: #f8fafc;
  color: #64748b;
  transition: all 0.2s ease;
  min-height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid transparent;
}

.monthItem:hover {
  background: #e2e8f0;
  color: #475569;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.monthItem.active {
  background: #1e293b;
  color: white;
  border-color: #1e293b;
  box-shadow: 0 2px 8px rgba(30, 41, 59, 0.3);
}

/* 热力图容器和网格 */
.heatmapContainer {
  display: flex;
  justify-content: flex-start;
  align-items: flex-start;
  width: 100%;
}

.heatmapGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(20px, 1fr));
  gap: 20px;
  max-width: 400px;
  width: 100%;
}

/* 月视图：最多31天，使用7列布局 */
.heatmapGrid[data-view="month"] {
  grid-template-columns: repeat(7, 1fr);
  max-width: 280px;
}

/* 年视图：12个月，使用4列布局 */
.heatmapGrid[data-view="year"] {
  grid-template-columns: repeat(4, 1fr);
  max-width: 200px;
}

.heatmapCell {
  width: 20px;
  height: 20px;
  border-radius: 4px;
  background: #f5f5f5;
  transition: all 0.2s ease;
  cursor: pointer;
  position: relative;
  border: 1px solid transparent;
}

.heatmapCell:hover {
  transform: scale(1.2);
  box-shadow: 0 4px 16px rgba(0,0,0,0.2);
  z-index: 10;
  border: 1px solid rgba(0,0,0,0.1);
  filter: brightness(1.1);
}

.heatmapCell.empty {
  background: #e5e7eb;
}

.heatmapCell.low {
  background: #d1d5db;
}

.heatmapCell.medium {
  background: #6b7280;
}

.heatmapCell.high {
  background: #374151;
}
