'use client';

import { useState, useEffect } from 'react';
import { Row, Col, Spin, message } from 'antd';
import { EarningsCard, UptimeCalendar, ChartCard, SectionTitle } from './components';
import {
  getDashboardStats,
  getRevenueChartData,
  getRequestCountChartData,
  getChatHeatmapData
} from '@/api/dashboard';

import styles from './page.module.css';

export default function DashboardPage() {
  // 获取当前日期
  const getCurrentDate = () => {
    const now = new Date();
    const currentYear = now.getFullYear();
    const currentMonth = now.getMonth(); // 0-11
    const monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
    return {
      year: currentYear,
      month: monthNames[currentMonth]
    };
  };

  const currentDate = getCurrentDate();

  const [loading, setLoading] = useState(true);
  const [timeRange, setTimeRange] = useState('30'); // Default to 30 days
  const [selectedYear, setSelectedYear] = useState(currentDate.year);
  const [selectedMonth, setSelectedMonth] = useState(currentDate.month);
  const [viewMode, setViewMode] = useState('month'); // 'month' 或 'year'

  const [stats, setStats] = useState({
    earnings: 0,
    totalRequests: 0,
    earningStats: 0
  });

  // 初始化为空数组，图表组件会处理空数据状态
  const [requestData, setRequestData] = useState<Array<{ date: string; value: number }>>([]);
  const [earningData, setEarningData] = useState<Array<{ date: string; value: number }>>([]);

  // 聊天热力图数据
  const [chatHeatmapData, setChatHeatmapData] = useState<Array<{ date: string; count: number }>>([]);

  // 获取月份索引的辅助函数
  const getMonthIndex = (monthName: string): number => {
    const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
    return months.indexOf(monthName);
  };



  // Load dashboard data
  const loadDashboardData = async () => {
    setLoading(true);
    try {
      // Prepare date parameters
      const dateParams = {
        timeRange: timeRange,
        year: selectedYear,
        month: viewMode === 'month' ? getMonthIndex(selectedMonth) + 1 : undefined
      };

      // Load stats
      const statsResponse = await getDashboardStats(timeRange, selectedYear, viewMode === 'month' ? getMonthIndex(selectedMonth) + 1 : undefined);
      setStats({
        earnings: statsResponse.data.earnings || 0,
        totalRequests: statsResponse.data.tasks || 0,
        earningStats: statsResponse.data.earnings || 0
      });

      // Load chart data using new API endpoints
      const [revenueResponse, requestCountResponse, chatHeatmapResponse] = await Promise.all([
        getRevenueChartData(dateParams),
        getRequestCountChartData(dateParams),
        getChatHeatmapData(dateParams)
      ]);

      setEarningData(revenueResponse.data || []);
      setRequestData(requestCountResponse.data || []);
      setChatHeatmapData(chatHeatmapResponse.data || []);

    } catch (error) {
      console.error('Error loading dashboard data:', error);
      message.error('Failed to load dashboard data');
    } finally {
      setLoading(false);
    }
  };

  // Handle time range change from UptimeCalendar
  const handleUptimeTimeRangeChange = (newTimeRange: string) => {
    setTimeRange(newTimeRange);
  };

  useEffect(() => {
    loadDashboardData();
  }, [timeRange, selectedYear, selectedMonth, viewMode]);

  if (loading) {
    return (
      <div className={styles.container}>
        <div className={styles.loadingContainer}>
          <Spin size="large" />
        </div>
      </div>
    );
  }



  // 处理时间范围变化
  const handleTimeRangeChange = (range: string) => {
    setTimeRange(range);
  };

  // 处理年份变化
  const handleYearChange = (year: number) => {
    setSelectedYear(year);
  };

  // 处理月份变化
  const handleMonthChange = (month: string) => {
    setSelectedMonth(month);
  };

  // 处理视图模式变化
  const handleViewModeChange = (mode: string) => {
    setViewMode(mode);
  };

  return (
    <div className={styles.container}>
      {/* 头部两个模块 */}
      <Row gutter={[24, 24]} className={styles.topSection}>
        <Col xs={24} lg={8}>
          <div className={styles.earningsSection}>
            <SectionTitle title="Earnings" />
            <EarningsCard earnings={stats.earnings} />
          </div>
        </Col>
        <Col xs={24} lg={16}>
          <div className={styles.uptimeSection}>
            <SectionTitle title="Uptime" />
            <UptimeCalendar
              selectedYear={selectedYear}
              selectedMonth={selectedMonth}
              viewMode={viewMode}
              onYearChange={handleYearChange}
              onMonthChange={handleMonthChange}
              onViewModeChange={handleViewModeChange}
              onTimeRangeChange={handleUptimeTimeRangeChange}
              currentTimeRange={timeRange}
              chatData={chatHeatmapData}
            />
          </div>
        </Col>
      </Row>

      {/* AI 请求处理图表 */}
      <Row gutter={[24, 24]} className={styles.chartsRow}>
        <Col xs={24}>
          <div className={styles.chartSection}>
            <ChartCard
              title="Total AI Requests Processed"
              data={requestData}
              timeRange={timeRange}
              onTimeRangeChange={handleTimeRangeChange}
              loading={loading}
              showTimeRangeControls={false}
              viewMode={viewMode}
              selectedYear={selectedYear}
              selectedMonth={selectedMonth}
            />
          </div>
        </Col>
      </Row>

      {/* 收益统计图表 */}
      <Row gutter={[24, 24]} className={styles.chartsRow}>
        <Col xs={24}>
          <div className={styles.chartSection}>
            <ChartCard
              title="Earning Statistics"
              data={earningData}
              timeRange={timeRange}
              onTimeRangeChange={handleTimeRangeChange}
              loading={loading}
              showTimeRangeControls={false}
              viewMode={viewMode}
              selectedYear={selectedYear}
              selectedMonth={selectedMonth}
            />
          </div>
        </Col>
      </Row>
    </div>
  );
}
