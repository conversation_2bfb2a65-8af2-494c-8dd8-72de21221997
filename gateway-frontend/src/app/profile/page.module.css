.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 24px;
}

.profileHeader {
  margin-bottom: 24px;
}

.profileCard {
  display: flex;
  align-items: center;
  padding: 32px;
  background: linear-gradient(135deg, #f0f4ff 0%, #e6edff 100%);
  border: none;
  border-radius: 16px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.avatarSection {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-right: 48px;
}

.uploadButton {
  margin-top: 16px;
}

.userInfo {
  flex: 1;
}

.username {
  font-size: 24px;
  font-weight: 600;
  color: #1a1a1a;
  margin: 0 0 8px 0;
}

.userRole {
  font-size: 16px;
  color: #666;
  margin: 0;
}

.settingsCard {
  background: #fff;
  border-radius: 16px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.settingsCard :global(.ant-tabs-nav) {
  margin-bottom: 24px;
}

.settingsCard :global(.ant-tabs-tab) {
  padding: 12px 24px;
  font-size: 16px;
}

.settingsCard :global(.ant-form-item-label) {
  font-weight: 500;
}

.settingsCard :global(.ant-input-affix-wrapper) {
  padding: 8px 12px;
}

.settingsCard :global(.ant-btn) {
  height: 40px;
  padding: 0 24px;
  font-size: 16px;
}

@media (max-width: 768px) {
  .container {
    padding: 16px;
  }

  .profileCard {
    flex-direction: column;
    text-align: center;
    padding: 24px;
  }

  .avatarSection {
    margin-right: 0;
    margin-bottom: 24px;
  }
} 