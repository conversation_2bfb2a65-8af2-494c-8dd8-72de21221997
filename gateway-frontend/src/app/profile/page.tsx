'use client';

import { Card, Avatar, Tabs, Form, Input, Button, message, Upload } from 'antd';
import { UserOutlined, UploadOutlined, LockOutlined, MailOutlined, PhoneOutlined } from '@ant-design/icons';
import { useLocaleContext } from '@/contexts/LocaleContext';
import styles from './page.module.css';
import { useState } from 'react';

const { TabPane } = Tabs;

export default function ProfilePage() {
  const { messages } = useLocaleContext();
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);

  const handleUpdateProfile = async (values: any) => {
    setLoading(true);
    try {
      // TODO: 实现更新个人信息的API调用
      console.log('Update profile:', values);
      message.success(messages.profile.messages.updateSuccess);
    } catch (error) {
      message.error(messages.profile.messages.updateFailed);
    } finally {
      setLoading(false);
    }
  };

  const handleUpdatePassword = async (values: any) => {
    setLoading(true);
    try {
      // TODO: 实现更新密码的API调用
      console.log('Update password:', values);
      message.success(messages.profile.messages.passwordUpdateSuccess);
    } catch (error) {
      message.error(messages.profile.messages.passwordUpdateFailed);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className={styles.container}>
      <div className={styles.profileHeader}>
        <Card className={styles.profileCard}>
          <div className={styles.avatarSection}>
            <Avatar
              size={120}
              src="https://gw.alipayobjects.com/zos/antfincdn/efFD%24IOql2/weixintupian_20170331104822.jpg"
              icon={<UserOutlined />}
            />
            <Upload className={styles.uploadButton}>
              <Button icon={<UploadOutlined />}>{messages.profile.avatar.change}</Button>
            </Upload>
          </div>
          <div className={styles.userInfo}>
            <h1 className={styles.username}>{messages.header.user}</h1>
            <p className={styles.userRole}>{messages.profile.role}</p>
          </div>
        </Card>
      </div>

      <Card className={styles.settingsCard}>
        <Tabs defaultActiveKey="1">
          <TabPane tab={messages.profile.basicInfo} key="1">
            <Form
              form={form}
              layout="vertical"
              onFinish={handleUpdateProfile}
              initialValues={{
                email: '<EMAIL>',
                phone: '13800138000',
                bio: '这是一段个人简介'
              }}
            >
              <Form.Item
                name="email"
                label={messages.profile.form.email}
                rules={[{ required: true, message: messages.profile.form.emailPlaceholder }]}
              >
                <Input prefix={<MailOutlined />} placeholder={messages.profile.form.emailPlaceholder} />
              </Form.Item>
              <Form.Item
                name="phone"
                label={messages.profile.form.phone}
                rules={[{ required: true, message: messages.profile.form.phonePlaceholder }]}
              >
                <Input prefix={<PhoneOutlined />} placeholder={messages.profile.form.phonePlaceholder} />
              </Form.Item>
              <Form.Item
                name="bio"
                label={messages.profile.form.bio}
              >
                <Input.TextArea rows={4} placeholder={messages.profile.form.bioPlaceholder} />
              </Form.Item>
              <Form.Item>
                <Button type="primary" htmlType="submit" loading={loading} className='button'>
                  {messages.profile.form.save}
                </Button>
              </Form.Item>
            </Form>
          </TabPane>
          <TabPane tab={messages.profile.security} key="2">
            <Form
              layout="vertical"
              onFinish={handleUpdatePassword}
            >
              <Form.Item
                name="oldPassword"
                label={messages.profile.form.oldPassword}
                rules={[{ required: true, message: messages.profile.form.oldPasswordPlaceholder }]}
              >
                <Input.Password prefix={<LockOutlined />} placeholder={messages.profile.form.oldPasswordPlaceholder} />
              </Form.Item>
              <Form.Item
                name="newPassword"
                label={messages.profile.form.newPassword}
                rules={[{ required: true, message: messages.profile.form.newPasswordPlaceholder }]}
              >
                <Input.Password prefix={<LockOutlined />} placeholder={messages.profile.form.newPasswordPlaceholder} />
              </Form.Item>
              <Form.Item
                name="confirmPassword"
                label={messages.profile.form.confirmPassword}
                dependencies={['newPassword']}
                rules={[
                  { required: true, message: messages.profile.form.confirmPasswordPlaceholder },
                  ({ getFieldValue }) => ({
                    validator(_, value) {
                      if (!value || getFieldValue('newPassword') === value) {
                        return Promise.resolve();
                      }
                      return Promise.reject(new Error(messages.profile.messages.passwordMismatch));
                    },
                  }),
                ]}
              >
                <Input.Password prefix={<LockOutlined />} placeholder={messages.profile.form.confirmPasswordPlaceholder} />
              </Form.Item>
              <Form.Item>
                <Button type="primary" htmlType="submit" loading={loading} className='button'>
                  {messages.profile.form.updatePassword}
                </Button>
              </Form.Item>
            </Form>
          </TabPane>
        </Tabs>
      </Card>
    </div>
  );
} 