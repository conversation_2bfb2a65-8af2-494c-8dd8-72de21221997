'use client';

import styles from './page.module.css';
import { useRouter } from 'next/navigation';
import Header from '@/components/Header';
import Image9 from '../../public/Frame 318.png';
import Image10 from '../../public/Frame 319.png';
import Image13 from '../../public/Frame 320.png';
import Image11 from '../../public/Frame 321.png';
import Image12 from '../../public/Frame 322.png';
import Vector41 from '../../public/Vector 41.png';
import SightSpace from '../../public/SIGHT · SPACE.png';
import Tunnel from '../../public/Tunnel.png';


export default function HomePage() {
  const router = useRouter();

  // Card click handler
  const handleCardClick = (index: number) => {
    switch (index) {
      case 1:
        router.push('/management/dashboard');
        break;
      case 2:
        router.push('/api-keys');
        break;
      case 3:
        router.push('/earnings');
        break;
      default:
        break;
    }
  };

  return (
    <div className={styles.bg3d} style={{ backgroundImage: `url(${Tunnel.src})` }}>
      {/* Use the Header component with isHomePage prop */}
      <Header isHomePage={false} />
      {/* 中心内容 */}
      <div className={styles.centerBox}>
        <img src={SightSpace.src} alt="title" className={styles.title} />
        <div className={styles.cardContainer}>
          <img src={Vector41.src} alt="" className={styles.bgImg} />
          <div className={styles.cardRow}>
            <div className={`${styles.card} ${styles.cardGradient1}`} style={{ backgroundImage: `url(${Image9.src})` }} onClick={() => handleCardClick(0)}>
              <img src={Image9.src} style={{ width: '100%', height: '100%' }} />
            </div>
            <div className={`${styles.card} ${styles.cardGradient2}`} style={{ backgroundImage: `url(${Image10.src})` }} onClick={() => handleCardClick(1)}>
              <img src={Image10.src} style={{ width: '100%', height: '100%' }} />
            </div>
            <div className={`${styles.card} ${styles.cardGradient3}`} style={{ backgroundImage: `url(${Image13.src})` }} onClick={() => handleCardClick(2)}>
              <img src={Image13.src} style={{ width: '100%', height: '100%' }} />
            </div>
            <div className={`${styles.card} ${styles.cardGradient4}`} style={{ backgroundImage: `url(${Image11.src})` }} onClick={() => handleCardClick(3)}>
              <img src={Image11.src} style={{ width: '100%', height: '100%' }} />
            </div>
            <div className={`${styles.card} ${styles.cardGradient5}`} style={{ backgroundImage: `url(${Image12.src})` }} onClick={() => handleCardClick(4)}>
              <img src={Image12.src} style={{ width: '100%', height: '100%' }} />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
