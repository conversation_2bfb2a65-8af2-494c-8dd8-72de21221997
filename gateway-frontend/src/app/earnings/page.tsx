'use client';

import { Table, Button, Typography, Select, Spin, message, Tag, Space, TableColumnProps } from 'antd';
import { useLocaleContext } from '@/contexts/LocaleContext';
import styles from './page.module.css';
import { useState, useEffect } from 'react';
import { PlusOutlined, ReloadOutlined } from '@ant-design/icons';
import { useRouter } from 'next/navigation';
import {
  getEarningsStats,
  getServerStatus,
  getTaskQueue,
  getNodePerformance,
  ServerStatusData,
  TaskQueueData,
  NodePerformanceData,
  StatsData
} from '@/api/earnings';

const { Title, Text, Link } = Typography;
const { Option } = Select;

export default function EarningsPage() {
  const { messages } = useLocaleContext();
  const router = useRouter();

  const [timeRange, setTimeRange] = useState('30');

  // Data states
  const [statsData, setStatsData] = useState<StatsData | null>(null);
  const [serverStatusData, setServerStatusData] = useState<ServerStatusData[]>([]);
  const [taskQueueData, setTaskQueueData] = useState<TaskQueueData[]>([]);
  const [taskQueueTotal, setTaskQueueTotal] = useState(0);
  const [nodePerformanceData, setNodePerformanceData] = useState<NodePerformanceData[]>([]);
  const [nodePerformanceTotal, setNodePerformanceTotal] = useState(0);

  // Loading states
  const [loadingStats, setLoadingStats] = useState(false);
  const [loadingServerStatus, setLoadingServerStatus] = useState(false);
  const [loadingTaskQueue, setLoadingTaskQueue] = useState(false);
  const [loadingNodePerformance, setLoadingNodePerformance] = useState(false);

  // Pagination states
  const [taskQueuePage, setTaskQueuePage] = useState(1);
  const [taskQueuePageSize, setTaskQueuePageSize] = useState(10);
  const [nodePerformancePage, setNodePerformancePage] = useState(1);
  const [nodePerformancePageSize, setNodePerformancePageSize] = useState(10);



  // Fetch data functions
  const fetchStats = async () => {
    setLoadingStats(true);
    try {
      const response = await getEarningsStats(timeRange);
      setStatsData(response.data);
    } catch (error) {
      console.error('Error fetching stats:', error);
      message.error('Failed to load earnings statistics');
    } finally {
      setLoadingStats(false);
    }
  };

  // Refresh all data with notification
  const refreshAllData = () => {
    message.info(messages.common?.loading || 'Loading...');
    Promise.all([
      fetchStats(),
      fetchServerStatus(),
      fetchTaskQueue(),
      fetchNodePerformance()
    ]).then(() => {
      message.success(messages.common?.refreshSuccess || 'Data refreshed successfully');
    }).catch(() => {
      // Error messages are already shown in individual fetch functions
    });
  };

  const fetchServerStatus = async () => {
    setLoadingServerStatus(true);
    try {
      const response = await getServerStatus(timeRange);
      setServerStatusData(response.data);
    } catch (error) {
      console.error('Error fetching server status:', error);
      message.error('Failed to load server status');
    } finally {
      setLoadingServerStatus(false);
    }
  };

  const fetchTaskQueue = async () => {
    setLoadingTaskQueue(true);
    try {
      const response = await getTaskQueue({
        page: taskQueuePage,
        pageSize: taskQueuePageSize,
        timeRange: timeRange
      });
      setTaskQueueData(response.data.data);
      setTaskQueueTotal(response.data.total);
    } catch (error) {
      console.error('Error fetching task queue:', error);
      message.error('Failed to load task queue');
    } finally {
      setLoadingTaskQueue(false);
    }
  };

  const fetchNodePerformance = async () => {
    setLoadingNodePerformance(true);
    try {
      const response = await getNodePerformance({
        page: nodePerformancePage,
        pageSize: nodePerformancePageSize,
        timeRange: timeRange
      });
      setNodePerformanceData(response.data.data);
      setNodePerformanceTotal(response.data.total);
    } catch (error) {
      console.error('Error fetching node performance:', error);
      message.error('Failed to load node performance');
    } finally {
      setLoadingNodePerformance(false);
    }
  };



  // Load data on component mount
  useEffect(() => {
    fetchStats();
    fetchServerStatus();
    fetchTaskQueue();
    fetchNodePerformance();
  }, []);

  // Reload data when time range changes
  useEffect(() => {
    fetchStats();
    fetchServerStatus();
    fetchTaskQueue();
    fetchNodePerformance();
  }, [timeRange]);



  // Reload task queue when pagination changes
  useEffect(() => {
    fetchTaskQueue();
  }, [taskQueuePage, taskQueuePageSize]);

  // Reload node performance when pagination changes
  useEffect(() => {
    fetchNodePerformance();
  }, [nodePerformancePage, nodePerformancePageSize]);

  // Format stats data for display
  const stats = [
    {
      label: messages.nodes.stats.earnings,
      value: statsData ? statsData.earnings.toLocaleString() : '6,000,000',
      displayValue: statsData ? statsData.earnings.toLocaleString() : '6,000,000'
    },
    {
      label: messages.nodes.stats.tasks,
      value: statsData ? statsData.tasks.toString() : '907,462',
      displayValue: statsData ? statsData.tasks.toLocaleString() : '907,462'
    },
    {
      label: messages.nodes.stats.nodes,
      value: statsData ? statsData.nodes.toString() : '600',
      displayValue: statsData ? statsData.nodes.toLocaleString() : '600'
    }
  ];

  const gatewayStatusColumns: TableColumnProps<ServerStatusData>[] = [
    { title: messages.nodes.table.gateway.server, dataIndex: 'server', key: 'server', align: 'center' },
    {
      title: messages.nodes.table.gateway.apiLoad,
      dataIndex: 'apiLoad',
      key: 'apiLoad',
      align: 'center',
      render: (value: number) => (
        <div className={styles.progressBar}>
          <div
            className={styles.progressBarInner}
            style={{ width: `${value}%` }}
          />
        </div>
      )
    },
    {
      title: messages.nodes.table.gateway.wsStatus,
      dataIndex: 'wsStatus',
      key: 'wsStatus',
      render: (status: string) => {
        let statusClass = styles.statusTagConnected;
        let dotClass = styles.statusDotGreen;

        if (status === 'Minor Latency') {
          statusClass = styles.statusTagMinor;
          dotClass = styles.statusDotYellow;
        }

        return (
          <span className={`${styles.statusTag} ${statusClass}`}>
            <span className={`${styles.statusDot} ${dotClass}`}></span>
            {status}
          </span>
        );
      }
    },
    { title: messages.nodes.table.gateway.taskTime, dataIndex: 'taskTime', key: 'taskTime', align: 'center' },
    {
      title: messages.nodes.table.gateway.health,
      dataIndex: 'health',
      key: 'health',
      align: 'center',
      render: (status: string) => {
        let statusClass = styles.statusTagNormal;
        let dotClass = styles.statusDotGreen;

        if (status === 'High Load') {
          statusClass = styles.statusTagHigh;
          dotClass = styles.statusDotYellow;
        }

        return (
          <span className={`${styles.statusTag} ${statusClass}`}>
            <span className={`${styles.statusDot} ${dotClass}`}></span>
            {status}
          </span>
        );
      }
    },
  ];

  const taskQueueColumns: TableColumnProps<TaskQueueData>[] = [
    { title: messages.nodes.table.task.requestId, dataIndex: 'id', key: 'id', align: 'center' },
    {
      title: messages.nodes.table.task.model,
      dataIndex: 'model',
      key: 'model',
      align: 'center',
      render: (model: string) => (
        <span className={styles.modelTag}>{model}</span>
      )
    },
    {
      title: messages.nodes.table.task.status,
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => {
        let statusClass = styles.statusTagPending;
        let dotClass = styles.statusDotYellow;

        if (status === 'Completed') {
          statusClass = styles.statusTagCompleted;
          dotClass = styles.statusDotGreen;
        } else if (status === 'Processing') {
          statusClass = styles.statusTagProcessing;
          dotClass = styles.statusDotGreen;
        } else if (status === 'Failed') {
          statusClass = styles.statusTagFailed;
          dotClass = styles.statusDotRed;
        }

        return (
          <span className={`${styles.statusTag} ${statusClass}`}>
            <span className={`${styles.statusDot} ${dotClass}`}></span>
            {status}
          </span>
        );
      }
    },
    { title: messages.nodes.table.task.queueTime, dataIndex: 'queue', key: 'queue', align: 'center' },
    { title: messages.nodes.table.task.deviceId, dataIndex: 'device', key: 'device', align: 'center' },
    { title: messages.nodes.table.task.processTime, dataIndex: 'ptime', key: 'ptime', align: 'center' },
    { title: messages.nodes.table.task.earnings, dataIndex: 'earn', key: 'earn', align: 'center' },
  ];
  // Handle view device details
  const handleViewDevice = (deviceId: string) => {
    router.push(`/nodes/device/${deviceId}`);
  };
  const nodePerfColumns: TableColumnProps<NodePerformanceData>[] = [
    { title: messages.nodes.table.performance.deviceId, dataIndex: 'id', key: 'id', align: 'center' },
    { title: messages.nodes.table.performance.deviceType, dataIndex: 'type', key: 'type', align: 'center' },
    { title: messages.nodes.table.performance.currentTasks, dataIndex: 'tasks', key: 'tasks', align: 'center' },
    {
      title: messages.nodes.management.table.status,
      dataIndex: 'status',
      key: 'status',
      // render: (status: DeviceStatus) => (
      //   <Tag color={statusColors[status] || 'default'}>
      //     {messages.nodes.management.status[status]}
      //   </Tag>
      // ),
      render: (status: string) => {
        let statusClass = styles.statusTagPending;
        let dotClass = styles.statusDotYellow;

        if (status === 'connected') {
          statusClass = styles.statusTagCompleted;
          dotClass = styles.statusDotGreen;
        } else if (status === 'waiting') {
          statusClass = styles.statusTagProcessing;
          dotClass = styles.statusDotGreen;
        } else if (status === 'failed') {
          statusClass = styles.statusTagFailed;
          dotClass = styles.statusDotRed;
        }

        return (
          <span className={`${styles.statusTag} ${statusClass}`}>
            <span className={`${styles.statusDot} ${dotClass}`}></span>
            {status}
          </span>
        );
      }
    },
    {
      title: messages.nodes.table.performance.memoryUsage,
      dataIndex: 'mem',
      key: 'mem',
      align: 'center',
      render: (value: number) => (
        <div className={styles.progressBar}>
          <div
            className={styles.progressBarInner}
            style={{ width: `${value}%` }}
          />
        </div>
      )
    },

    {
      title: messages.nodes.table.performance.completionRate,
      dataIndex: 'rate',
      key: 'rate',
      align: 'center',
      render: (value: number) => (
        <div className={styles.progressBar}>
          <div
            className={styles.progressBarInner}
            style={{ width: `${value}%` }}
          />
        </div>
      )
    },
    { title: messages.nodes.table.performance.totalEarnings, dataIndex: 'earn', key: 'earn', align: 'center' },
    {
      title: messages.nodes.management.table.actions,
      key: 'action',
      render: (_: any, record: any) => (
        <Space size="middle">
          <Button style={{ color: '#a01ec1' }} onClick={() => handleViewDevice(record.id || '')}>
            {messages.nodes.management.table.view}
          </Button>
        </Space>
      ),
    },
  ];
  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <div>
          <Text type="secondary">
          </Text>
        </div>
        <div className={styles.headerActions}>
          {/* <Button   style={{ width: 'auto', marginRight: 16 }} type="primary" icon={<PlusOutlined />} className='button' onClick={() => setAddDeviceModalVisible(true)}>
            {messages.nodes.management.table.addDevice}
          </Button> */}
          <Select
            defaultValue="30"
            style={{ width: 150, marginRight: 16 }}
            onChange={(value) => setTimeRange(value)}
          >
            <Option value="7">{messages.reports?.timeRange?.days7}</Option>
            <Option value="30">{messages.reports?.timeRange?.days30}</Option>
            <Option value="90">{messages.reports?.timeRange?.days90}</Option>
            <Option value="365">{messages.reports?.timeRange?.year}</Option>
          </Select>
          <Button
            icon={<ReloadOutlined />}
            onClick={refreshAllData}
            loading={loadingStats || loadingServerStatus || loadingTaskQueue || loadingNodePerformance}
          >
            {messages.apiKeys?.table?.refresh || 'Refresh'}
          </Button>
        </div>
      </div>

      <div className={styles.statsRow}>
        {stats.map((s, i) => (
          <div key={i} className={styles.statCard}>
            <div className={styles.statLabel}>{s.label}</div>
            <div className={styles.statValue}>
              {loadingStats ? <Spin size="small" /> : s.displayValue}
            </div>
          </div>
        ))}
      </div>

      <div className={styles.section}>
        <div className={styles.sectionHeader}>
          <Title level={5}>{messages.nodes.sections.gatewayStatus}</Title>
        </div>
        <Table
          columns={gatewayStatusColumns}
          dataSource={serverStatusData.length > 0 ? serverStatusData.map((item, index) => ({ ...item, key: index })) : []}
          pagination={false}
          size="middle"
          className={styles.table}
          loading={loadingServerStatus}
        />
      </div>

      <div className={styles.section}>
        <div className={styles.sectionHeader}>
          <Title level={5}>{messages.nodes.sections.taskQueue}</Title>
          <Link className={styles.viewAllLink} onClick={() => router.push('/earnings/task-queue')}>
            View all
          </Link>
        </div>
        <Table
          columns={taskQueueColumns}
          dataSource={taskQueueData.length > 0 ? taskQueueData.map((item, index) => ({ ...item, key: index })) : []}
          // pagination={{
          //   position:[ 'bottomCenter'],
          //   current: taskQueuePage,
          //   pageSize: taskQueuePageSize,
          //   total: taskQueueTotal,
          //   onChange: (page, pageSize) => {
          //     setTaskQueuePage(page);
          //     if (pageSize) setTaskQueuePageSize(pageSize);
          //   }
          // }}
          pagination={false}
          size="middle"
          className={styles.table}
          loading={loadingTaskQueue}
        />
      </div>

      <div className={styles.section}>
        <div className={styles.sectionHeader}>
          <Title level={5}>{messages.nodes.sections.nodePerformance}</Title>
          <Link className={styles.viewAllLink} onClick={() => router.push('/earnings/node-performance')}>
            View all
          </Link>
        </div>
        <Table
          columns={nodePerfColumns}
          dataSource={nodePerformanceData.length > 0 ? nodePerformanceData.map((item, index) => ({ ...item, key: index })) : []}
          // pagination={{
          //   current: nodePerformancePage,
          //   pageSize: nodePerformancePageSize,
          //   position:[ 'bottomCenter'],
          //   total: nodePerformanceTotal,
          //   onChange: (page, pageSize) => {
          //     setNodePerformancePage(page);
          //     if (pageSize) setNodePerformancePageSize(pageSize);
          //   }
          // }}
          pagination={false}
          size="middle"
          className={styles.table}
          loading={loadingNodePerformance}
        />
      </div>

    </div>
  );
}