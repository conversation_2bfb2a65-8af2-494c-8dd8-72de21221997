.container {
  padding: 24px;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.titleSection {
  display: flex;
  flex-direction: column;
}

.backButton {
  margin-bottom: 15px;
  /* padding: 0; */
  position: relative;
  left: -15px;
  /* font-weight: 500; */
  color: #6D20F5;
}

.headerActions {
  display: flex;
  align-items: center;
}

.table {
  background: white;
  border-radius: 8px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

/* Status tags */
.statusTag {
  display: inline-flex;
  align-items: center;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  line-height: 1;
}

.statusDot {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  margin-right: 6px;
}

.statusTagPending {
  color: #D46B08;
}

.statusTagProcessing {
  color: #1890FF;
}

.statusTagCompleted {
  color: #52C41A;
}

.statusTagFailed {
  color: #F5222D;
}

.statusDotRed {
  background-color: #F5222D;
}

.statusDotGreen {
  background-color: #52C41A;
}

.statusDotYellow {
  background-color: #FAAD14;
}

.modelTag {
  display: inline-block;
  padding: 4px 8px;
  background-color: #F0F0F0;
  border-radius: 4px;
  font-size: 12px;
  color: #333;
}

/* Progress bar */
.progressBar {
  width: 100%;
  height: 8px;
  background-color: #F5F5F5;
  border-radius: 4px;
  overflow: hidden;
}

.progressBarInner {
  height: 100%;
  background: linear-gradient(90deg, #6D20F5 0%, #E7337A 100%);
  border-radius: 4px;
}
