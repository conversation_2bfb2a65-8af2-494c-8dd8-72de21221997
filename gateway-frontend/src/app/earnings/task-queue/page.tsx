'use client';

import { Table, Typography, Select, Button, message, TableColumnProps } from 'antd';
import { useLocaleContext } from '@/contexts/LocaleContext';
import styles from './page.module.css';
import { useState, useEffect } from 'react';
import { ArrowLeftOutlined, ReloadOutlined } from '@ant-design/icons';
import { getTaskQueue, TaskQueueData } from '@/api/earnings';
import { useRouter } from 'next/navigation';

const { Title, Text } = Typography;
const { Option } = Select;

export default function TaskQueuePage() {
  const { messages } = useLocaleContext();
  const router = useRouter();
  const [timeRange, setTimeRange] = useState('30');

  // Data states
  const [taskQueueData, setTaskQueueData] = useState<TaskQueueData[]>([]);
  const [taskQueueTotal, setTaskQueueTotal] = useState(0);

  // Loading states
  const [loadingTaskQueue, setLoadingTaskQueue] = useState(false);

  // Pagination states
  const [taskQueuePage, setTaskQueuePage] = useState(1);
  const [taskQueuePageSize, setTaskQueuePageSize] = useState(10);

  // Fetch task queue data
  const fetchTaskQueue = async (showNotification = false) => {
    setLoadingTaskQueue(true);
    if (showNotification) {
      message.info(messages.common?.loading || 'Loading...');
    }
    try {
      const response = await getTaskQueue({
        page: taskQueuePage,
        pageSize: taskQueuePageSize,
        timeRange: timeRange
      });
      setTaskQueueData(response.data.data);
      setTaskQueueTotal(response.data.total);
      if (showNotification) {
        message.success(messages.common?.refreshSuccess || 'Data refreshed successfully');
      }
    } catch (error) {
      console.error('Error fetching task queue:', error);
      message.error('Failed to load task queue');
    } finally {
      setLoadingTaskQueue(false);
    }
  };

  // Handle refresh button click
  const handleRefresh = () => {
    fetchTaskQueue(true);
  };

  // Load data on component mount
  useEffect(() => {
    fetchTaskQueue();
  }, []);

  // Reload data when time range changes
  useEffect(() => {
    fetchTaskQueue();
  }, [timeRange]);

  // Reload task queue when pagination changes
  useEffect(() => {
    fetchTaskQueue();
  }, [taskQueuePage, taskQueuePageSize]);

  const taskQueueColumns: TableColumnProps<TaskQueueData>[] = [
    { title: messages.nodes.table.task.requestId, dataIndex: 'id', key: 'id', align: 'center' },
    {
      title: messages.nodes.table.task.model,
      dataIndex: 'model',
      align: 'center',
      key: 'model',
      render: (model: string) => (
        <span className={styles.modelTag}>{model}</span>
      )
    },
    {
      title: messages.nodes.table.task.status,
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => {
        let statusClass = styles.statusTagPending;
        let dotClass = styles.statusDotYellow;

        if (status === 'Completed') {
          statusClass = styles.statusTagCompleted;
          dotClass = styles.statusDotGreen;
        } else if (status === 'Processing') {
          statusClass = styles.statusTagProcessing;
          dotClass = styles.statusDotGreen;
        } else if (status === 'Failed') {
          statusClass = styles.statusTagFailed;
          dotClass = styles.statusDotRed;
        }

        return (
          <span className={`${styles.statusTag} ${statusClass}`}>
            <span className={`${styles.statusDot} ${dotClass}`}></span>
            {status}
          </span>
        );
      }
    },

    { title: messages.nodes.table.task.queueTime, dataIndex: 'queue', align: 'center', key: 'queue' },
    { title: messages.nodes.table.task.deviceId, dataIndex: 'device', align: 'center', key: 'device' },
    { title: messages.nodes.table.task.processTime, dataIndex: 'ptime', align: 'center', key: 'ptime' },
    { title: messages.nodes.table.task.earnings, dataIndex: 'earn', align: 'center', key: 'earn' },
  ];

  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <div className={styles.titleSection}>
          <Button
            icon={<ArrowLeftOutlined />}
            type="text"
            onClick={() => router.push('/earnings')}
            className={styles.backButton}
          >
            {messages.common?.back || 'Back'} {messages.nodes?.sections?.gatewayStatus && 'to gateway overall'}
          </Button>
          <Title level={3}>{messages.nodes?.sections?.taskQueue || 'Task Queue'}</Title>
        </div>
        <div className={styles.headerActions}>
          <Select
            defaultValue="30"
            style={{ width: 120, marginRight: 16 }}
            onChange={(value) => setTimeRange(value)}
            value={timeRange}
          >
            <Option value="7">{messages.reports?.timeRange?.days7}</Option>
            <Option value="30">{messages.reports?.timeRange?.days30}</Option>
            <Option value="90">{messages.reports?.timeRange?.days90}</Option>
            <Option value="365">{messages.reports?.timeRange?.year}</Option>
          </Select>
          <Button
            icon={<ReloadOutlined />}
            onClick={handleRefresh}
            loading={loadingTaskQueue}
          >
            {messages.apiKeys.table.refresh || 'Refresh'}
          </Button>
        </div>
      </div>

      <Table
        columns={taskQueueColumns}
        dataSource={taskQueueData.length > 0 ? taskQueueData.map((item, index) => ({ ...item, key: index })) : []}
        pagination={{
          current: taskQueuePage,
          position: ['bottomCenter'],
          pageSize: taskQueuePageSize,
          total: taskQueueTotal,
          onChange: (page, pageSize) => {
            setTaskQueuePage(page);
            if (pageSize) setTaskQueuePageSize(pageSize);
          },
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total) => `Total ${total} items`,
        }}
        size="middle"
        className={styles.table}
        loading={loadingTaskQueue}
        scroll={{ x: 'max-content' }}
      />
    </div>
  );
}
