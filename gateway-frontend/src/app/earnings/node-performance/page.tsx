'use client';

import { Table, Typography, Select, Button, message, Tag, Space, TableColumnProps } from 'antd';
import { useLocaleContext } from '@/contexts/LocaleContext';
import styles from './page.module.css';
import { useState, useEffect } from 'react';
import { ArrowLeftOutlined, ReloadOutlined } from '@ant-design/icons';
import { getNodePerformance, NodePerformanceData } from '@/api/earnings';
import { useRouter } from 'next/navigation';


const { Title } = Typography;
const { Option } = Select;

export default function NodePerformancePage() {
  const { messages } = useLocaleContext();
  const router = useRouter();
  const [timeRange, setTimeRange] = useState('30');

  // Data states
  const [nodePerformanceData, setNodePerformanceData] = useState<NodePerformanceData[]>([]);
  const [nodePerformanceTotal, setNodePerformanceTotal] = useState(0);

  // Loading states
  const [loadingNodePerformance, setLoadingNodePerformance] = useState(false);

  // Pagination states
  const [nodePerformancePage, setNodePerformancePage] = useState(1);
  const [nodePerformancePageSize, setNodePerformancePageSize] = useState(10);

  // Fetch node performance data
  const fetchNodePerformance = async (showNotification = false) => {
    setLoadingNodePerformance(true);
    if (showNotification) {
      message.info(messages.common?.loading || 'Loading...');
    }
    try {
      const response = await getNodePerformance({
        page: nodePerformancePage,
        pageSize: nodePerformancePageSize,
        timeRange: timeRange
      });
      setNodePerformanceData(response.data.data);
      setNodePerformanceTotal(response.data.total);
      if (showNotification) {
        message.success(messages.common?.refreshSuccess || 'Data refreshed successfully');
      }
    } catch (error) {
      console.error('Error fetching node performance:', error);
      message.error('Failed to load node performance');
    } finally {
      setLoadingNodePerformance(false);
    }
  };

  // Handle refresh button click
  const handleRefresh = () => {
    fetchNodePerformance(true);
  };

  // Load data on component mount
  useEffect(() => {
    fetchNodePerformance();
  }, []);

  // Reload data when time range changes
  useEffect(() => {
    fetchNodePerformance();
  }, [timeRange]);

  // Reload node performance when pagination changes
  useEffect(() => {
    fetchNodePerformance();
  }, [nodePerformancePage, nodePerformancePageSize]);
  // Handle view device details
  const handleViewDevice = (deviceId: string) => {
    router.push(`/nodes/device/${deviceId}`);
  };
  const nodePerfColumns: TableColumnProps<NodePerformanceData>[] = [
    {
      title: messages.nodes?.table?.performance?.deviceId || 'Device ID',
      dataIndex: 'id', align: 'center',
      key: 'id'
    },
    {
      title: messages.nodes?.table?.performance?.deviceType || 'Device Type',
      dataIndex: 'type', align: 'center',
      key: 'type'
    },
    {
      title: messages.nodes?.table?.performance?.currentTasks || 'Current Tasks',
      dataIndex: 'tasks', align: 'center',
      key: 'tasks'
    },
    {
      title: messages.nodes?.management?.table?.status || 'Status',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => {
        let statusClass = styles.statusTagPending;
        let dotClass = styles.statusDotYellow;

        if (status === 'connected') {
          statusClass = styles.statusTagCompleted;
          dotClass = styles.statusDotGreen;
        } else if (status === 'waiting') {
          statusClass = styles.statusTagProcessing;
          dotClass = styles.statusDotGreen;
        } else if (status === 'failed') {
          statusClass = styles.statusTagFailed;
          dotClass = styles.statusDotRed;
        }

        return (
          <span className={`${styles.statusTag} ${statusClass}`}>
            <span className={`${styles.statusDot} ${dotClass}`}></span>
            {status}
          </span>
        );
      }
    },
    {
      title: messages.nodes?.table?.performance?.memoryUsage || 'Memory Usage (%)',
      dataIndex: 'mem',
      key: 'mem',
      align: 'center',
      render: (value: number) => (
        <div className={styles.progressBar}>
          <div
            className={styles.progressBarInner}
            style={{ width: `${value}%` }}
          />
        </div>
      )
    },
    {
      title: messages.nodes?.table?.performance?.completionRate || 'Task Completion Rate (%)',
      dataIndex: 'rate',
      align: 'center',
      key: 'rate',
      render: (value: number) => (
        <div className={styles.progressBar}>
          <div
            className={styles.progressBarInner}
            style={{ width: `${value}%` }}
          />
        </div>
      )
    },
    {
      title: messages.nodes?.table?.performance?.totalEarnings || 'Total Earnings (SIGHT)',
      dataIndex: 'earn',
      align: 'center',
      key: 'earn'
    },
    {
      title: messages.nodes?.management?.table?.actions || 'Actions',
      key: 'action',
      align: 'center',
      render: (_: any, record: any) => (
        <Space size="middle">
          <Button style={{ color: '#a01ec1' }} onClick={() => handleViewDevice(record.id || '')}>
            {messages.nodes?.management?.table?.view || 'View'}
          </Button>
        </Space>
      ),
    },
  ];

  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <div className={styles.titleSection}>
          <Button
            icon={<ArrowLeftOutlined />}
            type="text"
            onClick={() => router.push('/earnings')}
            className={styles.backButton}
          >
            {messages.common?.back || 'Back'} {messages.nodes?.sections?.gatewayStatus && 'to gateway overall'}
          </Button>
          <Title level={3}>{messages.nodes?.sections?.nodePerformance || 'Node Performance'}</Title>
        </div>
        <div className={styles.headerActions}>
          <Select
            defaultValue="30"
            style={{ width: 120, marginRight: 16 }}
            onChange={(value) => setTimeRange(value)}
            value={timeRange}
          >
            <Option value="7">{messages.reports?.timeRange?.days7}</Option>
            <Option value="30">{messages.reports?.timeRange?.days30}</Option>
            <Option value="90">{messages.reports?.timeRange?.days90}</Option>
            <Option value="365">{messages.reports?.timeRange?.year}</Option>
          </Select>
          <Button
            icon={<ReloadOutlined />}
            onClick={handleRefresh}
            loading={loadingNodePerformance}
          >
            {messages.apiKeys.table.refresh || 'Refresh'}
          </Button>
        </div>
      </div>

      <Table
        columns={nodePerfColumns}
        dataSource={nodePerformanceData.length > 0 ? nodePerformanceData.map((item, index) => ({ ...item, key: index })) : []}
        pagination={{
          current: nodePerformancePage,
          position: ['bottomCenter'],
          pageSize: nodePerformancePageSize,
          total: nodePerformanceTotal,
          onChange: (page, pageSize) => {
            setNodePerformancePage(page);
            if (pageSize) setNodePerformancePageSize(pageSize);
          },
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total) => `Total ${total} items`,
        }}
        size="middle"
        className={styles.table}
        loading={loadingNodePerformance}
        scroll={{ x: 'max-content' }}
      />
    </div>
  );
}
