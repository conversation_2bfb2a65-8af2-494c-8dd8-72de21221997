.container {
  /* padding: 24px; */
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
}

.headerActions {
  display: flex;
  align-items: center;
}

.statsRow {
  display: flex;
  gap: 16px;
  margin-bottom: 24px;
}

.statCard {
  flex: 1;
  min-width: 220px;
  background: #fff;
  border-radius: 16px;
  padding: 20px 24px;
  display: flex;
  /* flex-direction: column; */
  align-items: center;
  position: relative;
  border: 1px solid #6D20F5;
}

.statContent {
  width: 100%;
}

.statValue {
  font-family: var(--font-aldrich);
  font-size: 32px;
  font-weight: 400;
  color: #333;
  text-align: right;
  line-height: 100%;
  letter-spacing: 0;
  text-transform: capitalize;
  width: 100%;
}

.statLabel {
  font-size: 20px;
  font-weight: 500;
  color: #333;
  margin-bottom: 8px;
  line-height: 1.2;
}

.filterSection {
  display: flex;
  gap: 16px;
  margin-bottom: 24px;
}

.searchInput {
  max-width: 320px;
}

.section {
  margin-bottom: 24px;
  position: relative;
}

.sectionHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.sectionHeader :global(.ant-typography) {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 0;
  color: #333;
}

.viewAllLink {
  font-size: 12px;
  color: #1890ff;
  display: flex;
  align-items: center;
  gap: 4px;
  font-family: Inter;
  font-weight: 400;
  font-size: 16px;
  line-height: 100%;
  letter-spacing: 0%;

}

.table {
  background: #fff;
  border-radius: 16px;
  border: 1px solid #6D20F5;
  overflow: hidden;
}

.table :global(.ant-table) {
  background: transparent;
}

.table :global(.ant-table-thead > tr > th) {
  background: #fafafa;
  font-weight: 500;
  color: #333;
  padding: 10px 16px;
  font-size: 14px;
}

.table :global(.ant-table-tbody > tr > td) {
  padding: 10px 16px;
  color: #333;
  font-size: 14px;
}

.statusDot {
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-right: 8px;
}

.statusDotGreen {
  background-color: #52c41a;
}

.statusDotYellow {
  background-color: #faad14;
}

.statusDotRed {
  background-color: #f5222d;
}
.statusTagFailed {
  color: #F5222D;
}

.statusDotRed {
  background-color: #F5222D;
}

.statusTagPending {
  color: #D46B08;
}

.statusTagProcessing {
  color: #1890FF;
}

.statusTagCompleted {
  color: #52C41A;
}

.statusTagFailed {
  color: #F5222D;
}

.statusDotRed {
  background-color: #F5222D;
}

.statusDotGreen {
  background-color: #52C41A;
}

.statusDotYellow {
  background-color: #FAAD14;
}


.progressBar {
  height: 6px;
  border-radius: 3px;
  /* background-color: #f5f5f5; */
  overflow: hidden;
  background-color: #F5F5F5;
  width: 100%;
  max-width: 100px;
  position: relative;
}

.progressBarInner {
  height: 100%;
  border-radius: 3px;
  /* background-color: #000; */
  background: linear-gradient(90deg, #6D20F5 0%, #E7337A 100%);
  position: absolute;
  top: 0;
  left: 0;
}

.modelTag {
  display: inline-block;
  padding: 2px 6px;
  border-radius: 4px;
  background-color: #f0f0f0;
  font-size: 12px;
  color: #666;
  font-weight: 500;
}

.statusTag {
  display: inline-flex;
  align-items: center;
  font-size: 14px;
}

.statusTagConnected {
  color: #52c41a;
  font-weight: 500;
}

.statusTagMinor {
  color: #faad14;
  font-weight: 500;
}

.statusTagNormal {
  color: #52c41a;
  font-weight: 500;
}

.statusTagHigh {
  color: #faad14;
  font-weight: 500;
}

.statusTagPending {
  color: #faad14;
  font-weight: 500;
}

.statusTagProcessing {
  color: #1890ff;
  font-weight: 500;
}

.statusTagCompleted {
  color: #52c41a;
  font-weight: 500;
}