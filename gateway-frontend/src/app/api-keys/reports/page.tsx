'use client';

import { Typo<PERSON>, <PERSON>, Row, Col, Select, Button, message } from 'antd';
import { DownloadOutlined, ReloadOutlined } from '@ant-design/icons';
import { useLocaleContext } from '@/contexts/LocaleContext';
import styles from './page.module.css';
import MonthlyUsageChart from '@/components/api-keys/reports/MonthlyUsageChart';
import ChannelUsageTable from '@/components/api-keys/reports/ChannelUsageTable';
import UsageSummaryCards from '@/components/api-keys/reports/UsageSummaryCards';
import { getChannelUsage, getUsageSummary, ChannelUsage } from '@/api/apiKeys';
import { useState, useEffect } from 'react';

const { Title, Text } = Typography;
const { Option } = Select;

export default function KeysReportPage() {
  const { messages } = useLocaleContext();
  const [loading, setLoading] = useState(false);
  const [timeRange, setTimeRange] = useState('30');
  const [channelData, setChannelData] = useState<ChannelUsage[]>([]);
  const [summaryData, setSummaryData] = useState({
    totalRequests: '0',
    totalTokens: '0',
    avgResponseTime: '0ms',
    costThisMonth: '$0.00'
  });
  const [error, setError] = useState<string | null>(null);

  // 获取数据
  const fetchData = async () => {
    try {
      setLoading(true);
      setError(null);

      // 获取汇总数据
      console.log(`Fetching usage summary data with timeRange: ${timeRange}`);
      const summaryResult = await getUsageSummary(timeRange);
      console.log('Usage summary data:', summaryResult);

      // 更新汇总数据
      setSummaryData({
        totalRequests: summaryResult.totalRequests.toLocaleString(),
        totalTokens: formatNumber(summaryResult.totalTokens),
        avgResponseTime: `${summaryResult.avgResponseTime}ms`,
        costThisMonth: `$${summaryResult.costThisMonth.toFixed(2)}`
      });

      // 获取渠道使用数据
      console.log(`Fetching channel usage data with timeRange: ${timeRange}`);
      const channelResult = await getChannelUsage(timeRange);
      console.log('Channel usage data:', channelResult);

      setChannelData(channelResult);
    } catch (error) {
      console.error('Error fetching data:', error);
      setError('Failed to load data');
      message.error('Failed to load data');
    } finally {
      setLoading(false);
    }
  };

  // 格式化大数字为可读形式（如1.2M）
  const formatNumber = (num: number): string => {
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + 'M';
    } else if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'K';
    }
    return num.toString();
  };

  // 处理时间范围变化
  const handleTimeRangeChange = (value: string) => {
    setTimeRange(value);
  };

  // 处理刷新按钮点击
  const handleRefresh = () => {
    fetchData();
  };

  // 初始加载和时间范围变化时获取数据
  useEffect(() => {
    fetchData();
  }, [timeRange]);

  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <div>
          <Title level={4}>{messages.reports?.title || "API Keys Usage Report"}</Title>
          <Text type="secondary">
            {messages.reports?.description || "Monitor and analyze your API usage across all keys and channels."}
          </Text>
        </div>
        <div className={styles.headerActions}>
          <Select
            defaultValue="30"
            style={{ width: 150, marginRight: 16 }}
            onChange={handleTimeRangeChange}
            value={timeRange}
          >
            <Option value="7">{messages.reports?.timeRange?.days7 || "Last 7 days"}</Option>
            <Option value="30">{messages.reports?.timeRange?.days30 || "Last 30 days"}</Option>
            <Option value="90">{messages.reports?.timeRange?.days90 || "Last 90 days"}</Option>
            <Option value="365">{messages.reports?.timeRange?.year || "Last year"}</Option>
          </Select>
          <Button
            icon={<ReloadOutlined />}
            style={{ marginRight: 8 }}
            onClick={handleRefresh}
            loading={loading}
          >
            {messages.reports?.refresh || "Refresh"}
          </Button>
          {/* <Button
            icon={<DownloadOutlined />}
            onClick={() => message.info('Export functionality will be implemented in the future')}
          >
            {messages.reports?.export || "Export"}
          </Button> */}
        </div>
      </div>

      <UsageSummaryCards
        data={summaryData}
        loading={loading}
        error={error}
      />

      <Card className={styles.chartCard}>
        <div className={styles.chartTitle}>
          <Title level={5}>{messages.reports?.tabs?.monthly || "Monthly Usage"}</Title>
        </div>
        <MonthlyUsageChart timeRange={timeRange} />
      </Card>

      <div className={styles.section}>
        <Title level={5}>{messages.reports?.channels?.title || "Channel Usage"}</Title>
        <Text type="secondary">
          {messages.reports?.channels?.description || "Usage breakdown by channel and model."}
        </Text>
        <ChannelUsageTable data={channelData} loading={loading} />
      </div>
    </div>
  );
}
