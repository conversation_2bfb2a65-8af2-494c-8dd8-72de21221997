.container {
  padding: 24px;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 32px;
}

.header > div {
  max-width: 800px;
}

.titleRow {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.backButton {
  padding: 4px 8px;
  height: auto;
}

.backButton:hover {
  background: rgba(0, 0, 0, 0.06);
}

.section {
  margin-bottom: 32px;
} 