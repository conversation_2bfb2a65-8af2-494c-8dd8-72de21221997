'use client';

import { useParams } from 'next/navigation';
import { Typography, Spin, Result, message, Select, Button } from 'antd';
import { useLocaleContext } from '@/contexts/LocaleContext';
import StatsCards from './StatsCards';
import QuickStartCard from './QuickStartCard';
import UsageChart from './UsageChart';
import styles from './page.module.css';
import { useEffect, useState } from 'react';
import { getApiKeyDetail, getApiKeyUsage } from '@/api/apiKeys';

const { Title } = Typography;

interface KeyDetail {
  name: string;
  description?: string;
  key: string;
  category?: string;
  provider?: string;
  status?: 'active' | 'inactive' | 'revoked';
  createdAt?: string;
  updatedAt?: string;
  lastUsed?: string | null;
  expirationDate?: string | null;
  stats: {
    requests: string;
    requestsChange: number;
    tokens: string;
    tokensChange: number;
    cost: number;
    lastCharge: string;
  }
}

const { Option } = Select;

export default function ApiKeyDetailPage() {
  const { id } = useParams();
  const { messages } = useLocaleContext();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [keyDetail, setKeyDetail] = useState<KeyDetail | null>(null);
  const [timeRange, setTimeRange] = useState('30');

  useEffect(() => {
    const fetchKeyDetail = async () => {
      try {
        setLoading(true);
        console.log('Fetching API key detail for ID:', id);

        // 获取API密钥详情
        try {
          const apiKeyDetail = await getApiKeyDetail(id as string);
          console.log('API key detail response:', apiKeyDetail);

          // 获取API密钥使用情况
          try {
            const apiKeyUsage = await getApiKeyUsage(id as string);
            console.log('API key usage response:', apiKeyUsage);

            // 格式化数据
            const formattedDetail: KeyDetail = {
              name: apiKeyDetail.name,
              description: apiKeyDetail.description || undefined,
              key: apiKeyDetail.key,
              category: apiKeyDetail.category,
              provider: apiKeyDetail.provider,
              status: apiKeyDetail.status,
              createdAt: apiKeyDetail.createdAt,
              lastUsed: apiKeyDetail.lastUsed,
              expirationDate: apiKeyDetail.expirationDate,
              stats: {
                requests: apiKeyUsage.totalRequests.toLocaleString(),
                requestsChange: apiKeyDetail.stats.requestsChange,
                tokens: formatNumber(apiKeyUsage.totalTokens),
                tokensChange: apiKeyDetail.stats.tokensChange,
                cost: apiKeyUsage.costThisMonth,
                lastCharge: new Date(apiKeyDetail.stats.lastCharge).toLocaleDateString()
              }
            };

            setKeyDetail(formattedDetail);
            setError(null);
          } catch (usageError) {
            console.error('Error fetching API key usage:', usageError);

            // 即使没有使用数据，也显示基本的API密钥信息
            const formattedDetail: KeyDetail = {
              name: apiKeyDetail.name,
              description: apiKeyDetail.description || undefined,
              key: apiKeyDetail.key,
              category: apiKeyDetail.category,
              provider: apiKeyDetail.provider,
              status: apiKeyDetail.status,
              createdAt: apiKeyDetail.createdAt,
              lastUsed: apiKeyDetail.lastUsed,
              expirationDate: apiKeyDetail.expirationDate,
              stats: {
                requests: '0',
                requestsChange: 0,
                tokens: '0',
                tokensChange: 0,
                cost: 0,
                lastCharge: new Date().toLocaleDateString()
              }
            };

            setKeyDetail(formattedDetail);
            setError(messages.detail?.usageLoadError || '无法加载使用数据，但API密钥信息已显示');
          }
        } catch (detailError) {
          console.error('Error fetching API key detail:', detailError);
          setError(messages.detail?.notFound || '找不到该API密钥，请确认密钥ID是否正确');
        }
      } catch (err) {
        console.error('Error in fetchKeyDetail:', err);
        setError(err instanceof Error ? err.message : messages.detail?.loadError || '加载API密钥详情失败');
      } finally {
        setLoading(false);
      }
    };

    // 格式化大数字为可读形式（如1.2M）
    const formatNumber = (num: number): string => {
      if(!num) {
        return '0';
      }
      if (num >= 1000000) {
        return (num / 1000000).toFixed(1) + 'M';
      } else if (num >= 1000) {
        return (num / 1000).toFixed(1) + 'K';
      }
      return num.toString();
    };

    if (id) {
      fetchKeyDetail();
    }
  }, [id]);

  if (loading) {
    return (
      <div className={styles.loadingContainer}>
        <Spin size="large" />
      </div>
    );
  }

  if (error || !keyDetail) {
    return (
      <Result
        status="error"
        title={messages.detail?.notFound || "API Key Not Found"}
        subTitle={error || messages.detail?.loadError || "Could not load API key details, please check if the key ID is correct"}
        extra={[
          <Button
            key="back"
            onClick={() => window.history.back()}
          >
            {messages.common?.back || "Back"}
          </Button>,
          <Button
            key="create"
            type="primary"
            onClick={() => window.location.href = '/api-keys'}
          >
            {messages.detail?.viewAllKeys || "View All API Keys"}
          </Button>
        ]}
      />
    );
  }

  const stats = [
    {
      title: messages.detail.requests,
      value: keyDetail.stats.requests,
      tip: `${keyDetail.stats.requestsChange > 0 ? '+' : ''}${keyDetail.stats.requestsChange}% ${messages.detail.requestsChange}`,
      tipType: keyDetail.stats.requestsChange >= 0 ? 'up' as const : 'down' as const,
    },
    {
      title: messages.detail.tokens,
      value: keyDetail.stats.tokens,
      tip: `${keyDetail.stats.tokensChange > 0 ? '+' : ''}${keyDetail.stats.tokensChange}% ${messages.detail.tokensChange}`,
      tipType: keyDetail.stats.tokensChange >= 0 ? 'up' as const : 'down' as const,
    },
    {
      title: messages.detail.cost,
      value: `$ ${keyDetail.stats.cost?.toFixed(1) || 0}`,
      tip: messages.detail.lastCharge?.replace('{date}', keyDetail.stats.lastCharge),
      tipType: 'date' as const,
    },
  ];

  // 处理时间范围变化
  const handleTimeRangeChange = (value: string) => {
    setTimeRange(value);
  };

  return (
    <div className={styles.container}>
      <div className={styles.headerRow}>
        <div>
          <Title level={4} className={styles.title}>{keyDetail.name}</Title>
          <div className={styles.keyInfo}>
            <span className={styles.keySuffix}>{keyDetail.key}</span>
            {keyDetail.status && (
              <span className={`${styles.keyStatus} ${styles[`status${keyDetail.status.charAt(0).toUpperCase() + keyDetail.status.slice(1)}`]}`}>
                {keyDetail.status}
              </span>
            )}
          </div>
          {keyDetail.description && (
            <p className={styles.keyDescription}>{keyDetail.description}</p>
          )}
          <div className={styles.keyMeta}>
            {keyDetail.provider && keyDetail.category && (
              <span className={styles.keyMetaItem}>
                {keyDetail.provider} - {keyDetail.category}
              </span>
            )}
            {keyDetail.createdAt && (
              <span className={styles.keyMetaItem}>
                {messages.detail?.createdAt || "Created"}: {new Date(keyDetail.createdAt).toLocaleDateString()}
              </span>
            )}
            {keyDetail.updatedAt && (
              <span className={styles.keyMetaItem}>
                {messages.detail?.updatedAt || "Updated"}: {new Date(keyDetail.updatedAt).toLocaleDateString()}
              </span>
            )}
            {keyDetail.lastUsed && (
              <span className={styles.keyMetaItem}>
                {messages.detail?.lastUsed || "Last used"}: {new Date(keyDetail.lastUsed).toLocaleDateString()}
              </span>
            )}
            {keyDetail.expirationDate && (
              <span className={styles.keyMetaItem}>
                {messages.detail?.expirationDate || "Expires"}: {new Date(keyDetail.expirationDate).toLocaleDateString()}
              </span>
            )}
          </div>
        </div>
        <Select
          defaultValue="30"
          style={{ width: 120 }}
          onChange={handleTimeRangeChange}
          value={timeRange}
        >
          <Option value="7">{messages.detail?.timeRange?.days7 || "Last 7 days"}</Option>
          <Option value="30">{messages.detail?.timeRange?.days30 || "Last 30 days"}</Option>
          <Option value="90">{messages.detail?.timeRange?.days90 || "Last 90 days"}</Option>
          <Option value="365">{messages.detail?.timeRange?.year || "Last year"}</Option>
        </Select>
      </div>
      <StatsCards stats={stats} />
      <UsageChart keyId={id as string} timeRange={timeRange} />
      <QuickStartCard />
    </div>
  );
}