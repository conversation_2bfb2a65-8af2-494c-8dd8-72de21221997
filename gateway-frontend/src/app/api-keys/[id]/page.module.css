.container {
  padding: 24px;
}

.headerRow {
  display: flex;
  align-items: flex-start;
  width: 100%;
  margin-bottom: 24px;
  justify-content: space-between;
}

.title {
  margin: 0 !important;
  margin-bottom: 8px !important;
}

.keyInfo {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.keySuffix {
  font-family: var(--font-geist-mono);
  color: rgba(0, 0, 0, 0.45);
  font-size: 14px;
  margin-right: 12px;
}

.keyStatus {
  font-size: 12px;
  padding: 2px 8px;
  border-radius: 10px;
  text-transform: capitalize;
}

.statusActive {
  background-color: #f6ffed;
  border: 1px solid #b7eb8f;
  color: #52c41a;
}

.statusInactive {
  background-color: #fff7e6;
  border: 1px solid #ffd591;
  color: #fa8c16;
}

.statusRevoked {
  background-color: #fff1f0;
  border: 1px solid #ffa39e;
  color: #f5222d;
}

.keyDescription {
  color: rgba(0, 0, 0, 0.65);
  font-size: 14px;
  margin-bottom: 12px;
}

.keyMeta {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  margin-bottom: 8px;
}

.keyMetaItem {
  color: rgba(0, 0, 0, 0.45);
  font-size: 12px;
}

.loadingContainer {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
}