.cardsRow {
  display: flex;
  gap: 32px;
  margin: 32px 0 40px 0;
}

.card {
  flex: 1;
  border-radius: 16px !important;
  box-shadow: none !important;
  border: 1px solid #E5E7EB !important;
  padding: 24px 32px;
  min-width: 220px;
}

.title {
  color: #6B7280;
  font-size: 16px;
  margin-bottom: 8px;
}

.value {
  font-size: 32px;
  font-weight: 600;
  margin-bottom: 8px;
  letter-spacing: 1px;
}

.tipUp {
  color: #22C55E;
  font-size: 14px;
}

.tipDown {
  color: #EF4444;
  font-size: 14px;
}

.tipDate {
  color: #22C55E;
  font-size: 14px;
} 