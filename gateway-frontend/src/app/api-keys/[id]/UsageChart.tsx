'use client';

import { useEffect, useState } from 'react';
import { Card, Radio, Spin } from 'antd';
import { useLocaleContext } from '@/contexts/LocaleContext';
import { getApiKeyUsage, ApiKeyUsage } from '@/api/apiKeys';
import styles from './UsageChart.module.css';

interface UsageChartProps {
  keyId: string;
  timeRange?: string;
}

export default function UsageChart({ keyId, timeRange = '30' }: UsageChartProps) {
  const { messages } = useLocaleContext();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [usageData, setUsageData] = useState<ApiKeyUsage | null>(null);
  const [metric, setMetric] = useState<'requests' | 'tokens' | 'cost'>('requests');

  useEffect(() => {
    const fetchUsageData = async () => {
      try {
        setLoading(true);
        const data = await getApiKeyUsage(keyId, timeRange);
        setUsageData(data);
        setError(null);
      } catch (err) {
        console.error('Error fetching key usage:', err);
        setError(err instanceof Error ? err.message : 'Failed to load key usage data');
      } finally {
        setLoading(false);
      }
    };

    if (keyId) {
      fetchUsageData();
    }
  }, [keyId, timeRange]);

  const handleMetricChange = (e: any) => {
    setMetric(e.target.value);
  };

  if (loading) {
    return (
      <div className={styles.loadingContainer}>
        <Spin />
      </div>
    );
  }

  if (error || !usageData) {
    return (
      <Card className={styles.errorCard}>
        <p className={styles.errorText}>
          {error || 'No usage data available'}
        </p>
      </Card>
    );
  }

  // 准备图表数据
  const chartData = usageData.dailyRequests.map(item => ({
    date: item.date,
    requests: item.count,
    // 这里我们假设tokens和cost与requests成比例
    // 在实际应用中，您应该使用真实数据
    tokens: Math.round(item.count * (usageData.totalTokens / usageData.totalRequests)),
    cost: parseFloat((item.count * (usageData.costThisMonth / usageData.totalRequests)).toFixed(2))
  }));

  return (
    <Card className={styles.card} title={messages.detail?.usageChart?.title || "API Key Usage"}>
      <div className={styles.header}>
        <Radio.Group value={metric} onChange={handleMetricChange}>
          <Radio.Button value="requests">{messages.detail?.usageChart?.requests || "Requests"}</Radio.Button>
          <Radio.Button value="tokens">{messages.detail?.usageChart?.tokens || "Tokens"}</Radio.Button>
          <Radio.Button value="cost">{messages.detail?.usageChart?.cost || "Cost"}</Radio.Button>
        </Radio.Group>
      </div>

      <div className={styles.chartContainer}>
        {/* 这里是一个简化的图表模拟 */}
        <div className={styles.chart}>
          {chartData.map((item, index) => {
            const value = item[metric];
            const maxValue = Math.max(...chartData.map(d => d[metric]));
            const height = maxValue > 0 ? (value / maxValue) * 100 : 0;
            
            return (
              <div 
                key={index} 
                className={styles.barContainer}
              >
                <div 
                  className={styles.bar} 
                  style={{ 
                    height: `${height}%`,
                    backgroundColor: metric === 'requests' ? '#1890ff' : metric === 'tokens' ? '#722ed1' : '#52c41a'
                  }}
                  title={`${item.date}: ${value}`}
                />
                {index % 5 === 0 && (
                  <div className={styles.dateLabel}>
                    {new Date(item.date).getDate()}
                  </div>
                )}
              </div>
            );
          })}
        </div>
      </div>
      
      <div className={styles.summary}>
        <div className={styles.summaryItem}>
          <span className={styles.summaryLabel}>
            {metric === 'requests' 
              ? (messages.detail?.usageChart?.totalRequests || "Total Requests") 
              : metric === 'tokens' 
                ? (messages.detail?.usageChart?.totalTokens || "Total Tokens") 
                : (messages.detail?.usageChart?.totalCost || "Total Cost")}:
          </span>
          <span className={styles.summaryValue}>
            {metric === 'requests' 
              ? usageData.totalRequests.toLocaleString() 
              : metric === 'tokens' 
                ? usageData.totalTokens.toLocaleString() 
                : `$${usageData.costThisMonth.toFixed(2)}`}
          </span>
        </div>
        {metric === 'requests' && (
          <div className={styles.summaryItem}>
            <span className={styles.summaryLabel}>
              {messages.detail?.usageChart?.avgResponseTime || "Avg Response Time"}:
            </span>
            <span className={styles.summaryValue}>
              {`${Math.round(usageData.avgResponseTime)}ms`}
            </span>
          </div>
        )}
      </div>
    </Card>
  );
}
