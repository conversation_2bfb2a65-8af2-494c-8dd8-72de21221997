.root {
  display: flex;
  background: #FAFAFA;
  border-radius: 16px;
  border: 1px solid #E5E7EB;
  margin-top: 16px;
  padding: 32px 40px;
  gap: 32px;
  align-items: stretch;
}

.left {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  min-width: 220px;
}

.title {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 8px;
}

.desc {
  color: #6B7280;
  font-size: 15px;
  margin-bottom: 16px;
}

.timeRow {
  display: flex;
  align-items: center;
  color: #6B7280;
  font-size: 14px;
  gap: 6px;
}

.timeIcon {
  font-size: 16px;
}

.right {
  flex: 2;
  position: relative;
  min-width: 320px;
}

.codeHeader {
  display: flex;
  align-items: center;
  justify-content: space-between;
  color: #6B7280;
  font-size: 14px;
  margin-bottom: 8px;
}

.reloadIcon {
  font-size: 16px;
}

.codeBlock {
  background: #F4F4F5;
  border-radius: 8px;
  padding: 20px 16px 16px 16px;
  font-family: var(--font-geist-mono);
  font-size: 14px;
  line-height: 1.6;
  position: relative;
  overflow-x: auto;
  min-height: 180px;
}

.copyBtn {
  position: absolute;
  top: 12px;
  right: 12px;
} 