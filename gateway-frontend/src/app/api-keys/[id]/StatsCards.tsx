'use client';
import { Card } from 'antd';
import styles from './StatsCards.module.css';

interface Stat {
  title: string;
  value: string;
  tip: string;
  tipType?: 'up' | 'down' | 'date';
}

export default function StatsCards({ stats }: { stats: Stat[] }) {
  return (
    <div className={styles.cardsRow}>
      {stats.map((stat, i) => (
        <Card key={i} className={styles.card} bordered={false}>
          <div className={styles.title}>{stat.title}</div>
          <div className={styles.value}>{stat.value}</div>
          <div className={stat.tipType === 'up' ? styles.tipUp : stat.tipType === 'down' ? styles.tipDown : styles.tipDate}>
            {stat.tip}
          </div>
        </Card>
      ))}
    </div>
  );
} 