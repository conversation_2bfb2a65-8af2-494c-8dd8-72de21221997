'use client';
import { <PERSON><PERSON>, Tooltip, message } from 'antd';
import { CopyOutlined, ReloadOutlined } from '@ant-design/icons';
import { useLocaleContext } from '@/contexts/LocaleContext';
import styles from './QuickStartCard.module.css';

const CODE = `import OpenAI from "openai";
const client = new OpenAI();

const response = await client.responses.create({
  model: "gpt-4.1",
  input: "Write a one-sentence bedtime story about a unicorn.",
});
console.log(response.output_text);`;

export default function QuickStartCard() {
  const { messages } = useLocaleContext();

  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(CODE);
      message.success(messages.detail.quickstartCopy, 1);
    } catch {
      message.error('Copy failed', 1);
    }
  };

  return (
    <div className={styles.root}>
      <div className={styles.left}>
        <div className={styles.title}>{messages.detail.quickstartTitle}</div>
        <div className={styles.desc}>{messages.detail.quickstartDesc}</div>
        <div className={styles.timeRow}>
          <span className={styles.timeIcon}>⏱</span>
          <span className={styles.time}>{messages.detail.quickstartTime}</span>
        </div>
      </div>
      <div className={styles.right}>
        <div className={styles.codeHeader}>
          <span>{messages.detail.quickstartLang}</span>
          <ReloadOutlined className={styles.reloadIcon} />
        </div>
        <pre className={styles.codeBlock}>
          <code>{CODE}</code>
          <Tooltip title={messages.detail.quickstartCopy}>
            <Button
              type="text"
              icon={<CopyOutlined />}
              className={styles.copyBtn}
              onClick={handleCopy}
            />
          </Tooltip>
        </pre>
      </div>
    </div>
  );
} 