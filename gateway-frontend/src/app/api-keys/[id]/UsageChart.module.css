.card {
  margin-top: 24px;
  margin-bottom: 24px;
}

.header {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 16px;
}

.chartContainer {
  height: 300px;
  margin-bottom: 16px;
  position: relative;
}

.chart {
  display: flex;
  align-items: flex-end;
  height: 100%;
  gap: 2px;
}

.barContainer {
  flex: 1;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
}

.bar {
  width: 100%;
  border-radius: 2px 2px 0 0;
  transition: height 0.3s ease;
}

.dateLabel {
  font-size: 12px;
  color: #999;
  margin-top: 4px;
}

.loadingContainer {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 300px;
}

.errorCard {
  margin-top: 24px;
  margin-bottom: 24px;
}

.errorText {
  text-align: center;
  color: #ff4d4f;
}

.summary {
  display: flex;
  justify-content: space-between;
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
}

.summaryItem {
  display: flex;
  flex-direction: column;
}

.summaryLabel {
  font-size: 14px;
  color: #8c8c8c;
  margin-bottom: 4px;
}

.summaryValue {
  font-size: 20px;
  font-weight: 500;
}
