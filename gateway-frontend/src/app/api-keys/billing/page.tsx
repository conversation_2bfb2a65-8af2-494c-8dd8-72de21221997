'use client';

import { <PERSON>, But<PERSON>, Row, Col, Statistic, Table, Tag } from 'antd';
import { PlusOutlined, DownloadOutlined } from '@ant-design/icons';
import styles from './page.module.css';

export default function BillingPage() {
  // Sample transaction data
  const transactionData = [
    {
      key: '1',
      type: 'Account top-up via MoonPay',
      date: '1/20/2024 at 6:30:00 PM',
      amount: '+$100.00',
      status: 'Completed',
      statusType: 'success'
    },
    {
      key: '2',
      type: 'Account top-up via MoonPay',
      date: '1/20/2024 at 6:30:00 PM',
      amount: '-$23.40',
      status: 'Completed',
      statusType: 'success'
    },
    {
      key: '3',
      type: 'Account top-up via MoonPay',
      date: '1/20/2024 at 6:30:00 PM',
      amount: '-$23.40',
      status: 'Pending',
      statusType: 'warning'
    },
    {
      key: '4',
      type: 'Account top-up via MoonPay',
      date: '1/20/2024 at 6:30:00 PM',
      amount: '-$23.40',
      status: 'Failed',
      statusType: 'error'
    }
  ];

  const columns = [
    {
      title: '',
      dataIndex: 'type',
      key: 'type',
      render: (text: string, record: any) => (
        <div className={styles.transactionItem}>
          <div className={styles.transactionIcon}>
            {record.amount.startsWith('+') ? '💳' : '💸'}
          </div>
          <div>
            <div className={styles.transactionType}>{text}</div>
            <div className={styles.transactionDate}>{record.date}</div>
          </div>
        </div>
      ),
    },
    {
      title: '',
      dataIndex: 'amount',
      key: 'amount',
      align: 'right' as const,
      render: (amount: string) => (
        <div className={styles.amountContainer}>
          <div className={`${styles.amount} ${amount.startsWith('+') ? styles.positive : styles.negative}`}>
            {amount}
          </div>
        </div>
      ),
    },
    {
      title: '',
      dataIndex: 'status',
      key: 'status',
      align: 'right' as const,
      render: (status: string, record: any) => (
        <Tag color={record.statusType === 'success' ? 'green' : record.statusType === 'warning' ? 'orange' : 'red'}>
          {status}
        </Tag>
      ),
    },
  ];

  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <div>
          <h1 className={styles.title}>Billing & Balance</h1>
          <p className={styles.subtitle}>Manage your API keys and access controls</p>
        </div>
        <Button type="primary" icon={<PlusOutlined />} className={styles.addButton}>
          Add Funds
        </Button>
      </div>

      <Row gutter={[24, 24]} className={styles.statsRow}>
        <Col xs={24} sm={8}>
          <Card className={styles.statCard}>
            <Statistic
              title="Current Balance"
              value={247.50}
              prefix="$"
              precision={2}
              className={styles.statistic}
            />
            <div className={styles.statSubtext}>Available for API calls</div>
          </Card>
        </Col>
        <Col xs={24} sm={8}>
          <Card className={styles.statCard}>
            <Statistic
              title="This Month's Usage"
              value={38.60}
              prefix="$"
              precision={2}
              className={styles.statistic}
            />
            <div className={styles.statSubtext}>-12% from last month</div>
          </Card>
        </Col>
        <Col xs={24} sm={8}>
          <Card className={styles.statCard}>
            <Statistic
              title="Estimated Daily Cost"
              value={4.20}
              prefix="$"
              precision={2}
              className={styles.statistic}
            />
            <div className={styles.statSubtext}>Based on current usage</div>
          </Card>
        </Col>
      </Row>

      <Card className={styles.transactionCard}>
        <div className={styles.transactionHeader}>
          <h2>Transaction History</h2>
          <Button icon={<DownloadOutlined />} className={styles.exportButton}>
            Export
          </Button>
        </div>
        <Table
          dataSource={transactionData}
          columns={columns}
          pagination={false}
          showHeader={false}
          className={styles.transactionTable}
        />
      </Card>
    </div>
  );
}
