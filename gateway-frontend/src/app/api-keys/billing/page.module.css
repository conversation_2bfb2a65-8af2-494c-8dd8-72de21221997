.container {
  padding: 0;
  max-width: 1200px;
  margin: 0 auto;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 32px;
}

.title {
  font-size: 32px;
  font-weight: 600;
  margin: 0 0 8px 0;
  color: #1a1a1a;
}

.subtitle {
  font-size: 16px;
  color: #666;
  margin: 0;
}

.addButton {
  background: #9333EA;
  border: none;
  border-radius: 8px;
  height: 40px;
  padding: 0 20px;
  font-weight: 500;
}

.addButton:hover {
  background: #7c3aed !important;
}

.statsRow {
  margin-bottom: 32px;
}

.statCard {
  border-radius: 12px;
  border: 1px solid #e5e7eb;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
}

.statistic :global(.ant-statistic-title) {
  color: #6b7280;
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 8px;
}

.statistic :global(.ant-statistic-content) {
  color: #1f2937;
  font-size: 28px;
  font-weight: 600;
}

.statSubtext {
  color: #9ca3af;
  font-size: 12px;
  margin-top: 4px;
}

.transactionCard {
  border-radius: 12px;
  border: 1px solid #e5e7eb;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
}

.transactionHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.transactionHeader h2 {
  font-size: 20px;
  font-weight: 600;
  margin: 0;
  color: #1f2937;
}

.exportButton {
  border: 1px solid #d1d5db;
  border-radius: 6px;
  color: #374151;
  background: white;
}

.exportButton:hover {
  border-color: #9ca3af;
  color: #1f2937;
}

.transactionTable :global(.ant-table-tbody > tr > td) {
  padding: 16px 0;
  border-bottom: 1px solid #f3f4f6;
}

.transactionTable :global(.ant-table-tbody > tr:last-child > td) {
  border-bottom: none;
}

.transactionItem {
  display: flex;
  align-items: center;
  gap: 12px;
}

.transactionIcon {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  background: #f9fafb;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
}

.transactionType {
  font-weight: 500;
  color: #1f2937;
  font-size: 14px;
}

.transactionDate {
  color: #6b7280;
  font-size: 12px;
  margin-top: 2px;
}

.amountContainer {
  text-align: right;
}

.amount {
  font-weight: 600;
  font-size: 16px;
}

.amount.positive {
  color: #059669;
}

.amount.negative {
  color: #dc2626;
}

/* Responsive design */
@media (max-width: 768px) {
  .header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }
  
  .title {
    font-size: 24px;
  }
  
  .addButton {
    width: 100%;
    justify-content: center;
  }
  
  .transactionHeader {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }
  
  .exportButton {
    width: 100%;
    justify-content: center;
  }
}
