.container {
  padding: var(--spacing-xl);
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-xl);
}

.section {
  margin-bottom: var(--spacing-2xl);
}

.addButton {
  composes: btn btn-primary from global;
  height: 40px;
  padding: 0 var(--spacing-lg);
  font-weight: var(--font-weight-medium);
}

.providersGrid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-xl);
}

.providerCard {
  border: 1px solid var(--color-border-light);
  border-radius: var(--radius-lg);
  padding: var(--spacing-lg);
  transition: var(--transition-fast);
  cursor: pointer;
  background: var(--color-background);
}

.providerCard:hover {
  border-color: var(--color-primary);
  box-shadow: var(--shadow-sm);
  transform: translateY(-2px);
}

.providerCard.disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.providerCard.disabled:hover {
  transform: none;
  border-color: var(--color-border-light);
  box-shadow: none;
}

.providerHeader {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-sm);
}

.providerIcon {
  width: 32px;
  height: 32px;
  border-radius: var(--radius-sm);
  object-fit: contain;
}

.providerName {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-medium);
  color: var(--color-text-primary);
  margin: 0;
}

.providerDescription {
  color: var(--color-text-secondary);
  font-size: var(--font-size-sm);
  margin: 0;
  line-height: 1.4;
}

.providerModels {
  margin-top: var(--spacing-sm);
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-xs);
}

.modelTag {
  background: var(--color-background-secondary);
  color: var(--color-text-secondary);
  padding: 2px var(--spacing-xs);
  border-radius: var(--radius-xs);
  font-size: var(--font-size-xs);
  border: none;
}

.tableSection {
  background: var(--color-background);
  border-radius: var(--radius-lg);
  padding: var(--spacing-lg);
  border: 1px solid var(--color-border-light);
}

.tableHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-lg);
}

.tableTitle {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-medium);
  color: var(--color-text-primary);
  margin: 0;
}

.refreshButton {
  border: 1px solid var(--color-border-medium);
  background: transparent;
  color: var(--color-text-secondary);
  height: 32px;
  padding: 0 var(--spacing-md);
  border-radius: var(--radius-md);
  transition: var(--transition-fast);
}

.refreshButton:hover {
  border-color: var(--color-primary);
  color: var(--color-primary);
}

.statusTag {
  border-radius: var(--radius-sm);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
  padding: 2px var(--spacing-xs);
  border: none;
}

.statusTag.active {
  background: var(--color-success-light);
  color: var(--color-success);
}

.statusTag.inactive {
  background: var(--color-warning-light);
  color: var(--color-warning);
}

.statusTag.error {
  background: var(--color-error-light);
  color: var(--color-error);
}

.actionButton {
  border: none;
  background: transparent;
  color: var(--color-text-secondary);
  padding: var(--spacing-xs);
  border-radius: var(--radius-sm);
  transition: var(--transition-fast);
  cursor: pointer;
}

.actionButton:hover {
  background: var(--color-background-hover);
  color: var(--color-primary);
}

.actionButton.danger:hover {
  color: var(--color-error);
}

.emptyState {
  text-align: center;
  padding: var(--spacing-2xl);
  color: var(--color-text-secondary);
}

.emptyIcon {
  font-size: 48px;
  color: var(--color-text-disabled);
  margin-bottom: var(--spacing-md);
}

.emptyTitle {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-medium);
  color: var(--color-text-primary);
  margin-bottom: var(--spacing-sm);
}

.emptyDescription {
  color: var(--color-text-secondary);
  margin-bottom: var(--spacing-lg);
}

.loadingContainer {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: var(--spacing-2xl);
}
