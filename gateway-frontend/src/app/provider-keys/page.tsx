'use client';

import { useState, useEffect } from 'react';
import { Button, Typography, message, Card, Spin } from 'antd';
import { PlusOutlined, KeyOutlined } from '@ant-design/icons';
import { useLocaleContext } from '@/contexts/LocaleContext';
import ProviderKeyTable from '@/components/provider-keys/ProviderKeyTable';
import AddProviderKeyModal from '@/components/provider-keys/AddProviderKeyModal';
import { getProviderKeys, getProviders, ProviderKey, Provider } from '@/api/providerKeys';
import styles from './page.module.css';

const { Title, Text } = Typography;

// Default provider icons
const providerIcons: Record<string, string> = {
  'OpenAI': '/openai.png',
  'DeepSeek': '/deepseek.png',
  'Claude': '/claude.png',
  'Anthropic': '/claude.png',
  'Ollama': '/ollama.png',
};

export default function ProviderKeysPage() {
  const { messages } = useLocaleContext();
  const [addModalOpen, setAddModalOpen] = useState(false);
  const [selectedProvider, setSelectedProvider] = useState<Provider | null>(null);
  const [providerKeys, setProviderKeys] = useState<ProviderKey[]>([]);
  const [providers, setProviders] = useState<Provider[]>([]);
  const [loading, setLoading] = useState(false);
  const [providersLoading, setProvidersLoading] = useState(false);
  const [totalKeys, setTotalKeys] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);

  // Fetch provider keys
  const fetchProviderKeys = async () => {
    setLoading(true);
    try {
      const response = await getProviderKeys({
        page: currentPage,
        pageSize: pageSize
      });
      console.log(response)
      setProviderKeys(response.data.items);
      setTotalKeys(response.total);
    } catch (error) {
      console.error('Error fetching provider keys:', error);
      message.error('Failed to load provider keys');
    } finally {
      setLoading(false);
    }
  };

  // Fetch available providers
  const fetchProviders = async () => {
    setProvidersLoading(true);
    try {
      const providersData = await getProviders();
      // Add default icons to providers
      const providersWithIcons = providersData.map(provider => ({
        ...provider,
        iconUrl: provider.iconUrl || providerIcons[provider.name] || providerIcons['OpenAI']
      }));
      setProviders(providersWithIcons);
    } catch (error) {
      console.error('Error fetching providers:', error);
      message.error('Failed to load providers');
    } finally {
      setProvidersLoading(false);
    }
  };

  // Load data on component mount
  useEffect(() => {
    fetchProviderKeys();
  }, [currentPage, pageSize]);

  useEffect(() => {
    fetchProviders();
  }, []);

  const handleAddSuccess = () => {
    fetchProviderKeys();
    setSelectedProvider(null);
  };

  const handleProviderCardClick = (provider: Provider) => {
    if (provider.isActive) {
      setSelectedProvider(provider);
      setAddModalOpen(true);
    }
  };

  const handleCloseModal = () => {
    setAddModalOpen(false);
    setSelectedProvider(null);
  };

  const handlePageChange = (page: number, size?: number) => {
    setCurrentPage(page);
    if (size) setPageSize(size);
  };

  const renderProvidersSection = () => {
    if (providersLoading) {
      return (
        <div className={styles.loadingContainer}>
          <Spin size="large" />
        </div>
      );
    }

    return (
      <div className={styles.providersGrid}>
        {providers.map(provider => (
          <Card
            key={provider.id}
            className={`${styles.providerCard} ${!provider.isActive ? styles.disabled : ''}`}
            onClick={() => handleProviderCardClick(provider)}
          >
            <div className={styles.providerHeader}>
              {provider.iconUrl && (
                <img
                  src={provider.iconUrl}
                  alt={provider.displayName}
                  className={styles.providerIcon}
                />
              )}
              <h4 className={styles.providerName}>{provider.displayName}</h4>
            </div>
            <p className={styles.providerDescription}>
              Configure your {provider.displayName} API key to enable model access
            </p>
            <div className={styles.providerModels}>
              {provider.supportedModels.slice(0, 4).map(model => (
                <span key={model} className={styles.modelTag}>{model}</span>
              ))}
              {provider.supportedModels.length > 4 && (
                <span className={styles.modelTag}>+{provider.supportedModels.length - 4} more</span>
              )}
            </div>
          </Card>
        ))}
      </div>
    );
  };

  const renderEmptyState = () => {
    if (loading) return null;

    if (providerKeys?.length === 0) {
      return (
        <div className={styles.emptyState}>
          <KeyOutlined className={styles.emptyIcon} />
          <h3 className={styles.emptyTitle}>No Provider Keys</h3>
          <p className={styles.emptyDescription}>
            Add your first provider API key to start using AI models through the SIGHT network.
          </p>
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => setAddModalOpen(true)}
          >
            {messages.providerKeys.addButton}
          </Button>
        </div>
      );
    }

    return null;
  };

  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <div className={styles.headerContent}>
          <h1 className={styles.headerTitle}>Provider Keys</h1>
          <p className={styles.headerDescription}>
            Manage your AI provider API keys to access various language models through the SIGHT network. 
            Configure and monitor your keys for seamless AI integration.
          </p>
        </div>
        <Button
          type="primary"
          icon={<PlusOutlined />}
          className={styles.addButton}
          onClick={() => setAddModalOpen(true)}
        >
          {messages.providerKeys.addButton}
        </Button>
      </div>

      <div className={styles.section}>
        <Title level={5}>{messages.providerKeys.availableProviders}</Title>
        {/* {renderProvidersSection()} */}
      </div>

      <div className={styles.section}>
        <div className={styles.tableSection}>
          <div className={styles.tableHeader}>
            <h3 className={styles.tableTitle}>Your Provider Keys</h3>
            <Button
              className={styles.refreshButton}
              onClick={fetchProviderKeys}
              loading={loading}
            >
              {messages.providerKeys.table.refresh}
            </Button>
          </div>

          {renderEmptyState()}

            <ProviderKeyTable
              loading={loading}
              dataSource={providerKeys}
              total={totalKeys}
              page={currentPage}
              pageSize={pageSize}
              onPageChange={handlePageChange}
              onRefresh={fetchProviderKeys}
            />
        </div>
      </div>

      <AddProviderKeyModal
        open={addModalOpen}
        onClose={handleCloseModal}
        onSuccess={handleAddSuccess}
        providers={providers}
        preSelectedProvider={selectedProvider}
      />
    </div>
  );
}