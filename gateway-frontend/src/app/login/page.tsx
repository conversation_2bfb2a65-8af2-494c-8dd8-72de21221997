'use client';

import { Card, ConfigProvider, Button, Typography, Dropdown, Space } from 'antd';
import { useLocaleContext } from '@/contexts/LocaleContext';
import styles from './page.module.css';
import { useRouter } from 'next/navigation';
import WalletLogin from '@/components/WalletLogin';
import { ArrowLeftOutlined, GlobalOutlined, DownOutlined } from '@ant-design/icons';
import Image from 'next/image';
import SIGHT from '../../../public/SIGHT AI Brand Kit Vector.svg';

const { Title, Text } = Typography;

export default function LoginPage() {
  const { messages, locale, setLocale } = useLocaleContext();
  const router = useRouter();

  const handleBackToHome = () => {
    router.push('/');
  };

  const handleLanguageChange = ({ key }: { key: string }) => {
    setLocale(key as 'zh' | 'en');
  };

  const langMenu = {
    items: [
      { key: 'zh', label: '中文' },
      { key: 'en', label: 'English' },
    ],
    onClick: handleLanguageChange,
  };

  return (
    <ConfigProvider
      theme={{
        components: {
          Card: {
            borderRadius: 24,
          },
          Button: {
            borderRadius: 12,
          },
        },
        token: {
          colorPrimary: '#9333EA',
          borderRadius: 12,
        },
      }}
    >
      <div className={styles.container}>
        <div className={styles.backgroundElements}>
          <div className={styles.circle1}></div>
          <div className={styles.circle2}></div>
          <div className={styles.circle3}></div>
        </div>

        <div className={styles.topButtons}>
          <Button
            type="text"
            icon={<ArrowLeftOutlined />}
            onClick={handleBackToHome}
            className={styles.backButton}
          >
            {messages.login.backToHome}
          </Button>

          <Dropdown menu={langMenu} placement="bottomRight">
            <Button
              type="text"
              icon={<GlobalOutlined />}
              className={styles.langButton}
            >
              {locale === 'zh' ? '中文' : 'English'}
              <DownOutlined style={{ fontSize: '12px', marginLeft: '5px' }} />
            </Button>
          </Dropdown>
        </div>

        <div className={styles.loginBox}>
          <div className={styles.logoContainer}>
            <div className={styles.logoWrapper}>
              <Image
                src={SIGHT.src}
                alt="Logo"
                width={60}
                height={60}
                className={styles.logo}
              />
            </div>
          </div>

          <Card className={styles.loginCard} bordered={false}>
            <div className={styles.header}>
              <Title level={2} className={styles.title}>{messages.login.title}</Title>
              <Text className={styles.subtitle}>{messages.login.subtitle}</Text>
            </div>

            <div className={styles.walletSection}>
              <WalletLogin />
            </div>

            <div className={styles.footer}>
              <Text className={styles.footerText}>
                © {new Date().getFullYear()} Sight AI. {messages.login.footer}
              </Text>
            </div>
          </Card>
        </div>
      </div>
    </ConfigProvider>
  );
}