.container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
  position: relative;
  overflow: hidden;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Se<PERSON>e UI', <PERSON><PERSON>, 'Helvetica Neue', sans-serif;
}

/* 背景元素 */
.backgroundElements {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  z-index: 0;
}

.circle1, .circle2, .circle3 {
  position: absolute;
  border-radius: 50%;
  filter: blur(60px);
}

.circle1 {
  width: 400px;
  height: 400px;
  background: rgba(147, 51, 234, 0.15);
  top: -100px;
  right: -100px;
  animation: float 8s ease-in-out infinite alternate;
}

.circle2 {
  width: 300px;
  height: 300px;
  background: rgba(79, 70, 229, 0.15);
  bottom: -50px;
  left: -50px;
  animation: float 10s ease-in-out infinite alternate-reverse;
}

.circle3 {
  width: 250px;
  height: 250px;
  background: rgba(236, 72, 153, 0.1);
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  animation: pulse 12s ease-in-out infinite;
}

/* 顶部按钮区域 */
.topButtons {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  display: flex;
  justify-content: space-between;
  padding: 24px;
  z-index: 10;
}

/* 返回按钮 */
.backButton {
  color: rgba(255, 255, 255, 0.8);
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
}

.backButton:hover {
  color: white !important;
  transform: translateX(-4px);
}

/* 语言选择按钮 */
.langButton {
  color: rgba(255, 255, 255, 0.8);
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 4px;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(8px);
  border-radius: 12px;
  padding: 4px 12px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.langButton:hover {
  color: white !important;
  background: rgba(255, 255, 255, 0.2);
}

/* 登录框 */
.loginBox {
  width: 100%;
  max-width: 420px;
  position: relative;
  z-index: 1;
  animation: slideUp 0.8s ease-out;
  display: flex;
  flex-direction: column;
  align-items: center;
}

/* Logo 容器 */
.logoContainer {
  margin-bottom: 24px;
  display: flex;
  justify-content: center;
}

.logoWrapper {
  width: 80px;
  height: 80px;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.1);
  animation: fadeIn 0.8s ease-out;
}

.logo {
  width: 60px;
  height: 60px;
  object-fit: contain;
}

/* 登录卡片 */
.loginCard {
  background: rgba(30, 41, 59, 0.7);
  backdrop-filter: blur(16px);
  box-shadow: 0 20px 50px rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 24px;
  overflow: hidden;
  padding: 40px 32px;
  width: 100%;
  position: relative;
}

.loginCard::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #9333EA, #EC4899);
  z-index: 2;
}

/* 卡片内容 */
.header {
  text-align: center;
  margin-bottom: 32px;
  animation: fadeIn 0.8s ease-out 0.3s both;
  position: relative;
}

.header::after {
  content: '';
  position: absolute;
  bottom: -16px;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 2px;
  background: linear-gradient(90deg, #9333EA, #EC4899);
  border-radius: 2px;
}

.title {
  font-size: 28px !important;
  font-weight: 700 !important;
  margin-bottom: 12px !important;
  background: linear-gradient(135deg, #9333EA 0%, #EC4899 100%);
  -webkit-background-clip: text !important;
  -webkit-text-fill-color: transparent !important;
  letter-spacing: -0.5px;
}

.subtitle {
  font-size: 16px;
  color: rgba(255, 255, 255, 0.7) !important;
  margin: 0;
  display: block;
}

.walletSection {
  padding: 20px 0;
  animation: fadeIn 0.8s ease-out 0.6s both;
  position: relative;
  z-index: 2;
}

/* 页脚 */
.footer {
  margin-top: 24px;
  text-align: center;
  animation: fadeIn 0.8s ease-out 0.9s both;
}

.footerText {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.5) !important;
}

/* 动画 */
@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(40px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes float {
  0% {
    transform: translateY(0) translateX(0);
  }
  100% {
    transform: translateY(-20px) translateX(20px);
  }
}

@keyframes pulse {
  0% {
    opacity: 0.3;
    transform: translate(-50%, -50%) scale(0.8);
  }
  50% {
    opacity: 0.5;
    transform: translate(-50%, -50%) scale(1.2);
  }
  100% {
    opacity: 0.3;
    transform: translate(-50%, -50%) scale(0.8);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .loginBox {
    max-width: 90%;
    padding: 0 16px;
  }

  .loginCard {
    padding: 32px 24px;
  }

  .title {
    font-size: 24px !important;
  }

  .subtitle {
    font-size: 14px;
  }

  .circle1 {
    width: 250px;
    height: 250px;
  }

  .circle2 {
    width: 200px;
    height: 200px;
  }

  .circle3 {
    width: 150px;
    height: 150px;
  }
}
