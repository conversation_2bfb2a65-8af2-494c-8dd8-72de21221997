.bg3d {
  min-height: 100vh;
  width: 100vw;
  background: var(--color-background);
  position: relative;
  overflow: hidden;
  font-family: var(--font-family-base);
  background-size: 100% 100%;
  background-position: center;
  background-repeat: no-repeat;
}

/* Header styles are now in header.module.css */

.centerBox {
  width: 100vw;
  height: calc(100vh - var(--header-height));
  bottom: 0;
  position: absolute;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  /* z-index: var(--z-index-modal); */
}

.cardContainer {
  position: relative;
  width: 100%;                                          
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.bgImg {
  width: 80%;
  display: flex;
  flex-direction: column;
  align-items: center;
  position: absolute;
  bottom: 5%;
}

.title {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -100%);
  width: 30%;
  max-width: 1200px;
  display: flex;
  flex-direction: column;
  align-items: center;
  z-index: var(--z-index-modal);
}

.cardRow {
  display: flex;
  gap: 3%;
  justify-content: center;
  position: absolute;
  bottom: 10%;
}

.card {
  cursor: pointer;
   composes: card from global;
   width: 9%;
}

/* All card gradients share the same background properties */
.cardGradient1,
.cardGradient2,
.cardGradient3,
.cardGradient4,
.cardGradient5 {
  background-size: 100% 100%;
  background-position: center;
  background-repeat: no-repeat;
}

/* User info styles are now in header.module.css */