@import "tailwindcss";
@import "../styles/variables.css";
@import "../styles/common.css";

:root {
  /* --background: #ffffff; */
  --foreground: #171717;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    /* --foreground: #ededed; */
  }
}

body {
  background: #fff;
  color: var(--foreground);
  font-family: var(--font-family-base);
  margin: 0;
  padding: 0;
}
:where(.css-dev-only-do-not-override-89ejyu).ant-layout.ant-layout-has-sider {
  background: #fff;

}
/* Global reset */
* {
  box-sizing: border-box;
}

/* Remove default margin */
h1, h2, h3, h4, h5, h6, p, ul, ol {
  margin: 0;
}

/* Remove list styles */
ul, ol {
  list-style: none;
  padding: 0;
}

/* Set core body defaults */
html, body {
  height: 100%;
}

/* Make images easier to work with */
img {
  max-width: 100%;
  display: block;
}

/* Inherit fonts for inputs and buttons */
input, button, textarea, select {
  font: inherit;
}