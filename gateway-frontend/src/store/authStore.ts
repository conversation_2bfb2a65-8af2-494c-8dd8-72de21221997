import { create } from 'zustand'
import { persist, createJSONStorage } from 'zustand/middleware'

interface AuthState {
  isLoggedIn: boolean
  walletAddress: string | null
  walletType: string | null
  lastLogin: number | null
  accessToken: string | null
  userId: string | null

  // Actions
  login: (address: string, walletType: string) => void
  setAuthData: (accessToken: string, userId: string) => void
  logout: () => void
}

export const useAuthStore = create<AuthState>()(
  persist(
    (set, get) => ({
      isLoggedIn: false,
      walletAddress: null,
      walletType: null,
      lastLogin: null,
      accessToken: null,
      userId: null,

      login: (address, walletType) => {
        // Check if already logged in with the same address to prevent unnecessary updates
        const state = get();
        if (state.isLoggedIn && state.walletAddress === address) {
          return; // Already logged in with this address, do nothing
        }

        // Otherwise update the state
        set({
          isLoggedIn: true,
          walletAddress: address,
          walletType,
          lastLogin: Date.now(),
        });
      },

      setAuthData: (accessToken, userId) => {
        // Store the JWT token and user ID
        set({
          accessToken,
          userId,
          isLoggedIn: true,
        });

        // Also store the token in localStorage for API requests
        window.localStorage.setItem('accessToken', accessToken);
      },

      logout: () => {
        // Check if already logged out to prevent unnecessary updates
        const state = get();
        if (!state.isLoggedIn) {
          return; // Already logged out, do nothing
        }

        // Otherwise update the state
        set({
          isLoggedIn: false,
          walletAddress: null,
          walletType: null,
          accessToken: null,
          userId: null,
        });

        // Remove token from localStorage
        window.localStorage.removeItem('accessToken');
      },
    }),
    {
      name: 'sight-auth-storage',
      storage: createJSONStorage(() => localStorage),
    }
  )
)
