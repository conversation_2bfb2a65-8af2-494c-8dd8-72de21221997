import { create } from 'zustand'
import { persist, createJSONStorage } from 'zustand/middleware'

export type UserProviderMode = 'user' | 'provider';

interface UserProviderState {
  mode: UserProviderMode
  
  // Actions
  setMode: (mode: UserProviderMode) => void
  toggleMode: () => void
}

export const useUserProviderStore = create<UserProviderState>()(
  persist(
    (set, get) => ({
      mode: 'user',

      setMode: (mode: UserProviderMode) => {
        set({ mode });
      },

      toggleMode: () => {
        const currentMode = get().mode;
        set({ mode: currentMode === 'user' ? 'provider' : 'user' });
      },
    }),
    {
      name: 'sight-user-provider-storage',
      storage: createJSONStorage(() => localStorage),
    }
  )
)
