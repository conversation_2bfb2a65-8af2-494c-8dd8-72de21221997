'use client';

import React, { createContext, useContext, useState, useCallback } from 'react';
import { message } from 'antd';
import { useLocaleContext } from './LocaleContext';

interface RefreshContextType {
  refreshPage: () => void;
  isRefreshing: boolean;
}

const RefreshContext = createContext<RefreshContextType | undefined>(undefined);

export const RefreshProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [isRefreshing, setIsRefreshing] = useState(false);
  const { messages } = useLocaleContext();

  const refreshPage = useCallback(() => {
    setIsRefreshing(true);
    message.info(messages.common?.loading || 'Loading...');
    
    // Reload the current page
    window.location.reload();
    
    // This will not execute immediately due to page reload
    setTimeout(() => {
      setIsRefreshing(false);
      message.success(messages.common?.refreshSuccess || 'Page refreshed successfully');
    }, 1000);
  }, [messages]);

  return (
    <RefreshContext.Provider value={{ refreshPage, isRefreshing }}>
      {children}
    </RefreshContext.Provider>
  );
};

export const useRefresh = (): RefreshContextType => {
  const context = useContext(RefreshContext);
  if (context === undefined) {
    throw new Error('useRefresh must be used within a RefreshProvider');
  }
  return context;
};
