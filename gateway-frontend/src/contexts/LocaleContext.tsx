'use client';

import React, { createContext, useContext, useState, ReactNode } from 'react';
import zhCN from 'antd/locale/zh_CN';
import enUS from 'antd/locale/en_US';
import zhMessages from '@/locales/zh.json';
import enMessages from '@/locales/en.json';

const messagesMap = {
  zh: zhMessages,
  en: enMessages,
};

const antdLocaleMap = {
  zh: zhCN,
  en: enUS,
};

interface LocaleContextProps {
  locale: 'zh' | 'en';
  setLocale: (locale: 'zh' | 'en') => void;
  messages: any;
  antdLocale: typeof zhCN;
}

const LocaleContext = createContext<LocaleContextProps | undefined>(undefined);

export const LocaleProvider = ({ children }: { children: ReactNode }) => {
  const [locale, setLocale] = useState<'zh' | 'en'>('en');
  const messages = messagesMap[locale];
  const antdLocale = antdLocaleMap[locale];
  return (
    <LocaleContext.Provider value={{ locale, setLocale, messages, antdLocale }}>
      {children}
    </LocaleContext.Provider>
  );
};

export const useLocaleContext = () => {
  const ctx = useContext(LocaleContext);
  if (!ctx) throw new Error('useLocaleContext must be used within LocaleProvider');
  return ctx;
};