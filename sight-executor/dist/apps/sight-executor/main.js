/******/ (() => { // webpackBootstrap
/******/ 	"use strict";
/******/ 	var __webpack_modules__ = ({

/***/ "./apps/sight-executor/src/app.module.ts":
/*!***********************************************!*\
  !*** ./apps/sight-executor/src/app.module.ts ***!
  \***********************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.AppModule = void 0;
const common_1 = __webpack_require__(/*! @nestjs/common */ "@nestjs/common");
const config_1 = __webpack_require__(/*! @nestjs/config */ "@nestjs/config");
const etcd_1 = __webpack_require__(/*! @app/etcd */ "./libs/etcd/src/index.ts");
const key_manager_1 = __webpack_require__(/*! @app/key-manager */ "./libs/key-manager/src/index.ts");
const providers_1 = __webpack_require__(/*! @app/providers */ "./libs/providers/src/index.ts");
const executor_registry_1 = __webpack_require__(/*! @app/executor-registry */ "./libs/executor-registry/src/index.ts");
const config_2 = __webpack_require__(/*! @app/config */ "./libs/config/src/index.ts");
const inference_controller_1 = __webpack_require__(/*! ./inference.controller */ "./apps/sight-executor/src/inference.controller.ts");
const key_manager_controller_1 = __webpack_require__(/*! ./key-manager.controller */ "./apps/sight-executor/src/key-manager.controller.ts");
let AppModule = class AppModule {
};
exports.AppModule = AppModule;
exports.AppModule = AppModule = __decorate([
    (0, common_1.Module)({
        imports: [
            config_1.ConfigModule.forRoot({
                isGlobal: true,
                load: [config_2.configuration],
            }),
            etcd_1.EtcdModule,
            key_manager_1.KeyManagerModule,
            providers_1.ProvidersModule,
            executor_registry_1.ExecutorRegistryModule,
        ],
        controllers: [inference_controller_1.InferenceController, key_manager_controller_1.KeyManagerController],
        providers: [config_2.ConfigValidationService],
    })
], AppModule);


/***/ }),

/***/ "./apps/sight-executor/src/inference.controller.ts":
/*!*********************************************************!*\
  !*** ./apps/sight-executor/src/inference.controller.ts ***!
  \*********************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var InferenceController_1;
var _a, _b, _c, _d, _e;
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.InferenceController = void 0;
const common_1 = __webpack_require__(/*! @nestjs/common */ "@nestjs/common");
const providers_1 = __webpack_require__(/*! @app/providers */ "./libs/providers/src/index.ts");
const providers_2 = __webpack_require__(/*! @app/providers */ "./libs/providers/src/index.ts");
let InferenceController = InferenceController_1 = class InferenceController {
    providerFactoryService;
    logger = new common_1.Logger(InferenceController_1.name);
    constructor(providerFactoryService) {
        this.providerFactoryService = providerFactoryService;
    }
    async chatCompletions(request) {
        this.logger.log(`Received chat completion request for model: ${request.model}`);
        try {
            if (!request.model) {
                throw new common_1.HttpException('Model is required', common_1.HttpStatus.BAD_REQUEST);
            }
            if (!request.messages || request.messages.length === 0) {
                throw new common_1.HttpException('Messages are required', common_1.HttpStatus.BAD_REQUEST);
            }
            const response = await this.providerFactoryService.executeRequest(request);
            if (typeof response === 'object' && Symbol.asyncIterator in response) {
                throw new common_1.HttpException('Streaming not supported in this endpoint', common_1.HttpStatus.BAD_REQUEST);
            }
            this.logger.log(`Chat completion successful for model: ${request.model}`);
            return response;
        }
        catch (error) {
            this.logger.error(`Chat completion failed: ${error instanceof Error ? error.message : String(error)}`, error instanceof Error ? error.stack : undefined);
            if (error instanceof common_1.HttpException) {
                throw error;
            }
            const errorObj = error;
            if (errorObj.status) {
                throw new common_1.HttpException(errorObj.message || 'Provider error', errorObj.status);
            }
            throw new common_1.HttpException('Internal server error', common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async completions(request) {
        this.logger.log(`Received completion request for model: ${request.model}`);
        try {
            if (!request.model) {
                throw new common_1.HttpException('Model is required', common_1.HttpStatus.BAD_REQUEST);
            }
            if (!request.prompt) {
                throw new common_1.HttpException('Prompt is required', common_1.HttpStatus.BAD_REQUEST);
            }
            const chatRequest = {
                model: request.model,
                messages: [
                    {
                        role: 'user',
                        content: request.prompt,
                    },
                ],
                max_tokens: request.max_tokens,
                temperature: request.temperature,
                top_p: request.top_p,
                frequency_penalty: request.frequency_penalty,
                presence_penalty: request.presence_penalty,
                stop: request.stop,
                stream: request.stream,
            };
            const chatResponse = await this.providerFactoryService.executeRequest(chatRequest);
            if (typeof chatResponse === 'object' && Symbol.asyncIterator in chatResponse) {
                throw new common_1.HttpException('Streaming not supported for completions endpoint', common_1.HttpStatus.BAD_REQUEST);
            }
            const response = chatResponse;
            const completionResponse = {
                id: response.id,
                object: 'text_completion',
                created: response.created,
                model: response.model,
                choices: response.choices.map((choice, index) => ({
                    text: choice.message.content,
                    index: index,
                    finish_reason: choice.finish_reason,
                })),
                usage: response.usage,
            };
            this.logger.log(`Completion successful for model: ${request.model}`);
            return completionResponse;
        }
        catch (error) {
            this.logger.error(`Completion failed: ${error instanceof Error ? error.message : String(error)}`, error instanceof Error ? error.stack : undefined);
            if (error instanceof common_1.HttpException) {
                throw error;
            }
            const errorObj = error;
            if (errorObj.status) {
                throw new common_1.HttpException(errorObj.message || 'Provider error', errorObj.status);
            }
            throw new common_1.HttpException('Internal server error', common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
};
exports.InferenceController = InferenceController;
__decorate([
    (0, common_1.Post)('v1/chat/completions'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [typeof (_b = typeof providers_2.ChatCompletionRequest !== "undefined" && providers_2.ChatCompletionRequest) === "function" ? _b : Object]),
    __metadata("design:returntype", typeof (_c = typeof Promise !== "undefined" && Promise) === "function" ? _c : Object)
], InferenceController.prototype, "chatCompletions", null);
__decorate([
    (0, common_1.Post)('v1/completions'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [typeof (_d = typeof providers_2.CompletionRequest !== "undefined" && providers_2.CompletionRequest) === "function" ? _d : Object]),
    __metadata("design:returntype", typeof (_e = typeof Promise !== "undefined" && Promise) === "function" ? _e : Object)
], InferenceController.prototype, "completions", null);
exports.InferenceController = InferenceController = InferenceController_1 = __decorate([
    (0, common_1.Controller)(),
    __metadata("design:paramtypes", [typeof (_a = typeof providers_1.ProviderFactoryService !== "undefined" && providers_1.ProviderFactoryService) === "function" ? _a : Object])
], InferenceController);


/***/ }),

/***/ "./apps/sight-executor/src/key-manager.controller.ts":
/*!***********************************************************!*\
  !*** ./apps/sight-executor/src/key-manager.controller.ts ***!
  \***********************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var KeyManagerController_1;
var _a, _b, _c, _d, _e, _f, _g;
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.KeyManagerController = void 0;
const common_1 = __webpack_require__(/*! @nestjs/common */ "@nestjs/common");
const etcd_service_1 = __webpack_require__(/*! @app/etcd/etcd.service */ "./libs/etcd/src/etcd.service.ts");
const key_manager_1 = __webpack_require__(/*! @app/key-manager */ "./libs/key-manager/src/index.ts");
let KeyManagerController = KeyManagerController_1 = class KeyManagerController {
    etcdService;
    keyManagerService;
    gatewayKeyAllocationService;
    logger = new common_1.Logger(KeyManagerController_1.name);
    constructor(etcdService, keyManagerService, gatewayKeyAllocationService) {
        this.etcdService = etcdService;
        this.keyManagerService = keyManagerService;
        this.gatewayKeyAllocationService = gatewayKeyAllocationService;
    }
    async getKeys(provider) {
        try {
            const keys = await this.etcdService.getActiveApiKeys(provider);
            return {
                success: true,
                keys: keys.map((key) => ({
                    keyId: key.etcdKeyId,
                    status: key.status,
                    lastUsedAt: key.lastUsedAt,
                    note: key.note,
                    lastError: key.lastError,
                })),
            };
        }
        catch (error) {
            this.logger.error(`Failed to get keys for provider ${provider}: ${error instanceof Error ? error.message : String(error)}`);
            throw new common_1.HttpException('Failed to retrieve keys', common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async updateKey(provider, keyId, request) {
        try {
            const updates = {};
            if (request.status) {
                updates.status = request.status;
            }
            if (request.note !== undefined) {
                updates.note = request.note;
            }
            if (Object.keys(updates).length > 0) {
                await this.etcdService.updateApiKey(provider, keyId, updates);
            }
            this.logger.log(`Updated key ${keyId} for provider ${provider}`);
            return {
                success: true,
                message: 'Key updated successfully',
            };
        }
        catch (error) {
            this.logger.error(`Failed to update key ${keyId}: ${error instanceof Error ? error.message : String(error)}`);
            throw new common_1.HttpException('Failed to update key', common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async getKeyPoolStatus() {
        return await this.keyManagerService.getKeyPoolStatus();
    }
    getGatewayAllocationStats() {
        try {
            const stats = this.gatewayKeyAllocationService.getAllocationStats();
            const hasAvailableKeys = this.gatewayKeyAllocationService.hasAvailableKeys();
            return {
                success: true,
                data: {
                    ...stats,
                    hasAvailableKeys,
                },
            };
        }
        catch (error) {
            this.logger.error(`Failed to get Gateway allocation stats: ${error instanceof Error ? error.message : String(error)}`);
            throw new common_1.HttpException('Failed to get Gateway allocation stats', common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async refreshGatewayAllocation() {
        try {
            await this.gatewayKeyAllocationService.refreshKeyAllocation();
            return {
                success: true,
                message: 'Gateway key allocation refreshed successfully',
            };
        }
        catch (error) {
            this.logger.error(`Failed to refresh Gateway allocation: ${error instanceof Error ? error.message : String(error)}`);
            throw new common_1.HttpException('Failed to refresh Gateway allocation', common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
};
exports.KeyManagerController = KeyManagerController;
__decorate([
    (0, common_1.Get)(':provider'),
    __param(0, (0, common_1.Param)('provider')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", typeof (_d = typeof Promise !== "undefined" && Promise) === "function" ? _d : Object)
], KeyManagerController.prototype, "getKeys", null);
__decorate([
    (0, common_1.Put)(':provider/:keyId'),
    __param(0, (0, common_1.Param)('provider')),
    __param(1, (0, common_1.Param)('keyId')),
    __param(2, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, Object]),
    __metadata("design:returntype", typeof (_e = typeof Promise !== "undefined" && Promise) === "function" ? _e : Object)
], KeyManagerController.prototype, "updateKey", null);
__decorate([
    (0, common_1.Get)('pool/status'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", typeof (_f = typeof Promise !== "undefined" && Promise) === "function" ? _f : Object)
], KeyManagerController.prototype, "getKeyPoolStatus", null);
__decorate([
    (0, common_1.Get)('gateway/allocation-stats'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Object)
], KeyManagerController.prototype, "getGatewayAllocationStats", null);
__decorate([
    (0, common_1.Put)('gateway/refresh-allocation'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", typeof (_g = typeof Promise !== "undefined" && Promise) === "function" ? _g : Object)
], KeyManagerController.prototype, "refreshGatewayAllocation", null);
exports.KeyManagerController = KeyManagerController = KeyManagerController_1 = __decorate([
    (0, common_1.Controller)('api/keys'),
    __metadata("design:paramtypes", [typeof (_a = typeof etcd_service_1.EtcdService !== "undefined" && etcd_service_1.EtcdService) === "function" ? _a : Object, typeof (_b = typeof key_manager_1.KeyManagerService !== "undefined" && key_manager_1.KeyManagerService) === "function" ? _b : Object, typeof (_c = typeof key_manager_1.GatewayKeyAllocationService !== "undefined" && key_manager_1.GatewayKeyAllocationService) === "function" ? _c : Object])
], KeyManagerController);


/***/ }),

/***/ "./libs/config/src/config-validation.service.ts":
/*!******************************************************!*\
  !*** ./libs/config/src/config-validation.service.ts ***!
  \******************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var ConfigValidationService_1;
var _a;
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.ConfigValidationService = void 0;
const common_1 = __webpack_require__(/*! @nestjs/common */ "@nestjs/common");
const config_1 = __webpack_require__(/*! @nestjs/config */ "@nestjs/config");
let ConfigValidationService = ConfigValidationService_1 = class ConfigValidationService {
    configService;
    logger = new common_1.Logger(ConfigValidationService_1.name);
    constructor(configService) {
        this.configService = configService;
    }
    validateConfiguration() {
        const errors = [];
        const warnings = [];
        this.validateRequiredConfig(errors);
        this.validateEtcdConfig(errors, warnings);
        this.validateProviderConfigs(warnings);
        const result = {
            valid: errors.length === 0,
            errors,
            warnings,
        };
        this.logValidationResult(result);
        return result;
    }
    validateRequiredConfig(errors) {
        const requiredConfigs = ['executor.modelType', 'etcd.endpoints'];
        for (const config of requiredConfigs) {
            const value = this.configService.get(config);
            if (!value) {
                errors.push(`Required configuration missing: ${config}`);
            }
        }
    }
    validateEtcdConfig(errors, warnings) {
        const endpoints = this.configService.get('etcd.endpoints');
        if (endpoints) {
            for (const endpoint of endpoints) {
                if (!this.isValidEndpoint(endpoint)) {
                    errors.push(`Invalid etcd endpoint format: ${endpoint}`);
                }
            }
        }
        const username = this.configService.get('etcd.username');
        const password = this.configService.get('etcd.password');
        if (username && !password) {
            warnings.push('etcd username provided but password is missing');
        }
        if (password && !username) {
            warnings.push('etcd password provided but username is missing');
        }
    }
    validateProviderConfigs(warnings) {
        const modelType = this.configService.get('executor.modelType');
        if (modelType === 'openai') {
            const baseUrl = this.configService.get('openai.baseUrl');
            if (baseUrl && !this.isValidUrl(baseUrl)) {
                warnings.push(`Invalid OpenAI base URL: ${baseUrl}`);
            }
        }
        if (modelType === 'anthropic') {
            const baseUrl = this.configService.get('anthropic.baseUrl');
            if (baseUrl && !this.isValidUrl(baseUrl)) {
                warnings.push(`Invalid Anthropic base URL: ${baseUrl}`);
            }
        }
        if (modelType === 'deepseek') {
            const baseUrl = this.configService.get('deepseek.baseUrl');
            if (baseUrl && !this.isValidUrl(baseUrl)) {
                warnings.push(`Invalid DeepSeek base URL: ${baseUrl}`);
            }
        }
    }
    isValidUrl(url) {
        try {
            new URL(url);
            return true;
        }
        catch {
            return false;
        }
    }
    isValidEndpoint(endpoint) {
        const pattern = /^[a-zA-Z0-9.-]+:\d+$/;
        return pattern.test(endpoint);
    }
    logValidationResult(result) {
        if (result.valid) {
            this.logger.log('✅ Configuration validation passed');
        }
        else {
            this.logger.error('❌ Configuration validation failed');
            result.errors.forEach((error) => this.logger.error(`  - ${error}`));
        }
        if (result.warnings.length > 0) {
            this.logger.warn('⚠️  Configuration warnings:');
            result.warnings.forEach((warning) => this.logger.warn(`  - ${warning}`));
        }
    }
    getConfigSummary() {
        return {
            executor: {
                modelType: this.configService.get('executor.modelType'),
                keyPoolSize: this.configService.get('executor.keyPoolSize'),
            },
            etcd: {
                endpoints: this.configService.get('etcd.endpoints'),
                hasAuth: !!(this.configService.get('etcd.username') &&
                    this.configService.get('etcd.password')),
            },
            providers: {
                openai: {
                    baseUrl: this.configService.get('openai.baseUrl'),
                    defaultModel: this.configService.get('openai.defaultModel'),
                },
            },
        };
    }
};
exports.ConfigValidationService = ConfigValidationService;
exports.ConfigValidationService = ConfigValidationService = ConfigValidationService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [typeof (_a = typeof config_1.ConfigService !== "undefined" && config_1.ConfigService) === "function" ? _a : Object])
], ConfigValidationService);


/***/ }),

/***/ "./libs/config/src/configuration.ts":
/*!******************************************!*\
  !*** ./libs/config/src/configuration.ts ***!
  \******************************************/
/***/ ((__unused_webpack_module, exports) => {


Object.defineProperty(exports, "__esModule", ({ value: true }));
exports["default"] = () => ({
    port: parseInt(process.env.PORT || '3000', 10),
    executor: {
        id: process.env.EXECUTOR_ID || 'executor-' + Date.now(),
        region: process.env.EXECUTOR_REGION || 'default',
        modelType: process.env.EXECUTOR_MODEL_TYPE || 'openai',
        publicUrl: process.env.PUBLIC_URL || 'http://localhost:3000',
        maxConcurrentRequests: parseInt(process.env.MAX_CONCURRENT_REQUESTS || '10', 10),
        keyPoolSize: parseInt(process.env.KEY_POOL_SIZE || '5', 10),
    },
    gateway: {
        url: process.env.GATEWAY_URL || 'http://localhost:3000',
        timeout: parseInt(process.env.GATEWAY_TIMEOUT || '30000', 10),
    },
    etcd: {
        endpoints: process.env.ETCD_ENDPOINTS?.split(',') || ['localhost:2379'],
        username: process.env.ETCD_USERNAME,
        password: process.env.ETCD_PASSWORD,
    },
    openai: {
        baseUrl: process.env.OPENAI_BASE_URL || 'https://api.openai.com/v1',
        defaultModel: process.env.OPENAI_DEFAULT_MODEL || 'gpt-3.5-turbo',
    },
    rateLimit: {
        retryAfterMs: parseInt(process.env.RATE_LIMIT_RETRY_AFTER_MS || '60000', 10),
        maxRetries: parseInt(process.env.MAX_RETRIES || '3', 10),
    },
    kms: {
        provider: process.env.KMS_PROVIDER || 'gcp',
        gcp: {
            projectId: process.env.GCP_KMS_PROJECT_ID,
            location: process.env.GCP_KMS_LOCATION || 'global',
            keyRing: process.env.GCP_KMS_KEY_RING,
            keyName: process.env.GCP_KMS_KEY_NAME,
            credentialsPath: process.env.GOOGLE_APPLICATION_CREDENTIALS,
        },
    },
});


/***/ }),

/***/ "./libs/config/src/etcd-paths.ts":
/*!***************************************!*\
  !*** ./libs/config/src/etcd-paths.ts ***!
  \***************************************/
/***/ ((__unused_webpack_module, exports) => {


Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.ETCD_PATH_CONSTANTS = exports.EtcdPathManager = void 0;
class EtcdPathManager {
    static executorPath(region, modelType, executorId) {
        return `/executors/${region}/${modelType}/${executorId}`;
    }
    static executorDiscoveryPrefix(region, modelType) {
        return `/executors/${region}/${modelType}/`;
    }
    static publicKeyPath(provider, region, keyId) {
        return `/keys/public/${provider}/${region}/${keyId}`;
    }
    static publicKeyDiscoveryPrefix(provider, region) {
        return `/keys/public/${provider}/${region}/`;
    }
    static encryptedApiKeyPath(provider, region, uuid) {
        return `/api-keys/${provider}/${region}/${uuid}`;
    }
    static encryptedApiKeyDiscoveryPrefix(provider, region) {
        return `/api-keys/${provider}/${region}/`;
    }
    static keyStatusPath(provider, keyId) {
        return `/keys/${provider}/${keyId}`;
    }
    static keyStatusDiscoveryPrefix(provider) {
        return `/keys/${provider}/`;
    }
    static legacyThirdPartyKeyPath(provider, keyId) {
        return `/third-party-keys/${provider}/${keyId}`;
    }
    static legacyThirdPartyKeyDiscoveryPrefix(provider) {
        return `/third-party-keys/${provider}/`;
    }
    static validateProvider(provider) {
        const validProviders = ['openai', 'claude', 'anthropic', 'google', 'cohere'];
        return validProviders.includes(provider.toLowerCase());
    }
    static validateRegion(region) {
        return /^[a-z0-9-]+$/.test(region) && region.length > 0 && region.length <= 50;
    }
    static validateModelType(modelType) {
        const validModelTypes = ['openai', 'claude', 'anthropic', 'google', 'cohere'];
        return validModelTypes.includes(modelType.toLowerCase());
    }
    static validateExecutorId(executorId) {
        return /^[a-zA-Z0-9-_]+$/.test(executorId) && executorId.length > 0 && executorId.length <= 100;
    }
    static validateKeyId(keyId) {
        return /^[a-zA-Z0-9-_]+$/.test(keyId) && keyId.length > 0 && keyId.length <= 100;
    }
    static validateUUID(uuid) {
        const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
        return uuidRegex.test(uuid);
    }
    static parseExecutorPath(path) {
        const match = path.match(/^\/executors\/([^/]+)\/([^/]+)\/([^/]+)$/);
        if (!match)
            return null;
        return {
            region: match[1],
            modelType: match[2],
            executorId: match[3],
        };
    }
    static parsePublicKeyPath(path) {
        const match = path.match(/^\/keys\/public\/([^/]+)\/([^/]+)\/([^/]+)$/);
        if (!match)
            return null;
        return {
            provider: match[1],
            region: match[2],
            keyId: match[3],
        };
    }
    static parseEncryptedApiKeyPath(path) {
        const match = path.match(/^\/api-keys\/([^/]+)\/([^/]+)\/([^/]+)$/);
        if (!match)
            return null;
        return {
            provider: match[1],
            region: match[2],
            uuid: match[3],
        };
    }
    static parseKeyStatusPath(path) {
        const match = path.match(/^\/keys\/([^/]+)\/([^/]+)$/);
        if (!match)
            return null;
        return {
            provider: match[1],
            keyId: match[2],
        };
    }
    static getPathPatterns() {
        return {
            executor: '/executors/{region}/{model_type}/{executor_id}',
            executorDiscovery: '/executors/{region}/{model_type}/',
            publicKey: '/keys/public/{provider}/{region}/{keyId}',
            publicKeyDiscovery: '/keys/public/{provider}/{region}/',
            encryptedApiKey: '/api-keys/{provider}/{region}/{uuid}',
            encryptedApiKeyDiscovery: '/api-keys/{provider}/{region}/',
            keyStatus: '/keys/{provider}/{key_id}',
            keyStatusDiscovery: '/keys/{provider}/',
            legacyThirdPartyKey: '/third-party-keys/{provider}/{keyId}',
            legacyThirdPartyKeyDiscovery: '/third-party-keys/{provider}/',
        };
    }
}
exports.EtcdPathManager = EtcdPathManager;
exports.ETCD_PATH_CONSTANTS = {
    EXECUTORS_PREFIX: '/executors',
    KEYS_PREFIX: '/keys',
    API_KEYS_PREFIX: '/api-keys',
    PUBLIC_KEYS_PREFIX: '/keys/public',
    THIRD_PARTY_KEYS_PREFIX: '/third-party-keys',
    PATH_SEPARATOR: '/',
    MAX_REGION_LENGTH: 50,
    MAX_MODEL_TYPE_LENGTH: 50,
    MAX_EXECUTOR_ID_LENGTH: 100,
    MAX_KEY_ID_LENGTH: 100,
    MAX_PROVIDER_LENGTH: 50,
    SUPPORTED_PROVIDERS: ['openai', 'claude', 'anthropic', 'google', 'cohere'],
    SUPPORTED_MODEL_TYPES: ['openai', 'claude', 'anthropic', 'google', 'cohere'],
};


/***/ }),

/***/ "./libs/config/src/executor.constants.ts":
/*!***********************************************!*\
  !*** ./libs/config/src/executor.constants.ts ***!
  \***********************************************/
/***/ ((__unused_webpack_module, exports) => {


Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.SUPPORTED_MODELS = exports.EXECUTOR_CONSTANTS = void 0;
exports.EXECUTOR_CONSTANTS = {
    HEARTBEAT_INTERVAL_MS: 30000,
    LEASE_TTL_SECONDS: 60,
    REGISTRATION_DELAY_MS: 2000,
    DEFAULT_REGION: 'default',
    DEFAULT_MODEL_TYPE: 'openai',
    DEFAULT_PUBLIC_URL: 'http://localhost:3000',
    DEFAULT_MAX_CONCURRENT_REQUESTS: 10,
    KEY_LOCK_TIMEOUT_MS: 5 * 60 * 1000,
    LEASE_RENEWAL_INTERVAL_MS: 30000,
};
exports.SUPPORTED_MODELS = {
    openai: [
        'gpt-3.5-turbo',
        'gpt-3.5-turbo-16k',
        'gpt-4',
        'gpt-4-32k',
        'gpt-4-turbo',
        'gpt-4-turbo-preview',
        'gpt-4o',
        'gpt-4o-mini',
    ],
};


/***/ }),

/***/ "./libs/config/src/executor.utils.ts":
/*!*******************************************!*\
  !*** ./libs/config/src/executor.utils.ts ***!
  \*******************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {


Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.getSupportedModels = getSupportedModels;
exports.generateExecutorPath = generateExecutorPath;
const executor_constants_1 = __webpack_require__(/*! ./executor.constants */ "./libs/config/src/executor.constants.ts");
function getSupportedModels(modelType) {
    const models = executor_constants_1.SUPPORTED_MODELS[modelType];
    return models ? [...models] : [];
}
function generateExecutorPath(region, modelType, executorId) {
    return `/executors/${region}/${modelType}/${executorId}`;
}


/***/ }),

/***/ "./libs/config/src/index.ts":
/*!**********************************!*\
  !*** ./libs/config/src/index.ts ***!
  \**********************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __exportStar = (this && this.__exportStar) || function(m, exports) {
    for (var p in m) if (p !== "default" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);
};
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.configuration = void 0;
__exportStar(__webpack_require__(/*! ./config-validation.service */ "./libs/config/src/config-validation.service.ts"), exports);
var configuration_1 = __webpack_require__(/*! ./configuration */ "./libs/config/src/configuration.ts");
Object.defineProperty(exports, "configuration", ({ enumerable: true, get: function () { return configuration_1.default; } }));
__exportStar(__webpack_require__(/*! ./executor.constants */ "./libs/config/src/executor.constants.ts"), exports);
__exportStar(__webpack_require__(/*! ./executor.utils */ "./libs/config/src/executor.utils.ts"), exports);
__exportStar(__webpack_require__(/*! ./etcd-paths */ "./libs/config/src/etcd-paths.ts"), exports);


/***/ }),

/***/ "./libs/etcd/src/etcd.module.ts":
/*!**************************************!*\
  !*** ./libs/etcd/src/etcd.module.ts ***!
  \**************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.EtcdModule = void 0;
const common_1 = __webpack_require__(/*! @nestjs/common */ "@nestjs/common");
const etcd_service_1 = __webpack_require__(/*! ./etcd.service */ "./libs/etcd/src/etcd.service.ts");
let EtcdModule = class EtcdModule {
};
exports.EtcdModule = EtcdModule;
exports.EtcdModule = EtcdModule = __decorate([
    (0, common_1.Module)({
        providers: [etcd_service_1.EtcdService],
        exports: [etcd_service_1.EtcdService],
    })
], EtcdModule);


/***/ }),

/***/ "./libs/etcd/src/etcd.service.ts":
/*!***************************************!*\
  !*** ./libs/etcd/src/etcd.service.ts ***!
  \***************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var EtcdService_1;
var _a;
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.EtcdService = void 0;
const common_1 = __webpack_require__(/*! @nestjs/common */ "@nestjs/common");
const config_1 = __webpack_require__(/*! @nestjs/config */ "@nestjs/config");
const etcd3_1 = __webpack_require__(/*! etcd3 */ "etcd3");
let EtcdService = EtcdService_1 = class EtcdService {
    configService;
    logger = new common_1.Logger(EtcdService_1.name);
    etcdClient;
    modelType;
    leaseRenewalIntervals = new Map();
    activeLeasesMap = new Map();
    constructor(configService) {
        this.configService = configService;
        this.modelType = this.configService.get('EXECUTOR_MODEL_TYPE', 'openai');
    }
    onModuleInit() {
        const etcdEndpoints = this.configService.get('etcd.endpoints', ['localhost:2379']);
        this.etcdClient = new etcd3_1.Etcd3({
            hosts: etcdEndpoints,
        });
        this.logger.log(`Initialized etcd client with endpoints: ${etcdEndpoints.join(', ')}`);
        this.logger.log(`Model Type: ${this.modelType}`);
    }
    onModuleDestroy() {
        if (this.etcdClient) {
            this.stopAllLeaseRenewals();
            this.etcdClient.close();
            this.logger.log('etcd client closed');
        }
    }
    async getActiveApiKeys(provider) {
        const keyPrefix = `/third-party-keys/${provider}/`;
        this.logger.debug(`Fetching third-party keys with prefix: ${keyPrefix}`);
        if (!this.etcdClient) {
            this.logger.error('etcd client is not initialized');
            return [];
        }
        try {
            const kvs = await this.etcdClient.getAll().prefix(keyPrefix);
            this.logger.debug(`Raw etcd result:`, kvs);
            const keys = [];
            for (const [fullPath, jsonValue] of Object.entries(kvs)) {
                this.logger.debug(`Processing third-party key: ${fullPath}, value: ${jsonValue}`);
                try {
                    const keyInfo = JSON.parse(jsonValue);
                    if (keyInfo.status === 'active') {
                        const pathParts = fullPath.split('/');
                        const etcdKeyId = pathParts[pathParts.length - 1];
                        keys.push({
                            ...keyInfo,
                            etcdKeyId: etcdKeyId,
                        });
                        this.logger.debug(`Added third-party key: ${fullPath} with etcdKeyId: ${etcdKeyId}`);
                    }
                    else {
                        this.logger.debug(`Skipped non-active key: ${fullPath}, status: ${keyInfo.status}`);
                    }
                }
                catch (error) {
                    this.logger.warn(`Failed to parse key info for ${fullPath}: ${error instanceof Error ? error.message : String(error)}`);
                }
            }
            this.logger.log(`Found ${keys.length} third-party keys for provider ${provider}`);
            return keys;
        }
        catch (error) {
            this.logger.error(`Failed to fetch keys from etcd: ${error instanceof Error ? error.message : String(error)}`);
            return [];
        }
    }
    async debugGetRawEtcdData() {
        const keyPrefix = `/third-party-keys/openai/`;
        try {
            const result = await this.etcdClient.getAll().prefix(keyPrefix);
            this.logger.debug(`Raw debug result:`, result);
            return result;
        }
        catch (error) {
            this.logger.error(`Debug etcd query failed: ${error instanceof Error ? error.message : String(error)}`);
            throw error;
        }
    }
    getModelType() {
        return this.modelType;
    }
    getEtcdClient() {
        return this.etcdClient || null;
    }
    isClientReady() {
        return !!this.etcdClient;
    }
    async updateApiKey(provider, keyId, updates) {
        if (!this.etcdClient) {
            throw new Error('etcd client is not initialized');
        }
        const keyPath = `/third-party-keys/${provider}/${keyId}`;
        try {
            const currentData = await this.etcdClient.get(keyPath);
            if (!currentData) {
                throw new Error(`Key ${keyId} not found`);
            }
            const currentKeyInfo = JSON.parse(currentData);
            const updatedKeyInfo = {
                ...currentKeyInfo,
                ...updates,
            };
            await this.etcdClient.put(keyPath).value(JSON.stringify(updatedKeyInfo));
            this.logger.log(`Updated API key: ${keyPath}`);
        }
        catch (error) {
            this.logger.error(`Failed to update API key ${keyPath}: ${error instanceof Error ? error.message : String(error)}`);
            throw error;
        }
    }
    async getAllKeys(provider) {
        if (!this.etcdClient) {
            this.logger.error('etcd client is not initialized');
            return {};
        }
        const keyPrefix = provider ? `/third-party-keys/${provider}/` : '/third-party-keys/';
        try {
            const kvs = await this.etcdClient.getAll().prefix(keyPrefix);
            const result = {};
            for (const [fullPath, jsonValue] of Object.entries(kvs)) {
                try {
                    const keyInfo = JSON.parse(jsonValue);
                    result[fullPath] = keyInfo;
                }
                catch {
                    this.logger.warn(`Failed to parse key info: ${fullPath}`);
                }
            }
            return result;
        }
        catch (error) {
            this.logger.error(`Failed to get all keys: ${error instanceof Error ? error.message : String(error)}`);
            return {};
        }
    }
    stopAllLeaseRenewals() {
        for (const [leaseId, interval] of this.leaseRenewalIntervals) {
            clearInterval(interval);
            this.logger.debug(`Stopped lease renewal for ${leaseId} during shutdown`);
        }
        this.leaseRenewalIntervals.clear();
        this.activeLeasesMap.clear();
        this.logger.log('Stopped all lease renewals');
    }
};
exports.EtcdService = EtcdService;
exports.EtcdService = EtcdService = EtcdService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [typeof (_a = typeof config_1.ConfigService !== "undefined" && config_1.ConfigService) === "function" ? _a : Object])
], EtcdService);


/***/ }),

/***/ "./libs/etcd/src/index.ts":
/*!********************************!*\
  !*** ./libs/etcd/src/index.ts ***!
  \********************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __exportStar = (this && this.__exportStar) || function(m, exports) {
    for (var p in m) if (p !== "default" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);
};
Object.defineProperty(exports, "__esModule", ({ value: true }));
__exportStar(__webpack_require__(/*! ./etcd.module */ "./libs/etcd/src/etcd.module.ts"), exports);
__exportStar(__webpack_require__(/*! ./etcd.service */ "./libs/etcd/src/etcd.service.ts"), exports);


/***/ }),

/***/ "./libs/executor-registry/src/executor-registry.module.ts":
/*!****************************************************************!*\
  !*** ./libs/executor-registry/src/executor-registry.module.ts ***!
  \****************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.ExecutorRegistryModule = void 0;
const common_1 = __webpack_require__(/*! @nestjs/common */ "@nestjs/common");
const executor_registry_service_1 = __webpack_require__(/*! ./executor-registry.service */ "./libs/executor-registry/src/executor-registry.service.ts");
const etcd_1 = __webpack_require__(/*! @app/etcd */ "./libs/etcd/src/index.ts");
const secret_manager_1 = __webpack_require__(/*! @app/secret-manager */ "./libs/secret-manager/src/index.ts");
let ExecutorRegistryModule = class ExecutorRegistryModule {
};
exports.ExecutorRegistryModule = ExecutorRegistryModule;
exports.ExecutorRegistryModule = ExecutorRegistryModule = __decorate([
    (0, common_1.Module)({
        imports: [etcd_1.EtcdModule, secret_manager_1.SecretManagerModule],
        providers: [executor_registry_service_1.ExecutorRegistryService],
        exports: [executor_registry_service_1.ExecutorRegistryService],
    })
], ExecutorRegistryModule);


/***/ }),

/***/ "./libs/executor-registry/src/executor-registry.service.ts":
/*!*****************************************************************!*\
  !*** ./libs/executor-registry/src/executor-registry.service.ts ***!
  \*****************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var ExecutorRegistryService_1;
var _a, _b;
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.ExecutorRegistryService = void 0;
const common_1 = __webpack_require__(/*! @nestjs/common */ "@nestjs/common");
const config_1 = __webpack_require__(/*! @nestjs/config */ "@nestjs/config");
const etcd_1 = __webpack_require__(/*! @app/etcd */ "./libs/etcd/src/index.ts");
const config_2 = __webpack_require__(/*! @app/config */ "./libs/config/src/index.ts");
class SimpleLatencyTracker {
    latencies = [];
    maxSamples = 60;
    addLatency(latency) {
        this.latencies.push(latency);
        if (this.latencies.length > this.maxSamples) {
            this.latencies.shift();
        }
    }
    getAverageLatency() {
        if (this.latencies.length === 0)
            return 0;
        const sum = this.latencies.reduce((acc, lat) => acc + lat, 0);
        return Math.round(sum / this.latencies.length);
    }
    reset() {
        this.latencies = [];
    }
}
let ExecutorRegistryService = ExecutorRegistryService_1 = class ExecutorRegistryService {
    etcdService;
    configService;
    logger = new common_1.Logger(ExecutorRegistryService_1.name);
    executorInfo = null;
    executorLease = null;
    heartbeatInterval = null;
    etcdClient = null;
    latencyTracker = new SimpleLatencyTracker();
    startedAt = Date.now();
    constructor(etcdService, configService) {
        this.etcdService = etcdService;
        this.configService = configService;
        this.logger.log('ExecutorRegistryService constructor called');
    }
    onApplicationBootstrap() {
        try {
            this.logger.log('ExecutorRegistryService starting...');
            this.etcdClient = this.etcdService.getEtcdClient();
            if (!this.etcdClient) {
                throw new Error('Failed to get etcd client');
            }
            setTimeout(() => {
                void this.registerExecutor();
            }, 2000);
            this.logger.log('ExecutorRegistryService initialized');
        }
        catch (error) {
            this.logger.error(`Failed to initialize ExecutorRegistryService: ${error instanceof Error ? error.message : String(error)}`);
        }
    }
    async onModuleDestroy() {
        if (this.executorInfo) {
            await this.unregisterExecutor();
        }
    }
    async registerExecutor() {
        try {
            const executorId = this.configService.get('executor.id', 'executor-001');
            const region = this.configService.get('executor.region', 'default');
            const modelType = this.configService.get('executor.modelType', 'openai');
            const publicUrl = this.configService.get('executor.publicUrl', 'http://localhost:3000');
            const maxConcurrentRequests = this.configService.get('executor.maxConcurrentRequests', 10);
            this.executorInfo = {
                id: executorId,
                region,
                modelType,
                publicUrl,
                status: 'active',
                lastHeartbeat: Date.now(),
                maxConcurrentRequests,
                currentRequests: 0,
                supportedModels: (0, config_2.getSupportedModels)(modelType),
            };
            await this.registerExecutorWithEtcd(this.executorInfo);
            this.logger.log(`Executor registered successfully: ${executorId}`);
            this.logger.log(`Region: ${region}, Model Type: ${modelType}`);
            this.logger.log(`Public URL: ${publicUrl}`);
        }
        catch (error) {
            this.logger.error(`Failed to register executor: ${error instanceof Error ? error.message : String(error)}`);
            throw error;
        }
    }
    async registerExecutorWithEtcd(executorInfo) {
        if (!this.etcdClient) {
            throw new Error('etcd client is not initialized');
        }
        const executorPath = (0, config_2.generateExecutorPath)(executorInfo.region, executorInfo.modelType, executorInfo.id);
        try {
            const lease = this.etcdClient.lease(60);
            const leaseId = await lease.grant();
            this.executorLease = lease;
            const registrationData = {
                url: executorInfo.publicUrl,
                latency: this.latencyTracker.getAverageLatency(),
                load: this.calculateLoad(),
                started_at: this.startedAt,
                last_heartbeat: Date.now(),
                id: executorInfo.id,
                region: executorInfo.region,
                modelType: executorInfo.modelType,
                status: executorInfo.status,
                lastHeartbeat: Date.now(),
                maxConcurrentRequests: executorInfo.maxConcurrentRequests,
                currentRequests: executorInfo.currentRequests,
                supportedModels: executorInfo.supportedModels,
                publicUrl: executorInfo.publicUrl,
            };
            await this.etcdClient
                .put(executorPath)
                .value(JSON.stringify(registrationData))
                .lease(leaseId)
                .exec();
            this.startHeartbeat(executorPath, executorInfo);
            this.logger.log(`Registered executor: ${executorPath}`);
        }
        catch (error) {
            this.logger.error(`Failed to register executor: ${error instanceof Error ? error.message : String(error)}`);
            throw error;
        }
    }
    startHeartbeat(executorPath, executorInfo) {
        this.heartbeatInterval = setInterval(() => {
            void (async () => {
                try {
                    if (this.executorLease && this.etcdClient) {
                        await this.executorLease.keepaliveOnce();
                        executorInfo.lastHeartbeat = Date.now();
                        const registrationData = {
                            url: executorInfo.publicUrl,
                            latency: this.latencyTracker.getAverageLatency(),
                            load: this.calculateLoad(),
                            started_at: this.startedAt,
                            last_heartbeat: Date.now(),
                            id: executorInfo.id,
                            region: executorInfo.region,
                            modelType: executorInfo.modelType,
                            status: executorInfo.status,
                            lastHeartbeat: Date.now(),
                            maxConcurrentRequests: executorInfo.maxConcurrentRequests,
                            currentRequests: executorInfo.currentRequests,
                            supportedModels: executorInfo.supportedModels,
                            publicUrl: executorInfo.publicUrl,
                        };
                        await this.etcdClient.put(executorPath).value(JSON.stringify(registrationData)).exec();
                        this.logger.debug(`Heartbeat sent for executor: ${executorPath}`);
                    }
                }
                catch (error) {
                    this.logger.error(`Heartbeat failed for executor ${executorPath}: ${error instanceof Error ? error.message : String(error)}`);
                    void this.registerExecutorWithEtcd(executorInfo).catch((regError) => {
                        this.logger.error(`Failed to re-register executor: ${regError instanceof Error ? regError.message : String(regError)}`);
                    });
                }
            })();
        }, 30000);
    }
    async unregisterExecutor() {
        if (!this.executorInfo) {
            return;
        }
        try {
            if (this.heartbeatInterval) {
                clearInterval(this.heartbeatInterval);
                this.heartbeatInterval = null;
            }
            if (this.executorLease) {
                await this.executorLease.revoke();
                this.executorLease = null;
            }
            this.logger.log(`Executor unregistered: ${this.executorInfo.id}`);
            this.executorInfo = null;
        }
        catch (error) {
            this.logger.error(`Failed to unregister executor: ${error instanceof Error ? error.message : String(error)}`);
        }
    }
    updateCurrentRequests(count) {
        if (this.executorInfo) {
            this.executorInfo.currentRequests = count;
        }
    }
    getExecutorInfo() {
        return this.executorInfo;
    }
    calculateLoad() {
        if (!this.executorInfo)
            return 0;
        return this.executorInfo.currentRequests / this.executorInfo.maxConcurrentRequests;
    }
    recordLatency(latency) {
        this.latencyTracker.addLatency(latency);
    }
    getAverageLatency() {
        return this.latencyTracker.getAverageLatency();
    }
    getCurrentLoad() {
        return this.calculateLoad();
    }
};
exports.ExecutorRegistryService = ExecutorRegistryService;
exports.ExecutorRegistryService = ExecutorRegistryService = ExecutorRegistryService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [typeof (_a = typeof etcd_1.EtcdService !== "undefined" && etcd_1.EtcdService) === "function" ? _a : Object, typeof (_b = typeof config_1.ConfigService !== "undefined" && config_1.ConfigService) === "function" ? _b : Object])
], ExecutorRegistryService);


/***/ }),

/***/ "./libs/executor-registry/src/index.ts":
/*!*********************************************!*\
  !*** ./libs/executor-registry/src/index.ts ***!
  \*********************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __exportStar = (this && this.__exportStar) || function(m, exports) {
    for (var p in m) if (p !== "default" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);
};
Object.defineProperty(exports, "__esModule", ({ value: true }));
__exportStar(__webpack_require__(/*! ./executor-registry.module */ "./libs/executor-registry/src/executor-registry.module.ts"), exports);
__exportStar(__webpack_require__(/*! ./executor-registry.service */ "./libs/executor-registry/src/executor-registry.service.ts"), exports);


/***/ }),

/***/ "./libs/key-manager/src/api-key-status.service.ts":
/*!********************************************************!*\
  !*** ./libs/key-manager/src/api-key-status.service.ts ***!
  \********************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var ApiKeyStatusService_1;
var _a, _b;
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.ApiKeyStatusService = void 0;
const common_1 = __webpack_require__(/*! @nestjs/common */ "@nestjs/common");
const etcd_1 = __webpack_require__(/*! @app/etcd */ "./libs/etcd/src/index.ts");
const config_1 = __webpack_require__(/*! @nestjs/config */ "@nestjs/config");
let ApiKeyStatusService = ApiKeyStatusService_1 = class ApiKeyStatusService {
    etcdService;
    configService;
    logger = new common_1.Logger(ApiKeyStatusService_1.name);
    provider;
    statusWatcher = null;
    constructor(etcdService, configService) {
        this.etcdService = etcdService;
        this.configService = configService;
        this.provider = this.configService.get('executor.modelType', 'openai');
    }
    async onModuleInit() {
        this.logger.log(`API Key Status Service initialized for provider: ${this.provider}`);
        await this.startStatusWatcher();
    }
    async onModuleDestroy() {
        if (this.statusWatcher) {
            await this.statusWatcher.cancel();
            this.statusWatcher = null;
        }
        this.logger.log('API Key Status Service destroyed');
    }
    async getKeyStatus(keyId) {
        try {
            const etcdClient = this.etcdService.getEtcdClient();
            if (!etcdClient) {
                throw new Error('etcd client is not initialized');
            }
            const keyPath = `/keys/${this.provider}/${keyId}`;
            const value = await etcdClient.get(keyPath);
            if (!value) {
                return null;
            }
            const status = JSON.parse(value);
            this.logger.debug(`Retrieved status for key ${keyId}: ${status.status}`);
            return status;
        }
        catch (error) {
            this.logger.error(`Failed to get key status for ${keyId}: ${error instanceof Error ? error.message : String(error)}`);
            return null;
        }
    }
    async updateKeyStatus(keyId, status, lastError, note) {
        try {
            const etcdClient = this.etcdService.getEtcdClient();
            if (!etcdClient) {
                throw new Error('etcd client is not initialized');
            }
            const keyPath = `/keys/${this.provider}/${keyId}`;
            let existingStatus = null;
            try {
                const existingValue = await etcdClient.get(keyPath);
                if (existingValue) {
                    existingStatus = JSON.parse(existingValue);
                }
            }
            catch {
                this.logger.debug(`No existing status found for key ${keyId}, creating new one`);
            }
            const updatedStatus = {
                key: existingStatus?.key || `sk-${keyId}`,
                status,
                lastUsedAt: Date.now(),
                lastError,
                note,
            };
            await etcdClient.put(keyPath).value(JSON.stringify(updatedStatus));
            this.logger.log(`Updated key ${keyId} status to ${status}`);
            return true;
        }
        catch (error) {
            this.logger.error(`Failed to update key status for ${keyId}: ${error instanceof Error ? error.message : String(error)}`);
            return false;
        }
    }
    async getAllKeyStatuses() {
        try {
            const etcdClient = this.etcdService.getEtcdClient();
            if (!etcdClient) {
                throw new Error('etcd client is not initialized');
            }
            const prefix = `/keys/${this.provider}/`;
            const kvs = await etcdClient.getAll().prefix(prefix);
            const statusMap = new Map();
            for (const [fullPath, jsonValue] of Object.entries(kvs)) {
                try {
                    const keyId = fullPath.replace(prefix, '');
                    const status = JSON.parse(jsonValue);
                    statusMap.set(keyId, status);
                }
                catch (parseError) {
                    this.logger.warn(`Failed to parse key status for ${fullPath}: ${parseError instanceof Error ? parseError.message : String(parseError)}`);
                }
            }
            this.logger.debug(`Retrieved ${statusMap.size} key statuses for provider ${this.provider}`);
            return statusMap;
        }
        catch (error) {
            this.logger.error(`Failed to get all key statuses: ${error instanceof Error ? error.message : String(error)}`);
            return new Map();
        }
    }
    async getActiveKeys() {
        const allStatuses = await this.getAllKeyStatuses();
        const activeKeys = [];
        for (const [keyId, status] of allStatuses.entries()) {
            if (status.status === 'active') {
                activeKeys.push(keyId);
            }
        }
        this.logger.debug(`Found ${activeKeys.length} active keys for provider ${this.provider}`);
        return activeKeys;
    }
    async markKeyRateLimited(keyId, reason) {
        return this.updateKeyStatus(keyId, 'rate_limited', reason);
    }
    async markKeyExhausted(keyId, reason) {
        return this.updateKeyStatus(keyId, 'exhausted', reason);
    }
    async markKeyRevoked(keyId, reason) {
        return this.updateKeyStatus(keyId, 'revoked', reason);
    }
    async markKeyActive(keyId, note) {
        return this.updateKeyStatus(keyId, 'active', undefined, note);
    }
    async isKeyAvailable(keyId) {
        const status = await this.getKeyStatus(keyId);
        return status?.status === 'active';
    }
    async startStatusWatcher() {
        try {
            const etcdClient = this.etcdService.getEtcdClient();
            if (!etcdClient) {
                this.logger.warn('etcd client not available, status watcher not started');
                return;
            }
            const watchPrefix = `/keys/${this.provider}/`;
            this.statusWatcher = await etcdClient.watch().prefix(watchPrefix).create();
            this.statusWatcher.on('put', (event) => {
                try {
                    const keyPath = event.key.toString();
                    const keyId = keyPath.replace(watchPrefix, '');
                    const status = JSON.parse(event.value.toString());
                    this.logger.debug(`Key status changed: ${keyId} -> ${status.status}`);
                    this.handleKeyStatusChange(keyId, status);
                }
                catch (error) {
                    this.logger.error(`Failed to process key status change: ${error instanceof Error ? error.message : String(error)}`);
                }
            });
            this.statusWatcher.on('delete', (event) => {
                try {
                    const keyPath = event.key.toString();
                    const keyId = keyPath.replace(watchPrefix, '');
                    this.logger.debug(`Key status deleted: ${keyId}`);
                    this.handleKeyStatusDelete(keyId);
                }
                catch (error) {
                    this.logger.error(`Failed to process key status deletion: ${error instanceof Error ? error.message : String(error)}`);
                }
            });
            this.logger.log(`Started watching key status changes with prefix: ${watchPrefix}`);
        }
        catch (error) {
            this.logger.error(`Failed to start status watcher: ${error instanceof Error ? error.message : String(error)}`);
        }
    }
    handleKeyStatusChange(keyId, status) {
        this.logger.log(`Key ${keyId} status changed to ${status.status}`);
    }
    handleKeyStatusDelete(keyId) {
        this.logger.log(`Key ${keyId} status was deleted`);
    }
    async getStatusStatistics() {
        const allStatuses = await this.getAllKeyStatuses();
        const stats = {
            total: allStatuses.size,
            active: 0,
            rateLimited: 0,
            exhausted: 0,
            revoked: 0,
        };
        for (const status of allStatuses.values()) {
            switch (status.status) {
                case 'active':
                    stats.active++;
                    break;
                case 'rate_limited':
                    stats.rateLimited++;
                    break;
                case 'exhausted':
                    stats.exhausted++;
                    break;
                case 'revoked':
                    stats.revoked++;
                    break;
            }
        }
        return stats;
    }
};
exports.ApiKeyStatusService = ApiKeyStatusService;
exports.ApiKeyStatusService = ApiKeyStatusService = ApiKeyStatusService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [typeof (_a = typeof etcd_1.EtcdService !== "undefined" && etcd_1.EtcdService) === "function" ? _a : Object, typeof (_b = typeof config_1.ConfigService !== "undefined" && config_1.ConfigService) === "function" ? _b : Object])
], ApiKeyStatusService);


/***/ }),

/***/ "./libs/key-manager/src/encryption.service.ts":
/*!****************************************************!*\
  !*** ./libs/key-manager/src/encryption.service.ts ***!
  \****************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var EncryptionService_1;
var _a, _b, _c, _d;
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.EncryptionService = void 0;
const common_1 = __webpack_require__(/*! @nestjs/common */ "@nestjs/common");
const config_1 = __webpack_require__(/*! @nestjs/config */ "@nestjs/config");
const etcd_1 = __webpack_require__(/*! @app/etcd */ "./libs/etcd/src/index.ts");
const secret_manager_1 = __webpack_require__(/*! @app/secret-manager */ "./libs/secret-manager/src/index.ts");
const key_status_manager_service_1 = __webpack_require__(/*! ./key-status-manager.service */ "./libs/key-manager/src/key-status-manager.service.ts");
const nacl = __webpack_require__(/*! tweetnacl */ "tweetnacl");
const chacha_1 = __webpack_require__(/*! @noble/ciphers/chacha */ "@noble/ciphers/chacha");
let EncryptionService = EncryptionService_1 = class EncryptionService {
    configService;
    etcdService;
    secretManagerService;
    keyStatusManager;
    logger = new common_1.Logger(EncryptionService_1.name);
    executorKeyPair = null;
    provider;
    region;
    executorId;
    constructor(configService, etcdService, secretManagerService, keyStatusManager) {
        this.configService = configService;
        this.etcdService = etcdService;
        this.secretManagerService = secretManagerService;
        this.keyStatusManager = keyStatusManager;
        this.provider = this.configService.get('executor.modelType', 'openai');
        this.region = this.configService.get('executor.region', 'default');
        this.executorId = this.configService.get('executor.id', 'executor-001');
    }
    async onModuleInit() {
        await this.initializeKeyPair();
        await this.registerPublicKey();
        void this.startApiKeyWatcher();
        void this.startPeriodicKeyCheck();
        this.logger.log('Encryption service initialized');
    }
    async initializeKeyPair() {
        try {
            this.logger.log('Initializing executor key pair...');
            try {
                const privateKeyBase64 = await this.secretManagerService.loadPrivateKey();
                if (privateKeyBase64) {
                    const privateKey = this.base64ToUint8Array(privateKeyBase64);
                    if (privateKey.length !== 32) {
                        throw new Error(`Invalid private key length: ${privateKey.length}, expected 32 bytes`);
                    }
                    const publicKey = nacl.scalarMult.base(privateKey);
                    this.executorKeyPair = {
                        secretKey: privateKey,
                        publicKey: publicKey,
                    };
                    this.logger.log('✅ Loaded executor key pair from GCP Secret Manager');
                    this.logger.log(`   Public key: ${this.uint8ArrayToBase64(this.executorKeyPair.publicKey)}`);
                    return;
                }
            }
            catch (secretError) {
                this.logger.warn(`Failed to load from GCP Secret Manager: ${secretError instanceof Error ? secretError.message : String(secretError)}`);
                this.logger.log('Falling back to environment variable...');
            }
            const privateKeyBase64 = this.configService.get('EXECUTOR_PRIVATE_KEY');
            if (privateKeyBase64) {
                try {
                    const privateKey = this.base64ToUint8Array(privateKeyBase64);
                    if (privateKey.length !== 32) {
                        throw new Error(`Invalid private key length: ${privateKey.length}, expected 32 bytes`);
                    }
                    const publicKey = nacl.scalarMult.base(privateKey);
                    this.executorKeyPair = {
                        secretKey: privateKey,
                        publicKey: publicKey,
                    };
                    this.logger.log('✅ Loaded executor key pair from environment variable');
                    this.logger.log(`   Public key: ${this.uint8ArrayToBase64(this.executorKeyPair.publicKey)}`);
                    return;
                }
                catch (error) {
                    this.logger.warn(`Failed to load private key from environment: ${error instanceof Error ? error.message : String(error)}`);
                }
            }
            this.logger.warn('⚠️  No persistent key found, generating new key pair (DEVELOPMENT MODE ONLY)');
            this.executorKeyPair = nacl.box.keyPair();
            this.logger.warn('🔑 Generated new executor key pair:');
            this.logger.warn(`   Private key: ${this.uint8ArrayToBase64(this.executorKeyPair.secretKey)}`);
            this.logger.warn(`   Public key: ${this.uint8ArrayToBase64(this.executorKeyPair.publicKey)}`);
            this.logger.warn('⚠️  This key pair will be lost on restart! Use GCP Secret Manager for production.');
        }
        catch (error) {
            this.logger.error(`Failed to initialize key pair: ${error instanceof Error ? error.message : String(error)}`);
            throw error;
        }
    }
    async registerPublicKey() {
        if (!this.executorKeyPair) {
            throw new Error('Executor key pair not initialized');
        }
        try {
            const etcdClient = this.etcdService.getEtcdClient();
            if (!etcdClient) {
                throw new Error('etcd client is not initialized');
            }
            const keyPath = `/keys/public/${this.provider}/${this.region}/${this.executorId}`;
            const publicKeyRegistration = {
                keyId: this.executorId,
                publicKey: this.uint8ArrayToBase64(this.executorKeyPair.publicKey),
                provider: this.provider,
                region: this.region,
                status: 'active',
                registeredAt: new Date().toISOString(),
                lastHeartbeat: new Date().toISOString(),
                executorUrl: `http://localhost:${this.configService.get('port', 3001)}`,
            };
            await etcdClient.put(keyPath).value(JSON.stringify(publicKeyRegistration));
            this.logger.log(`Registered public key at path: ${keyPath}`);
            this.startHeartbeat();
        }
        catch (error) {
            this.logger.error(`Failed to register public key: ${error instanceof Error ? error.message : String(error)}`);
            throw error;
        }
    }
    startHeartbeat() {
        setInterval(() => {
            void (async () => {
                try {
                    await this.sendHeartbeat();
                }
                catch (error) {
                    this.logger.error(`Failed to send heartbeat: ${error instanceof Error ? error.message : String(error)}`);
                }
            })();
        }, 2 * 60 * 1000);
    }
    async startApiKeyWatcher() {
        try {
            const etcdClient = this.etcdService.getEtcdClient();
            if (!etcdClient) {
                this.logger.warn('etcd client not available, API key watcher not started');
                return;
            }
            const watchPrefix = `/api-keys/${this.provider}/${this.region}/`;
            this.logger.log(`🔍 Starting API key watcher for prefix: ${watchPrefix}`);
            const watcher = await etcdClient.watch().prefix(watchPrefix).create();
            watcher.on('put', (event) => {
                void (async () => {
                    try {
                        const keyPath = event.key.toString();
                        const keyData = JSON.parse(event.value.toString());
                        this.logger.log(`📥 New API key detected: ${keyPath}`);
                        if (keyData.keyId === this.executorId && keyData.status === 'waiting-to-verify') {
                            this.logger.log(`🔐 Attempting to decrypt and verify new API key: ${keyData.keyId}`);
                            try {
                                const decryptedKey = this.decryptApiKey(keyData);
                                if (decryptedKey) {
                                    await this.markKeyAsActive(keyData);
                                    this.logger.log(`✅ Successfully verified and activated new API key: ${keyData.keyId}`);
                                }
                            }
                            catch (decryptError) {
                                this.logger.error(`❌ Failed to decrypt new API key: ${decryptError instanceof Error ? decryptError.message : String(decryptError)}`);
                            }
                        }
                    }
                    catch (error) {
                        this.logger.error(`Error processing API key watch event: ${error instanceof Error ? error.message : String(error)}`);
                    }
                })();
            });
            watcher.on('error', (error) => {
                this.logger.error(`API key watcher error: ${error instanceof Error ? error.message : String(error)}`);
                setTimeout(() => {
                    this.logger.log('Restarting API key watcher...');
                    void this.startApiKeyWatcher();
                }, 5000);
            });
            this.logger.log('✅ API key watcher started successfully');
        }
        catch (error) {
            this.logger.error(`Failed to start API key watcher: ${error instanceof Error ? error.message : String(error)}`);
        }
    }
    startPeriodicKeyCheck() {
        setInterval(() => {
            void (async () => {
                try {
                    await this.checkAndVerifyWaitingKeys();
                }
                catch (error) {
                    this.logger.debug(`Periodic key check error: ${error instanceof Error ? error.message : String(error)}`);
                }
            })();
        }, 30 * 1000);
    }
    async checkAndVerifyWaitingKeys() {
        try {
            const encryptedKeys = await this.getEncryptedApiKeys();
            const waitingKeys = encryptedKeys.filter((key) => key.status === 'waiting-to-verify');
            if (waitingKeys.length > 0) {
                this.logger.debug(`🔍 Found ${waitingKeys.length} waiting-to-verify keys, attempting verification...`);
                for (const keyData of waitingKeys) {
                    try {
                        const decryptedKey = this.decryptApiKey(keyData);
                        if (decryptedKey) {
                            await this.markKeyAsActive(keyData);
                            this.logger.log(`✅ Verified and activated waiting key: ${keyData.keyId}`);
                        }
                    }
                    catch (decryptError) {
                        this.logger.debug(`Failed to decrypt waiting key ${keyData.keyId}: ${decryptError instanceof Error ? decryptError.message : String(decryptError)}`);
                    }
                }
            }
        }
        catch (error) {
            this.logger.debug(`Error in periodic key check: ${error instanceof Error ? error.message : String(error)}`);
        }
    }
    async sendHeartbeat() {
        if (!this.executorKeyPair) {
            return;
        }
        try {
            const etcdClient = this.etcdService.getEtcdClient();
            if (!etcdClient) {
                throw new Error('etcd client is not initialized');
            }
            const keyPath = `/keys/public/${this.provider}/${this.region}/${this.executorId}`;
            const currentData = await etcdClient.get(keyPath);
            if (currentData) {
                const registration = JSON.parse(currentData);
                registration.lastHeartbeat = new Date().toISOString();
                await etcdClient.put(keyPath).value(JSON.stringify(registration));
                this.logger.debug(`Sent heartbeat for key: ${this.executorId}`);
            }
        }
        catch (error) {
            this.logger.error(`Failed to send heartbeat: ${error instanceof Error ? error.message : String(error)}`);
        }
    }
    decryptApiKey(encryptedData) {
        if (!this.executorKeyPair) {
            throw new Error('Executor key pair not initialized');
        }
        const cacheKey = `${encryptedData.keyId}-${encryptedData.nonce}`;
        const cachedKey = this.keyStatusManager?.getCachedKey(cacheKey);
        if (cachedKey) {
            this.logger.debug(`Using cached decrypted key for keyId: ${encryptedData.keyId}`);
            return cachedKey;
        }
        try {
            const ciphertext = this.base64ToUint8Array(encryptedData.encryptedKey);
            const nonce = this.base64ToUint8Array(encryptedData.nonce);
            const tag = this.base64ToUint8Array(encryptedData.tag);
            const ephemeralPubKey = this.base64ToUint8Array(encryptedData.ephemeralPubKey);
            const sharedSecret = nacl.scalarMult(this.executorKeyPair.secretKey, ephemeralPubKey);
            const aead = (0, chacha_1.chacha20poly1305)(sharedSecret, nonce);
            const encryptedPayload = new Uint8Array(ciphertext.length + tag.length);
            encryptedPayload.set(ciphertext);
            encryptedPayload.set(tag, ciphertext.length);
            const plaintext = aead.decrypt(encryptedPayload);
            const decoder = new TextDecoder();
            const apiKey = decoder.decode(plaintext);
            if (this.keyStatusManager) {
                this.keyStatusManager.cacheDecryptedKey(cacheKey, apiKey);
            }
            this.logger.debug(`Successfully decrypted API key for keyId: ${encryptedData.keyId}`);
            return apiKey;
        }
        catch (error) {
            this.logger.error(`Failed to decrypt API key: ${error instanceof Error ? error.message : String(error)}`);
            throw new Error('Failed to decrypt API key');
        }
    }
    async getEncryptedApiKeys() {
        try {
            const etcdClient = this.etcdService.getEtcdClient();
            if (!etcdClient) {
                throw new Error('etcd client is not initialized');
            }
            const prefix = `/api-keys/${this.provider}/${this.region}/`;
            const kvs = await etcdClient.getAll().prefix(prefix);
            const encryptedKeys = [];
            for (const [fullPath, jsonValue] of Object.entries(kvs)) {
                try {
                    const encryptedData = JSON.parse(jsonValue);
                    if (encryptedData.keyId === this.executorId &&
                        (encryptedData.status === 'active' || encryptedData.status === 'waiting-to-verify')) {
                        encryptedKeys.push(encryptedData);
                    }
                }
                catch (parseError) {
                    this.logger.warn(`Failed to parse encrypted key data for ${fullPath}: ${parseError instanceof Error ? parseError.message : String(parseError)}`);
                }
            }
            this.logger.debug(`Found ${encryptedKeys.length} encrypted API keys for this executor`);
            return encryptedKeys;
        }
        catch (error) {
            this.logger.error(`Failed to get encrypted API keys: ${error instanceof Error ? error.message : String(error)}`);
            return [];
        }
    }
    async getDecryptedApiKeys() {
        const encryptedKeys = await this.getEncryptedApiKeys();
        const availableKeys = await this.filterAvailableEncryptedKeys(encryptedKeys);
        const decryptedKeys = [];
        for (const encryptedData of availableKeys) {
            try {
                const apiKey = this.decryptApiKey(encryptedData);
                const uuid = encryptedData.keyId || 'encrypted-key-' + Date.now();
                decryptedKeys.push({
                    uuid,
                    apiKey,
                    createdAt: encryptedData.createdAt,
                });
                if (encryptedData.status === 'waiting-to-verify') {
                    await this.markKeyAsActive(encryptedData);
                }
            }
            catch (error) {
                this.logger.warn(`Failed to decrypt key for keyId ${encryptedData.keyId}: ${error instanceof Error ? error.message : String(error)}`);
            }
        }
        this.logger.log(`Successfully decrypted ${decryptedKeys.length} out of ${availableKeys.length} available keys (${encryptedKeys.length} total)`);
        return decryptedKeys;
    }
    async markKeyAsActive(encryptedData) {
        try {
            const etcdClient = this.etcdService.getEtcdClient();
            if (!etcdClient) {
                this.logger.warn('etcd client not available, cannot update key status');
                return;
            }
            const provider = this.configService.get('EXECUTOR_MODEL_TYPE', 'openai');
            const region = this.configService.get('EXECUTOR_REGION', 'asia');
            const keyPrefix = `/api-keys/${provider}/${region}/`;
            const allKeys = await etcdClient.getAll().prefix(keyPrefix);
            for (const [keyPath, value] of Object.entries(allKeys)) {
                try {
                    const keyData = JSON.parse(value);
                    if (keyData.keyId === encryptedData.keyId &&
                        keyData.encryptedKey === encryptedData.encryptedKey &&
                        keyData.status === 'waiting-to-verify') {
                        const updatedData = {
                            ...keyData,
                            status: 'active',
                        };
                        await etcdClient.put(keyPath).value(JSON.stringify(updatedData));
                        this.logger.log(`✅ Marked encrypted API key as active: ${keyPath}`);
                        return;
                    }
                }
                catch (parseError) {
                    this.logger.debug(`Failed to parse key data at ${keyPath}: ${parseError}`);
                }
            }
            this.logger.warn(`Could not find encrypted key to mark as active for keyId: ${encryptedData.keyId}`);
        }
        catch (error) {
            this.logger.error(`Failed to mark key as active: ${error instanceof Error ? error.message : String(error)}`);
        }
    }
    async filterAvailableEncryptedKeys(encryptedKeys) {
        const availableKeys = [];
        for (const encryptedKey of encryptedKeys) {
            try {
                const keyStatus = await this.getKeyStatusFromEtcd(encryptedKey.keyId);
                if (!keyStatus) {
                    this.logger.debug(`No status found for key ${encryptedKey.keyId}, assuming available`);
                    availableKeys.push(encryptedKey);
                    continue;
                }
                if (keyStatus.status === 'rate_limited' ||
                    keyStatus.status === 'exhausted' ||
                    keyStatus.status === 'revoked') {
                    this.logger.debug(`Skipping key ${encryptedKey.keyId} due to status: ${keyStatus.status}` +
                        (keyStatus.lastError ? ` (${keyStatus.lastError})` : ''));
                    continue;
                }
                availableKeys.push(encryptedKey);
            }
            catch (error) {
                this.logger.warn(`Failed to check status for key ${encryptedKey.keyId}: ${error instanceof Error ? error.message : String(error)}`);
                availableKeys.push(encryptedKey);
            }
        }
        this.logger.log(`Filtered ${encryptedKeys.length} keys down to ${availableKeys.length} available keys for provider ${this.provider}`);
        return availableKeys;
    }
    async getKeyStatusFromEtcd(keyId) {
        try {
            const etcdClient = this.etcdService.getEtcdClient();
            if (!etcdClient) {
                return null;
            }
            const keyPath = `/keys/${this.provider}/${keyId}`;
            const statusJson = await etcdClient.get(keyPath);
            if (!statusJson) {
                return null;
            }
            return JSON.parse(statusJson);
        }
        catch (error) {
            this.logger.debug(`Failed to get key status for ${keyId}: ${error}`);
            return null;
        }
    }
    getPublicKey() {
        if (!this.executorKeyPair) {
            return null;
        }
        return this.uint8ArrayToBase64(this.executorKeyPair.publicKey);
    }
    getExecutorInfo() {
        return {
            id: this.executorId,
            provider: this.provider,
            region: this.region,
            publicKey: this.getPublicKey(),
        };
    }
    uint8ArrayToBase64(array) {
        return Buffer.from(array).toString('base64');
    }
    base64ToUint8Array(base64) {
        return new Uint8Array(Buffer.from(base64, 'base64'));
    }
    testEncryptionFlow(testApiKey = 'sk-test1234567890') {
        if (!this.executorKeyPair) {
            this.logger.error('Cannot test encryption: key pair not initialized');
            return false;
        }
        try {
            const ephemeralKeyPair = nacl.box.keyPair();
            const sharedSecret = nacl.scalarMult(ephemeralKeyPair.secretKey, this.executorKeyPair.publicKey);
            const encoder = new TextEncoder();
            const nonce = nacl.randomBytes(12);
            const aead = (0, chacha_1.chacha20poly1305)(sharedSecret, nonce);
            const encrypted = aead.encrypt(encoder.encode(testApiKey));
            const tagLength = 16;
            const ciphertext = encrypted.slice(0, encrypted.length - tagLength);
            const tag = encrypted.slice(encrypted.length - tagLength);
            const encryptedData = {
                keyId: this.executorId,
                encryptedKey: this.uint8ArrayToBase64(ciphertext),
                nonce: this.uint8ArrayToBase64(nonce),
                tag: this.uint8ArrayToBase64(tag),
                ephemeralPubKey: this.uint8ArrayToBase64(ephemeralKeyPair.publicKey),
                status: 'active',
                createdAt: new Date().toISOString(),
            };
            const decryptedKey = this.decryptApiKey(encryptedData);
            const success = decryptedKey === testApiKey;
            this.logger.log(`Encryption test ${success ? 'PASSED' : 'FAILED'}`);
            return success;
        }
        catch (error) {
            this.logger.error(`Encryption test failed: ${error instanceof Error ? error.message : String(error)}`);
            return false;
        }
    }
};
exports.EncryptionService = EncryptionService;
exports.EncryptionService = EncryptionService = EncryptionService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(3, (0, common_1.Inject)((0, common_1.forwardRef)(() => key_status_manager_service_1.KeyStatusManagerService))),
    __metadata("design:paramtypes", [typeof (_a = typeof config_1.ConfigService !== "undefined" && config_1.ConfigService) === "function" ? _a : Object, typeof (_b = typeof etcd_1.EtcdService !== "undefined" && etcd_1.EtcdService) === "function" ? _b : Object, typeof (_c = typeof secret_manager_1.SecretManagerService !== "undefined" && secret_manager_1.SecretManagerService) === "function" ? _c : Object, typeof (_d = typeof key_status_manager_service_1.KeyStatusManagerService !== "undefined" && key_status_manager_service_1.KeyStatusManagerService) === "function" ? _d : Object])
], EncryptionService);


/***/ }),

/***/ "./libs/key-manager/src/gateway-key-allocation.service.ts":
/*!****************************************************************!*\
  !*** ./libs/key-manager/src/gateway-key-allocation.service.ts ***!
  \****************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var GatewayKeyAllocationService_1;
var _a, _b;
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.GatewayKeyAllocationService = void 0;
const common_1 = __webpack_require__(/*! @nestjs/common */ "@nestjs/common");
const config_1 = __webpack_require__(/*! @nestjs/config */ "@nestjs/config");
const axios_1 = __webpack_require__(/*! axios */ "axios");
const crypto = __webpack_require__(/*! crypto */ "crypto");
const encryption_service_1 = __webpack_require__(/*! ./encryption.service */ "./libs/key-manager/src/encryption.service.ts");
let GatewayKeyAllocationService = GatewayKeyAllocationService_1 = class GatewayKeyAllocationService {
    configService;
    encryptionService;
    logger = new common_1.Logger(GatewayKeyAllocationService_1.name);
    httpClient;
    allocatedKeys = new Map();
    allocationId = null;
    gatewayUrl;
    executorId;
    executorRegion;
    executorModelType;
    executorSecretKey;
    constructor(configService, encryptionService) {
        this.configService = configService;
        this.encryptionService = encryptionService;
        this.gatewayUrl = this.configService.get('gateway.url', 'http://localhost:3000');
        this.executorId = this.configService.get('executor.id', 'executor-001');
        this.executorRegion = this.configService.get('executor.region', 'default');
        this.executorModelType = this.configService.get('executor.modelType', 'openai');
        this.executorSecretKey = this.configService.get('EXECUTOR_SECRET_KEY', 'default-secret-key');
        this.initializeHttpClient();
    }
    onModuleInit() {
        this.logger.log('Waiting for executor registration to complete...');
        setTimeout(() => {
            void (async () => {
                try {
                    await this.requestKeyAllocation();
                }
                catch (error) {
                    this.logger.error(`Failed to request initial key allocation: ${error instanceof Error ? error.message : String(error)}`);
                    this.logger.log('Will retry key allocation in 30 seconds...');
                }
            })();
        }, 5000);
        setInterval(() => {
            this.refreshKeyAllocation().catch((error) => {
                this.logger.error(`Failed to refresh key allocation: ${error instanceof Error ? error.message : String(error)}`);
            });
        }, 30 * 60 * 1000);
    }
    initializeHttpClient() {
        this.httpClient = axios_1.default.create({
            baseURL: this.gatewayUrl,
            timeout: 30000,
            headers: {
                'Content-Type': 'application/json',
                'User-Agent': `sight-executor/${this.executorId}`,
            },
        });
        this.httpClient.interceptors.request.use((config) => {
            const executorSecret = this.generateExecutorSecret();
            config.headers['x-executor-id'] = this.executorId;
            config.headers['x-executor-secret'] = executorSecret;
            config.headers['x-executor-region'] = this.executorRegion;
            return config;
        });
        this.httpClient.interceptors.response.use((response) => response, (error) => {
            const apiError = error;
            this.logger.error(`Gateway request failed: ${apiError.response?.status} ${apiError.response?.status}`);
            if (apiError.response?.data) {
                this.logger.error(`Error details:`, apiError.response.data);
            }
            return Promise.reject(error instanceof Error ? error : new Error(String(error)));
        });
    }
    generateExecutorSecret() {
        const payload = `${this.executorId}:${this.executorRegion}:${this.executorModelType}:${this.executorSecretKey}`;
        return crypto.createHash('sha256').update(payload).digest('hex').substring(0, 32);
    }
    async requestKeyAllocation() {
        try {
            const scope = `${this.executorModelType}:${this.executorRegion}`;
            this.logger.log(`Requesting API key allocation for scope: ${scope}`);
            try {
                const response = await this.httpClient.get('/executor/key', {
                    params: { scope },
                });
                if (response.data.success && response.data.data.keys.length > 0) {
                    this.processAllocatedKeys(response.data.data);
                    this.allocationId = response.data.data.allocationId;
                    this.logger.log(`Successfully allocated ${response.data.data.keys.length} API keys from Gateway`);
                    return;
                }
            }
            catch (gatewayError) {
                this.logger.warn(`Gateway allocation failed, falling back to direct etcd access: ${gatewayError instanceof Error ? gatewayError.message : String(gatewayError)}`);
            }
            await this.requestKeysFromEtcd();
        }
        catch (error) {
            this.logger.error(`Failed to request key allocation: ${error instanceof Error ? error.message : String(error)}`);
            throw error;
        }
    }
    async requestKeysFromEtcd() {
        try {
            this.logger.log('Getting API keys directly from etcd...');
            const encryptedKeys = await this.encryptionService.getDecryptedApiKeys();
            if (encryptedKeys.length === 0) {
                this.logger.warn('No encrypted API keys available for allocation');
                return;
            }
            this.logger.log(`Found ${encryptedKeys.length} encrypted keys to allocate`);
            for (const keyData of encryptedKeys) {
                if (this.allocatedKeys.has(keyData.uuid)) {
                    this.logger.debug(`Key ${keyData.uuid} already allocated, skipping`);
                    continue;
                }
                const managedKey = {
                    id: keyData.uuid,
                    key: keyData.apiKey,
                    status: 'active',
                    lastUsedAt: 0,
                    lastError: undefined,
                    note: 'Direct etcd allocation',
                    usageCount: 0,
                    errorCount: 0,
                    keyId: keyData.uuid,
                    lockedAt: 0,
                    lockTimeout: 0,
                };
                this.allocatedKeys.set(keyData.uuid, managedKey);
                this.logger.debug(`Processed allocated key: ${keyData.uuid}`);
            }
            this.logger.log(`Successfully allocated ${this.allocatedKeys.size} API keys from etcd (${encryptedKeys.length} total available)`);
        }
        catch (error) {
            this.logger.error(`Failed to get keys from etcd: ${error instanceof Error ? error.message : String(error)}`);
            throw error;
        }
    }
    async refreshKeyAllocation() {
        this.logger.debug('Refreshing API key allocation...');
        try {
            this.cleanupExpiredKeys();
            if (this.allocatedKeys.size < 2) {
                await this.requestKeyAllocation();
            }
        }
        catch (error) {
            this.logger.error(`Failed to refresh key allocation: ${error instanceof Error ? error.message : String(error)}`);
        }
    }
    processAllocatedKeys(allocationData) {
        for (const allocatedKey of allocationData.keys) {
            try {
                const decryptedApiKey = this.encryptionService.decryptApiKey({
                    keyId: this.executorId,
                    encryptedKey: allocatedKey.encryptedKey,
                    nonce: allocatedKey.nonce,
                    tag: allocatedKey.tag,
                    ephemeralPubKey: allocatedKey.ephemeralPubKey,
                    status: allocatedKey.status,
                    createdAt: allocatedKey.createdAt,
                });
                const managedKey = {
                    id: allocatedKey.uuid,
                    key: decryptedApiKey,
                    status: 'active',
                    lastUsedAt: 0,
                    lastError: undefined,
                    note: `Allocated from Gateway - ${allocatedKey.scope}`,
                    usageCount: 0,
                    errorCount: 0,
                    keyId: allocatedKey.uuid,
                    lockedAt: undefined,
                    lockTimeout: undefined,
                };
                this.allocatedKeys.set(allocatedKey.uuid, managedKey);
                this.logger.debug(`Processed allocated key: ${allocatedKey.uuid}`);
            }
            catch (error) {
                this.logger.error(`Failed to process allocated key ${allocatedKey.uuid}: ${error instanceof Error ? error.message : String(error)}`);
            }
        }
    }
    async getAvailableKey() {
        const availableKeys = Array.from(this.allocatedKeys.entries())
            .filter(([, managedKey]) => managedKey.status === 'active')
            .map(([keyId, managedKey]) => ({ keyId, managedKey }));
        if (availableKeys.length === 0) {
            this.logger.warn('No available allocated keys found');
            if (this.allocatedKeys.size === 0) {
                await this.requestKeyAllocation();
                return this.getAvailableKey();
            }
            return null;
        }
        availableKeys.sort((a, b) => (a.managedKey.lastUsedAt || 0) - (b.managedKey.lastUsedAt || 0));
        const selected = availableKeys[0];
        const { keyId, managedKey } = selected;
        managedKey.status = 'in_use';
        managedKey.lockedAt = Date.now();
        managedKey.lockTimeout = Date.now() + 5 * 60 * 1000;
        managedKey.lastUsedAt = Date.now();
        this.logger.debug(`Allocated key ${keyId} for use (LRU selection)`);
        return managedKey;
    }
    releaseKey(keyId) {
        const managedKey = this.allocatedKeys.get(keyId);
        if (!managedKey) {
            this.logger.warn(`Key ${keyId} not found in allocated keys`);
            return false;
        }
        managedKey.status = 'active';
        managedKey.lockedAt = undefined;
        managedKey.lockTimeout = undefined;
        this.logger.debug(`Released key ${keyId} back to active pool`);
        return true;
    }
    async handleApiError(keyId, error) {
        const managedKey = this.allocatedKeys.get(keyId);
        if (!managedKey) {
            return;
        }
        const apiError = error;
        const errorMessage = apiError?.message || String(error);
        const statusCode = apiError?.status || apiError?.response?.status || 0;
        const errorCode = apiError?.code || apiError?.error?.code || '';
        managedKey.errorCount++;
        managedKey.lastError = errorMessage;
        this.logger.debug(`Handling API error for key ${keyId}: ${statusCode} - ${errorCode} - ${errorMessage}`);
        if (statusCode === 401) {
            this.logger.error(`Key ${keyId} authentication failed, removing from pool: ${errorMessage}`);
            this.allocatedKeys.delete(keyId);
            await this.requestKeyAllocation();
            return;
        }
        else if (statusCode === 429) {
            if (errorMessage.toLowerCase().includes('quota') ||
                errorMessage.toLowerCase().includes('billing') ||
                errorMessage.toLowerCase().includes('credits')) {
                this.logger.error(`Key ${keyId} quota exhausted, removing from pool: ${errorMessage}`);
                this.allocatedKeys.delete(keyId);
                await this.requestKeyAllocation();
                return;
            }
            else {
                managedKey.status = 'rate_limited';
                managedKey.lockTimeout = Date.now() + 5 * 60 * 1000;
                this.logger.warn(`Key ${keyId} rate limited for 5 minutes: ${errorMessage}`);
                return;
            }
        }
        else if (statusCode === 403) {
            if (errorMessage.toLowerCase().includes('country') ||
                errorMessage.toLowerCase().includes('region') ||
                errorMessage.toLowerCase().includes('territory')) {
                this.logger.warn(`Key ${keyId} region restricted: ${errorMessage}`);
                this.releaseKey(keyId);
                return;
            }
            else {
                this.logger.error(`Key ${keyId} access forbidden, removing from pool: ${errorMessage}`);
                this.allocatedKeys.delete(keyId);
                await this.requestKeyAllocation();
                return;
            }
        }
        else if (statusCode >= 500) {
            this.logger.warn(`Server error for key ${keyId}, releasing: ${statusCode} - ${errorMessage}`);
            this.releaseKey(keyId);
            return;
        }
        else if (errorCode === 'model_not_found' ||
            (errorMessage.toLowerCase().includes('model') &&
                errorMessage.toLowerCase().includes('does not exist'))) {
            this.logger.debug(`Key ${keyId} doesn't support requested model, releasing: ${errorMessage}`);
            this.releaseKey(keyId);
            return;
        }
        if (this.isKeyInvalidError(error)) {
            this.logger.warn(`Removing invalid key ${keyId} due to error: ${managedKey.lastError}`);
            this.allocatedKeys.delete(keyId);
            await this.requestKeyAllocation();
        }
        else {
            this.releaseKey(keyId);
        }
    }
    isKeyInvalidError(error) {
        const apiError = error;
        const errorMessage = (apiError?.message || String(error)).toLowerCase();
        return (errorMessage.includes('insufficient_quota') ||
            errorMessage.includes('invalid_api_key') ||
            errorMessage.includes('account_deactivated') ||
            errorMessage.includes('billing_not_active'));
    }
    cleanupExpiredKeys() {
        const now = Date.now();
        const expiredKeys = [];
        for (const [keyId, managedKey] of this.allocatedKeys.entries()) {
            if (managedKey.lockTimeout && now > managedKey.lockTimeout) {
                expiredKeys.push(keyId);
            }
        }
        for (const keyId of expiredKeys) {
            this.releaseKey(keyId);
            this.logger.debug(`Released expired key: ${keyId}`);
        }
    }
    getAllocationStats() {
        let availableKeys = 0;
        let inUseKeys = 0;
        for (const managedKey of this.allocatedKeys.values()) {
            if (managedKey.status === 'active') {
                availableKeys++;
            }
            else if (managedKey.status === 'in_use') {
                inUseKeys++;
            }
        }
        return {
            totalKeys: this.allocatedKeys.size,
            availableKeys,
            inUseKeys,
            allocationId: this.allocationId,
        };
    }
    hasAvailableKeys() {
        for (const managedKey of this.allocatedKeys.values()) {
            if (managedKey.status === 'active') {
                return true;
            }
        }
        return false;
    }
};
exports.GatewayKeyAllocationService = GatewayKeyAllocationService;
exports.GatewayKeyAllocationService = GatewayKeyAllocationService = GatewayKeyAllocationService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [typeof (_a = typeof config_1.ConfigService !== "undefined" && config_1.ConfigService) === "function" ? _a : Object, typeof (_b = typeof encryption_service_1.EncryptionService !== "undefined" && encryption_service_1.EncryptionService) === "function" ? _b : Object])
], GatewayKeyAllocationService);


/***/ }),

/***/ "./libs/key-manager/src/index.ts":
/*!***************************************!*\
  !*** ./libs/key-manager/src/index.ts ***!
  \***************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __exportStar = (this && this.__exportStar) || function(m, exports) {
    for (var p in m) if (p !== "default" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);
};
Object.defineProperty(exports, "__esModule", ({ value: true }));
__exportStar(__webpack_require__(/*! ./key-manager.module */ "./libs/key-manager/src/key-manager.module.ts"), exports);
__exportStar(__webpack_require__(/*! ./key-manager.service */ "./libs/key-manager/src/key-manager.service.ts"), exports);
__exportStar(__webpack_require__(/*! ./key-status-manager.service */ "./libs/key-manager/src/key-status-manager.service.ts"), exports);
__exportStar(__webpack_require__(/*! ./encryption.service */ "./libs/key-manager/src/encryption.service.ts"), exports);
__exportStar(__webpack_require__(/*! ./api-key-status.service */ "./libs/key-manager/src/api-key-status.service.ts"), exports);
__exportStar(__webpack_require__(/*! ./gateway-key-allocation.service */ "./libs/key-manager/src/gateway-key-allocation.service.ts"), exports);


/***/ }),

/***/ "./libs/key-manager/src/key-manager.module.ts":
/*!****************************************************!*\
  !*** ./libs/key-manager/src/key-manager.module.ts ***!
  \****************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.KeyManagerModule = void 0;
const common_1 = __webpack_require__(/*! @nestjs/common */ "@nestjs/common");
const key_manager_service_1 = __webpack_require__(/*! ./key-manager.service */ "./libs/key-manager/src/key-manager.service.ts");
const key_status_manager_service_1 = __webpack_require__(/*! ./key-status-manager.service */ "./libs/key-manager/src/key-status-manager.service.ts");
const encryption_service_1 = __webpack_require__(/*! ./encryption.service */ "./libs/key-manager/src/encryption.service.ts");
const api_key_status_service_1 = __webpack_require__(/*! ./api-key-status.service */ "./libs/key-manager/src/api-key-status.service.ts");
const gateway_key_allocation_service_1 = __webpack_require__(/*! ./gateway-key-allocation.service */ "./libs/key-manager/src/gateway-key-allocation.service.ts");
const etcd_1 = __webpack_require__(/*! @app/etcd */ "./libs/etcd/src/index.ts");
const secret_manager_1 = __webpack_require__(/*! @app/secret-manager */ "./libs/secret-manager/src/index.ts");
let KeyManagerModule = class KeyManagerModule {
};
exports.KeyManagerModule = KeyManagerModule;
exports.KeyManagerModule = KeyManagerModule = __decorate([
    (0, common_1.Module)({
        imports: [etcd_1.EtcdModule, secret_manager_1.SecretManagerModule],
        controllers: [],
        providers: [
            key_manager_service_1.KeyManagerService,
            key_status_manager_service_1.KeyStatusManagerService,
            encryption_service_1.EncryptionService,
            api_key_status_service_1.ApiKeyStatusService,
            gateway_key_allocation_service_1.GatewayKeyAllocationService,
        ],
        exports: [
            key_manager_service_1.KeyManagerService,
            key_status_manager_service_1.KeyStatusManagerService,
            encryption_service_1.EncryptionService,
            api_key_status_service_1.ApiKeyStatusService,
            gateway_key_allocation_service_1.GatewayKeyAllocationService,
        ],
    })
], KeyManagerModule);


/***/ }),

/***/ "./libs/key-manager/src/key-manager.service.ts":
/*!*****************************************************!*\
  !*** ./libs/key-manager/src/key-manager.service.ts ***!
  \*****************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var KeyManagerService_1;
var _a, _b, _c, _d, _e;
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.KeyManagerService = void 0;
const common_1 = __webpack_require__(/*! @nestjs/common */ "@nestjs/common");
const config_1 = __webpack_require__(/*! @nestjs/config */ "@nestjs/config");
const etcd_1 = __webpack_require__(/*! @app/etcd */ "./libs/etcd/src/index.ts");
const key_status_manager_service_1 = __webpack_require__(/*! ./key-status-manager.service */ "./libs/key-manager/src/key-status-manager.service.ts");
const encryption_service_1 = __webpack_require__(/*! ./encryption.service */ "./libs/key-manager/src/encryption.service.ts");
const gateway_key_allocation_service_1 = __webpack_require__(/*! ./gateway-key-allocation.service */ "./libs/key-manager/src/gateway-key-allocation.service.ts");
let KeyManagerService = KeyManagerService_1 = class KeyManagerService {
    etcdService;
    configService;
    keyStatusManager;
    encryptionService;
    gatewayKeyAllocationService;
    logger = new common_1.Logger(KeyManagerService_1.name);
    provider;
    constructor(etcdService, configService, keyStatusManager, encryptionService, gatewayKeyAllocationService) {
        this.etcdService = etcdService;
        this.configService = configService;
        this.keyStatusManager = keyStatusManager;
        this.encryptionService = encryptionService;
        this.gatewayKeyAllocationService = gatewayKeyAllocationService;
        this.provider = this.etcdService.getModelType();
        this.logger.log(`KeyManagerService initialized with provider: ${this.provider}`);
    }
    async onApplicationBootstrap() {
        this.logger.log(`Starting key manager bootstrap for provider: ${this.provider}`);
        await this.waitForEtcdClient();
        this.logger.log(`Key manager fully initialized for provider: ${this.provider} (etcd-only mode)`);
    }
    async waitForEtcdClient() {
        const maxWaitTime = 10000;
        const checkInterval = 100;
        let waited = 0;
        while (waited < maxWaitTime) {
            try {
                if (this.etcdService.isClientReady()) {
                    this.logger.debug('etcd client is ready');
                    return;
                }
            }
            catch {
            }
            await new Promise((resolve) => setTimeout(resolve, checkInterval));
            waited += checkInterval;
        }
        throw new Error('etcd client failed to initialize within timeout');
    }
    async getAndLockKey() {
        this.logger.log('Getting and locking API key...');
        const gatewayKey = await this.getGatewayAllocatedKey();
        if (gatewayKey) {
            return gatewayKey;
        }
        const encryptedKey = await this.getAndLockEncryptedKey();
        if (encryptedKey) {
            return encryptedKey;
        }
        return this.getAndLockThirdPartyKey();
    }
    async getGatewayAllocatedKey() {
        try {
            if (!this.gatewayKeyAllocationService.hasAvailableKeys()) {
                this.logger.debug('No Gateway allocated keys available');
                return null;
            }
            const allocatedKey = await this.gatewayKeyAllocationService.getAvailableKey();
            if (allocatedKey) {
                this.logger.log(`Successfully got Gateway allocated key: ${allocatedKey.id}`);
                return allocatedKey;
            }
            return null;
        }
        catch (error) {
            this.logger.error(`Failed to get Gateway allocated key: ${error instanceof Error ? error.message : String(error)}`);
            return null;
        }
    }
    async getAndLockEncryptedKey() {
        try {
            const decryptedKeys = await this.encryptionService.getDecryptedApiKeys();
            if (decryptedKeys.length === 0) {
                this.logger.debug('No encrypted API keys found');
                return null;
            }
            const selectedKey = decryptedKeys[0];
            const managedKey = {
                id: selectedKey.uuid,
                key: selectedKey.apiKey,
                status: 'in_use',
                lastUsedAt: Date.now(),
                lastError: undefined,
                note: 'Encrypted API key from design document',
                usageCount: 0,
                errorCount: 0,
                keyId: selectedKey.uuid,
                lockedAt: Date.now(),
                lockTimeout: Date.now() + 5 * 60 * 1000,
            };
            this.logger.log(`Successfully locked encrypted API key: ${selectedKey.uuid}`);
            return managedKey;
        }
        catch (error) {
            this.logger.error(`Failed to get encrypted API key: ${error instanceof Error ? error.message : String(error)}`);
            return null;
        }
    }
    async getAndLockThirdPartyKey() {
        this.logger.log('Getting and locking third-party key (legacy)...');
        await this.cleanupExpiredLocksFromEtcd();
        const thirdPartyKeys = await this.getThirdPartyKeys();
        this.logger.log(`Found ${thirdPartyKeys.length} third-party keys from etcd`);
        if (thirdPartyKeys.length === 0) {
            this.logger.warn('No third-party keys found in etcd');
            return null;
        }
        const availableKeys = thirdPartyKeys.filter((key) => {
            if (key.status !== 'active') {
                return false;
            }
            if (!this.keyStatusManager.isKeyAvailable(key.keyId)) {
                this.logger.debug(`Key ${key.keyId} is blocked in memory (rate limited or exhausted)`);
                return false;
            }
            return true;
        });
        this.logger.log(`Found ${availableKeys.length} available keys (not in use and not blocked)`);
        if (availableKeys.length === 0) {
            this.logger.warn('No available keys for locking (all in use or blocked)');
            return null;
        }
        const sortedKeys = availableKeys.sort((a, b) => (a.lastUsedAt || 0) - (b.lastUsedAt || 0));
        for (const keyInfo of sortedKeys) {
            this.logger.log(`Attempting to lock third-party key ${keyInfo.keyId}`);
            const locked = await this.lockThirdPartyKey(keyInfo);
            if (locked) {
                try {
                    if (!keyInfo.encryptedApiKey) {
                        throw new Error(`Missing API key data for key ${keyInfo.keyId}`);
                    }
                    const apiKey = keyInfo.encryptedApiKey;
                    if (!apiKey.startsWith('sk-')) {
                        this.logger.warn(`Key ${keyInfo.keyId} does not appear to be a valid OpenAI API key`);
                    }
                    const managedKey = {
                        id: keyInfo.keyId,
                        key: apiKey,
                        status: 'in_use',
                        lastUsedAt: Date.now(),
                        lastError: undefined,
                        note: keyInfo.note,
                        usageCount: 0,
                        errorCount: 0,
                        keyId: keyInfo.keyId,
                        lockedAt: Date.now(),
                        lockTimeout: Date.now() + 5 * 60 * 1000,
                    };
                    this.logger.log(`Successfully locked and assigned third-party key ${keyInfo.keyId}`);
                    return managedKey;
                }
                catch (error) {
                    this.logger.error(`Failed to process key ${keyInfo.keyId}: ${error instanceof Error ? error.message : String(error)}`);
                    await this.releaseThirdPartyKey(keyInfo.keyId);
                }
            }
            else {
                this.logger.warn(`Failed to lock key ${keyInfo.keyId}, trying next one`);
            }
        }
        this.logger.warn('Failed to lock any available third-party key from etcd');
        return null;
    }
    async getThirdPartyKeys() {
        const etcdClient = this.etcdService.getEtcdClient();
        if (!etcdClient) {
            throw new Error('etcd client is not initialized');
        }
        try {
            const prefix = `/third-party-keys/${this.provider}/`;
            const kvs = await etcdClient.getAll().prefix(prefix);
            const keys = [];
            for (const [fullPath, jsonValue] of Object.entries(kvs)) {
                try {
                    const keyInfo = JSON.parse(jsonValue);
                    keys.push(keyInfo);
                    this.logger.debug(`Loaded third-party key: ${keyInfo.keyId}`);
                }
                catch (parseError) {
                    this.logger.warn(`Failed to parse key info for ${fullPath}: ${parseError instanceof Error ? parseError.message : String(parseError)}`);
                }
            }
            return keys;
        }
        catch (error) {
            this.logger.error(`Failed to fetch third-party keys: ${error instanceof Error ? error.message : String(error)}`);
            throw error;
        }
    }
    async lockThirdPartyKey(keyInfo) {
        try {
            if (keyInfo.status === 'in_use') {
                this.logger.debug(`Key ${keyInfo.keyId} is already in use`);
                return false;
            }
            if (keyInfo.status !== 'active') {
                this.logger.debug(`Key ${keyInfo.keyId} is not active (status: ${keyInfo.status})`);
                return false;
            }
            const etcdClient = this.etcdService.getEtcdClient();
            if (!etcdClient) {
                throw new Error('etcd client is not initialized');
            }
            const now = Date.now();
            const lockTimeout = now + 5 * 60 * 1000;
            const keyPath = `/third-party-keys/${this.provider}/${keyInfo.userId}/${keyInfo.keyId}`;
            const updatedKeyInfo = {
                ...keyInfo,
                status: 'in_use',
                lastUsedAt: now,
                lockedAt: now,
                lockTimeout: lockTimeout,
            };
            await etcdClient.put(keyPath).value(JSON.stringify(updatedKeyInfo));
            this.logger.log(`Locked third-party key ${keyInfo.keyId} for exclusive use`);
            return true;
        }
        catch (error) {
            this.logger.error(`Failed to lock key ${keyInfo.keyId}: ${error instanceof Error ? error.message : String(error)}`);
            return false;
        }
    }
    async releaseKey(keyId) {
        const gatewayReleased = this.gatewayKeyAllocationService.releaseKey(keyId);
        if (gatewayReleased) {
            this.logger.debug(`Released Gateway allocated key: ${keyId}`);
            return true;
        }
        return this.releaseThirdPartyKey(keyId);
    }
    async handleApiError(keyId, error) {
        const errorObj = error;
        const errorMessage = errorObj?.message ||
            (typeof errorObj?.toString === 'function' ? errorObj.toString() : String(error));
        try {
            await this.gatewayKeyAllocationService.handleApiError(keyId, error);
            this.logger.debug(`Handled Gateway allocated key error for ${keyId}`);
            return;
        }
        catch {
            this.logger.debug(`Key ${keyId} not found in Gateway allocation, using legacy error handling`);
        }
        if (this.isRateLimitError(error)) {
            this.keyStatusManager.markKeyRateLimited(keyId, `Rate limited: ${errorMessage}`);
            this.logger.warn(`Key ${keyId} rate limited locally, will retry in 5 minutes`);
            return;
        }
        if (this.isQuotaExhaustedError(error)) {
            this.keyStatusManager.markKeyExhausted(keyId, `Quota exhausted: ${errorMessage}`);
            await this.updateKeyStatusInEtcd(keyId, 'revoked', errorMessage);
            this.logger.error(`Key ${keyId} quota exhausted, updated etcd status to revoked`);
            return;
        }
        this.logger.debug(`Key ${keyId} encountered non-blocking error: ${errorMessage}`);
    }
    isRateLimitError(error) {
        const errorObj = error;
        if (errorObj?.code) {
            if (errorObj.code === 'rate_limit_exceeded' ||
                errorObj.code === 'requests_per_minute_limit_exceeded' ||
                errorObj.code === 'tokens_per_minute_limit_exceeded') {
                return true;
            }
        }
        if (errorObj?.status === 429 || errorObj?.statusCode === 429) {
            return true;
        }
        const errorMessage = errorObj;
        const errorStr = (errorMessage?.message ||
            (typeof errorMessage?.toString === 'function' ? errorMessage.toString() : String(error))).toLowerCase();
        if (errorStr.includes('rate limit') ||
            errorStr.includes('too many requests') ||
            errorStr.includes('429')) {
            return true;
        }
        return false;
    }
    isQuotaExhaustedError(error) {
        const errorObj = error;
        if (errorObj?.code) {
            if (errorObj.code === 'insufficient_quota' ||
                errorObj.code === 'billing_not_active' ||
                errorObj.code === 'invalid_api_key' ||
                errorObj.code === 'account_deactivated' ||
                errorObj.code === 'billing_hard_limit_reached') {
                return true;
            }
        }
        if (errorObj?.status === 401 ||
            errorObj?.statusCode === 401 ||
            errorObj?.status === 403 ||
            errorObj?.statusCode === 403) {
            return true;
        }
        const errorMessage = errorObj;
        const errorStr = (errorMessage?.message ||
            (typeof errorMessage?.toString === 'function' ? errorMessage.toString() : String(error))).toLowerCase();
        if (errorStr.includes('insufficient_quota') ||
            errorStr.includes('billing_not_active') ||
            errorStr.includes('invalid_api_key') ||
            errorStr.includes('account deactivated') ||
            errorStr.includes('quota exceeded') ||
            errorStr.includes('billing')) {
            return true;
        }
        return false;
    }
    async updateKeyStatusInEtcd(keyId, status, reason) {
        try {
            const thirdPartyKeys = await this.getThirdPartyKeys();
            const targetKey = thirdPartyKeys.find((key) => key.keyId === keyId);
            if (targetKey) {
                const keyPath = `/third-party-keys/${this.provider}/${targetKey.userId}/${keyId}`;
                const updatedKeyInfo = {
                    ...targetKey,
                    status: status,
                    lastError: reason,
                    lastUsedAt: Date.now(),
                };
                const etcdClient = this.etcdService.getEtcdClient();
                if (etcdClient) {
                    await etcdClient.put(keyPath).value(JSON.stringify(updatedKeyInfo));
                }
                this.logger.log(`Updated key ${keyId} status to ${status} in etcd`);
            }
            else {
                this.logger.warn(`Key ${keyId} not found in etcd for status update`);
            }
        }
        catch (error) {
            this.logger.error(`Failed to update key ${keyId} status in etcd: ${error instanceof Error ? error.message : String(error)}`);
        }
    }
    async releaseThirdPartyKey(keyId) {
        try {
            const etcdClient = this.etcdService.getEtcdClient();
            if (!etcdClient) {
                throw new Error('etcd client is not initialized');
            }
            const prefix = `/third-party-keys/${this.provider}/`;
            const kvs = await etcdClient.getAll().prefix(prefix);
            let keyInfo = null;
            let keyPath = '';
            for (const [fullPath, jsonValue] of Object.entries(kvs)) {
                try {
                    const parsedKeyInfo = JSON.parse(jsonValue);
                    if (parsedKeyInfo.keyId === keyId) {
                        keyInfo = parsedKeyInfo;
                        keyPath = fullPath;
                        break;
                    }
                }
                catch (parseError) {
                    this.logger.warn(`Failed to parse key info for ${fullPath}: ${parseError instanceof Error ? parseError.message : String(parseError)}`);
                }
            }
            if (!keyInfo) {
                this.logger.warn(`Third-party key ${keyId} not found for release`);
                return false;
            }
            if (keyInfo.status !== 'in_use') {
                this.logger.debug(`Key ${keyId} is not in use (status: ${keyInfo.status})`);
                return false;
            }
            const updatedKeyInfo = {
                ...keyInfo,
                status: 'active',
                lastUsedAt: Date.now(),
                lockedAt: undefined,
                lockTimeout: undefined,
            };
            await etcdClient.put(keyPath).value(JSON.stringify(updatedKeyInfo));
            this.logger.log(`Released third-party key ${keyId} from exclusive use`);
            return true;
        }
        catch (error) {
            this.logger.error(`Failed to release key ${keyId} in etcd: ${error instanceof Error ? error.message : String(error)}`);
            return false;
        }
    }
    async cleanupExpiredLocksFromEtcd() {
        try {
            const now = Date.now();
            const thirdPartyKeys = await this.getThirdPartyKeys();
            const expiredKeys = [];
            for (const keyInfo of thirdPartyKeys) {
                if (keyInfo.status === 'in_use' && keyInfo.lockTimeout && now > keyInfo.lockTimeout) {
                    expiredKeys.push(keyInfo.keyId);
                }
            }
            for (const keyId of expiredKeys) {
                this.logger.warn(`Releasing expired lock for third-party key ${keyId}`);
                await this.releaseThirdPartyKey(keyId);
            }
            if (expiredKeys.length > 0) {
                this.logger.log(`Cleaned up ${expiredKeys.length} expired third-party key locks`);
            }
        }
        catch (error) {
            this.logger.error(`Failed to cleanup expired locks: ${error instanceof Error ? error.message : String(error)}`);
        }
    }
    async hasAvailableKeys() {
        try {
            const thirdPartyKeys = await this.getThirdPartyKeys();
            return thirdPartyKeys.some((key) => key.status === 'active');
        }
        catch (error) {
            this.logger.error(`Failed to check available keys: ${error instanceof Error ? error.message : String(error)}`);
            return false;
        }
    }
    async getKeyPoolStatus() {
        try {
            const thirdPartyKeys = await this.getThirdPartyKeys();
            const blockedKeys = this.keyStatusManager.getBlockedKeys();
            const rateLimitedKeys = blockedKeys.filter((key) => key.status === 'rate_limited').length;
            const exhaustedKeys = thirdPartyKeys.filter((key) => key.status === 'revoked').length;
            const availableKeys = thirdPartyKeys.filter((key) => {
                if (key.status !== 'active')
                    return false;
                return this.keyStatusManager.isKeyAvailable(key.keyId);
            }).length;
            const status = {
                totalKeys: thirdPartyKeys.length,
                activeKeys: availableKeys,
                inUseKeys: thirdPartyKeys.filter((key) => key.status === 'in_use').length,
                rateLimitedKeys: rateLimitedKeys,
                exhaustedKeys: exhaustedKeys,
                revokedKeys: thirdPartyKeys.filter((key) => key.status === 'revoked').length,
            };
            this.logger.debug(`Third-party key pool status: ${JSON.stringify(status)}`);
            return status;
        }
        catch (error) {
            this.logger.error(`Failed to get key pool status: ${error instanceof Error ? error.message : String(error)}`);
            return {
                totalKeys: 0,
                activeKeys: 0,
                inUseKeys: 0,
                rateLimitedKeys: 0,
                exhaustedKeys: 0,
                revokedKeys: 0,
            };
        }
    }
};
exports.KeyManagerService = KeyManagerService;
exports.KeyManagerService = KeyManagerService = KeyManagerService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [typeof (_a = typeof etcd_1.EtcdService !== "undefined" && etcd_1.EtcdService) === "function" ? _a : Object, typeof (_b = typeof config_1.ConfigService !== "undefined" && config_1.ConfigService) === "function" ? _b : Object, typeof (_c = typeof key_status_manager_service_1.KeyStatusManagerService !== "undefined" && key_status_manager_service_1.KeyStatusManagerService) === "function" ? _c : Object, typeof (_d = typeof encryption_service_1.EncryptionService !== "undefined" && encryption_service_1.EncryptionService) === "function" ? _d : Object, typeof (_e = typeof gateway_key_allocation_service_1.GatewayKeyAllocationService !== "undefined" && gateway_key_allocation_service_1.GatewayKeyAllocationService) === "function" ? _e : Object])
], KeyManagerService);


/***/ }),

/***/ "./libs/key-manager/src/key-status-manager.service.ts":
/*!************************************************************!*\
  !*** ./libs/key-manager/src/key-status-manager.service.ts ***!
  \************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var KeyStatusManagerService_1;
var _a, _b;
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.KeyStatusManagerService = void 0;
const common_1 = __webpack_require__(/*! @nestjs/common */ "@nestjs/common");
const etcd_1 = __webpack_require__(/*! @app/etcd */ "./libs/etcd/src/index.ts");
const config_1 = __webpack_require__(/*! @nestjs/config */ "@nestjs/config");
let KeyStatusManagerService = KeyStatusManagerService_1 = class KeyStatusManagerService {
    etcdService;
    configService;
    logger = new common_1.Logger(KeyStatusManagerService_1.name);
    executorId;
    provider;
    memoryKeyStatus = new Map();
    cachedKeys = new Map();
    cleanupInterval;
    constructor(etcdService, configService) {
        this.etcdService = etcdService;
        this.configService = configService;
        this.provider = this.configService.get('model.type', 'openai');
        this.executorId = this.generateExecutorId();
    }
    onModuleInit() {
        this.startStatusWatcher();
        this.startCleanupTask();
        this.logger.log(`Key status manager initialized for executor ${this.executorId}`);
    }
    onModuleDestroy() {
        if (this.cleanupInterval) {
            clearInterval(this.cleanupInterval);
        }
        this.logger.log('Key status manager destroyed');
    }
    generateExecutorId() {
        return `executor-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    }
    markKeyRateLimited(keyId, reason) {
        const now = Date.now();
        const blockedUntil = now + 5 * 60 * 1000;
        this.memoryKeyStatus.set(keyId, {
            keyId,
            status: 'rate_limited',
            blockedUntil,
            reason,
        });
        this.logger.warn(`Key ${keyId} marked as rate limited locally, will recover at ${new Date(blockedUntil).toISOString()}`);
    }
    markKeyExhausted(keyId, reason) {
        this.logger.error(`Key ${keyId} marked as exhausted: ${reason}`);
    }
    isKeyAvailable(keyId) {
        const status = this.memoryKeyStatus.get(keyId);
        if (!status) {
            return true;
        }
        const now = Date.now();
        if (status.status === 'rate_limited' && now >= status.blockedUntil) {
            this.memoryKeyStatus.delete(keyId);
            this.logger.log(`Key ${keyId} automatically recovered from rate limit`);
            return true;
        }
        if (status.status === 'rate_limited' && now < status.blockedUntil) {
            return false;
        }
        return true;
    }
    getBlockedKeys() {
        return Array.from(this.memoryKeyStatus.values());
    }
    cacheDecryptedKey(keyId, decryptedKey) {
        this.cachedKeys.set(keyId, {
            keyId,
            decryptedKey,
            cachedAt: Date.now(),
        });
        this.logger.debug(`Cached decrypted key: ${keyId}`);
    }
    getCachedKey(keyId) {
        const cached = this.cachedKeys.get(keyId);
        if (cached) {
            this.logger.debug(`Using cached key: ${keyId}`);
            return cached.decryptedKey;
        }
        return null;
    }
    clearCachedKey(keyId) {
        this.cachedKeys.delete(keyId);
        this.logger.debug(`Cleared cached key: ${keyId}`);
    }
    getCachedKeyIds() {
        return Array.from(this.cachedKeys.keys());
    }
    startStatusWatcher() {
        const etcdClient = this.etcdService.getEtcdClient();
        if (!etcdClient) {
            this.logger.warn('etcd client not available, status watcher not started');
            return;
        }
        try {
            const watchPrefix = `/key-status-notifications/${this.provider}/`;
            this.logger.log(`Would start watching key status notifications with prefix: ${watchPrefix}`);
            this.logger.log('Status watcher initialized (simplified mode)');
        }
        catch (error) {
            this.logger.error(`Failed to start status watcher: ${error instanceof Error ? error.message : String(error)}`);
        }
    }
    startCleanupTask() {
        this.cleanupInterval = setInterval(() => {
            this.cleanupExpiredStatuses();
        }, 60 * 1000);
    }
    cleanupExpiredStatuses() {
        const now = Date.now();
        let cleanedCount = 0;
        for (const [keyId, status] of this.memoryKeyStatus.entries()) {
            if (status.status === 'rate_limited' && now >= status.blockedUntil) {
                this.memoryKeyStatus.delete(keyId);
                cleanedCount++;
                this.logger.debug(`Cleaned up expired rate limit for key ${keyId}`);
            }
        }
        if (cleanedCount > 0) {
            this.logger.log(`Cleaned up ${cleanedCount} expired rate limit statuses`);
        }
    }
};
exports.KeyStatusManagerService = KeyStatusManagerService;
exports.KeyStatusManagerService = KeyStatusManagerService = KeyStatusManagerService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [typeof (_a = typeof etcd_1.EtcdService !== "undefined" && etcd_1.EtcdService) === "function" ? _a : Object, typeof (_b = typeof config_1.ConfigService !== "undefined" && config_1.ConfigService) === "function" ? _b : Object])
], KeyStatusManagerService);


/***/ }),

/***/ "./libs/providers/src/base-provider.interface.ts":
/*!*******************************************************!*\
  !*** ./libs/providers/src/base-provider.interface.ts ***!
  \*******************************************************/
/***/ ((__unused_webpack_module, exports) => {


Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.BaseModelProvider = void 0;
class BaseModelProvider {
    handleError(error) {
        const errorObj = error;
        if (errorObj.response) {
            const status = errorObj.response.status;
            const data = errorObj.response.data;
            switch (status) {
                case 429:
                    return {
                        code: 'rate_limit_exceeded',
                        message: data?.error?.message || 'Rate limit exceeded',
                        type: 'rate_limit',
                        retryable: true,
                    };
                case 403:
                    return {
                        code: 'insufficient_quota',
                        message: data?.error?.message || 'Insufficient quota',
                        type: 'insufficient_quota',
                        retryable: false,
                    };
                case 400:
                    return {
                        code: 'invalid_request',
                        message: data?.error?.message || 'Invalid request',
                        type: 'invalid_request',
                        retryable: false,
                    };
                case 500:
                case 502:
                case 503:
                case 504:
                    return {
                        code: 'server_error',
                        message: data?.error?.message || 'Server error',
                        type: 'server_error',
                        retryable: true,
                    };
                default:
                    return {
                        code: 'unknown_error',
                        message: data?.error?.message ||
                            (errorObj instanceof Error ? errorObj.message : JSON.stringify(errorObj)) ||
                            'Unknown error',
                        type: 'unknown',
                        retryable: false,
                    };
            }
        }
        return {
            code: 'network_error',
            message: (errorObj instanceof Error ? errorObj.message : JSON.stringify(errorObj)) ||
                'Network error',
            type: 'unknown',
            retryable: true,
        };
    }
}
exports.BaseModelProvider = BaseModelProvider;


/***/ }),

/***/ "./libs/providers/src/index.ts":
/*!*************************************!*\
  !*** ./libs/providers/src/index.ts ***!
  \*************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __exportStar = (this && this.__exportStar) || function(m, exports) {
    for (var p in m) if (p !== "default" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);
};
Object.defineProperty(exports, "__esModule", ({ value: true }));
__exportStar(__webpack_require__(/*! ./providers.module */ "./libs/providers/src/providers.module.ts"), exports);
__exportStar(__webpack_require__(/*! ./provider-factory.service */ "./libs/providers/src/provider-factory.service.ts"), exports);
__exportStar(__webpack_require__(/*! ./openai.provider */ "./libs/providers/src/openai.provider.ts"), exports);
__exportStar(__webpack_require__(/*! ./base-provider.interface */ "./libs/providers/src/base-provider.interface.ts"), exports);


/***/ }),

/***/ "./libs/providers/src/openai-error-classifier.service.ts":
/*!***************************************************************!*\
  !*** ./libs/providers/src/openai-error-classifier.service.ts ***!
  \***************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var OpenAIErrorClassifierService_1;
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.OpenAIErrorClassifierService = exports.OpenAIErrorType = void 0;
const common_1 = __webpack_require__(/*! @nestjs/common */ "@nestjs/common");
var OpenAIErrorType;
(function (OpenAIErrorType) {
    OpenAIErrorType["INVALID_AUTHENTICATION"] = "invalid_authentication";
    OpenAIErrorType["INCORRECT_API_KEY"] = "incorrect_api_key";
    OpenAIErrorType["ORGANIZATION_REQUIRED"] = "organization_required";
    OpenAIErrorType["RATE_LIMIT"] = "rate_limit";
    OpenAIErrorType["QUOTA_EXCEEDED"] = "quota_exceeded";
    OpenAIErrorType["SERVER_ERROR"] = "server_error";
    OpenAIErrorType["ENGINE_OVERLOADED"] = "engine_overloaded";
    OpenAIErrorType["SLOW_DOWN"] = "slow_down";
    OpenAIErrorType["REGION_NOT_SUPPORTED"] = "region_not_supported";
    OpenAIErrorType["MODEL_NOT_FOUND"] = "model_not_found";
    OpenAIErrorType["OTHER"] = "other";
})(OpenAIErrorType || (exports.OpenAIErrorType = OpenAIErrorType = {}));
let OpenAIErrorClassifierService = OpenAIErrorClassifierService_1 = class OpenAIErrorClassifierService {
    logger = new common_1.Logger(OpenAIErrorClassifierService_1.name);
    classifyError(error) {
        const statusCode = this.extractStatusCode(error);
        const errorCode = this.extractErrorCode(error);
        const errorMessage = this.extractErrorMessage(error);
        this.logger.debug(`Classifying error: status=${statusCode}, code=${errorCode}, message=${errorMessage}`);
        if (statusCode === 401) {
            return this.classify401Error(errorMessage, errorCode);
        }
        if (statusCode === 403) {
            return this.classify403Error(errorMessage);
        }
        if (statusCode === 429) {
            return this.classify429Error(errorMessage);
        }
        if (statusCode === 500) {
            return this.classify500Error();
        }
        if (statusCode === 503) {
            return this.classify503Error(errorMessage);
        }
        if (this.isModelError(errorMessage, errorCode)) {
            return {
                type: OpenAIErrorType.MODEL_NOT_FOUND,
                shouldRetryWithSameKey: false,
                shouldTryOtherKeys: true,
                shouldMarkKeyBad: false,
                shouldNotifyOtherExecutors: false,
                retryDelayMs: 0,
                description: 'Model not available for this API key',
            };
        }
        return {
            type: OpenAIErrorType.OTHER,
            shouldRetryWithSameKey: false,
            shouldTryOtherKeys: true,
            shouldMarkKeyBad: false,
            shouldNotifyOtherExecutors: false,
            retryDelayMs: 1000,
            description: 'Unknown error',
        };
    }
    classify401Error(message, code) {
        const lowerMessage = message.toLowerCase();
        if (lowerMessage.includes('invalid authentication') || code === 'invalid_api_key') {
            return {
                type: OpenAIErrorType.INVALID_AUTHENTICATION,
                shouldRetryWithSameKey: false,
                shouldTryOtherKeys: true,
                shouldMarkKeyBad: true,
                shouldNotifyOtherExecutors: true,
                retryDelayMs: 0,
                description: 'Invalid API key authentication',
            };
        }
        if (lowerMessage.includes('incorrect api key') || lowerMessage.includes('api key provided')) {
            return {
                type: OpenAIErrorType.INCORRECT_API_KEY,
                shouldRetryWithSameKey: false,
                shouldTryOtherKeys: true,
                shouldMarkKeyBad: true,
                shouldNotifyOtherExecutors: true,
                retryDelayMs: 0,
                description: 'Incorrect API key provided',
            };
        }
        if (lowerMessage.includes('organization') || lowerMessage.includes('member')) {
            return {
                type: OpenAIErrorType.ORGANIZATION_REQUIRED,
                shouldRetryWithSameKey: false,
                shouldTryOtherKeys: true,
                shouldMarkKeyBad: true,
                shouldNotifyOtherExecutors: true,
                retryDelayMs: 0,
                description: 'API key requires organization membership',
            };
        }
        return {
            type: OpenAIErrorType.INVALID_AUTHENTICATION,
            shouldRetryWithSameKey: false,
            shouldTryOtherKeys: true,
            shouldMarkKeyBad: true,
            shouldNotifyOtherExecutors: true,
            retryDelayMs: 0,
            description: 'Authentication failed',
        };
    }
    classify403Error(message) {
        const lowerMessage = message.toLowerCase();
        if (lowerMessage.includes('country') ||
            lowerMessage.includes('region') ||
            lowerMessage.includes('territory')) {
            return {
                type: OpenAIErrorType.REGION_NOT_SUPPORTED,
                shouldRetryWithSameKey: false,
                shouldTryOtherKeys: true,
                shouldMarkKeyBad: false,
                shouldNotifyOtherExecutors: false,
                retryDelayMs: 0,
                description: 'Country, region, or territory not supported',
            };
        }
        return {
            type: OpenAIErrorType.OTHER,
            shouldRetryWithSameKey: false,
            shouldTryOtherKeys: true,
            shouldMarkKeyBad: false,
            shouldNotifyOtherExecutors: false,
            retryDelayMs: 1000,
            description: 'Forbidden access',
        };
    }
    classify429Error(message) {
        const lowerMessage = message.toLowerCase();
        if (lowerMessage.includes('rate limit')) {
            return {
                type: OpenAIErrorType.RATE_LIMIT,
                shouldRetryWithSameKey: false,
                shouldTryOtherKeys: true,
                shouldMarkKeyBad: false,
                shouldNotifyOtherExecutors: false,
                retryDelayMs: 60000,
                description: 'Rate limit reached for requests',
            };
        }
        if (lowerMessage.includes('quota') ||
            lowerMessage.includes('billing') ||
            lowerMessage.includes('credits')) {
            return {
                type: OpenAIErrorType.QUOTA_EXCEEDED,
                shouldRetryWithSameKey: false,
                shouldTryOtherKeys: true,
                shouldMarkKeyBad: true,
                shouldNotifyOtherExecutors: true,
                retryDelayMs: 0,
                description: 'Quota exceeded, check plan and billing',
            };
        }
        return {
            type: OpenAIErrorType.RATE_LIMIT,
            shouldRetryWithSameKey: false,
            shouldTryOtherKeys: true,
            shouldMarkKeyBad: false,
            shouldNotifyOtherExecutors: false,
            retryDelayMs: 60000,
            description: 'Too many requests',
        };
    }
    classify500Error() {
        return {
            type: OpenAIErrorType.SERVER_ERROR,
            shouldRetryWithSameKey: true,
            shouldTryOtherKeys: true,
            shouldMarkKeyBad: false,
            shouldNotifyOtherExecutors: false,
            retryDelayMs: 5000,
            description: 'Server had an error while processing request',
        };
    }
    classify503Error(message) {
        const lowerMessage = message.toLowerCase();
        if (lowerMessage.includes('slow down')) {
            return {
                type: OpenAIErrorType.SLOW_DOWN,
                shouldRetryWithSameKey: false,
                shouldTryOtherKeys: true,
                shouldMarkKeyBad: false,
                shouldNotifyOtherExecutors: false,
                retryDelayMs: 15 * 60 * 1000,
                description: 'Slow down - reduce request rate',
            };
        }
        return {
            type: OpenAIErrorType.ENGINE_OVERLOADED,
            shouldRetryWithSameKey: true,
            shouldTryOtherKeys: true,
            shouldMarkKeyBad: false,
            shouldNotifyOtherExecutors: false,
            retryDelayMs: 10000,
            description: 'Engine is currently overloaded',
        };
    }
    isModelError(message, code) {
        const lowerMessage = message.toLowerCase();
        return (code === 'model_not_found' ||
            (lowerMessage.includes('model') &&
                (lowerMessage.includes('does not exist') ||
                    lowerMessage.includes('not available') ||
                    lowerMessage.includes('access'))));
    }
    extractStatusCode(error) {
        const errorObj = error;
        return errorObj?.status || errorObj?.response?.status || errorObj?.statusCode || 0;
    }
    extractErrorCode(error) {
        const errorObj = error;
        return errorObj?.code || errorObj?.error?.code || errorObj?.response?.data?.error?.code || '';
    }
    extractErrorMessage(error) {
        const errorObj = error;
        return (errorObj?.message ||
            errorObj?.error?.message ||
            errorObj?.response?.data?.error?.message ||
            errorObj?.response?.data?.message ||
            String(error));
    }
};
exports.OpenAIErrorClassifierService = OpenAIErrorClassifierService;
exports.OpenAIErrorClassifierService = OpenAIErrorClassifierService = OpenAIErrorClassifierService_1 = __decorate([
    (0, common_1.Injectable)()
], OpenAIErrorClassifierService);


/***/ }),

/***/ "./libs/providers/src/openai.provider.ts":
/*!***********************************************!*\
  !*** ./libs/providers/src/openai.provider.ts ***!
  \***********************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var OpenAIProvider_1;
var _a;
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.OpenAIProvider = void 0;
const common_1 = __webpack_require__(/*! @nestjs/common */ "@nestjs/common");
const config_1 = __webpack_require__(/*! @nestjs/config */ "@nestjs/config");
const axios_1 = __webpack_require__(/*! axios */ "axios");
const https_proxy_agent_1 = __webpack_require__(/*! https-proxy-agent */ "https-proxy-agent");
const base_provider_interface_1 = __webpack_require__(/*! ./base-provider.interface */ "./libs/providers/src/base-provider.interface.ts");
const config_2 = __webpack_require__(/*! @app/config */ "./libs/config/src/index.ts");
let OpenAIProvider = OpenAIProvider_1 = class OpenAIProvider extends base_provider_interface_1.BaseModelProvider {
    configService;
    providerName = 'openai';
    logger = new common_1.Logger(OpenAIProvider_1.name);
    baseUrl;
    defaultModel;
    constructor(configService) {
        super();
        this.configService = configService;
        this.baseUrl = this.configService.get('openai.baseUrl') || 'https://api.openai.com/';
        this.defaultModel = this.configService.get('openai.defaultModel') || 'gpt-3.5-turbo';
    }
    async chatCompletion(request, apiKey) {
        console.log(`OpenAI request: ${JSON.stringify(request)} ${apiKey} ${this.baseUrl}`);
        const baseUrl = this.baseUrl.endsWith('/') ? this.baseUrl : this.baseUrl + '/';
        const url = baseUrl.includes('/v1/')
            ? `${baseUrl}chat/completions`
            : `${baseUrl}v1/chat/completions`;
        const requestBody = {
            model: request.model || this.defaultModel,
            messages: request.messages,
            temperature: request.temperature,
            max_tokens: request.max_tokens,
            top_p: request.top_p,
            frequency_penalty: request.frequency_penalty,
            presence_penalty: request.presence_penalty,
            stream: request.stream || false,
        };
        try {
            this.logger.debug(`Making OpenAI request with model: ${request.model}, API key: ${apiKey.substring(0, 10)}...`);
            this.logger.debug(`Request URL: ${url}`);
            this.logger.debug(`Request body: ${JSON.stringify(requestBody)}`);
            const proxyUrl = process.env.HTTPS_PROXY || process.env.HTTP_PROXY;
            const axiosConfig = {
                headers: {
                    Authorization: `Bearer ${apiKey}`,
                    'Content-Type': 'application/json',
                },
                timeout: 60000,
                validateStatus: (status) => status < 500,
            };
            if (proxyUrl) {
                axiosConfig.httpsAgent = new https_proxy_agent_1.HttpsProxyAgent(proxyUrl);
                this.logger.debug(`Using proxy: ${proxyUrl}`);
            }
            if (request.stream) {
                axiosConfig.responseType = 'stream';
                const response = await axios_1.default.post(url, requestBody, axiosConfig);
                this.logger.debug(`OpenAI streaming response status: ${response.status}`);
                return this.createStreamIterable(response.data);
            }
            else {
                const response = await axios_1.default.post(url, requestBody, axiosConfig);
                console.log('OpenAI response:', response.data);
                const responseData = response.data;
                if (responseData.error) {
                    const error = responseData.error;
                    throw new Error(`OpenAI API Error: ${error.message} (${error.type})`);
                }
                if (!responseData.choices || !Array.isArray(responseData.choices)) {
                    throw new Error('Invalid OpenAI response: missing choices array');
                }
                const standardResponse = {
                    id: responseData.id || '',
                    object: responseData.object || 'chat.completion',
                    created: responseData.created || Date.now(),
                    model: responseData.model || '',
                    choices: responseData.choices.map((choice) => ({
                        index: choice.index,
                        message: {
                            role: choice.message.role,
                            content: choice.message.content || '',
                        },
                        finish_reason: choice.finish_reason || 'stop',
                    })),
                    usage: {
                        prompt_tokens: responseData.usage?.prompt_tokens || 0,
                        completion_tokens: responseData.usage?.completion_tokens || 0,
                        total_tokens: responseData.usage?.total_tokens || 0,
                    },
                };
                this.logger.debug(`OpenAI request completed successfully. Tokens used: ${standardResponse.usage.total_tokens}`);
                return standardResponse;
            }
        }
        catch (error) {
            this.logger.error(`OpenAI request failed:`, error);
            const providerError = this.handleAxiosError(error);
            const finalError = new Error(providerError.message);
            finalError.code = providerError.code;
            finalError.type = providerError.type;
            finalError.retryable = providerError.retryable;
            throw finalError;
        }
    }
    handleAxiosError(error) {
        console.log(error);
        let statusCode = 500;
        let errorMessage = 'Unknown error';
        let errorType = 'unknown';
        if (axios_1.default.isAxiosError(error)) {
            if (error.response) {
                statusCode = error.response.status;
                errorMessage =
                    error.response.data?.error?.message ||
                        error.message;
            }
            else if (error.request) {
                errorType = 'server_error';
                errorMessage = 'Network timeout or connection error';
                statusCode = 408;
            }
            else {
                errorMessage = error.message;
            }
        }
        else {
            errorMessage = (error instanceof Error ? error.message : String(error)) || 'Network error';
        }
        switch (statusCode) {
            case 429:
                errorType = 'rate_limit';
                break;
            case 401:
            case 403:
                errorType = 'insufficient_quota';
                break;
            case 400:
                errorType = 'invalid_request';
                break;
            case 500:
            case 502:
            case 503:
            case 504:
            case 408:
                errorType = 'server_error';
                break;
        }
        return {
            code: `http_${statusCode}`,
            type: errorType,
            message: errorMessage,
            retryable: this.isRetryableByStatus(statusCode),
        };
    }
    isRetryableByStatus(statusCode) {
        return [429, 500, 502, 503, 504, 408].includes(statusCode);
    }
    getSupportedModels() {
        return [...config_2.SUPPORTED_MODELS.openai];
    }
    validateModel(model) {
        return this.getSupportedModels().includes(model);
    }
    async *createStreamIterable(stream) {
        let buffer = '';
        for await (const chunk of stream) {
            buffer += chunk.toString();
            const lines = buffer.split('\n');
            buffer = lines.pop() || '';
            for (const line of lines) {
                if (line.trim() === '')
                    continue;
                if (line.startsWith('data: ')) {
                    const data = line.slice(6).trim();
                    if (data === '[DONE]') {
                        return;
                    }
                    try {
                        const parsed = JSON.parse(data);
                        if (parsed.choices && parsed.choices[0] && parsed.choices[0].delta) {
                            const delta = parsed.choices[0].delta;
                            if (delta.content) {
                                yield `data: ${JSON.stringify({
                                    id: parsed.id,
                                    object: 'chat.completion.chunk',
                                    created: parsed.created,
                                    model: parsed.model,
                                    choices: [
                                        {
                                            index: 0,
                                            delta: { content: delta.content },
                                            finish_reason: parsed.choices[0].finish_reason || null,
                                        },
                                    ],
                                })}\n\n`;
                            }
                        }
                    }
                    catch {
                        this.logger.warn(`Failed to parse streaming data: ${data}`);
                    }
                }
            }
        }
    }
};
exports.OpenAIProvider = OpenAIProvider;
exports.OpenAIProvider = OpenAIProvider = OpenAIProvider_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [typeof (_a = typeof config_1.ConfigService !== "undefined" && config_1.ConfigService) === "function" ? _a : Object])
], OpenAIProvider);


/***/ }),

/***/ "./libs/providers/src/provider-factory.service.ts":
/*!********************************************************!*\
  !*** ./libs/providers/src/provider-factory.service.ts ***!
  \********************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var ProviderFactoryService_1;
var _a, _b, _c, _d;
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.ProviderFactoryService = void 0;
const common_1 = __webpack_require__(/*! @nestjs/common */ "@nestjs/common");
const config_1 = __webpack_require__(/*! @nestjs/config */ "@nestjs/config");
const openai_provider_1 = __webpack_require__(/*! ./openai.provider */ "./libs/providers/src/openai.provider.ts");
const key_manager_1 = __webpack_require__(/*! @app/key-manager */ "./libs/key-manager/src/index.ts");
const openai_error_classifier_service_1 = __webpack_require__(/*! ./openai-error-classifier.service */ "./libs/providers/src/openai-error-classifier.service.ts");
let ProviderFactoryService = ProviderFactoryService_1 = class ProviderFactoryService {
    configService;
    keyManagerService;
    openaiProvider;
    errorClassifier;
    logger = new common_1.Logger(ProviderFactoryService_1.name);
    providers = new Map();
    modelType;
    constructor(configService, keyManagerService, openaiProvider, errorClassifier) {
        this.configService = configService;
        this.keyManagerService = keyManagerService;
        this.openaiProvider = openaiProvider;
        this.errorClassifier = errorClassifier;
        this.modelType = this.configService.get('executor.modelType', 'openai');
        this.initializeProviders();
    }
    initializeProviders() {
        this.providers.set('openai', this.openaiProvider);
        this.logger.log(`Initialized providers: ${Array.from(this.providers.keys()).join(', ')}`);
        this.logger.log(`Current model type: ${this.modelType}`);
    }
    async executeRequest(request) {
        this.logger.debug(`Starting request execution for model: ${request.model}`);
        const provider = this.providers.get(this.modelType);
        if (!provider) {
            this.logger.error(`Provider not found for model type: ${this.modelType}`);
            throw new Error(`Provider not found for model type: ${this.modelType}`);
        }
        this.logger.debug(`Provider found: ${provider.providerName}`);
        if (provider.validateModel && !provider.validateModel(request.model)) {
            this.logger.error(`Model ${request.model} is not supported by provider ${this.modelType}`);
            throw new Error(`Model ${request.model} is not supported by provider ${this.modelType}`);
        }
        let lastError = null;
        let currentApiKey = null;
        const maxKeyAttempts = this.configService.get('executor.maxKeyAttempts', 5);
        const usedKeyIds = new Set();
        try {
            for (let keyAttempt = 1; keyAttempt <= maxKeyAttempts; keyAttempt++) {
                this.logger.debug(`Getting available API key (attempt ${keyAttempt}/${maxKeyAttempts})...`);
                currentApiKey = await this.keyManagerService.getAndLockKey();
                if (!currentApiKey) {
                    this.logger.warn(`No available API keys found on attempt ${keyAttempt}`);
                    if (keyAttempt === maxKeyAttempts) {
                        throw new Error('No available API keys after trying all attempts');
                    }
                    continue;
                }
                if (usedKeyIds.has(currentApiKey.id)) {
                    this.logger.debug(`Key ${currentApiKey.id} already tried, releasing and getting another`);
                    await this.keyManagerService.releaseKey(currentApiKey.id);
                    continue;
                }
                usedKeyIds.add(currentApiKey.id);
                if (!currentApiKey.key) {
                    this.logger.error(`API key ${currentApiKey.id} has no key value, skipping`);
                    await this.keyManagerService.releaseKey(currentApiKey.id);
                    continue;
                }
                this.logger.debug(`Using API key: ${currentApiKey.id}, key starts with: ${currentApiKey.key.substring(0, 10)}... (attempt ${keyAttempt}/${maxKeyAttempts})`);
                try {
                    const response = await provider.chatCompletion(request, currentApiKey.key);
                    this.logger.debug(`Request completed successfully with key ${currentApiKey.id}`);
                    return response;
                }
                catch (error) {
                    lastError = error;
                    this.logger.warn(`Key ${currentApiKey.id} failed: ${lastError.code} - ${lastError.message}`);
                    const errorClassification = this.errorClassifier.classifyError(lastError);
                    this.logger.debug(`Error classification for key ${currentApiKey.id}: ${errorClassification.type} - ${errorClassification.description}`);
                    if (errorClassification.shouldMarkKeyBad) {
                        await this.updateKeyStatusOnError(currentApiKey.id, lastError, errorClassification);
                    }
                    if (errorClassification.shouldNotifyOtherExecutors) {
                        this.logger.warn(`Key ${currentApiKey.id} requires notification to other executors: ${errorClassification.description}`);
                    }
                    await this.keyManagerService.releaseKey(currentApiKey.id);
                    this.logger.warn(`Key ${currentApiKey.id} failed (${errorClassification.type}), trying next key`);
                    if (errorClassification.retryDelayMs > 0 &&
                        errorClassification.type === openai_error_classifier_service_1.OpenAIErrorType.RATE_LIMIT) {
                        const waitTime = Math.min(errorClassification.retryDelayMs, 10000);
                        this.logger.log(`Rate limited, waiting ${waitTime}ms before trying next key`);
                        await this.sleep(waitTime);
                    }
                    currentApiKey = null;
                }
            }
        }
        finally {
            if (currentApiKey) {
                await this.keyManagerService.releaseKey(currentApiKey.id);
                this.logger.debug(`Released key ${currentApiKey.id}`);
            }
        }
        throw new Error(`Request failed after trying ${maxKeyAttempts} different keys. Last error: ${lastError?.message || 'Unknown error'}`);
    }
    sleep(ms) {
        return new Promise((resolve) => setTimeout(resolve, ms));
    }
    getProvider(modelType) {
        return this.providers.get(modelType || this.modelType);
    }
    getSupportedProviders() {
        return Array.from(this.providers.keys());
    }
    getCurrentModelType() {
        return this.modelType;
    }
    async getProviderStatus() {
        const provider = this.providers.get(this.modelType);
        const keyStatus = this.keyManagerService.getKeyPoolStatus();
        const hasKeys = await this.keyManagerService.hasAvailableKeys();
        return {
            modelType: this.modelType,
            providerName: provider?.providerName || 'unknown',
            supportedModels: provider?.getSupportedModels?.() || [],
            keyPoolStatus: keyStatus,
            hasAvailableKeys: hasKeys,
        };
    }
    validateRequest(request) {
        if (!request.model) {
            return { valid: false, error: 'Model is required' };
        }
        if (!request.messages || request.messages.length === 0) {
            return { valid: false, error: 'Messages are required' };
        }
        const provider = this.providers.get(this.modelType);
        if (provider?.validateModel && !provider.validateModel(request.model)) {
            return {
                valid: false,
                error: `Model ${request.model} is not supported by provider ${this.modelType}`,
            };
        }
        return { valid: true };
    }
    async updateKeyStatusOnError(keyId, error, classification) {
        try {
            await this.keyManagerService.handleApiError(keyId, error);
            if (classification) {
                this.logger.debug(`Updated key ${keyId} status based on ${classification.type}: ${classification.description}`);
            }
        }
        catch (updateError) {
            this.logger.error(`Failed to update key ${keyId} status: ${updateError instanceof Error ? updateError.message : String(updateError)}`);
        }
    }
};
exports.ProviderFactoryService = ProviderFactoryService;
exports.ProviderFactoryService = ProviderFactoryService = ProviderFactoryService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [typeof (_a = typeof config_1.ConfigService !== "undefined" && config_1.ConfigService) === "function" ? _a : Object, typeof (_b = typeof key_manager_1.KeyManagerService !== "undefined" && key_manager_1.KeyManagerService) === "function" ? _b : Object, typeof (_c = typeof openai_provider_1.OpenAIProvider !== "undefined" && openai_provider_1.OpenAIProvider) === "function" ? _c : Object, typeof (_d = typeof openai_error_classifier_service_1.OpenAIErrorClassifierService !== "undefined" && openai_error_classifier_service_1.OpenAIErrorClassifierService) === "function" ? _d : Object])
], ProviderFactoryService);


/***/ }),

/***/ "./libs/providers/src/providers.module.ts":
/*!************************************************!*\
  !*** ./libs/providers/src/providers.module.ts ***!
  \************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.ProvidersModule = void 0;
const common_1 = __webpack_require__(/*! @nestjs/common */ "@nestjs/common");
const openai_provider_1 = __webpack_require__(/*! ./openai.provider */ "./libs/providers/src/openai.provider.ts");
const provider_factory_service_1 = __webpack_require__(/*! ./provider-factory.service */ "./libs/providers/src/provider-factory.service.ts");
const openai_error_classifier_service_1 = __webpack_require__(/*! ./openai-error-classifier.service */ "./libs/providers/src/openai-error-classifier.service.ts");
const key_manager_1 = __webpack_require__(/*! @app/key-manager */ "./libs/key-manager/src/index.ts");
let ProvidersModule = class ProvidersModule {
};
exports.ProvidersModule = ProvidersModule;
exports.ProvidersModule = ProvidersModule = __decorate([
    (0, common_1.Module)({
        imports: [key_manager_1.KeyManagerModule],
        providers: [openai_provider_1.OpenAIProvider, provider_factory_service_1.ProviderFactoryService, openai_error_classifier_service_1.OpenAIErrorClassifierService],
        exports: [provider_factory_service_1.ProviderFactoryService],
    })
], ProvidersModule);


/***/ }),

/***/ "./libs/secret-manager/src/index.ts":
/*!******************************************!*\
  !*** ./libs/secret-manager/src/index.ts ***!
  \******************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __exportStar = (this && this.__exportStar) || function(m, exports) {
    for (var p in m) if (p !== "default" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);
};
Object.defineProperty(exports, "__esModule", ({ value: true }));
__exportStar(__webpack_require__(/*! ./secret-manager.module */ "./libs/secret-manager/src/secret-manager.module.ts"), exports);
__exportStar(__webpack_require__(/*! ./secret-manager.service */ "./libs/secret-manager/src/secret-manager.service.ts"), exports);


/***/ }),

/***/ "./libs/secret-manager/src/secret-manager.module.ts":
/*!**********************************************************!*\
  !*** ./libs/secret-manager/src/secret-manager.module.ts ***!
  \**********************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.SecretManagerModule = void 0;
const common_1 = __webpack_require__(/*! @nestjs/common */ "@nestjs/common");
const secret_manager_service_1 = __webpack_require__(/*! ./secret-manager.service */ "./libs/secret-manager/src/secret-manager.service.ts");
let SecretManagerModule = class SecretManagerModule {
};
exports.SecretManagerModule = SecretManagerModule;
exports.SecretManagerModule = SecretManagerModule = __decorate([
    (0, common_1.Module)({
        providers: [secret_manager_service_1.SecretManagerService],
        exports: [secret_manager_service_1.SecretManagerService],
    })
], SecretManagerModule);


/***/ }),

/***/ "./libs/secret-manager/src/secret-manager.service.ts":
/*!***********************************************************!*\
  !*** ./libs/secret-manager/src/secret-manager.service.ts ***!
  \***********************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var SecretManagerService_1;
var _a;
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.SecretManagerService = void 0;
const common_1 = __webpack_require__(/*! @nestjs/common */ "@nestjs/common");
const config_1 = __webpack_require__(/*! @nestjs/config */ "@nestjs/config");
const secret_manager_1 = __webpack_require__(/*! @google-cloud/secret-manager */ "@google-cloud/secret-manager");
let SecretManagerService = SecretManagerService_1 = class SecretManagerService {
    configService;
    logger = new common_1.Logger(SecretManagerService_1.name);
    secretClient;
    projectId;
    executorId;
    constructor(configService) {
        this.configService = configService;
        this.projectId = this.configService.get('GCP_KMS_PROJECT_ID') || '';
        this.executorId = this.configService.get('executor.id', 'executor-' + Date.now());
        const nodeEnv = this.configService.get('NODE_ENV', 'development');
        const hasLocalPrivateKey = this.configService.get('EXECUTOR_PRIVATE_KEY');
        if (!this.projectId && nodeEnv === 'production') {
            throw new Error('GCP project ID is required in production. Please set GCP_KMS_PROJECT_ID');
        }
        if (this.projectId) {
            this.initializeSecretClient();
        }
        else {
            this.logger.warn('GCP_KMS_PROJECT_ID not configured. Secret Manager will be disabled.');
            if (!hasLocalPrivateKey) {
                this.logger.warn('No EXECUTOR_PRIVATE_KEY found. Key pair will be generated automatically.');
            }
        }
    }
    initializeSecretClient() {
        try {
            this.secretClient = new secret_manager_1.SecretManagerServiceClient();
            this.logger.log(`GCP Secret Manager client initialized for project: ${this.projectId}`);
        }
        catch (error) {
            this.logger.error(`Failed to initialize GCP Secret Manager client: ${error}`);
            throw error;
        }
    }
    async getExecutorPrivateKey() {
        try {
            const secretIds = [
                this.configService.get('EXECUTOR_SECRET_ID', `executor-keypair-${this.executorId}`),
                `executor-keypair-${this.executorId}`,
                'executor-keypair-001',
            ];
            for (const secretId of secretIds) {
                try {
                    this.logger.debug(`Trying secret ID: ${secretId}`);
                    const keyPair = await this.getExecutorKeyPair(secretId);
                    this.logger.log(`✅ Successfully retrieved private key from secret: ${secretId}`);
                    return keyPair.privateKey;
                }
                catch (error) {
                    this.logger.debug(`Secret ${secretId} not found or inaccessible: ${error instanceof Error ? error.message : String(error)}`);
                    continue;
                }
            }
            throw new Error(`No accessible executor key pair found in any of the tried secret IDs: ${secretIds.join(', ')}`);
        }
        catch (error) {
            this.logger.error(`Failed to get executor private key: ${error instanceof Error ? error.message : String(error)}`);
            throw error;
        }
    }
    async getExecutorKeyPair(secretId) {
        try {
            const actualSecretId = secretId || `executor-keypair-${this.executorId}`;
            const payload = await this.accessSecret(actualSecretId);
            const keyPair = JSON.parse(payload);
            if (!keyPair.publicKey || !keyPair.privateKey || !keyPair.keyId) {
                throw new Error('Invalid key pair structure in secret');
            }
            this.logger.debug(`Retrieved executor key pair: ${keyPair.keyId}`);
            return keyPair;
        }
        catch (error) {
            this.logger.error(`Failed to get executor key pair: ${error}`);
            throw error;
        }
    }
    async accessSecret(secretId, version = 'latest') {
        try {
            const name = `projects/${this.projectId}/secrets/${secretId}/versions/${version}`;
            const [accessResponse] = await this.secretClient.accessSecretVersion({
                name: name,
            });
            if (!accessResponse.payload?.data) {
                throw new Error('Secret payload is empty');
            }
            const payload = accessResponse.payload.data.toString('utf8');
            this.logger.debug(`Successfully accessed secret: ${secretId}`);
            return payload;
        }
        catch (error) {
            this.logger.error(`Failed to access secret ${secretId}: ${error}`);
            throw error;
        }
    }
    async testConnection() {
        if (!this.projectId || !this.secretClient) {
            this.logger.debug('Secret Manager not configured, skipping connection test');
            return false;
        }
        try {
            this.logger.log('Testing Secret Manager connection...');
            const parent = `projects/${this.projectId}`;
            await this.secretClient.listSecrets({
                parent: parent,
                pageSize: 1,
            });
            this.logger.log('Secret Manager connection test successful');
            return true;
        }
        catch (error) {
            this.logger.error(`Secret Manager connection test failed: ${error}`);
            return false;
        }
    }
    async hasExecutorKeyPair() {
        try {
            const secretId = `executor-keypair-${this.executorId}`;
            await this.accessSecret(secretId);
            return true;
        }
        catch (error) {
            this.logger.debug(`Executor key pair not found: ${error}`);
            return false;
        }
    }
    getExecutorId() {
        return this.executorId;
    }
    getProjectId() {
        return this.projectId;
    }
    getPrivateKeyFromEnv() {
        try {
            const privateKeyBase64 = this.configService.get('EXECUTOR_PRIVATE_KEY');
            if (!privateKeyBase64) {
                throw new Error('EXECUTOR_PRIVATE_KEY environment variable is not set');
            }
            try {
                const privateKeyBuffer = Buffer.from(privateKeyBase64, 'base64');
                if (privateKeyBuffer.length !== 32) {
                    throw new Error('Private key must be 32 bytes (X25519)');
                }
            }
            catch (error) {
                throw new Error(`Invalid private key format: ${error}`);
            }
            this.logger.log('Using private key from environment variable (fallback mode)');
            return privateKeyBase64;
        }
        catch (error) {
            this.logger.error(`Failed to get private key from environment: ${error}`);
            throw error;
        }
    }
    async loadPrivateKey() {
        try {
            if (this.projectId && this.secretClient) {
                if (await this.testConnection()) {
                    try {
                        return await this.getExecutorPrivateKey();
                    }
                    catch (secretError) {
                        this.logger.warn(`Failed to load from Secret Manager, trying fallback: ${secretError}`);
                    }
                }
            }
            else {
                this.logger.debug('GCP Secret Manager not configured, skipping...');
            }
            try {
                return this.getPrivateKeyFromEnv();
            }
            catch (envError) {
                this.logger.debug(`No private key in environment variable: ${envError}`);
            }
            this.logger.debug('No private key found in Secret Manager or environment variables');
            return null;
        }
        catch (error) {
            this.logger.error(`Failed to load private key: ${error}`);
            return null;
        }
    }
    async getSecretInfo(secretId) {
        const actualSecretId = secretId || `executor-keypair-${this.executorId}`;
        const exists = await this.hasExecutorKeyPair();
        return {
            secretId: actualSecretId,
            projectId: this.projectId,
            executorId: this.executorId,
            exists,
        };
    }
};
exports.SecretManagerService = SecretManagerService;
exports.SecretManagerService = SecretManagerService = SecretManagerService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [typeof (_a = typeof config_1.ConfigService !== "undefined" && config_1.ConfigService) === "function" ? _a : Object])
], SecretManagerService);


/***/ }),

/***/ "@google-cloud/secret-manager":
/*!***********************************************!*\
  !*** external "@google-cloud/secret-manager" ***!
  \***********************************************/
/***/ ((module) => {

module.exports = require("@google-cloud/secret-manager");

/***/ }),

/***/ "@nestjs/common":
/*!*********************************!*\
  !*** external "@nestjs/common" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("@nestjs/common");

/***/ }),

/***/ "@nestjs/config":
/*!*********************************!*\
  !*** external "@nestjs/config" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("@nestjs/config");

/***/ }),

/***/ "@nestjs/core":
/*!*******************************!*\
  !*** external "@nestjs/core" ***!
  \*******************************/
/***/ ((module) => {

module.exports = require("@nestjs/core");

/***/ }),

/***/ "@noble/ciphers/chacha":
/*!****************************************!*\
  !*** external "@noble/ciphers/chacha" ***!
  \****************************************/
/***/ ((module) => {

module.exports = require("@noble/ciphers/chacha");

/***/ }),

/***/ "axios":
/*!************************!*\
  !*** external "axios" ***!
  \************************/
/***/ ((module) => {

module.exports = require("axios");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("crypto");

/***/ }),

/***/ "etcd3":
/*!************************!*\
  !*** external "etcd3" ***!
  \************************/
/***/ ((module) => {

module.exports = require("etcd3");

/***/ }),

/***/ "https-proxy-agent":
/*!************************************!*\
  !*** external "https-proxy-agent" ***!
  \************************************/
/***/ ((module) => {

module.exports = require("https-proxy-agent");

/***/ }),

/***/ "tweetnacl":
/*!****************************!*\
  !*** external "tweetnacl" ***!
  \****************************/
/***/ ((module) => {

module.exports = require("tweetnacl");

/***/ })

/******/ 	});
/************************************************************************/
/******/ 	// The module cache
/******/ 	var __webpack_module_cache__ = {};
/******/ 	
/******/ 	// The require function
/******/ 	function __webpack_require__(moduleId) {
/******/ 		// Check if module is in cache
/******/ 		var cachedModule = __webpack_module_cache__[moduleId];
/******/ 		if (cachedModule !== undefined) {
/******/ 			return cachedModule.exports;
/******/ 		}
/******/ 		// Create a new module (and put it into the cache)
/******/ 		var module = __webpack_module_cache__[moduleId] = {
/******/ 			// no module.id needed
/******/ 			// no module.loaded needed
/******/ 			exports: {}
/******/ 		};
/******/ 	
/******/ 		// Execute the module function
/******/ 		__webpack_modules__[moduleId].call(module.exports, module, module.exports, __webpack_require__);
/******/ 	
/******/ 		// Return the exports of the module
/******/ 		return module.exports;
/******/ 	}
/******/ 	
/************************************************************************/
var __webpack_exports__ = {};
// This entry needs to be wrapped in an IIFE because it needs to be isolated against other modules in the chunk.
(() => {
var exports = __webpack_exports__;
/*!*****************************************!*\
  !*** ./apps/sight-executor/src/main.ts ***!
  \*****************************************/

Object.defineProperty(exports, "__esModule", ({ value: true }));
const core_1 = __webpack_require__(/*! @nestjs/core */ "@nestjs/core");
const config_1 = __webpack_require__(/*! @nestjs/config */ "@nestjs/config");
const common_1 = __webpack_require__(/*! @nestjs/common */ "@nestjs/common");
const app_module_1 = __webpack_require__(/*! ./app.module */ "./apps/sight-executor/src/app.module.ts");
const config_2 = __webpack_require__(/*! @app/config */ "./libs/config/src/index.ts");
async function bootstrap() {
    const app = await core_1.NestFactory.create(app_module_1.AppModule);
    const configService = app.get(config_1.ConfigService);
    const configValidation = app.get(config_2.ConfigValidationService);
    const logger = new common_1.Logger('Bootstrap');
    const validationResult = configValidation.validateConfiguration();
    if (!validationResult.valid) {
        logger.error('Configuration validation failed. Exiting...');
        process.exit(1);
    }
    app.useGlobalPipes(new common_1.ValidationPipe({
        transform: true,
        whitelist: true,
        forbidNonWhitelisted: true,
    }));
    app.enableCors({
        origin: true,
        credentials: true,
    });
    const port = configService.get('port', 3000);
    const region = configService.get('executor.region');
    const modelType = configService.get('executor.modelType');
    await app.listen(port);
    logger.log(`🚀 Executor started on port ${port}`);
    logger.log(`📍 Region: ${region}, Model Type: ${modelType}`);
    logger.log(`🔗 Public URL: ${configService.get('executor.publicUrl')}`);
}
bootstrap().catch((error) => {
    console.error('Failed to start executor:', error);
    process.exit(1);
});

})();

/******/ })()
;