#!/usr/bin/env node

/**
 * 🛠️ SightAI Executor 统一环境配置脚本
 * 
 * 功能：
 * 1. 生成密钥对
 * 2. 配置开发/生产环境
 * 3. 同步 Gateway 和 Executor 认证密钥
 * 4. 验证配置完整性
 * 5. 测试系统连接
 * 
 * 使用方法：
 * node scripts/setup-environment.js [mode]
 * 
 * 模式：
 * dev     - 开发环境 (默认)
 * prod    - 生产环境
 * verify  - 验证配置
 * test    - 测试连接
 * clean   - 清理配置
 */

const fs = require('fs');
const crypto = require('crypto');
const { execSync } = require('child_process');

// 配置常量
const CONFIG = {
  DEV_SECRET_KEY: 'executor-secret-key-sight',
  
  // 路径配置
  PATHS: {
    executorEnv: '.env',
    executorEnvDev: '.env.development',
    gatewayEnv: '../.env',
    keysDir: 'keys',
    keyPairFile: 'keys/executor-keypair.json',
    publicKeyFile: 'keys/executor-public-key.json',
    privateKeyEnvFile: 'keys/executor-private-key.env'
  },
  
  // 默认配置
  DEFAULTS: {
    dev: {
      port: 3002,
      region: 'local',
      modelType: 'openai',
      gatewayUrl: 'http://localhost:8718',
      etcdEndpoints: 'localhost:2379'
    },
    prod: {
      port: 3002,
      region: 'asia',
      modelType: 'openai',
      gatewayUrl: 'http://gateway:8718',
      etcdEndpoints: 'etcd-cluster:2379'
    }
  }
};

// 工具函数
function execCommand(command, description) {
  try {
    console.log(`🔄 ${description}...`);
    const result = execSync(command, { encoding: 'utf8', stdio: 'pipe' });
    return { success: true, output: result.trim() };
  } catch (error) {
    return { success: false, error: error.message };
  }
}

function ensureDirectory(dirPath) {
  if (!fs.existsSync(dirPath)) {
    fs.mkdirSync(dirPath, { recursive: true });
  }
}

function readEnvFile(filePath) {
  if (!fs.existsSync(filePath)) {
    return {};
  }
  
  const content = fs.readFileSync(filePath, 'utf8');
  const env = {};
  
  content.split('\n').forEach(line => {
    const trimmed = line.trim();
    if (trimmed && !trimmed.startsWith('#')) {
      const [key, ...valueParts] = trimmed.split('=');
      if (key && valueParts.length > 0) {
        env[key] = valueParts.join('=');
      }
    }
  });
  
  return env;
}

function updateEnvFile(filePath, key, value) {
  let content = '';
  
  if (fs.existsSync(filePath)) {
    content = fs.readFileSync(filePath, 'utf8');
  }
  
  const lines = content.split('\n');
  let updated = false;
  
  // 查找并更新现有的键
  for (let i = 0; i < lines.length; i++) {
    const line = lines[i].trim();
    if (line.startsWith(`${key}=`)) {
      lines[i] = `${key}=${value}`;
      updated = true;
      break;
    }
  }
  
  // 如果没有找到，添加到文件末尾
  if (!updated) {
    if (content && !content.endsWith('\n')) {
      lines.push('');
    }
    lines.push(`${key}=${value}`);
  }
  
  fs.writeFileSync(filePath, lines.join('\n'));
}

// 生成密钥对
function generateKeyPair() {
  console.log('🔑 生成 X25519 密钥对...');
  
  try {
    ensureDirectory(CONFIG.PATHS.keysDir);
    
    const result = execCommand('node scripts/generate-executor-keypair.js', '生成密钥对');
    if (!result.success) {
      throw new Error(`密钥对生成失败: ${result.error}`);
    }
    
    console.log('✅ 密钥对生成成功');
    return true;
  } catch (error) {
    console.error('❌ 密钥对生成失败:', error.message);
    return false;
  }
}

// 读取生成的私钥
function getPrivateKey() {
  const privateKeyEnvFile = CONFIG.PATHS.privateKeyEnvFile;
  if (fs.existsSync(privateKeyEnvFile)) {
    const content = fs.readFileSync(privateKeyEnvFile, 'utf8');
    const match = content.match(/EXECUTOR_PRIVATE_KEY=(.+)/);
    if (match) {
      return match[1];
    }
  }
  return '';
}

// 生成 Executor 认证密钥
function generateExecutorSecret(executorId, region, modelType, secretKey) {
  const payload = `${executorId}:${region}:${modelType}:${secretKey}`;
  return crypto.createHash('sha256').update(payload).digest('hex').substring(0, 32);
}

// 配置开发环境
function setupDevelopment() {
  console.log('\n🔧 配置开发环境...');
  
  const timestamp = Date.now();
  const randomSuffix = Math.random().toString(36).substring(2, 8);
  const executorId = `executor-dev-${randomSuffix}`;
  const secretKey = CONFIG.DEV_SECRET_KEY;
  const privateKey = getPrivateKey();
  const defaults = CONFIG.DEFAULTS.dev;
  
  const envContent = `# ===========================================
# SightAI Executor 开发环境配置
# 生成时间: ${new Date().toISOString()}
# ===========================================

NODE_ENV=development
PORT=${defaults.port}
PUBLIC_URL=http://localhost:${defaults.port}

# Executor 配置 (动态生成)
EXECUTOR_ID=${executorId}
EXECUTOR_REGION=${defaults.region}
EXECUTOR_MODEL_TYPE=${defaults.modelType}
EXECUTOR_SECRET_KEY=${secretKey}

# etcd 配置
ETCD_ENDPOINTS=${defaults.etcdEndpoints}

# Gateway 配置
GATEWAY_URL=${defaults.gatewayUrl}

# ===========================================
# Executor 密钥配置 (开发环境 - 仅本地)
# ===========================================

# 开发环境：使用本地私钥 (base64 编码)
EXECUTOR_PRIVATE_KEY=${privateKey}

# GCP 配置 (开发环境不使用)
# 注释掉以避免 GCP 认证要求
# GCP_KMS_PROJECT_ID=celtic-descent-398803
# EXECUTOR_SECRET_ID=executor-keypair-001
# GOOGLE_APPLICATION_CREDENTIALS=./gcp-service-account.json

# 日志配置
LOG_LEVEL=debug
LOG_FORMAT=pretty

# 开发工具
ENABLE_SWAGGER=true
ENABLE_METRICS=true
`;

  fs.writeFileSync(CONFIG.PATHS.executorEnv, envContent);
  fs.writeFileSync(CONFIG.PATHS.executorEnvDev, envContent);
  
  console.log('✅ Executor 环境配置已创建');
  
  // 同步 Gateway 配置
  if (fs.existsSync(CONFIG.PATHS.gatewayEnv)) {
    updateEnvFile(CONFIG.PATHS.gatewayEnv, 'EXECUTOR_SECRET_KEY', secretKey);
    console.log('✅ Gateway 环境配置已同步');
  } else {
    console.log('⚠️  Gateway .env 文件不存在，请手动添加:');
    console.log(`   EXECUTOR_SECRET_KEY=${secretKey}`);
  }
  
  return { executorId, secretKey, authSecret: generateExecutorSecret(executorId, defaults.region, defaults.modelType, secretKey) };
}

// 配置生产环境
function setupProduction() {
  console.log('\n🚀 配置生产环境...');
  
  const timestamp = Date.now();
  const executorId = `executor-prod-${timestamp.toString().slice(-6)}`;
  const secretKey = crypto.randomBytes(32).toString('hex');
  const privateKey = getPrivateKey();
  const defaults = CONFIG.DEFAULTS.prod;
  
  const envContent = `# ===========================================
# SightAI Executor 生产环境配置
# 生成时间: ${new Date().toISOString()}
# ===========================================

NODE_ENV=production
PORT=${defaults.port}
PUBLIC_URL=http://localhost:${defaults.port}

# Executor 配置
EXECUTOR_ID=${executorId}
EXECUTOR_REGION=${defaults.region}
EXECUTOR_MODEL_TYPE=${defaults.modelType}
EXECUTOR_SECRET_KEY=${secretKey}

# etcd 配置
ETCD_ENDPOINTS=${defaults.etcdEndpoints}

# Gateway 配置
GATEWAY_URL=${defaults.gatewayUrl}

# ===========================================
# Executor 密钥配置 (生产环境)
# ===========================================

# GCP KMS 配置
GCP_KMS_PROJECT_ID=\${GCP_KMS_PROJECT_ID}
EXECUTOR_SECRET_ID=executor-keypair-001

# 生产环境：优先使用 GCP Secret Manager
# 如果 GCP 不可用，使用本地私钥作为备用
EXECUTOR_PRIVATE_KEY=${privateKey}

# GCP 认证配置
GOOGLE_APPLICATION_CREDENTIALS=./gcp-service-account.json

# 日志配置
LOG_LEVEL=info
LOG_FORMAT=json

# 生产工具
ENABLE_SWAGGER=false
ENABLE_METRICS=true
`;

  fs.writeFileSync('.env.production', envContent);
  console.log('✅ 生产环境配置已创建: .env.production');
  
  return { executorId, secretKey, authSecret: generateExecutorSecret(executorId, defaults.region, defaults.modelType, secretKey) };
}

// 验证配置
function verifyConfiguration() {
  console.log('\n🔍 验证配置...');

  const executorEnv = readEnvFile(CONFIG.PATHS.executorEnv);
  const gatewayEnv = readEnvFile(CONFIG.PATHS.gatewayEnv);

  const checks = [
    { name: '密钥对文件', path: CONFIG.PATHS.keyPairFile },
    { name: '公钥文件', path: CONFIG.PATHS.publicKeyFile },
    { name: '私钥环境文件', path: CONFIG.PATHS.privateKeyEnvFile },
    { name: 'Executor 环境配置', path: CONFIG.PATHS.executorEnv },
  ];

  let allPassed = true;

  console.log('📁 文件检查:');
  for (const check of checks) {
    const exists = fs.existsSync(check.path);
    console.log(`${exists ? '✅' : '❌'} ${check.name}: ${check.path}`);
    if (!exists) {
      allPassed = false;
    }
  }

  console.log('\n📋 配置检查:');
  const executorId = executorEnv.EXECUTOR_ID;
  const region = executorEnv.EXECUTOR_REGION;
  const modelType = executorEnv.EXECUTOR_MODEL_TYPE;
  const executorSecretKey = executorEnv.EXECUTOR_SECRET_KEY;
  const gatewaySecretKey = gatewayEnv.EXECUTOR_SECRET_KEY;

  console.log(`${executorId ? '✅' : '❌'} Executor ID: ${executorId || '未设置'}`);
  console.log(`${region ? '✅' : '❌'} Region: ${region || '未设置'}`);
  console.log(`${modelType ? '✅' : '❌'} Model Type: ${modelType || '未设置'}`);
  console.log(`${executorSecretKey ? '✅' : '❌'} Executor Secret Key: ${executorSecretKey || '未设置'}`);
  console.log(`${gatewaySecretKey ? '✅' : '❌'} Gateway Secret Key: ${gatewaySecretKey || '未设置'}`);

  if (executorSecretKey && gatewaySecretKey && executorSecretKey !== gatewaySecretKey) {
    console.log('❌ Executor 和 Gateway 的 EXECUTOR_SECRET_KEY 不一致');
    allPassed = false;
  } else if (executorSecretKey && gatewaySecretKey) {
    console.log('✅ 认证密钥配置一致');
    const authSecret = generateExecutorSecret(executorId, region, modelType, executorSecretKey);
    console.log(`✅ 期望的认证密钥: ${authSecret}`);
  }

  return allPassed;
}

// 清理配置
function cleanConfiguration() {
  console.log('\n🧹 清理配置文件...');

  const filesToClean = [
    CONFIG.PATHS.executorEnv,
    CONFIG.PATHS.executorEnvDev,
    '.env.production',
    CONFIG.PATHS.keysDir
  ];

  for (const file of filesToClean) {
    if (fs.existsSync(file)) {
      if (fs.statSync(file).isDirectory()) {
        fs.rmSync(file, { recursive: true, force: true });
        console.log(`✅ 已删除目录: ${file}`);
      } else {
        fs.unlinkSync(file);
        console.log(`✅ 已删除文件: ${file}`);
      }
    }
  }

  console.log('✅ 清理完成');
}

// 显示使用说明
function showUsage() {
  console.log('🛠️ SightAI Executor 统一环境配置脚本\n');
  console.log('使用方法:');
  console.log('  node scripts/setup-environment.js [mode]\n');
  console.log('模式:');
  console.log('  dev     - 配置开发环境 (默认)');
  console.log('  prod    - 配置生产环境');
  console.log('  verify  - 验证当前配置');
  console.log('  test    - 测试系统连接');
  console.log('  clean   - 清理所有配置');
  console.log('  help    - 显示此帮助信息\n');
  console.log('示例:');
  console.log('  node scripts/setup-environment.js dev');
  console.log('  node scripts/setup-environment.js verify');
  console.log('  node scripts/setup-environment.js test');
}

// 主函数
async function main() {
  const mode = process.argv[2] || 'dev';

  console.log('🛠️ SightAI Executor 统一环境配置工具\n');

  try {
    switch (mode) {
      case 'dev':
      case 'development':
        if (!generateKeyPair()) {
          process.exit(1);
        }
        const devConfig = setupDevelopment();
        console.log('\n🎯 开发环境配置完成!');
        console.log(`   Executor ID: ${devConfig.executorId}`);
        console.log(`   Secret Key: ${devConfig.secretKey}`);
        console.log(`   Auth Secret: ${devConfig.authSecret}`);
        console.log('\n🚀 启动命令: npm run start:dev');
        console.log('🔧 重启 Gateway: cd ../packages/apps/api-server && npm run start:dev');
        break;

      case 'prod':
      case 'production':
        if (!generateKeyPair()) {
          process.exit(1);
        }
        const prodConfig = setupProduction();
        console.log('\n🎯 生产环境配置完成!');
        console.log(`   Executor ID: ${prodConfig.executorId}`);
        console.log(`   Secret Key: ${prodConfig.secretKey}`);
        console.log(`   Auth Secret: ${prodConfig.authSecret}`);
        console.log('\n⚠️  请配置 GCP 认证后启动');
        break;

      case 'verify':
        const isValid = verifyConfiguration();
        process.exit(isValid ? 0 : 1);
        break;

        case 'clean':
        cleanConfiguration();
        break;

      case 'help':
      case '--help':
      case '-h':
        showUsage();
        break;

      default:
        console.log(`❌ 未知模式: ${mode}`);
        console.log('使用 --help 查看可用模式');
        process.exit(1);
    }
  } catch (error) {
    console.error('❌ 配置过程中发生错误:', error.message);
    process.exit(1);
  }
}

// 运行脚本
if (require.main === module) {
  main().catch((error) => {
    console.error('❌ 脚本执行失败:', error.message);
    process.exit(1);
  });
}
