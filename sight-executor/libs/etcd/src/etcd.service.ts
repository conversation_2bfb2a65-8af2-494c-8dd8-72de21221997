import { Injectable, Logger, OnModuleInit, OnModuleDestroy } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Etcd3 } from 'etcd3';

// Type definition for etcd lease
interface EtcdLease {
  revoke(): Promise<void>;
  keepaliveOnce(): Promise<unknown>;
}

@Injectable()
export class EtcdService implements OnModuleInit, OnModuleDestroy {
  private readonly logger = new Logger(EtcdService.name);
  private etcdClient: Etcd3;
  private readonly modelType: string;
  private leaseRenewalIntervals: Map<string, NodeJS.Timeout> = new Map(); // Track lease renewal intervals
  private activeLeasesMap: Map<string, EtcdLease> = new Map(); // Track active lease objects

  constructor(private configService: ConfigService) {
    this.modelType = this.configService.get<string>('EXECUTOR_MODEL_TYPE', 'openai');
  }

  onModuleInit(): void {
    const etcdEndpoints = this.configService.get<string[]>('etcd.endpoints', ['localhost:2379']);

    this.etcdClient = new Etcd3({
      hosts: etcdEndpoints,
    });

    this.logger.log(`Initialized etcd client with endpoints: ${etcdEndpoints.join(', ')}`);
    this.logger.log(`Model Type: ${this.modelType}`);
  }

  onModuleDestroy(): void {
    if (this.etcdClient) {
      // Stop all lease renewal intervals
      this.stopAllLeaseRenewals();

      this.etcdClient.close();
      this.logger.log('etcd client closed');
    }
  }

  getModelType(): string {
    return this.modelType;
  }

  getEtcdClient(): Etcd3 | null {
    return this.etcdClient || null;
  }

  isClientReady(): boolean {
    return !!this.etcdClient;
  }

  /**
   * Stop all lease renewals (called during shutdown)
   */
  private stopAllLeaseRenewals(): void {
    for (const [leaseId, interval] of this.leaseRenewalIntervals) {
      clearInterval(interval);
      this.logger.debug(`Stopped lease renewal for ${leaseId} during shutdown`);
    }
    this.leaseRenewalIntervals.clear();
    this.activeLeasesMap.clear();
    this.logger.log('Stopped all lease renewals');
  }
}
