import { Injectable, Logger, OnModuleInit, OnModuleDestroy } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Etcd3 } from 'etcd3';
import { ApiKeyInfo, ApiKeyWithEtcdId } from '@app/types';

// Type definition for etcd lease
interface EtcdLease {
  revoke(): Promise<void>;
  keepaliveOnce(): Promise<unknown>;
}

@Injectable()
export class EtcdService implements OnModuleInit, OnModuleDestroy {
  private readonly logger = new Logger(EtcdService.name);
  private etcdClient: Etcd3;
  private readonly modelType: string;
  private leaseRenewalIntervals: Map<string, NodeJS.Timeout> = new Map(); // Track lease renewal intervals
  private activeLeasesMap: Map<string, EtcdLease> = new Map(); // Track active lease objects

  constructor(private configService: ConfigService) {
    this.modelType = this.configService.get<string>('EXECUTOR_MODEL_TYPE', 'openai');
  }

  onModuleInit(): void {
    const etcdEndpoints = this.configService.get<string[]>('etcd.endpoints', ['localhost:2379']);

    this.etcdClient = new Etcd3({
      hosts: etcdEndpoints,
    });

    this.logger.log(`Initialized etcd client with endpoints: ${etcdEndpoints.join(', ')}`);
    this.logger.log(`Model Type: ${this.modelType}`);
  }

  onModuleDestroy(): void {
    if (this.etcdClient) {
      // Stop all lease renewal intervals
      this.stopAllLeaseRenewals();

      this.etcdClient.close();
      this.logger.log('etcd client closed');
    }
  }

  // API Key management functionality

  async getActiveApiKeys(provider: string): Promise<ApiKeyWithEtcdId[]> {
    const keyPrefix = `/third-party-keys/${provider}/`;
    this.logger.debug(`Fetching third-party keys with prefix: ${keyPrefix}`);

    if (!this.etcdClient) {
      this.logger.error('etcd client is not initialized');
      return [];
    }

    try {
      // 使用 etcd3 的正确 API
      const kvs = await this.etcdClient.getAll().prefix(keyPrefix);
      this.logger.debug(`Raw etcd result:`, kvs);

      const keys: ApiKeyWithEtcdId[] = [];

      // etcd3 getAll().prefix() 返回一个对象，键是完整路径，值是字符串
      for (const [fullPath, jsonValue] of Object.entries(kvs)) {
        this.logger.debug(`Processing third-party key: ${fullPath}, value: ${jsonValue}`);
        try {
          const keyInfo = JSON.parse(jsonValue) as ApiKeyInfo;
          if (keyInfo.status === 'active') {
            // Extract keyId from path: /third-party-keys/provider/userId/keyId -> keyId
            const pathParts = fullPath.split('/');
            const etcdKeyId = pathParts[pathParts.length - 1];

            keys.push({
              ...keyInfo,
              etcdKeyId: etcdKeyId, // 添加实际的etcd keyId
            });
            this.logger.debug(`Added third-party key: ${fullPath} with etcdKeyId: ${etcdKeyId}`);
          } else {
            this.logger.debug(`Skipped non-active key: ${fullPath}, status: ${keyInfo.status}`);
          }
        } catch (error: any) {
          this.logger.warn(
            `Failed to parse key info for ${fullPath}: ${error instanceof Error ? error.message : String(error)}`,
          );
        }
      }

      this.logger.log(`Found ${keys.length} third-party keys for provider ${provider}`);
      return keys;
    } catch (error) {
      this.logger.error(
        `Failed to fetch keys from etcd: ${error instanceof Error ? error.message : String(error)}`,
      );
      return [];
    }
  }

  async debugGetRawEtcdData(): Promise<any> {
    const keyPrefix = `/third-party-keys/openai/`;
    try {
      const result = await this.etcdClient.getAll().prefix(keyPrefix);
      this.logger.debug(`Raw debug result:`, result);
      return result;
    } catch (error) {
      this.logger.error(
        `Debug etcd query failed: ${error instanceof Error ? error.message : String(error)}`,
      );
      throw error;
    }
  }

  getModelType(): string {
    return this.modelType;
  }

  getEtcdClient(): Etcd3 | null {
    return this.etcdClient || null;
  }

  isClientReady(): boolean {
    return !!this.etcdClient;
  }

  // Removed cleanupExecutorKeys method as it's no longer needed

  async updateApiKey(provider: string, keyId: string, updates: Partial<ApiKeyInfo>): Promise<void> {
    if (!this.etcdClient) {
      throw new Error('etcd client is not initialized');
    }

    const keyPath = `/third-party-keys/${provider}/${keyId}`;

    try {
      // Get current key data
      const currentData = await this.etcdClient.get(keyPath);
      if (!currentData) {
        throw new Error(`Key ${keyId} not found`);
      }

      const currentKeyInfo = JSON.parse(currentData) as ApiKeyInfo;

      // Merge updates with current data
      const updatedKeyInfo: ApiKeyInfo = {
        ...currentKeyInfo,
        ...updates,
      };

      // Update in etcd
      await this.etcdClient.put(keyPath).value(JSON.stringify(updatedKeyInfo));

      this.logger.log(`Updated API key: ${keyPath}`);
    } catch (error) {
      this.logger.error(
        `Failed to update API key ${keyPath}: ${error instanceof Error ? error.message : String(error)}`,
      );
      throw error;
    }
  }

  async getAllKeys(provider?: string): Promise<Record<string, ApiKeyInfo>> {
    if (!this.etcdClient) {
      this.logger.error('etcd client is not initialized');
      return {};
    }

    const keyPrefix = provider ? `/third-party-keys/${provider}/` : '/third-party-keys/';

    try {
      const kvs = await this.etcdClient.getAll().prefix(keyPrefix);
      const result: Record<string, ApiKeyInfo> = {};

      for (const [fullPath, jsonValue] of Object.entries(kvs)) {
        try {
          const keyInfo = JSON.parse(jsonValue) as ApiKeyInfo;
          result[fullPath] = keyInfo;
        } catch {
          this.logger.warn(`Failed to parse key info: ${fullPath}`);
        }
      }

      return result;
    } catch (error) {
      this.logger.error(
        `Failed to get all keys: ${error instanceof Error ? error.message : String(error)}`,
      );
      return {};
    }
  }

  /**
   * Stop all lease renewals (called during shutdown)
   */
  private stopAllLeaseRenewals(): void {
    for (const [leaseId, interval] of this.leaseRenewalIntervals) {
      clearInterval(interval);
      this.logger.debug(`Stopped lease renewal for ${leaseId} during shutdown`);
    }
    this.leaseRenewalIntervals.clear();
    this.activeLeasesMap.clear();
    this.logger.log('Stopped all lease renewals');
  }
}
