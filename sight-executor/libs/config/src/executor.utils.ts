/**
 * Executor utility functions
 */

import { SUPPORTED_MODELS } from './executor.constants';

/**
 * Get supported models for a given model type
 */
export function getSupportedModels(modelType: string): string[] {
  const models = SUPPORTED_MODELS[modelType as keyof typeof SUPPORTED_MODELS];
  return models ? [...models] : [];
}

/**
 * Generate executor path for etcd
 */
export function generateExecutorPath(
  region: string,
  modelType: string,
  executorId: string,
): string {
  return `/executors/${region}/${modelType}/${executorId}`;
}
