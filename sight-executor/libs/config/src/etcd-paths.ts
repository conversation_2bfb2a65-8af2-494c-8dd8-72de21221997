/**
 * Unified etcd path management according to design document
 * Centralizes all etcd path generation to avoid hardcoded strings
 */

export class EtcdPathManager {
  /**
   * Generate executor registration path
   * Format: /executors/{region}/{model_type}/{executor_id}
   * According to design document section 5
   */
  static executorPath(region: string, modelType: string, executorId: string): string {
    return `/executors/${region}/${modelType}/${executorId}`;
  }

  /**
   * Generate executor discovery prefix path
   * Format: /executors/{region}/{model_type}/
   * Used for discovering all executors of a specific type in a region
   */
  static executorDiscoveryPrefix(region: string, modelType: string): string {
    return `/executors/${region}/${modelType}/`;
  }

  /**
   * Generate public key registration path
   * Format: /keys/public/{provider}/{region}/{keyId}
   * According to design document section 4.1
   */
  static publicKeyPath(provider: string, region: string, keyId: string): string {
    return `/keys/public/${provider}/${region}/${keyId}`;
  }

  /**
   * Generate public key discovery prefix path
   * Format: /keys/public/{provider}/{region}/
   * Used for discovering all public keys for a provider in a region
   */
  static publicKeyDiscoveryPrefix(provider: string, region: string): string {
    return `/keys/public/${provider}/${region}/`;
  }

  /**
   * Generate encrypted API key storage path
   * Format: /api-keys/{provider}/{region}/{uuid}
   * According to design document section 4.2
   */
  static encryptedApiKeyPath(provider: string, region: string, uuid: string): string {
    return `/api-keys/${provider}/${region}/${uuid}`;
  }

  /**
   * Generate encrypted API key discovery prefix path
   * Format: /api-keys/{provider}/{region}/
   * Used for discovering all encrypted API keys for a provider in a region
   */
  static encryptedApiKeyDiscoveryPrefix(provider: string, region: string): string {
    return `/api-keys/${provider}/${region}/`;
  }

  /**
   * Generate API key status path
   * Format: /keys/{provider}/{key_id}
   * According to design document section 4.3
   */
  static keyStatusPath(provider: string, keyId: string): string {
    return `/keys/${provider}/${keyId}`;
  }

  /**
   * Generate API key status discovery prefix path
   * Format: /keys/{provider}/
   * Used for discovering all key statuses for a provider
   */
  static keyStatusDiscoveryPrefix(provider: string): string {
    return `/keys/${provider}/`;
  }

  /**
   * Legacy third-party key path (for backward compatibility)
   * Format: /third-party-keys/{provider}/{keyId}
   * @deprecated Use encryptedApiKeyPath instead
   */
  static legacyThirdPartyKeyPath(provider: string, keyId: string): string {
    return `/third-party-keys/${provider}/${keyId}`;
  }

  /**
   * Legacy third-party key discovery prefix (for backward compatibility)
   * Format: /third-party-keys/{provider}/
   * @deprecated Use encryptedApiKeyDiscoveryPrefix instead
   */
  static legacyThirdPartyKeyDiscoveryPrefix(provider: string): string {
    return `/third-party-keys/${provider}/`;
  }

  /**
   * Validate provider name
   */
  static validateProvider(provider: string): boolean {
    const validProviders = ['openai', 'claude', 'anthropic', 'google', 'cohere'];
    return validProviders.includes(provider.toLowerCase());
  }

  /**
   * Validate region name
   */
  static validateRegion(region: string): boolean {
    // Basic validation - can be extended with specific region rules
    return /^[a-z0-9-]+$/.test(region) && region.length > 0 && region.length <= 50;
  }

  /**
   * Validate model type
   */
  static validateModelType(modelType: string): boolean {
    const validModelTypes = ['openai', 'claude', 'anthropic', 'google', 'cohere'];
    return validModelTypes.includes(modelType.toLowerCase());
  }

  /**
   * Validate executor ID
   */
  static validateExecutorId(executorId: string): boolean {
    // UUID format or custom format validation
    return /^[a-zA-Z0-9-_]+$/.test(executorId) && executorId.length > 0 && executorId.length <= 100;
  }

  /**
   * Validate key ID
   */
  static validateKeyId(keyId: string): boolean {
    return /^[a-zA-Z0-9-_]+$/.test(keyId) && keyId.length > 0 && keyId.length <= 100;
  }

  /**
   * Validate UUID format
   */
  static validateUUID(uuid: string): boolean {
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    return uuidRegex.test(uuid);
  }

  /**
   * Parse executor path to extract components
   * Returns null if path doesn't match expected format
   */
  static parseExecutorPath(
    path: string,
  ): { region: string; modelType: string; executorId: string } | null {
    const match = path.match(/^\/executors\/([^/]+)\/([^/]+)\/([^/]+)$/);
    if (!match) return null;

    return {
      region: match[1],
      modelType: match[2],
      executorId: match[3],
    };
  }

  /**
   * Parse public key path to extract components
   * Returns null if path doesn't match expected format
   */
  static parsePublicKeyPath(
    path: string,
  ): { provider: string; region: string; keyId: string } | null {
    const match = path.match(/^\/keys\/public\/([^/]+)\/([^/]+)\/([^/]+)$/);
    if (!match) return null;

    return {
      provider: match[1],
      region: match[2],
      keyId: match[3],
    };
  }

  /**
   * Parse encrypted API key path to extract components
   * Returns null if path doesn't match expected format
   */
  static parseEncryptedApiKeyPath(
    path: string,
  ): { provider: string; region: string; uuid: string } | null {
    const match = path.match(/^\/api-keys\/([^/]+)\/([^/]+)\/([^/]+)$/);
    if (!match) return null;

    return {
      provider: match[1],
      region: match[2],
      uuid: match[3],
    };
  }

  /**
   * Parse key status path to extract components
   * Returns null if path doesn't match expected format
   */
  static parseKeyStatusPath(path: string): { provider: string; keyId: string } | null {
    const match = path.match(/^\/keys\/([^/]+)\/([^/]+)$/);
    if (!match) return null;

    return {
      provider: match[1],
      keyId: match[2],
    };
  }

  /**
   * Get all path patterns for documentation/debugging
   */
  static getPathPatterns(): Record<string, string> {
    return {
      executor: '/executors/{region}/{model_type}/{executor_id}',
      executorDiscovery: '/executors/{region}/{model_type}/',
      publicKey: '/keys/public/{provider}/{region}/{keyId}',
      publicKeyDiscovery: '/keys/public/{provider}/{region}/',
      encryptedApiKey: '/api-keys/{provider}/{region}/{uuid}',
      encryptedApiKeyDiscovery: '/api-keys/{provider}/{region}/',
      keyStatus: '/keys/{provider}/{key_id}',
      keyStatusDiscovery: '/keys/{provider}/',
      legacyThirdPartyKey: '/third-party-keys/{provider}/{keyId}',
      legacyThirdPartyKeyDiscovery: '/third-party-keys/{provider}/',
    };
  }
}

/**
 * Constants for etcd path management
 */
export const ETCD_PATH_CONSTANTS = {
  // Root prefixes
  EXECUTORS_PREFIX: '/executors',
  KEYS_PREFIX: '/keys',
  API_KEYS_PREFIX: '/api-keys',
  PUBLIC_KEYS_PREFIX: '/keys/public',
  THIRD_PARTY_KEYS_PREFIX: '/third-party-keys', // Legacy

  // Separators
  PATH_SEPARATOR: '/',

  // Validation limits
  MAX_REGION_LENGTH: 50,
  MAX_MODEL_TYPE_LENGTH: 50,
  MAX_EXECUTOR_ID_LENGTH: 100,
  MAX_KEY_ID_LENGTH: 100,
  MAX_PROVIDER_LENGTH: 50,

  // Supported providers
  SUPPORTED_PROVIDERS: ['openai', 'claude', 'anthropic', 'google', 'cohere'] as const,

  // Supported model types
  SUPPORTED_MODEL_TYPES: ['openai', 'claude', 'anthropic', 'google', 'cohere'] as const,
} as const;

export type SupportedProvider = (typeof ETCD_PATH_CONSTANTS.SUPPORTED_PROVIDERS)[number];
export type SupportedModelType = (typeof ETCD_PATH_CONSTANTS.SUPPORTED_MODEL_TYPES)[number];
