export default () => ({
  port: parseInt(process.env.PORT || '3000', 10),

  // Executor configuration
  executor: {
    id: process.env.EXECUTOR_ID || 'executor-' + Date.now(),
    region: process.env.EXECUTOR_REGION || 'default',
    modelType: process.env.EXECUTOR_MODEL_TYPE || 'openai',
    publicUrl: process.env.PUBLIC_URL || 'http://localhost:3000',
    maxConcurrentRequests: parseInt(process.env.MAX_CONCURRENT_REQUESTS || '10', 10),
    keyPoolSize: parseInt(process.env.KEY_POOL_SIZE || '5', 10),
  },

  // etcd configuration
  etcd: {
    endpoints: process.env.ETCD_ENDPOINTS?.split(',') || ['localhost:2379'],
    username: process.env.ETCD_USERNAME,
    password: process.env.ETCD_PASSWORD,
  },

  // AI Provider configurations
  openai: {
    baseUrl: process.env.OPENAI_BASE_URL || 'https://api.openai.com/v1',
    defaultModel: process.env.OPENAI_DEFAULT_MODEL,
  },
  // Rate limiting configuration
  rateLimit: {
    retryAfterMs: parseInt(process.env.RATE_LIMIT_RETRY_AFTER_MS || '60000', 10), // 1 minute
    maxRetries: parseInt(process.env.MAX_RETRIES || '3', 10),
  },

});
