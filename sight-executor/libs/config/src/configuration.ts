export default () => ({
  port: parseInt(process.env.PORT || '3000', 10),

  // Executor configuration
  executor: {
    id: process.env.EXECUTOR_ID || 'executor-' + Date.now(),
    region: process.env.EXECUTOR_REGION || 'default',
    modelType: process.env.EXECUTOR_MODEL_TYPE || 'openai',
    publicUrl: process.env.PUBLIC_URL || 'http://localhost:3000',
    maxConcurrentRequests: parseInt(process.env.MAX_CONCURRENT_REQUESTS || '10', 10),
    keyPoolSize: parseInt(process.env.KEY_POOL_SIZE || '5', 10),
  },

  // Gateway configuration
  gateway: {
    url: process.env.GATEWAY_URL || 'http://localhost:3000',
    timeout: parseInt(process.env.GATEWAY_TIMEOUT || '30000', 10), // 30 seconds
  },

  // etcd configuration
  etcd: {
    endpoints: process.env.ETCD_ENDPOINTS?.split(',') || ['localhost:2379'],
    username: process.env.ETCD_USERNAME,
    password: process.env.ETCD_PASSWORD,
  },

  // AI Provider configurations
  openai: {
    baseUrl: process.env.OPENAI_BASE_URL || 'https://api.openai.com/v1',
    defaultModel: process.env.OPENAI_DEFAULT_MODEL || 'gpt-3.5-turbo',
  },
  // Rate limiting configuration
  rateLimit: {
    retryAfterMs: parseInt(process.env.RATE_LIMIT_RETRY_AFTER_MS || '60000', 10), // 1 minute
    maxRetries: parseInt(process.env.MAX_RETRIES || '3', 10),
  },

  // KMS configuration
  kms: {
    provider: process.env.KMS_PROVIDER || 'gcp',
    gcp: {
      projectId: process.env.GCP_KMS_PROJECT_ID,
      location: process.env.GCP_KMS_LOCATION || 'global',
      keyRing: process.env.GCP_KMS_KEY_RING,
      keyName: process.env.GCP_KMS_KEY_NAME,
      credentialsPath: process.env.GOOGLE_APPLICATION_CREDENTIALS,
    },
  },
});
