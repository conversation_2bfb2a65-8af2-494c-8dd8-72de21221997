import { Injectable, Logger } from '@nestjs/common';

/**
 * OpenAI API 错误分类
 * 基于官方文档：https://platform.openai.com/docs/guides/error-codes/api-errors
 */
export enum OpenAIErrorType {
  // 认证错误 - Key 无效，需要移除或更换
  INVALID_AUTHENTICATION = 'invalid_authentication',
  INCORRECT_API_KEY = 'incorrect_api_key',
  ORGANIZATION_REQUIRED = 'organization_required',

  // 限流错误 - 临时限制，可以重试
  RATE_LIMIT = 'rate_limit',

  // 额度错误 - Key 额度耗尽，需要通知其他 Executor
  QUOTA_EXCEEDED = 'quota_exceeded',

  // 服务器错误 - 临时问题，可以重试
  SERVER_ERROR = 'server_error',
  ENGINE_OVERLOADED = 'engine_overloaded',
  SLOW_DOWN = 'slow_down',

  // 地区限制 - Key 在当前地区不可用
  REGION_NOT_SUPPORTED = 'region_not_supported',

  // 模型访问错误 - Key 不支持该模型，尝试其他 Key
  MODEL_NOT_FOUND = 'model_not_found',

  // 其他错误 - 不影响 Key 状态
  OTHER = 'other',
}

export interface ErrorClassification {
  type: OpenAIErrorType;
  shouldRetryWithSameKey: boolean;
  shouldTryOtherKeys: boolean;
  shouldMarkKeyBad: boolean;
  shouldNotifyOtherExecutors: boolean;
  retryDelayMs: number;
  description: string;
}

@Injectable()
export class OpenAIErrorClassifierService {
  private readonly logger = new Logger(OpenAIErrorClassifierService.name);

  /**
   * 分类 OpenAI API 错误
   */
  classifyError(error: any): ErrorClassification {
    const statusCode = this.extractStatusCode(error);
    const errorCode = this.extractErrorCode(error);
    const errorMessage = this.extractErrorMessage(error);

    this.logger.debug(
      `Classifying error: status=${statusCode}, code=${errorCode}, message=${errorMessage}`,
    );

    // 401 错误 - 认证问题
    if (statusCode === 401) {
      return this.classify401Error(errorMessage, errorCode);
    }

    // 403 错误 - 地区限制
    if (statusCode === 403) {
      return this.classify403Error(errorMessage);
    }

    // 429 错误 - 限流和额度
    if (statusCode === 429) {
      return this.classify429Error(errorMessage);
    }

    // 500 错误 - 服务器错误
    if (statusCode === 500) {
      return this.classify500Error();
    }

    // 503 错误 - 服务过载
    if (statusCode === 503) {
      return this.classify503Error(errorMessage);
    }

    // 模型相关错误
    if (this.isModelError(errorMessage, errorCode)) {
      return {
        type: OpenAIErrorType.MODEL_NOT_FOUND,
        shouldRetryWithSameKey: false,
        shouldTryOtherKeys: true,
        shouldMarkKeyBad: false,
        shouldNotifyOtherExecutors: false,
        retryDelayMs: 0,
        description: 'Model not available for this API key',
      };
    }

    // 默认为其他错误
    return {
      type: OpenAIErrorType.OTHER,
      shouldRetryWithSameKey: false,
      shouldTryOtherKeys: true,
      shouldMarkKeyBad: false,
      shouldNotifyOtherExecutors: false,
      retryDelayMs: 1000,
      description: 'Unknown error',
    };
  }

  /**
   * 分类 401 错误
   */
  private classify401Error(message: string, code: string): ErrorClassification {
    const lowerMessage = message.toLowerCase();

    // Invalid Authentication
    if (lowerMessage.includes('invalid authentication') || code === 'invalid_api_key') {
      return {
        type: OpenAIErrorType.INVALID_AUTHENTICATION,
        shouldRetryWithSameKey: false,
        shouldTryOtherKeys: true,
        shouldMarkKeyBad: true,
        shouldNotifyOtherExecutors: true,
        retryDelayMs: 0,
        description: 'Invalid API key authentication',
      };
    }

    // Incorrect API key provided
    if (lowerMessage.includes('incorrect api key') || lowerMessage.includes('api key provided')) {
      return {
        type: OpenAIErrorType.INCORRECT_API_KEY,
        shouldRetryWithSameKey: false,
        shouldTryOtherKeys: true,
        shouldMarkKeyBad: true,
        shouldNotifyOtherExecutors: true,
        retryDelayMs: 0,
        description: 'Incorrect API key provided',
      };
    }

    // Organization membership required
    if (lowerMessage.includes('organization') || lowerMessage.includes('member')) {
      return {
        type: OpenAIErrorType.ORGANIZATION_REQUIRED,
        shouldRetryWithSameKey: false,
        shouldTryOtherKeys: true,
        shouldMarkKeyBad: true,
        shouldNotifyOtherExecutors: true,
        retryDelayMs: 0,
        description: 'API key requires organization membership',
      };
    }

    // 默认 401 处理
    return {
      type: OpenAIErrorType.INVALID_AUTHENTICATION,
      shouldRetryWithSameKey: false,
      shouldTryOtherKeys: true,
      shouldMarkKeyBad: true,
      shouldNotifyOtherExecutors: true,
      retryDelayMs: 0,
      description: 'Authentication failed',
    };
  }

  /**
   * 分类 403 错误
   */
  private classify403Error(message: string): ErrorClassification {
    const lowerMessage = message.toLowerCase();

    if (
      lowerMessage.includes('country') ||
      lowerMessage.includes('region') ||
      lowerMessage.includes('territory')
    ) {
      return {
        type: OpenAIErrorType.REGION_NOT_SUPPORTED,
        shouldRetryWithSameKey: false,
        shouldTryOtherKeys: true,
        shouldMarkKeyBad: false, // Key 本身可能是好的，只是地区限制
        shouldNotifyOtherExecutors: false,
        retryDelayMs: 0,
        description: 'Country, region, or territory not supported',
      };
    }

    return {
      type: OpenAIErrorType.OTHER,
      shouldRetryWithSameKey: false,
      shouldTryOtherKeys: true,
      shouldMarkKeyBad: false,
      shouldNotifyOtherExecutors: false,
      retryDelayMs: 1000,
      description: 'Forbidden access',
    };
  }

  /**
   * 分类 429 错误
   */
  private classify429Error(message: string): ErrorClassification {
    const lowerMessage = message.toLowerCase();

    // Rate limit reached
    if (lowerMessage.includes('rate limit')) {
      return {
        type: OpenAIErrorType.RATE_LIMIT,
        shouldRetryWithSameKey: false, // 当前 Key 被限流，先尝试其他 Key
        shouldTryOtherKeys: true,
        shouldMarkKeyBad: false, // 限流是临时的，不标记为坏 Key
        shouldNotifyOtherExecutors: false,
        retryDelayMs: 60000, // 1 分钟后可以重试
        description: 'Rate limit reached for requests',
      };
    }

    // Quota exceeded
    if (
      lowerMessage.includes('quota') ||
      lowerMessage.includes('billing') ||
      lowerMessage.includes('credits')
    ) {
      return {
        type: OpenAIErrorType.QUOTA_EXCEEDED,
        shouldRetryWithSameKey: false,
        shouldTryOtherKeys: true,
        shouldMarkKeyBad: true, // 额度耗尽，标记为坏 Key
        shouldNotifyOtherExecutors: true, // 通知其他 Executor
        retryDelayMs: 0,
        description: 'Quota exceeded, check plan and billing',
      };
    }

    // 默认 429 处理为限流
    return {
      type: OpenAIErrorType.RATE_LIMIT,
      shouldRetryWithSameKey: false,
      shouldTryOtherKeys: true,
      shouldMarkKeyBad: false,
      shouldNotifyOtherExecutors: false,
      retryDelayMs: 60000,
      description: 'Too many requests',
    };
  }

  /**
   * 分类 500 错误
   */
  private classify500Error(): ErrorClassification {
    return {
      type: OpenAIErrorType.SERVER_ERROR,
      shouldRetryWithSameKey: true, // 服务器错误，可以重试同一个 Key
      shouldTryOtherKeys: true,
      shouldMarkKeyBad: false,
      shouldNotifyOtherExecutors: false,
      retryDelayMs: 5000, // 5 秒后重试
      description: 'Server had an error while processing request',
    };
  }

  /**
   * 分类 503 错误
   */
  private classify503Error(message: string): ErrorClassification {
    const lowerMessage = message.toLowerCase();

    if (lowerMessage.includes('slow down')) {
      return {
        type: OpenAIErrorType.SLOW_DOWN,
        shouldRetryWithSameKey: false,
        shouldTryOtherKeys: true,
        shouldMarkKeyBad: false,
        shouldNotifyOtherExecutors: false,
        retryDelayMs: 15 * 60 * 1000, // 15 分钟后重试
        description: 'Slow down - reduce request rate',
      };
    }

    return {
      type: OpenAIErrorType.ENGINE_OVERLOADED,
      shouldRetryWithSameKey: true,
      shouldTryOtherKeys: true,
      shouldMarkKeyBad: false,
      shouldNotifyOtherExecutors: false,
      retryDelayMs: 10000, // 10 秒后重试
      description: 'Engine is currently overloaded',
    };
  }

  /**
   * 检查是否是模型相关错误
   */
  private isModelError(message: string, code: string): boolean {
    const lowerMessage = message.toLowerCase();
    return (
      code === 'model_not_found' ||
      (lowerMessage.includes('model') &&
        (lowerMessage.includes('does not exist') ||
          lowerMessage.includes('not available') ||
          lowerMessage.includes('access')))
    );
  }

  /**
   * 提取 HTTP 状态码
   */
  private extractStatusCode(error: unknown): number {
    const errorObj = error as {
      status?: number;
      response?: { status?: number };
      statusCode?: number;
    };
    return errorObj?.status || errorObj?.response?.status || errorObj?.statusCode || 0;
  }

  /**
   * 提取错误代码
   */
  private extractErrorCode(error: unknown): string {
    const errorObj = error as {
      code?: string;
      error?: { code?: string };
      response?: { data?: { error?: { code?: string } } };
    };
    return errorObj?.code || errorObj?.error?.code || errorObj?.response?.data?.error?.code || '';
  }

  /**
   * 提取错误消息
   */
  private extractErrorMessage(error: unknown): string {
    const errorObj = error as {
      message?: string;
      error?: { message?: string };
      response?: {
        data?: {
          error?: { message?: string };
          message?: string;
        };
      };
    };
    return (
      errorObj?.message ||
      errorObj?.error?.message ||
      errorObj?.response?.data?.error?.message ||
      errorObj?.response?.data?.message ||
      String(error)
    );
  }
}
