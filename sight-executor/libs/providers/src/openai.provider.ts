import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import axios, { AxiosRequestConfig } from 'axios';
import { HttpsProxyAgent } from 'https-proxy-agent';
import {
  BaseModelProvider,
  ChatCompletionRequest,
  ChatCompletionResponse,
  ProviderError,
} from './base-provider.interface';
import { SUPPORTED_MODELS } from '@app/config';

@Injectable()
export class OpenAIProvider extends BaseModelProvider {
  readonly providerName = 'openai';
  private readonly logger = new Logger(OpenAIProvider.name);
  private readonly baseUrl: string;
  private readonly defaultModel: string;

  constructor(private configService: ConfigService) {
    super();
    this.baseUrl = this.configService.get<string>('openai.baseUrl') || 'https://api.openai.com/';
    this.defaultModel = this.configService.get<string>('openai.defaultModel') || 'gpt-3.5-turbo';
  }

  async chatCompletion(
    request: ChatCompletionRequest,
    apiKey: string,
  ): Promise<ChatCompletionResponse | AsyncIterable<string>> {
    console.log(`OpenAI request: ${JSON.stringify(request)} ${apiKey} ${this.baseUrl}`);

    // 确保 URL 正确构造，避免重复的 v1
    const baseUrl = this.baseUrl.endsWith('/') ? this.baseUrl : this.baseUrl + '/';
    const url = baseUrl.includes('/v1/')
      ? `${baseUrl}chat/completions`
      : `${baseUrl}v1/chat/completions`;

    const requestBody = {
      model: request.model || this.defaultModel,
      messages: request.messages,
      temperature: request.temperature,
      max_tokens: request.max_tokens,
      top_p: request.top_p,
      frequency_penalty: request.frequency_penalty,
      presence_penalty: request.presence_penalty,
      stream: request.stream || false,
    };

    try {
      this.logger.debug(
        `Making OpenAI request with model: ${request.model}, API key: ${apiKey.substring(0, 10)}...`,
      );
      this.logger.debug(`Request URL: ${url}`);
      this.logger.debug(`Request body: ${JSON.stringify(requestBody)}`);

      // 配置代理（如果需要）
      const proxyUrl = process.env.HTTPS_PROXY || process.env.HTTP_PROXY;
      const axiosConfig: AxiosRequestConfig = {
        headers: {
          Authorization: `Bearer ${apiKey}`,
          'Content-Type': 'application/json',
        },
        timeout: 60000, // 60 seconds timeout
        validateStatus: (status: number) => status < 500, // Don't throw for 4xx errors
      };

      if (proxyUrl) {
        (axiosConfig as { httpsAgent?: unknown }).httpsAgent = new HttpsProxyAgent(proxyUrl);
        this.logger.debug(`Using proxy: ${proxyUrl}`);
      }

      if (request.stream) {
        // Handle streaming response
        axiosConfig.responseType = 'stream';
        const response = await axios.post(url, requestBody, axiosConfig);

        this.logger.debug(`OpenAI streaming response status: ${response.status}`);

        // Return an async iterable for streaming
        return this.createStreamIterable(response.data as NodeJS.ReadableStream);
      } else {
        // Handle non-streaming response
        const response = await axios.post(url, requestBody, axiosConfig);

        console.log('OpenAI response:', response.data);
        const responseData = response.data as {
          error?: { message: string; type: string };
          choices?: Array<{
            index: number;
            message: { role: string; content: string };
            finish_reason: string;
          }>;
          id?: string;
          object?: string;
          created?: number;
          model?: string;
          usage?: {
            prompt_tokens: number;
            completion_tokens: number;
            total_tokens: number;
          };
        };

        // Check for OpenAI API errors
        if (responseData.error) {
          const error = responseData.error;
          throw new Error(`OpenAI API Error: ${error.message} (${error.type})`);
        }

        // Check if response has choices
        if (!responseData.choices || !Array.isArray(responseData.choices)) {
          throw new Error('Invalid OpenAI response: missing choices array');
        }

        // Convert OpenAI response to our standard format
        const standardResponse: ChatCompletionResponse = {
          id: responseData.id || '',
          object: responseData.object || 'chat.completion',
          created: responseData.created || Date.now(),
          model: responseData.model || '',
          choices: responseData.choices.map((choice) => ({
            index: choice.index,
            message: {
              role: choice.message.role,
              content: choice.message.content || '',
            },
            finish_reason: choice.finish_reason || 'stop',
          })),
          usage: {
            prompt_tokens: responseData.usage?.prompt_tokens || 0,
            completion_tokens: responseData.usage?.completion_tokens || 0,
            total_tokens: responseData.usage?.total_tokens || 0,
          },
        };

        this.logger.debug(
          `OpenAI request completed successfully. Tokens used: ${standardResponse.usage.total_tokens}`,
        );
        return standardResponse;
      }
    } catch (error) {
      this.logger.error(`OpenAI request failed:`, error);
      const providerError = this.handleAxiosError(error);

      // Create an Error object with ProviderError properties
      const finalError = new Error(providerError.message) as Error & ProviderError;
      finalError.code = providerError.code;
      finalError.type = providerError.type;
      finalError.retryable = providerError.retryable;

      throw finalError;
    }
  }

  private handleAxiosError(error: unknown): ProviderError {
    // Handle axios errors
    console.log(error);
    let statusCode = 500;
    let errorMessage = 'Unknown error';
    let errorType:
      | 'rate_limit'
      | 'insufficient_quota'
      | 'invalid_request'
      | 'server_error'
      | 'unknown' = 'unknown';

    if (axios.isAxiosError(error)) {
      if (error.response) {
        // Server responded with error status
        statusCode = error.response.status;
        errorMessage =
          (error.response.data as { error?: { message?: string } })?.error?.message ||
          error.message;
      } else if (error.request) {
        // Request was made but no response received (timeout, network error)
        errorType = 'server_error';
        errorMessage = 'Network timeout or connection error';
        statusCode = 408;
      } else {
        // Something else happened
        errorMessage = error.message;
      }
    } else {
      errorMessage = (error instanceof Error ? error.message : String(error)) || 'Network error';
    }

    // Map status codes to error types
    switch (statusCode) {
      case 429:
        errorType = 'rate_limit';
        break;
      case 401:
      case 403:
        errorType = 'insufficient_quota';
        break;
      case 400:
        errorType = 'invalid_request';
        break;
      case 500:
      case 502:
      case 503:
      case 504:
      case 408:
        errorType = 'server_error';
        break;
    }

    return {
      code: `http_${statusCode}`,
      type: errorType,
      message: errorMessage,
      retryable: this.isRetryableByStatus(statusCode),
    };
  }

  private isRetryableByStatus(statusCode: number): boolean {
    // Retryable status codes
    return [429, 500, 502, 503, 504, 408].includes(statusCode);
  }

  getSupportedModels(): string[] {
    return [...SUPPORTED_MODELS.openai];
  }

  validateModel(model: string): boolean {
    return this.getSupportedModels().includes(model);
  }

  private async *createStreamIterable(stream: NodeJS.ReadableStream): AsyncIterable<string> {
    let buffer = '';

    for await (const chunk of stream) {
      buffer += chunk.toString();
      const lines = buffer.split('\n');
      buffer = lines.pop() || ''; // Keep the last incomplete line in buffer

      for (const line of lines) {
        if (line.trim() === '') continue;
        if (line.startsWith('data: ')) {
          const data = line.slice(6).trim();
          if (data === '[DONE]') {
            return;
          }

          try {
            const parsed = JSON.parse(data) as {
              id?: string;
              created?: number;
              model?: string;
              choices?: Array<{
                delta?: { content?: string };
                finish_reason?: string | null;
              }>;
            };
            // Convert OpenAI streaming format to our format
            if (parsed.choices && parsed.choices[0] && parsed.choices[0].delta) {
              const delta = parsed.choices[0].delta;
              if (delta.content) {
                yield `data: ${JSON.stringify({
                  id: parsed.id,
                  object: 'chat.completion.chunk',
                  created: parsed.created,
                  model: parsed.model,
                  choices: [
                    {
                      index: 0,
                      delta: { content: delta.content },
                      finish_reason: parsed.choices[0].finish_reason || null,
                    },
                  ],
                })}\n\n`;
              }
            }
          } catch {
            this.logger.warn(`Failed to parse streaming data: ${data}`);
          }
        }
      }
    }
  }
}
