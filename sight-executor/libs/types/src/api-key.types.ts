/**
 * API Key management types and interfaces
 */

export interface ApiKeyInfo {
  key: string;
  status: 'active' | 'rate_limited' | 'exhausted' | 'revoked' | 'in_use';
  lastUsedAt: number;
  lastError?: string;
  note?: string;
  leaseId?: string; // etcd lease ID for automatic expiration
  lockedAt?: number; // timestamp when key was locked for use
  lockTimeout?: number; // timeout for automatic release (default 5 minutes)
}

export interface ManagedApiKey {
  id: string;
  key: string;
  status: 'active' | 'rate_limited' | 'exhausted' | 'revoked' | 'in_use';
  lastUsedAt: number;
  lastError?: string;
  note?: string;
  usageCount: number;
  errorCount: number;
  keyId: string;
  lockedAt?: number; // timestamp when key was locked for use
  lockTimeout?: number; // timeout for automatic release
}

export interface ApiKeyWithEtcdId extends ApiKeyInfo {
  etcdKeyId: string;
}

export interface KeyPoolStatus {
  totalKeys: number;
  activeKeys: number;
  inUseKeys: number;
  rateLimitedKeys: number;
  exhaustedKeys: number;
  revokedKeys: number;
}

/**
 * Public key registration data stored in etcd (according to design document)
 * Path: /keys/public/{provider}/{region}/{keyId}
 */
export interface PublicKeyRegistration {
  keyId: string; // executor 公钥标识
  publicKey: string; // base64(X25519 pub) - base64 编码公钥
  provider: 'openai' | 'claude' | 'anthropic' | 'google' | 'cohere'; // 模型提供商
  region: string; // 区域
  executorUrl?: string; // Executor URL (optional)
  registeredAt: string; // 注册时间
  lastHeartbeat?: string; // 最后心跳时间
  status: 'active' | 'inactive'; // 状态
}

/**
 * Encrypted API Key data stored in etcd (according to design document)
 * Path: /api-keys/{provider}/{region}/{uuid}
 */
export interface EncryptedApiKeyData {
  keyId: string; // 目标 Executor 的 keyId
  encryptedKey: string; // base64(ciphertext) 加密后的Provider API KEY
  nonce: string; // base64(nonce) 随机数，ChaCha20-Poly1305要求解密用
  tag: string; // base64(tag) ChaCha20-Poly1305要求解密用
  ephemeralPubKey: string; // base64(X25519 公钥)
  status: 'active' | 'revoked' | 'waiting-to-verify';
  createdAt: string; // ISO8601 string
}

/**
 * API Key status data stored in etcd (according to design document)
 * Path: /keys/{provider}/{key_id}
 */
export interface ApiKeyStatus {
  key: string; // "sk-xxxx" - the actual API key (encrypted)
  status: 'active' | 'rate_limited' | 'exhausted' | 'revoked';
  lastUsedAt: number; // timestamp
  lastError?: string; // error message if any
  note?: string; // additional notes
}

// Legacy interfaces for backward compatibility
export interface ThirdPartyKeyInfo {
  keyId: string;
  userId: string;
  provider: string;
  encryptedApiKey?: string; // API 密钥 (简化版本，假设为明文)
  status: 'active' | 'inactive' | 'revoked' | 'in_use';
  createdAt: number;
  lastUsedAt?: number;
  lockedAt?: number;
  lockTimeout?: number;
  note?: string;
  keyType?: string; // 密钥类型
}

export interface ThirdPartyKeyWithPath extends ThirdPartyKeyInfo {
  etcdPath: string;
}

export interface CachedApiKey {
  keyId: string;
  decryptedKey: string;
  cachedAt: number;
}
