import { Injectable, Logger, OnModuleInit, OnModuleDestroy } from '@nestjs/common';
import { EtcdService } from '@app/etcd';
import { ConfigService } from '@nestjs/config';
import { CachedApiKey } from '@app/types';

export interface KeyStatusInfo {
  keyId: string;
  status: 'rate_limited' | 'exhausted';
  timestamp: number;
  executorId: string;
  reason?: string;
}

export interface MemoryKeyStatus {
  keyId: string;
  status: 'rate_limited';
  blockedUntil: number; // 限流密钥何时可以重新使用
  reason?: string;
}

@Injectable()
export class KeyStatusManagerService implements OnModuleInit, OnModuleDestroy {
  private readonly logger = new Logger(KeyStatusManagerService.name);
  private readonly executorId: string;
  private readonly provider: string;

  // 内存中的密钥状态缓存（仅限流状态）
  private memoryKeyStatus = new Map<string, MemoryKeyStatus>();

  // 内存中的解密密钥缓存
  private cachedKeys = new Map<string, CachedApiKey>();

  // 清理任务
  private cleanupInterval: NodeJS.Timeout;

  constructor(
    private etcdService: EtcdService,
    private configService: ConfigService,
  ) {
    this.provider = this.configService.get<string>('model.type', 'openai');
    this.executorId = this.generateExecutorId();
  }

  onModuleInit() {
    // 启动 etcd 状态监听
    this.startStatusWatcher();

    // 启动定期清理任务
    this.startCleanupTask();

    this.logger.log(`Key status manager initialized for executor ${this.executorId}`);
  }

  onModuleDestroy() {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
    }

    this.logger.log('Key status manager destroyed');
  }

  /**
   * 生成唯一的 Executor ID
   */
  private generateExecutorId(): string {
    return `executor-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 标记密钥为限流状态（5分钟后自动恢复，仅在本地内存中处理）
   */
  markKeyRateLimited(keyId: string, reason?: string): void {
    const now = Date.now();
    const blockedUntil = now + 5 * 60 * 1000; // 5分钟后恢复

    // 仅在内存中标记，不通知其他 Executor
    this.memoryKeyStatus.set(keyId, {
      keyId,
      status: 'rate_limited',
      blockedUntil,
      reason,
    });

    this.logger.warn(
      `Key ${keyId} marked as rate limited locally, will recover at ${new Date(blockedUntil).toISOString()}`,
    );
  }

  /**
   * 标记密钥为额度耗尽状态（通过修改 etcd 状态通知其他 Executor）
   * 注意：额度耗尽不在内存中处理，直接通过 etcd 状态同步
   */
  markKeyExhausted(keyId: string, reason?: string): void {
    this.logger.error(`Key ${keyId} marked as exhausted: ${reason}`);
    // 额度耗尽的处理将在 KeyManagerService 中通过修改 etcd 状态来实现
  }

  /**
   * 检查密钥是否可用（仅检查限流状态）
   */
  isKeyAvailable(keyId: string): boolean {
    const status = this.memoryKeyStatus.get(keyId);

    if (!status) {
      return true; // 没有状态记录，认为可用
    }

    const now = Date.now();

    if (status.status === 'rate_limited' && now >= status.blockedUntil) {
      // 限流时间已过，自动恢复
      this.memoryKeyStatus.delete(keyId);
      this.logger.log(`Key ${keyId} automatically recovered from rate limit`);
      return true;
    }

    if (status.status === 'rate_limited' && now < status.blockedUntil) {
      // 仍在限流期间
      return false;
    }

    return true;
  }

  /**
   * 获取所有被阻止的密钥状态
   */
  getBlockedKeys(): MemoryKeyStatus[] {
    return Array.from(this.memoryKeyStatus.values());
  }

  /**
   * 缓存解密后的密钥
   */
  cacheDecryptedKey(keyId: string, decryptedKey: string): void {
    this.cachedKeys.set(keyId, {
      keyId,
      decryptedKey,
      cachedAt: Date.now(),
    });
    this.logger.debug(`Cached decrypted key: ${keyId}`);
  }

  /**
   * 获取缓存的解密密钥
   */
  getCachedKey(keyId: string): string | null {
    const cached = this.cachedKeys.get(keyId);
    if (cached) {
      this.logger.debug(`Using cached key: ${keyId}`);
      return cached.decryptedKey;
    }
    return null;
  }

  /**
   * 清除缓存的密钥
   */
  clearCachedKey(keyId: string): void {
    this.cachedKeys.delete(keyId);
    this.logger.debug(`Cleared cached key: ${keyId}`);
  }

  /**
   * 获取所有缓存的密钥ID
   */
  getCachedKeyIds(): string[] {
    return Array.from(this.cachedKeys.keys());
  }

  /**
   * 启动 etcd 状态监听器
   */
  private startStatusWatcher(): void {
    const etcdClient = this.etcdService.getEtcdClient();
    if (!etcdClient) {
      this.logger.warn('etcd client not available, status watcher not started');
      return;
    }

    try {
      const watchPrefix = `/key-status-notifications/${this.provider}/`;

      // 简化监听逻辑，暂时禁用以避免类型错误
      this.logger.log(`Would start watching key status notifications with prefix: ${watchPrefix}`);
      this.logger.log('Status watcher initialized (simplified mode)');
    } catch (error) {
      this.logger.error(
        `Failed to start status watcher: ${error instanceof Error ? error.message : String(error)}`,
      );
    }
  }

  /**
   * 启动定期清理任务
   */
  private startCleanupTask(): void {
    this.cleanupInterval = setInterval(() => {
      this.cleanupExpiredStatuses();
    }, 60 * 1000); // 每分钟清理一次
  }

  /**
   * 清理过期的限流状态
   */
  private cleanupExpiredStatuses(): void {
    const now = Date.now();
    let cleanedCount = 0;

    for (const [keyId, status] of this.memoryKeyStatus.entries()) {
      if (status.status === 'rate_limited' && now >= status.blockedUntil) {
        this.memoryKeyStatus.delete(keyId);
        cleanedCount++;
        this.logger.debug(`Cleaned up expired rate limit for key ${keyId}`);
      }
    }

    if (cleanedCount > 0) {
      this.logger.log(`Cleaned up ${cleanedCount} expired rate limit statuses`);
    }
  }
}
