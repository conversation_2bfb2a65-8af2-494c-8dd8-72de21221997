import { Injectable, Logger, OnApplicationBootstrap } from '@nestjs/common';
import { EtcdService } from '@app/etcd';
import { KeyStatusManagerService } from './key-status-manager.service';
import { EncryptionService } from './encryption.service';
import { ManagedApiKey, KeyPoolStatus, ThirdPartyKeyInfo } from '@app/types';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class KeyManagerService implements OnApplicationBootstrap {
  private readonly logger = new Logger(KeyManagerService.name);
  private provider: string;
  private region: string;

  constructor(
    private etcdService: EtcdService,
    private keyStatusManager: KeyStatusManagerService,
    private encryptionService: EncryptionService,
    private configService: ConfigService
  ) {
    this.provider = this.etcdService.getModelType();
    this.region = this.configService.get<string>('executor.region', '');
    this.logger.log(`KeyManagerService initialized with provider: ${this.provider}`);
  }

  async onApplicationBootstrap() {
    this.logger.log(`Starting key manager bootstrap for provider: ${this.provider}`);

    await this.waitForEtcdClient();

    this.logger.log(
      `Key manager fully initialized for provider: ${this.provider} (etcd-only mode)`,
    );
  }

  /**
   * Wait for etcd client to be fully initialized
   */
  private async waitForEtcdClient(): Promise<void> {
    const maxWaitTime = 10000; // 10 seconds
    const checkInterval = 100; // 100ms
    let waited = 0;

    while (waited < maxWaitTime) {
      try {
        if (this.etcdService.isClientReady()) {
          this.logger.debug('etcd client is ready');
          return;
        }
      } catch {
        // Client not ready yet
      }

      await new Promise((resolve) => setTimeout(resolve, checkInterval));
      waited += checkInterval;
    }

    throw new Error('etcd client failed to initialize within timeout');
  }

  /**
   * Get an available API key and lock it for exclusive use
   * Priority order: Gateway allocated keys -> Encrypted keys -> Legacy third-party keys
   */
  async getAndLockKey(): Promise<ManagedApiKey | null> {
    this.logger.log('Getting and locking API key...');
    // Second priority: Try to get encrypted API keys (according to design document)
    const encryptedKey = await this.getAndLockEncryptedKey();
    if (encryptedKey) {
      return encryptedKey;
    }
    return null
  }

  /**
   * Get and lock encrypted API key according to design document
   * Path: /api-keys/{provider}/{region}/{uuid}
   */
  private async getAndLockEncryptedKey(): Promise<ManagedApiKey | null> {
    try {
      const decryptedKeys = await this.encryptionService.getDecryptedApiKeys();

      if (decryptedKeys.length === 0) {
        this.logger.debug('No encrypted API keys found');
        return null;
      }

      // Use the first available key (could implement more sophisticated selection)
      const selectedKey = decryptedKeys[0];

      const managedKey: ManagedApiKey = {
        id: selectedKey.uuid,
        key: selectedKey.apiKey,
        status: 'in_use',
        lastUsedAt: Date.now(),
        lastError: undefined,
        note: 'Encrypted API key from design document',
        usageCount: 0,
        errorCount: 0,
        keyId: selectedKey.uuid,
        lockedAt: Date.now(),
        lockTimeout: Date.now() + 5 * 60 * 1000, // 5 minutes timeout
      };

      this.logger.log(`Successfully locked encrypted API key: ${selectedKey.uuid}`);
      return managedKey;
    } catch (error) {
      this.logger.error(
        `Failed to get encrypted API key: ${error instanceof Error ? error.message : String(error)}`,
      );
      return null;
    }
  }


  /**
   * Handle API errors and update key status accordingly
   * Supports both Gateway allocated keys and legacy keys
   */
  async handleApiError(keyId: string, error: unknown): Promise<void> {
    const errorObj = error as { message?: string; toString?: () => string };
    const errorMessage =
      errorObj?.message ||
      (typeof errorObj?.toString === 'function' ? errorObj.toString() : String(error));

    // Legacy error handling for third-party keys
    // 检查是否是限流错误
    if (this.isRateLimitError(error)) {
      this.keyStatusManager.markKeyRateLimited(keyId, `Rate limited: ${errorMessage}`);
      this.logger.warn(`Key ${keyId} rate limited locally, will retry in 5 minutes`);
      return;
    }

    // 检查是否是额度耗尽错误
    if (this.isQuotaExhaustedError(error)) {
      this.keyStatusManager.markKeyExhausted(keyId, `Quota exhausted: ${errorMessage}`);
      // 通过修改 etcd 状态来通知其他 Executor
      await this.updateKeyStatusInEtcd(keyId, 'revoked', errorMessage);
      this.logger.error(`Key ${keyId} quota exhausted, updated etcd status to revoked`);
      return;
    }

    // 其他错误不影响密钥状态
    this.logger.debug(`Key ${keyId} encountered non-blocking error: ${errorMessage}`);
  }

  /**
   * Check if error indicates rate limiting based on OpenAI error codes
   */
  private isRateLimitError(error: unknown): boolean {
    const errorObj = error as {
      code?: string;
      status?: number;
      statusCode?: number;
    };

    // Check OpenAI specific error codes
    if (errorObj?.code) {
      // OpenAI rate limit error codes
      if (
        errorObj.code === 'rate_limit_exceeded' ||
        errorObj.code === 'requests_per_minute_limit_exceeded' ||
        errorObj.code === 'tokens_per_minute_limit_exceeded'
      ) {
        return true;
      }
    }

    // Check HTTP status code
    if (errorObj?.status === 429 || errorObj?.statusCode === 429) {
      return true;
    }

    // Fallback to message content check
    const errorMessage = errorObj as { message?: string; toString?: () => string };
    const errorStr = (
      errorMessage?.message ||
      (typeof errorMessage?.toString === 'function' ? errorMessage.toString() : String(error))
    ).toLowerCase();
    if (
      errorStr.includes('rate limit') ||
      errorStr.includes('too many requests') ||
      errorStr.includes('429')
    ) {
      return true;
    }

    return false;
  }

  /**
   * Check if error indicates quota exhaustion based on OpenAI error codes
   */
  private isQuotaExhaustedError(error: unknown): boolean {
    const errorObj = error as { code?: string; status?: number; statusCode?: number };

    // Check OpenAI specific error codes
    if (errorObj?.code) {
      // OpenAI quota/billing error codes
      if (
        errorObj.code === 'insufficient_quota' ||
        errorObj.code === 'billing_not_active' ||
        errorObj.code === 'invalid_api_key' ||
        errorObj.code === 'account_deactivated' ||
        errorObj.code === 'billing_hard_limit_reached'
      ) {
        return true;
      }
    }

    // Check HTTP status codes for authentication/authorization issues
    if (
      errorObj?.status === 401 ||
      errorObj?.statusCode === 401 ||
      errorObj?.status === 403 ||
      errorObj?.statusCode === 403
    ) {
      return true;
    }

    // Fallback to message content check
    const errorMessage = errorObj as { message?: string; toString?: () => string };
    const errorStr = (
      errorMessage?.message ||
      (typeof errorMessage?.toString === 'function' ? errorMessage.toString() : String(error))
    ).toLowerCase();
    if (
      errorStr.includes('insufficient_quota') ||
      errorStr.includes('billing_not_active') ||
      errorStr.includes('invalid_api_key') ||
      errorStr.includes('account deactivated') ||
      errorStr.includes('quota exceeded') ||
      errorStr.includes('billing')
    ) {
      return true;
    }

    return false;
  }

  /**
   * 更新 etcd 中的密钥状态（用于通知其他 Executor）
   */
  private async updateKeyStatusInEtcd(
    keyId: string,
    status: string,
    reason: string,
  ): Promise<void> {
    try {
      // 这里需要找到对应的 etcd 路径并更新状态
      // 由于我们需要找到具体的密钥路径，这需要遍历查找
      const thirdPartyKeys = await this.getThirdPartyKeys();
      const targetKey = thirdPartyKeys.find((key) => key.keyId === keyId);

      if (targetKey) {
        // 构建 etcd 路径并更新状态
        const keyPath = `/api-keys/${this.provider}/${this.region}/${keyId}`;
        const updatedKeyInfo = {
          ...targetKey,
          status: status,
          lastError: reason,
          lastUsedAt: Date.now(),
        };

        const etcdClient = this.etcdService.getEtcdClient();
        if (etcdClient) {
          await etcdClient.put(keyPath).value(JSON.stringify(updatedKeyInfo));
        }
        this.logger.log(`Updated key ${keyId} status to ${status} in etcd`);
      } else {
        this.logger.warn(`Key ${keyId} not found in etcd for status update`);
      }
    } catch (error) {
      this.logger.error(
        `Failed to update key ${keyId} status in etcd: ${error instanceof Error ? error.message : String(error)}`,
      );
    }
  }


  /**
   * Get all third-party keys from etcd
   */
  private async getThirdPartyKeys(): Promise<ThirdPartyKeyInfo[]> {
    const etcdClient = this.etcdService.getEtcdClient();
    if (!etcdClient) {
      throw new Error('etcd client is not initialized');
    }

    try {
      const prefix = `/api-keys/${this.provider}/`;
      const kvs = await etcdClient.getAll().prefix(prefix);
      const keys: ThirdPartyKeyInfo[] = [];

      for (const [fullPath, jsonValue] of Object.entries(kvs)) {
        try {
          const keyInfo = JSON.parse(jsonValue) as ThirdPartyKeyInfo;
          keys.push(keyInfo);
          this.logger.debug(`Loaded third-party key: ${keyInfo.keyId}`);
        } catch (parseError) {
          this.logger.warn(
            `Failed to parse key info for ${fullPath}: ${parseError instanceof Error ? parseError.message : String(parseError)}`,
          );
        }
      }

      return keys;
    } catch (error) {
      this.logger.error(
        `Failed to fetch third-party keys: ${error instanceof Error ? error.message : String(error)}`,
      );
      throw error;
    }
  }

  /**
   * Release a locked key after request completion
   * Handles both Gateway allocated keys and legacy third-party keys
   */
  async releaseKey(keyId: string): Promise<boolean> {
    return this.releaseThirdPartyKey(keyId);
  }
  /**
 * Release a third-party key in etcd
 */
  private async releaseThirdPartyKey(keyId: string): Promise<boolean> {
    try {
      // Find the third-party key to release
      const etcdClient = this.etcdService.getEtcdClient();
      if (!etcdClient) {
        throw new Error('etcd client is not initialized');
      }

      const prefix = `/api-keys/${this.provider}/`;
      const kvs = await etcdClient.getAll().prefix(prefix);

      let keyInfo: ThirdPartyKeyInfo | null = null;
      let keyPath: string = '';

      // Find matching key
      for (const [fullPath, jsonValue] of Object.entries(kvs)) {
        try {
          const parsedKeyInfo = JSON.parse(jsonValue) as ThirdPartyKeyInfo;
          if (parsedKeyInfo.keyId === keyId) {
            keyInfo = parsedKeyInfo;
            keyPath = fullPath;
            break;
          }
        } catch (parseError) {
          this.logger.warn(
            `Failed to parse key info for ${fullPath}: ${parseError instanceof Error ? parseError.message : String(parseError)}`,
          );
        }
      }

      if (!keyInfo) {
        this.logger.warn(`Third-party key ${keyId} not found for release`);
        return false;
      }

      if (keyInfo.status !== 'in_use') {
        this.logger.debug(`Key ${keyId} is not in use (status: ${keyInfo.status})`);
        return false;
      }

      // Update key status to active
      const updatedKeyInfo = {
        ...keyInfo,
        status: 'active' as const,
        lastUsedAt: Date.now(),
        lockedAt: undefined,
        lockTimeout: undefined,
      };

      await etcdClient.put(keyPath).value(JSON.stringify(updatedKeyInfo));
      this.logger.log(`Released third-party key ${keyId} from exclusive use`);

      return true;
    } catch (error) {
      this.logger.error(
        `Failed to release key ${keyId} in etcd: ${error instanceof Error ? error.message : String(error)}`,
      );
      return false;
    }
  }
}
