import { Injectable, Logger, OnApplicationBootstrap } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { EtcdService } from '@app/etcd';
import { KeyStatusManagerService } from './key-status-manager.service';
import { EncryptionService } from './encryption.service';
import { GatewayKeyAllocationService } from './gateway-key-allocation.service';
import { ManagedApiKey, KeyPoolStatus, ThirdPartyKeyInfo } from '@app/types';

@Injectable()
export class KeyManagerService implements OnApplicationBootstrap {
  private readonly logger = new Logger(KeyManagerService.name);
  private provider: string;

  constructor(
    private etcdService: EtcdService,
    private configService: ConfigService,
    private keyStatusManager: KeyStatusManagerService,
    private encryptionService: EncryptionService,
    private gatewayKeyAllocationService: GatewayKeyAllocationService,
  ) {
    this.provider = this.etcdService.getModelType();
    this.logger.log(`KeyManagerService initialized with provider: ${this.provider}`);
  }

  async onApplicationBootstrap() {
    this.logger.log(`Starting key manager bootstrap for provider: ${this.provider}`);

    await this.waitForEtcdClient();

    this.logger.log(
      `Key manager fully initialized for provider: ${this.provider} (etcd-only mode)`,
    );
  }

  /**
   * Wait for etcd client to be fully initialized
   */
  private async waitForEtcdClient(): Promise<void> {
    const maxWaitTime = 10000; // 10 seconds
    const checkInterval = 100; // 100ms
    let waited = 0;

    while (waited < maxWaitTime) {
      try {
        if (this.etcdService.isClientReady()) {
          this.logger.debug('etcd client is ready');
          return;
        }
      } catch {
        // Client not ready yet
      }

      await new Promise((resolve) => setTimeout(resolve, checkInterval));
      waited += checkInterval;
    }

    throw new Error('etcd client failed to initialize within timeout');
  }

  /**
   * Get an available API key and lock it for exclusive use
   * Priority order: Gateway allocated keys -> Encrypted keys -> Legacy third-party keys
   */
  async getAndLockKey(): Promise<ManagedApiKey | null> {
    this.logger.log('Getting and locking API key...');

    // First priority: Try to get keys allocated from Gateway
    const gatewayKey = await this.getGatewayAllocatedKey();
    if (gatewayKey) {
      return gatewayKey;
    }

    // Second priority: Try to get encrypted API keys (according to design document)
    const encryptedKey = await this.getAndLockEncryptedKey();
    if (encryptedKey) {
      return encryptedKey;
    }

    // Fallback: Legacy third-party keys
    return this.getAndLockThirdPartyKey();
  }

  /**
   * Get an available key from Gateway allocation service
   */
  private async getGatewayAllocatedKey(): Promise<ManagedApiKey | null> {
    try {
      if (!this.gatewayKeyAllocationService.hasAvailableKeys()) {
        this.logger.debug('No Gateway allocated keys available');
        return null;
      }

      const allocatedKey = await this.gatewayKeyAllocationService.getAvailableKey();
      if (allocatedKey) {
        this.logger.log(`Successfully got Gateway allocated key: ${allocatedKey.id}`);
        return allocatedKey;
      }

      return null;
    } catch (error) {
      this.logger.error(
        `Failed to get Gateway allocated key: ${error instanceof Error ? error.message : String(error)}`,
      );
      return null;
    }
  }

  /**
   * Get and lock encrypted API key according to design document
   * Path: /api-keys/{provider}/{region}/{uuid}
   */
  private async getAndLockEncryptedKey(): Promise<ManagedApiKey | null> {
    try {
      const decryptedKeys = await this.encryptionService.getDecryptedApiKeys();

      if (decryptedKeys.length === 0) {
        this.logger.debug('No encrypted API keys found');
        return null;
      }

      // Use the first available key (could implement more sophisticated selection)
      const selectedKey = decryptedKeys[0];

      const managedKey: ManagedApiKey = {
        id: selectedKey.uuid,
        key: selectedKey.apiKey,
        status: 'in_use',
        lastUsedAt: Date.now(),
        lastError: undefined,
        note: 'Encrypted API key from design document',
        usageCount: 0,
        errorCount: 0,
        keyId: selectedKey.uuid,
        lockedAt: Date.now(),
        lockTimeout: Date.now() + 5 * 60 * 1000, // 5 minutes timeout
      };

      this.logger.log(`Successfully locked encrypted API key: ${selectedKey.uuid}`);
      return managedKey;
    } catch (error) {
      this.logger.error(
        `Failed to get encrypted API key: ${error instanceof Error ? error.message : String(error)}`,
      );
      return null;
    }
  }

  /**
   * Get an available third-party key and lock it for exclusive use
   * Legacy implementation for backward compatibility
   */
  private async getAndLockThirdPartyKey(): Promise<ManagedApiKey | null> {
    this.logger.log('Getting and locking third-party key (legacy)...');

    // Clean up expired locks first
    await this.cleanupExpiredLocksFromEtcd();

    // Get all third-party keys directly from etcd
    const thirdPartyKeys = await this.getThirdPartyKeys();
    this.logger.log(`Found ${thirdPartyKeys.length} third-party keys from etcd`);

    if (thirdPartyKeys.length === 0) {
      this.logger.warn('No third-party keys found in etcd');
      return null;
    }

    // Filter available keys (not in use and not blocked)
    const availableKeys = thirdPartyKeys.filter((key) => {
      // 检查基本状态
      if (key.status !== 'active') {
        return false;
      }

      // 检查内存中的阻止状态
      if (!this.keyStatusManager.isKeyAvailable(key.keyId)) {
        this.logger.debug(`Key ${key.keyId} is blocked in memory (rate limited or exhausted)`);
        return false;
      }

      return true;
    });

    this.logger.log(`Found ${availableKeys.length} available keys (not in use and not blocked)`);

    if (availableKeys.length === 0) {
      this.logger.warn('No available keys for locking (all in use or blocked)');
      return null;
    }

    // Sort by last used time (least recently used first)
    const sortedKeys = availableKeys.sort((a, b) => (a.lastUsedAt || 0) - (b.lastUsedAt || 0));

    // Try to lock keys one by one
    for (const keyInfo of sortedKeys) {
      this.logger.log(`Attempting to lock third-party key ${keyInfo.keyId}`);
      const locked = await this.lockThirdPartyKey(keyInfo);

      if (locked) {
        try {
          // 简化版本：假设密钥已经是明文存储
          // 检查是否有明文 API 密钥
          if (!keyInfo.encryptedApiKey) {
            throw new Error(`Missing API key data for key ${keyInfo.keyId}`);
          }

          // 假设 encryptedApiKey 实际上是明文密钥（为了简化）
          const apiKey = keyInfo.encryptedApiKey;

          // 验证密钥格式
          if (!apiKey.startsWith('sk-')) {
            this.logger.warn(`Key ${keyInfo.keyId} does not appear to be a valid OpenAI API key`);
          }

          // Create managed key object for return
          const managedKey: ManagedApiKey = {
            id: keyInfo.keyId,
            key: apiKey,
            status: 'in_use',
            lastUsedAt: Date.now(),
            lastError: undefined,
            note: keyInfo.note,
            usageCount: 0,
            errorCount: 0,
            keyId: keyInfo.keyId,
            lockedAt: Date.now(),
            lockTimeout: Date.now() + 5 * 60 * 1000, // 5 minutes timeout
          };

          this.logger.log(`Successfully locked and assigned third-party key ${keyInfo.keyId}`);
          return managedKey;
        } catch (error) {
          this.logger.error(
            `Failed to process key ${keyInfo.keyId}: ${error instanceof Error ? error.message : String(error)}`,
          );
          // Release the locked key
          await this.releaseThirdPartyKey(keyInfo.keyId);
          // Continue to next key
        }
      } else {
        this.logger.warn(`Failed to lock key ${keyInfo.keyId}, trying next one`);
      }
    }

    this.logger.warn('Failed to lock any available third-party key from etcd');
    return null;
  }

  /**
   * Get all third-party keys from etcd
   */
  private async getThirdPartyKeys(): Promise<ThirdPartyKeyInfo[]> {
    const etcdClient = this.etcdService.getEtcdClient();
    if (!etcdClient) {
      throw new Error('etcd client is not initialized');
    }

    try {
      const prefix = `/third-party-keys/${this.provider}/`;
      const kvs = await etcdClient.getAll().prefix(prefix);
      const keys: ThirdPartyKeyInfo[] = [];

      for (const [fullPath, jsonValue] of Object.entries(kvs)) {
        try {
          const keyInfo = JSON.parse(jsonValue) as ThirdPartyKeyInfo;
          keys.push(keyInfo);
          this.logger.debug(`Loaded third-party key: ${keyInfo.keyId}`);
        } catch (parseError) {
          this.logger.warn(
            `Failed to parse key info for ${fullPath}: ${parseError instanceof Error ? parseError.message : String(parseError)}`,
          );
        }
      }

      return keys;
    } catch (error) {
      this.logger.error(
        `Failed to fetch third-party keys: ${error instanceof Error ? error.message : String(error)}`,
      );
      throw error;
    }
  }

  /**
   * Lock a third-party key in etcd
   */
  private async lockThirdPartyKey(keyInfo: ThirdPartyKeyInfo): Promise<boolean> {
    try {
      if (keyInfo.status === 'in_use') {
        this.logger.debug(`Key ${keyInfo.keyId} is already in use`);
        return false;
      }

      if (keyInfo.status !== 'active') {
        this.logger.debug(`Key ${keyInfo.keyId} is not active (status: ${keyInfo.status})`);
        return false;
      }

      const etcdClient = this.etcdService.getEtcdClient();
      if (!etcdClient) {
        throw new Error('etcd client is not initialized');
      }

      const now = Date.now();
      const lockTimeout = now + 5 * 60 * 1000; // 5 minutes timeout

      // Build the full path for third-party key
      const keyPath = `/third-party-keys/${this.provider}/${keyInfo.userId}/${keyInfo.keyId}`;

      const updatedKeyInfo = {
        ...keyInfo,
        status: 'in_use' as const,
        lastUsedAt: now,
        lockedAt: now,
        lockTimeout: lockTimeout,
      };

      await etcdClient.put(keyPath).value(JSON.stringify(updatedKeyInfo));
      this.logger.log(`Locked third-party key ${keyInfo.keyId} for exclusive use`);

      return true;
    } catch (error) {
      this.logger.error(
        `Failed to lock key ${keyInfo.keyId}: ${error instanceof Error ? error.message : String(error)}`,
      );
      return false;
    }
  }

  /**
   * Release a locked key after request completion
   * Handles both Gateway allocated keys and legacy third-party keys
   */
  async releaseKey(keyId: string): Promise<boolean> {
    // First try to release Gateway allocated key
    const gatewayReleased = this.gatewayKeyAllocationService.releaseKey(keyId);
    if (gatewayReleased) {
      this.logger.debug(`Released Gateway allocated key: ${keyId}`);
      return true;
    }

    // Fallback to legacy third-party key release
    return this.releaseThirdPartyKey(keyId);
  }

  /**
   * Handle API errors and update key status accordingly
   * Supports both Gateway allocated keys and legacy keys
   */
  async handleApiError(keyId: string, error: unknown): Promise<void> {
    const errorObj = error as { message?: string; toString?: () => string };
    const errorMessage =
      errorObj?.message ||
      (typeof errorObj?.toString === 'function' ? errorObj.toString() : String(error));

    // First try to handle Gateway allocated key error
    try {
      await this.gatewayKeyAllocationService.handleApiError(keyId, error);
      this.logger.debug(`Handled Gateway allocated key error for ${keyId}`);
      return;
    } catch {
      // If not a Gateway allocated key, continue with legacy handling
      this.logger.debug(
        `Key ${keyId} not found in Gateway allocation, using legacy error handling`,
      );
    }

    // Legacy error handling for third-party keys
    // 检查是否是限流错误
    if (this.isRateLimitError(error)) {
      this.keyStatusManager.markKeyRateLimited(keyId, `Rate limited: ${errorMessage}`);
      this.logger.warn(`Key ${keyId} rate limited locally, will retry in 5 minutes`);
      return;
    }

    // 检查是否是额度耗尽错误
    if (this.isQuotaExhaustedError(error)) {
      this.keyStatusManager.markKeyExhausted(keyId, `Quota exhausted: ${errorMessage}`);
      // 通过修改 etcd 状态来通知其他 Executor
      await this.updateKeyStatusInEtcd(keyId, 'revoked', errorMessage);
      this.logger.error(`Key ${keyId} quota exhausted, updated etcd status to revoked`);
      return;
    }

    // 其他错误不影响密钥状态
    this.logger.debug(`Key ${keyId} encountered non-blocking error: ${errorMessage}`);
  }

  /**
   * Check if error indicates rate limiting based on OpenAI error codes
   */
  private isRateLimitError(error: unknown): boolean {
    const errorObj = error as {
      code?: string;
      status?: number;
      statusCode?: number;
    };

    // Check OpenAI specific error codes
    if (errorObj?.code) {
      // OpenAI rate limit error codes
      if (
        errorObj.code === 'rate_limit_exceeded' ||
        errorObj.code === 'requests_per_minute_limit_exceeded' ||
        errorObj.code === 'tokens_per_minute_limit_exceeded'
      ) {
        return true;
      }
    }

    // Check HTTP status code
    if (errorObj?.status === 429 || errorObj?.statusCode === 429) {
      return true;
    }

    // Fallback to message content check
    const errorMessage = errorObj as { message?: string; toString?: () => string };
    const errorStr = (
      errorMessage?.message ||
      (typeof errorMessage?.toString === 'function' ? errorMessage.toString() : String(error))
    ).toLowerCase();
    if (
      errorStr.includes('rate limit') ||
      errorStr.includes('too many requests') ||
      errorStr.includes('429')
    ) {
      return true;
    }

    return false;
  }

  /**
   * Check if error indicates quota exhaustion based on OpenAI error codes
   */
  private isQuotaExhaustedError(error: unknown): boolean {
    const errorObj = error as { code?: string; status?: number; statusCode?: number };

    // Check OpenAI specific error codes
    if (errorObj?.code) {
      // OpenAI quota/billing error codes
      if (
        errorObj.code === 'insufficient_quota' ||
        errorObj.code === 'billing_not_active' ||
        errorObj.code === 'invalid_api_key' ||
        errorObj.code === 'account_deactivated' ||
        errorObj.code === 'billing_hard_limit_reached'
      ) {
        return true;
      }
    }

    // Check HTTP status codes for authentication/authorization issues
    if (
      errorObj?.status === 401 ||
      errorObj?.statusCode === 401 ||
      errorObj?.status === 403 ||
      errorObj?.statusCode === 403
    ) {
      return true;
    }

    // Fallback to message content check
    const errorMessage = errorObj as { message?: string; toString?: () => string };
    const errorStr = (
      errorMessage?.message ||
      (typeof errorMessage?.toString === 'function' ? errorMessage.toString() : String(error))
    ).toLowerCase();
    if (
      errorStr.includes('insufficient_quota') ||
      errorStr.includes('billing_not_active') ||
      errorStr.includes('invalid_api_key') ||
      errorStr.includes('account deactivated') ||
      errorStr.includes('quota exceeded') ||
      errorStr.includes('billing')
    ) {
      return true;
    }

    return false;
  }

  /**
   * 更新 etcd 中的密钥状态（用于通知其他 Executor）
   */
  private async updateKeyStatusInEtcd(
    keyId: string,
    status: string,
    reason: string,
  ): Promise<void> {
    try {
      // 这里需要找到对应的 etcd 路径并更新状态
      // 由于我们需要找到具体的密钥路径，这需要遍历查找
      const thirdPartyKeys = await this.getThirdPartyKeys();
      const targetKey = thirdPartyKeys.find((key) => key.keyId === keyId);

      if (targetKey) {
        // 构建 etcd 路径并更新状态
        const keyPath = `/third-party-keys/${this.provider}/${targetKey.userId}/${keyId}`;
        const updatedKeyInfo = {
          ...targetKey,
          status: status,
          lastError: reason,
          lastUsedAt: Date.now(),
        };

        const etcdClient = this.etcdService.getEtcdClient();
        if (etcdClient) {
          await etcdClient.put(keyPath).value(JSON.stringify(updatedKeyInfo));
        }
        this.logger.log(`Updated key ${keyId} status to ${status} in etcd`);
      } else {
        this.logger.warn(`Key ${keyId} not found in etcd for status update`);
      }
    } catch (error) {
      this.logger.error(
        `Failed to update key ${keyId} status in etcd: ${error instanceof Error ? error.message : String(error)}`,
      );
    }
  }

  /**
   * Release a third-party key in etcd
   */
  private async releaseThirdPartyKey(keyId: string): Promise<boolean> {
    try {
      // Find the third-party key to release
      const etcdClient = this.etcdService.getEtcdClient();
      if (!etcdClient) {
        throw new Error('etcd client is not initialized');
      }

      const prefix = `/third-party-keys/${this.provider}/`;
      const kvs = await etcdClient.getAll().prefix(prefix);

      let keyInfo: ThirdPartyKeyInfo | null = null;
      let keyPath: string = '';

      // Find matching key
      for (const [fullPath, jsonValue] of Object.entries(kvs)) {
        try {
          const parsedKeyInfo = JSON.parse(jsonValue) as ThirdPartyKeyInfo;
          if (parsedKeyInfo.keyId === keyId) {
            keyInfo = parsedKeyInfo;
            keyPath = fullPath;
            break;
          }
        } catch (parseError) {
          this.logger.warn(
            `Failed to parse key info for ${fullPath}: ${parseError instanceof Error ? parseError.message : String(parseError)}`,
          );
        }
      }

      if (!keyInfo) {
        this.logger.warn(`Third-party key ${keyId} not found for release`);
        return false;
      }

      if (keyInfo.status !== 'in_use') {
        this.logger.debug(`Key ${keyId} is not in use (status: ${keyInfo.status})`);
        return false;
      }

      // Update key status to active
      const updatedKeyInfo = {
        ...keyInfo,
        status: 'active' as const,
        lastUsedAt: Date.now(),
        lockedAt: undefined,
        lockTimeout: undefined,
      };

      await etcdClient.put(keyPath).value(JSON.stringify(updatedKeyInfo));
      this.logger.log(`Released third-party key ${keyId} from exclusive use`);

      return true;
    } catch (error) {
      this.logger.error(
        `Failed to release key ${keyId} in etcd: ${error instanceof Error ? error.message : String(error)}`,
      );
      return false;
    }
  }

  /**
   * Clean up expired locks from third-party keys in etcd
   */
  private async cleanupExpiredLocksFromEtcd(): Promise<void> {
    try {
      const now = Date.now();
      const thirdPartyKeys = await this.getThirdPartyKeys();
      const expiredKeys: string[] = [];

      for (const keyInfo of thirdPartyKeys) {
        if (keyInfo.status === 'in_use' && keyInfo.lockTimeout && now > keyInfo.lockTimeout) {
          expiredKeys.push(keyInfo.keyId);
        }
      }

      for (const keyId of expiredKeys) {
        this.logger.warn(`Releasing expired lock for third-party key ${keyId}`);
        await this.releaseThirdPartyKey(keyId);
      }

      if (expiredKeys.length > 0) {
        this.logger.log(`Cleaned up ${expiredKeys.length} expired third-party key locks`);
      }
    } catch (error) {
      this.logger.error(
        `Failed to cleanup expired locks: ${error instanceof Error ? error.message : String(error)}`,
      );
    }
  }

  /**
   * Check if there are available third-party keys
   */
  async hasAvailableKeys(): Promise<boolean> {
    try {
      const thirdPartyKeys = await this.getThirdPartyKeys();
      return thirdPartyKeys.some((key) => key.status === 'active');
    } catch (error) {
      this.logger.error(
        `Failed to check available keys: ${error instanceof Error ? error.message : String(error)}`,
      );
      return false;
    }
  }

  /**
   * Get third-party key pool status for monitoring
   */
  async getKeyPoolStatus(): Promise<KeyPoolStatus> {
    try {
      const thirdPartyKeys = await this.getThirdPartyKeys();
      const blockedKeys = this.keyStatusManager.getBlockedKeys();

      // 计算各种状态的密钥数量
      const rateLimitedKeys = blockedKeys.filter((key) => key.status === 'rate_limited').length;
      // 额度耗尽的密钥通过 etcd 状态判断，不在内存中处理
      const exhaustedKeys = thirdPartyKeys.filter((key) => key.status === 'revoked').length;

      // 计算真正可用的密钥数量（排除被阻止的）
      const availableKeys = thirdPartyKeys.filter((key) => {
        if (key.status !== 'active') return false;
        return this.keyStatusManager.isKeyAvailable(key.keyId);
      }).length;

      const status = {
        totalKeys: thirdPartyKeys.length,
        activeKeys: availableKeys, // 真正可用的密钥
        inUseKeys: thirdPartyKeys.filter((key) => key.status === 'in_use').length,
        rateLimitedKeys: rateLimitedKeys, // 内存中被限流的密钥
        exhaustedKeys: exhaustedKeys, // 内存中额度耗尽的密钥
        revokedKeys: thirdPartyKeys.filter((key) => key.status === 'revoked').length,
      };

      this.logger.debug(`Third-party key pool status: ${JSON.stringify(status)}`);
      return status;
    } catch (error) {
      this.logger.error(
        `Failed to get key pool status: ${error instanceof Error ? error.message : String(error)}`,
      );
      return {
        totalKeys: 0,
        activeKeys: 0,
        inUseKeys: 0,
        rateLimitedKeys: 0,
        exhaustedKeys: 0,
        revokedKeys: 0,
      };
    }
  }
}
