import { Modu<PERSON> } from '@nestjs/common';
import { KeyManagerService } from './key-manager.service';
import { KeyStatusManagerService } from './key-status-manager.service';
import { EncryptionService } from './encryption.service';
import { ApiKeyStatusService } from './api-key-status.service';
import { GatewayKeyAllocationService } from './gateway-key-allocation.service';
import { EtcdModule } from '@app/etcd';
import { SecretManagerModule } from '@app/secret-manager';

@Module({
  imports: [EtcdModule, SecretManagerModule],
  controllers: [],
  providers: [
    KeyManagerService,
    KeyStatusManagerService,
    EncryptionService,
    ApiKeyStatusService,
    GatewayKeyAllocationService,
  ],
  exports: [
    KeyManagerService,
    KeyStatusManagerService,
    EncryptionService,
    ApiKeyStatusService,
    GatewayKeyAllocationService,
  ],
})
export class KeyManagerModule {}
