import { <PERSON>du<PERSON> } from '@nestjs/common';
import { KeyManagerService } from './key-manager.service';
import { KeyStatusManagerService } from './key-status-manager.service';
import { EncryptionService } from './encryption.service';
import { ApiKeyStatusService } from './api-key-status.service';
import { EtcdModule } from '@app/etcd';
import { SecretManagerModule } from '@app/secret-manager';

@Module({
  imports: [EtcdModule, SecretManagerModule],
  controllers: [],
  providers: [
    KeyManagerService,
    KeyStatusManagerService,
    EncryptionService,
    ApiKeyStatusService,
  ],
  exports: [
    KeyManagerService,
    KeyStatusManagerService,
    EncryptionService,
    ApiKeyStatusService,
  ],
})
export class KeyManagerModule {}
