import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { EtcdModule } from '@app/etcd';
import { KeyManagerModule } from '@app/key-manager';
import { ProvidersModule } from '@app/providers';
import { ExecutorRegistryModule } from '@app/executor-registry';

import { ConfigValidationService, configuration } from '@app/config';
import { InferenceController } from './inference.controller';
import { KeyManagerController } from './key-manager.controller';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      load: [configuration],
    }),
    EtcdModule,
    KeyManagerModule,
    ProvidersModule,
    ExecutorRegistryModule,
  ],
  controllers: [InferenceController, KeyManagerController],
  providers: [ConfigValidationService],
})
export class AppModule {}
