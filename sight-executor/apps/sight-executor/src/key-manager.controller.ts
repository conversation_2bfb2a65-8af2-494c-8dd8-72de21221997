import {
  <PERSON>,
  Get,
  Put,
  Body,
  Param,
  HttpException,
  HttpStatus,
  Logger,
} from '@nestjs/common';
import { EtcdService } from '@app/etcd/etcd.service';
import { ApiKeyInfo } from '@app/types';
import { KeyManagerService, GatewayKeyAllocationService } from '@app/key-manager';

export interface UpdateKeyRequest {
  status?: 'active' | 'rate_limited' | 'exhausted' | 'revoked';
  note?: string;
}

@Controller('api/keys')
export class KeyManagerController {
  private readonly logger = new Logger(KeyManagerController.name);

  constructor(
    private etcdService: EtcdService,
    private keyManagerService: KeyManagerService,
    private gatewayKeyAllocationService: GatewayKeyAllocationService,
  ) {}

  @Get(':provider')
  async getKeys(@Param('provider') provider: string): Promise<{
    success: boolean;
    keys: Array<{
      keyId: string;
      status: string;
      lastUsedAt: number;
      note?: string;
      lastError?: string;
    }>;
  }> {
    try {
      const keys = await this.etcdService.getActiveApiKeys(provider);

      return {
        success: true,
        keys: keys.map((key) => ({
          keyId: key.etcdKeyId,
          status: key.status,
          lastUsedAt: key.lastUsedAt,
          note: key.note,
          lastError: key.lastError,
        })),
      };
    } catch (error) {
      this.logger.error(
        `Failed to get keys for provider ${provider}: ${error instanceof Error ? error.message : String(error)}`,
      );
      throw new HttpException('Failed to retrieve keys', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  @Put(':provider/:keyId')
  async updateKey(
    @Param('provider') provider: string,
    @Param('keyId') keyId: string,
    @Body() request: UpdateKeyRequest,
  ): Promise<{
    success: boolean;
    message: string;
  }> {
    try {
      // Update key status and/or note
      const updates: Partial<ApiKeyInfo> = {};

      if (request.status) {
        updates.status = request.status;
      }

      if (request.note !== undefined) {
        updates.note = request.note;
      }

      if (Object.keys(updates).length > 0) {
        await this.etcdService.updateApiKey(provider, keyId, updates);
      }

      this.logger.log(`Updated key ${keyId} for provider ${provider}`);

      return {
        success: true,
        message: 'Key updated successfully',
      };
    } catch (error) {
      this.logger.error(
        `Failed to update key ${keyId}: ${error instanceof Error ? error.message : String(error)}`,
      );
      throw new HttpException('Failed to update key', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  @Get('pool/status')
  async getKeyPoolStatus(): Promise<Record<string, any>> {
    return await this.keyManagerService.getKeyPoolStatus();
  }

  @Get('gateway/allocation-stats')
  getGatewayAllocationStats(): {
    success: boolean;
    data: {
      totalKeys: number;
      availableKeys: number;
      inUseKeys: number;
      allocationId: string | null;
      hasAvailableKeys: boolean;
    };
  } {
    try {
      const stats = this.gatewayKeyAllocationService.getAllocationStats();
      const hasAvailableKeys = this.gatewayKeyAllocationService.hasAvailableKeys();

      return {
        success: true,
        data: {
          ...stats,
          hasAvailableKeys,
        },
      };
    } catch (error) {
      this.logger.error(
        `Failed to get Gateway allocation stats: ${error instanceof Error ? error.message : String(error)}`,
      );
      throw new HttpException(
        'Failed to get Gateway allocation stats',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Put('gateway/refresh-allocation')
  async refreshGatewayAllocation(): Promise<{
    success: boolean;
    message: string;
  }> {
    try {
      await this.gatewayKeyAllocationService.refreshKeyAllocation();

      return {
        success: true,
        message: 'Gateway key allocation refreshed successfully',
      };
    } catch (error) {
      this.logger.error(
        `Failed to refresh Gateway allocation: ${error instanceof Error ? error.message : String(error)}`,
      );
      throw new HttpException(
        'Failed to refresh Gateway allocation',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}
