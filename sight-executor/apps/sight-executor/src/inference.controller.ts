import { <PERSON>, Post, Body, Logger, HttpException, HttpStatus } from '@nestjs/common';
import { ProviderFactoryService } from '@app/providers';
import {
  ChatCompletionRequest,
  ChatCompletionResponse,
  CompletionRequest,
  CompletionResponse,
} from '@app/providers';

@Controller()
export class InferenceController {
  private readonly logger = new Logger(InferenceController.name);

  constructor(private providerFactoryService: ProviderFactoryService) {}

  @Post('v1/chat/completions')
  async chatCompletions(@Body() request: ChatCompletionRequest): Promise<ChatCompletionResponse> {
    this.logger.log(`Received chat completion request for model: ${request.model}`);

    try {
      // Validate request
      if (!request.model) {
        throw new HttpException('Model is required', HttpStatus.BAD_REQUEST);
      }

      if (!request.messages || request.messages.length === 0) {
        throw new HttpException('Messages are required', HttpStatus.BAD_REQUEST);
      }

      // Process request through provider factory
      const response = await this.providerFactoryService.executeRequest(request);

      // Handle streaming response
      if (typeof response === 'object' && Symbol.asyncIterator in response) {
        throw new HttpException('Streaming not supported in this endpoint', HttpStatus.BAD_REQUEST);
      }

      this.logger.log(`Chat completion successful for model: ${request.model}`);

      return response;
    } catch (error) {
      this.logger.error(
        `Chat completion failed: ${error instanceof Error ? error.message : String(error)}`,
        error instanceof Error ? error.stack : undefined,
      );

      if (error instanceof HttpException) {
        throw error;
      }

      // Handle provider-specific errors
      const errorObj = error as { status?: number; message?: string };
      if (errorObj.status) {
        throw new HttpException(errorObj.message || 'Provider error', errorObj.status);
      }

      // Generic server error
      throw new HttpException(`${error instanceof Error ? error.message : String(error)}`, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  @Post('v1/completions')
  async completions(@Body() request: CompletionRequest): Promise<CompletionResponse> {
    this.logger.log(`Received completion request for model: ${request.model}`);

    try {
      // Validate request
      if (!request.model) {
        throw new HttpException('Model is required', HttpStatus.BAD_REQUEST);
      }

      if (!request.prompt) {
        throw new HttpException('Prompt is required', HttpStatus.BAD_REQUEST);
      }

      // Convert completion request to chat completion format
      const chatRequest: ChatCompletionRequest = {
        model: request.model,
        messages: [
          {
            role: 'user',
            content: request.prompt,
          },
        ],
        max_tokens: request.max_tokens,
        temperature: request.temperature,
        top_p: request.top_p,
        frequency_penalty: request.frequency_penalty,
        presence_penalty: request.presence_penalty,
        stop: request.stop,
        stream: request.stream,
      };

      const chatResponse = await this.providerFactoryService.executeRequest(chatRequest);

      // Handle streaming response
      if (typeof chatResponse === 'object' && Symbol.asyncIterator in chatResponse) {
        throw new HttpException(
          'Streaming not supported for completions endpoint',
          HttpStatus.BAD_REQUEST,
        );
      }

      // Convert chat response back to completion format
      const response = chatResponse;
      const completionResponse = {
        id: response.id,
        object: 'text_completion',
        created: response.created,
        model: response.model,
        choices: response.choices.map((choice, index) => ({
          text: choice.message.content,
          index: index,
          finish_reason: choice.finish_reason,
        })),
        usage: response.usage,
      };

      this.logger.log(`Completion successful for model: ${request.model}`);

      return completionResponse;
    } catch (error) {
      this.logger.error(
        `Completion failed: ${error instanceof Error ? error.message : String(error)}`,
        error instanceof Error ? error.stack : undefined,
      );

      if (error instanceof HttpException) {
        throw error;
      }

      // Handle provider-specific errors
      const errorObj = error as { status?: number; message?: string };
      if (errorObj.status) {
        throw new HttpException(errorObj.message || 'Provider error', errorObj.status);
      }

      // Generic server error
      throw new HttpException(`${error instanceof Error ? error.message : String(error)}`, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }
}
