import { NestFactory } from '@nestjs/core';
import { ConfigService } from '@nestjs/config';
import { Logger, ValidationPipe } from '@nestjs/common';
import { AppModule } from './app.module';
import { ConfigValidationService } from '@app/config';

async function bootstrap() {
  const app = await NestFactory.create(AppModule);
  const configService = app.get(ConfigService);
  const configValidation = app.get(ConfigValidationService);
  const logger = new Logger('Bootstrap');

  // Validate configuration before starting
  const validationResult = configValidation.validateConfiguration();
  if (!validationResult.valid) {
    logger.error('Configuration validation failed. Exiting...');
    process.exit(1);
  }

  // Enable validation pipes
  app.useGlobalPipes(
    new ValidationPipe({
      transform: true,
      whitelist: true,
      forbidNonWhitelisted: true,
    }),
  );

  // Enable CORS for development
  app.enableCors({
    origin: true,
    credentials: true,
  });

  const port = configService.get<number>('port', 3000);
  const region = configService.get<string>('executor.region');
  const modelType = configService.get<string>('executor.modelType');

  await app.listen(port);

  logger.log(`🚀 Executor started on port ${port}`);
  logger.log(`📍 Region: ${region}, Model Type: ${modelType}`);
  logger.log(`🔗 Public URL: ${configService.get<string>('executor.publicUrl')}`);
}

bootstrap().catch((error) => {
  console.error('Failed to start executor:', error);
  process.exit(1);
});
