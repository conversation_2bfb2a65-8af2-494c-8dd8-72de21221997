# ===========================================
# SightAI Executor 开发环境配置
# 生成时间: 2025-07-23T10:47:05.199Z
# ===========================================

NODE_ENV=development
PORT=3002
PUBLIC_URL=http://localhost:3002

# Executor 配置 (动态生成)
EXECUTOR_ID=executor-dev-cpsy8v
EXECUTOR_REGION=local
EXECUTOR_MODEL_TYPE=openai
EXECUTOR_SECRET_KEY=executor-secret-key-sight

# etcd 配置
ETCD_ENDPOINTS=localhost:2379

# Gateway 配置
GATEWAY_URL=http://localhost:8718

# ===========================================
# Executor 密钥配置 (开发环境 - 仅本地)
# ===========================================

# 开发环境：使用本地私钥 (base64 编码)
EXECUTOR_PRIVATE_KEY=ZogSdKaL3OrxFFm+iOwbYd+1NSPmHVsYjsnr9djLFjI=

# GCP 配置 (开发环境不使用)
# 注释掉以避免 GCP 认证要求
# GCP_KMS_PROJECT_ID=celtic-descent-398803
# EXECUTOR_SECRET_ID=executor-keypair-001
# GOOGLE_APPLICATION_CREDENTIALS=./gcp-service-account.json

# 日志配置
LOG_LEVEL=debug
LOG_FORMAT=pretty

# 开发工具
ENABLE_SWAGGER=true
ENABLE_METRICS=true
