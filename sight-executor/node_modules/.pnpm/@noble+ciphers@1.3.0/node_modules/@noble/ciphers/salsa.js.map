{"version": 3, "file": "salsa.js", "sourceRoot": "", "sources": ["src/salsa.ts"], "names": [], "mappings": ";;;AAgFA,wBA8BC;AAqED,8BASC;AA5LD;;;;;;;;;;;;;;GAcG;AACH,uCAA+C;AAC/C,iDAA0C;AAC1C,yCASoB;AAEpB,6BAA6B;AAC7B,kBAAkB;AAClB,SAAS,SAAS,CAChB,CAAc,EAAE,CAAc,EAAE,CAAc,EAAE,GAAgB,EAAE,GAAW,EAAE,MAAM,GAAG,EAAE;IAE1F,yCAAyC;IACzC,IAAI,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,6BAA6B;IAC7E,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,+BAA+B;IAC/E,GAAG,GAAG,GAAG,EAAG,GAAG,GAAG,CAAC,EAAK,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,4BAA4B;IAC5E,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,gCAAgC;IACpF,oCAAoC;IACpC,IAAI,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAC1C,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAC1C,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAC1C,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;IAC/C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC;QACnC,GAAG,IAAI,IAAA,cAAI,EAAC,GAAG,GAAG,GAAG,GAAG,CAAC,EAAG,CAAC,CAAC,CAAC;QAAC,GAAG,IAAI,IAAA,cAAI,EAAC,GAAG,GAAG,GAAG,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;QAC9D,GAAG,IAAI,IAAA,cAAI,EAAC,GAAG,GAAG,GAAG,GAAG,CAAC,EAAE,EAAE,CAAC,CAAC;QAAC,GAAG,IAAI,IAAA,cAAI,EAAC,GAAG,GAAG,GAAG,GAAG,CAAC,EAAE,EAAE,CAAC,CAAC;QAC/D,GAAG,IAAI,IAAA,cAAI,EAAC,GAAG,GAAG,GAAG,GAAG,CAAC,EAAG,CAAC,CAAC,CAAC;QAAC,GAAG,IAAI,IAAA,cAAI,EAAC,GAAG,GAAG,GAAG,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;QAC9D,GAAG,IAAI,IAAA,cAAI,EAAC,GAAG,GAAG,GAAG,GAAG,CAAC,EAAE,EAAE,CAAC,CAAC;QAAC,GAAG,IAAI,IAAA,cAAI,EAAC,GAAG,GAAG,GAAG,GAAG,CAAC,EAAE,EAAE,CAAC,CAAC;QAC/D,GAAG,IAAI,IAAA,cAAI,EAAC,GAAG,GAAG,GAAG,GAAG,CAAC,EAAG,CAAC,CAAC,CAAC;QAAC,GAAG,IAAI,IAAA,cAAI,EAAC,GAAG,GAAG,GAAG,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;QAC9D,GAAG,IAAI,IAAA,cAAI,EAAC,GAAG,GAAG,GAAG,GAAG,CAAC,EAAE,EAAE,CAAC,CAAC;QAAC,GAAG,IAAI,IAAA,cAAI,EAAC,GAAG,GAAG,GAAG,GAAG,CAAC,EAAE,EAAE,CAAC,CAAC;QAC/D,GAAG,IAAI,IAAA,cAAI,EAAC,GAAG,GAAG,GAAG,GAAG,CAAC,EAAG,CAAC,CAAC,CAAC;QAAC,GAAG,IAAI,IAAA,cAAI,EAAC,GAAG,GAAG,GAAG,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;QAC9D,GAAG,IAAI,IAAA,cAAI,EAAC,GAAG,GAAG,GAAG,GAAG,CAAC,EAAE,EAAE,CAAC,CAAC;QAAC,GAAG,IAAI,IAAA,cAAI,EAAC,GAAG,GAAG,GAAG,GAAG,CAAC,EAAE,EAAE,CAAC,CAAC;QAC/D,GAAG,IAAI,IAAA,cAAI,EAAC,GAAG,GAAG,GAAG,GAAG,CAAC,EAAG,CAAC,CAAC,CAAC;QAAC,GAAG,IAAI,IAAA,cAAI,EAAC,GAAG,GAAG,GAAG,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;QAC9D,GAAG,IAAI,IAAA,cAAI,EAAC,GAAG,GAAG,GAAG,GAAG,CAAC,EAAE,EAAE,CAAC,CAAC;QAAC,GAAG,IAAI,IAAA,cAAI,EAAC,GAAG,GAAG,GAAG,GAAG,CAAC,EAAE,EAAE,CAAC,CAAC;QAC/D,GAAG,IAAI,IAAA,cAAI,EAAC,GAAG,GAAG,GAAG,GAAG,CAAC,EAAG,CAAC,CAAC,CAAC;QAAC,GAAG,IAAI,IAAA,cAAI,EAAC,GAAG,GAAG,GAAG,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;QAC9D,GAAG,IAAI,IAAA,cAAI,EAAC,GAAG,GAAG,GAAG,GAAG,CAAC,EAAE,EAAE,CAAC,CAAC;QAAC,GAAG,IAAI,IAAA,cAAI,EAAC,GAAG,GAAG,GAAG,GAAG,CAAC,EAAE,EAAE,CAAC,CAAC;QAC/D,GAAG,IAAI,IAAA,cAAI,EAAC,GAAG,GAAG,GAAG,GAAG,CAAC,EAAG,CAAC,CAAC,CAAC;QAAC,GAAG,IAAI,IAAA,cAAI,EAAC,GAAG,GAAG,GAAG,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;QAC9D,GAAG,IAAI,IAAA,cAAI,EAAC,GAAG,GAAG,GAAG,GAAG,CAAC,EAAE,EAAE,CAAC,CAAC;QAAC,GAAG,IAAI,IAAA,cAAI,EAAC,GAAG,GAAG,GAAG,GAAG,CAAC,EAAE,EAAE,CAAC,CAAC;QAC/D,GAAG,IAAI,IAAA,cAAI,EAAC,GAAG,GAAG,GAAG,GAAG,CAAC,EAAG,CAAC,CAAC,CAAC;QAAC,GAAG,IAAI,IAAA,cAAI,EAAC,GAAG,GAAG,GAAG,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;QAC9D,GAAG,IAAI,IAAA,cAAI,EAAC,GAAG,GAAG,GAAG,GAAG,CAAC,EAAE,EAAE,CAAC,CAAC;QAAC,GAAG,IAAI,IAAA,cAAI,EAAC,GAAG,GAAG,GAAG,GAAG,CAAC,EAAE,EAAE,CAAC,CAAC;IACjE,CAAC;IACD,eAAe;IACf,IAAI,EAAE,GAAG,CAAC,CAAC;IACX,GAAG,CAAC,EAAE,EAAE,CAAC,GAAG,CAAC,GAAG,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC;IAAC,GAAG,CAAC,EAAE,EAAE,CAAC,GAAG,CAAC,GAAG,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC;IACzD,GAAG,CAAC,EAAE,EAAE,CAAC,GAAG,CAAC,GAAG,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC;IAAC,GAAG,CAAC,EAAE,EAAE,CAAC,GAAG,CAAC,GAAG,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC;IACzD,GAAG,CAAC,EAAE,EAAE,CAAC,GAAG,CAAC,GAAG,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC;IAAC,GAAG,CAAC,EAAE,EAAE,CAAC,GAAG,CAAC,GAAG,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC;IACzD,GAAG,CAAC,EAAE,EAAE,CAAC,GAAG,CAAC,GAAG,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC;IAAC,GAAG,CAAC,EAAE,EAAE,CAAC,GAAG,CAAC,GAAG,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC;IACzD,GAAG,CAAC,EAAE,EAAE,CAAC,GAAG,CAAC,GAAG,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC;IAAC,GAAG,CAAC,EAAE,EAAE,CAAC,GAAG,CAAC,GAAG,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC;IACzD,GAAG,CAAC,EAAE,EAAE,CAAC,GAAG,CAAC,GAAG,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC;IAAC,GAAG,CAAC,EAAE,EAAE,CAAC,GAAG,CAAC,GAAG,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC;IACzD,GAAG,CAAC,EAAE,EAAE,CAAC,GAAG,CAAC,GAAG,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC;IAAC,GAAG,CAAC,EAAE,EAAE,CAAC,GAAG,CAAC,GAAG,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC;IACzD,GAAG,CAAC,EAAE,EAAE,CAAC,GAAG,CAAC,GAAG,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC;IAAC,GAAG,CAAC,EAAE,EAAE,CAAC,GAAG,CAAC,GAAG,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC;AAC3D,CAAC;AAED;;;;;GAKG;AACH,kBAAkB;AAClB,SAAgB,MAAM,CACpB,CAAc,EAAE,CAAc,EAAE,CAAc,EAAE,GAAgB;IAEhE,IAAI,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,EAC9C,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,EAC9C,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,EAC9C,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;IACnD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC;QAC/B,GAAG,IAAI,IAAA,cAAI,EAAC,GAAG,GAAG,GAAG,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;QAAC,GAAG,IAAI,IAAA,cAAI,EAAC,GAAG,GAAG,GAAG,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;QAC7D,GAAG,IAAI,IAAA,cAAI,EAAC,GAAG,GAAG,GAAG,GAAG,CAAC,EAAE,EAAE,CAAC,CAAC;QAAC,GAAG,IAAI,IAAA,cAAI,EAAC,GAAG,GAAG,GAAG,GAAG,CAAC,EAAE,EAAE,CAAC,CAAC;QAC/D,GAAG,IAAI,IAAA,cAAI,EAAC,GAAG,GAAG,GAAG,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;QAAC,GAAG,IAAI,IAAA,cAAI,EAAC,GAAG,GAAG,GAAG,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;QAC7D,GAAG,IAAI,IAAA,cAAI,EAAC,GAAG,GAAG,GAAG,GAAG,CAAC,EAAE,EAAE,CAAC,CAAC;QAAC,GAAG,IAAI,IAAA,cAAI,EAAC,GAAG,GAAG,GAAG,GAAG,CAAC,EAAE,EAAE,CAAC,CAAC;QAC/D,GAAG,IAAI,IAAA,cAAI,EAAC,GAAG,GAAG,GAAG,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;QAAC,GAAG,IAAI,IAAA,cAAI,EAAC,GAAG,GAAG,GAAG,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;QAC7D,GAAG,IAAI,IAAA,cAAI,EAAC,GAAG,GAAG,GAAG,GAAG,CAAC,EAAE,EAAE,CAAC,CAAC;QAAC,GAAG,IAAI,IAAA,cAAI,EAAC,GAAG,GAAG,GAAG,GAAG,CAAC,EAAE,EAAE,CAAC,CAAC;QAC/D,GAAG,IAAI,IAAA,cAAI,EAAC,GAAG,GAAG,GAAG,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;QAAC,GAAG,IAAI,IAAA,cAAI,EAAC,GAAG,GAAG,GAAG,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;QAC7D,GAAG,IAAI,IAAA,cAAI,EAAC,GAAG,GAAG,GAAG,GAAG,CAAC,EAAE,EAAE,CAAC,CAAC;QAAC,GAAG,IAAI,IAAA,cAAI,EAAC,GAAG,GAAG,GAAG,GAAG,CAAC,EAAE,EAAE,CAAC,CAAC;QAC/D,GAAG,IAAI,IAAA,cAAI,EAAC,GAAG,GAAG,GAAG,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;QAAC,GAAG,IAAI,IAAA,cAAI,EAAC,GAAG,GAAG,GAAG,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;QAC7D,GAAG,IAAI,IAAA,cAAI,EAAC,GAAG,GAAG,GAAG,GAAG,CAAC,EAAE,EAAE,CAAC,CAAC;QAAC,GAAG,IAAI,IAAA,cAAI,EAAC,GAAG,GAAG,GAAG,GAAG,CAAC,EAAE,EAAE,CAAC,CAAC;QAC/D,GAAG,IAAI,IAAA,cAAI,EAAC,GAAG,GAAG,GAAG,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;QAAC,GAAG,IAAI,IAAA,cAAI,EAAC,GAAG,GAAG,GAAG,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;QAC7D,GAAG,IAAI,IAAA,cAAI,EAAC,GAAG,GAAG,GAAG,GAAG,CAAC,EAAE,EAAE,CAAC,CAAC;QAAC,GAAG,IAAI,IAAA,cAAI,EAAC,GAAG,GAAG,GAAG,GAAG,CAAC,EAAE,EAAE,CAAC,CAAC;QAC/D,GAAG,IAAI,IAAA,cAAI,EAAC,GAAG,GAAG,GAAG,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;QAAC,GAAG,IAAI,IAAA,cAAI,EAAC,GAAG,GAAG,GAAG,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;QAC7D,GAAG,IAAI,IAAA,cAAI,EAAC,GAAG,GAAG,GAAG,GAAG,CAAC,EAAE,EAAE,CAAC,CAAC;QAAC,GAAG,IAAI,IAAA,cAAI,EAAC,GAAG,GAAG,GAAG,GAAG,CAAC,EAAE,EAAE,CAAC,CAAC;QAC/D,GAAG,IAAI,IAAA,cAAI,EAAC,GAAG,GAAG,GAAG,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;QAAC,GAAG,IAAI,IAAA,cAAI,EAAC,GAAG,GAAG,GAAG,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;QAC7D,GAAG,IAAI,IAAA,cAAI,EAAC,GAAG,GAAG,GAAG,GAAG,CAAC,EAAE,EAAE,CAAC,CAAC;QAAC,GAAG,IAAI,IAAA,cAAI,EAAC,GAAG,GAAG,GAAG,GAAG,CAAC,EAAE,EAAE,CAAC,CAAC;IACjE,CAAC;IACD,IAAI,EAAE,GAAG,CAAC,CAAC;IACX,GAAG,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC;IAAC,GAAG,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC;IACjC,GAAG,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC;IAAC,GAAG,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC;IACjC,GAAG,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC;IAAC,GAAG,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC;IACjC,GAAG,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC;IAAC,GAAG,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC;AACnC,CAAC;AAED;;;;GAIG;AACU,QAAA,OAAO,GAA8B,IAAA,sBAAY,EAAC,SAAS,EAAE;IACxE,cAAc,EAAE,IAAI;IACpB,YAAY,EAAE,IAAI;CACnB,CAAC,CAAC;AAEH;;;GAGG;AACU,QAAA,QAAQ,GAA8B,IAAA,sBAAY,EAAC,SAAS,EAAE;IACzE,YAAY,EAAE,IAAI;IAClB,aAAa,EAAE,MAAM;CACtB,CAAC,CAAC;AAEH;;;;GAIG;AACU,QAAA,gBAAgB,GAA8B,IAAA,qBAAU,EACnE,EAAE,SAAS,EAAE,EAAE,EAAE,WAAW,EAAE,EAAE,EAAE,SAAS,EAAE,EAAE,EAAE,EACjD,CAAC,GAAe,EAAE,KAAiB,EAAoB,EAAE;IACvD,OAAO;QACL,OAAO,CAAC,SAAqB,EAAE,MAAmB;YAChD,yFAAyF;YACzF,gFAAgF;YAChF,mDAAmD;YACnD,MAAM,GAAG,IAAA,oBAAS,EAAC,SAAS,CAAC,MAAM,GAAG,EAAE,EAAE,MAAM,EAAE,KAAK,CAAC,CAAC,CAAC,sCAAsC;YAChG,MAAM,OAAO,GAAG,MAAM,CAAC,QAAQ,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,oCAAoC;YAC5E,MAAM,aAAa,GAAG,MAAM,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC,4CAA4C;YACvF,MAAM,CAAC,GAAG,CAAC,SAAS,EAAE,EAAE,CAAC,CAAC;YAC1B,IAAA,gBAAK,EAAC,OAAO,CAAC,CAAC,CAAC,2CAA2C;YAC3D,IAAA,gBAAQ,EAAC,GAAG,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC,CAAC,yDAAyD;YAC/F,MAAM,GAAG,GAAG,IAAA,uBAAQ,EAAC,aAAa,EAAE,OAAO,CAAC,CAAC,CAAC,gCAAgC;YAC9E,MAAM,CAAC,GAAG,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC,CAAC,uBAAuB;YAC5C,IAAA,gBAAK,EAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,0CAA0C;YAC9E,OAAO,MAAM,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC,sBAAsB;QACpD,CAAC;QACD,OAAO,CAAC,UAAsB,EAAE,MAAmB;YACjD,wCAAwC;YACxC,oCAAoC;YACpC,IAAA,iBAAM,EAAC,UAAU,CAAC,CAAC;YACnB,MAAM,GAAG,IAAA,oBAAS,EAAC,UAAU,CAAC,MAAM,GAAG,EAAE,EAAE,MAAM,EAAE,KAAK,CAAC,CAAC;YAC1D,MAAM,GAAG,GAAG,MAAM,CAAC,QAAQ,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,wCAAwC;YAC5E,MAAM,SAAS,GAAG,MAAM,CAAC,QAAQ,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,8BAA8B;YACzE,MAAM,aAAa,GAAG,MAAM,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC,4CAA4C;YACvF,MAAM,CAAC,GAAG,CAAC,UAAU,EAAE,EAAE,CAAC,CAAC,CAAC,8BAA8B;YAC1D,IAAA,gBAAK,EAAC,GAAG,CAAC,CAAC,CAAC,2CAA2C;YACvD,MAAM,OAAO,GAAG,IAAA,gBAAQ,EAAC,GAAG,EAAE,KAAK,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC,+BAA+B;YAC/E,MAAM,GAAG,GAAG,IAAA,uBAAQ,EAAC,aAAa,EAAE,OAAO,CAAC,CAAC,CAAC,gCAAgC;YAC9E,IAAI,CAAC,IAAA,qBAAU,EAAC,SAAS,EAAE,GAAG,CAAC;gBAAE,MAAM,IAAI,KAAK,CAAC,aAAa,CAAC,CAAC;YAChE,IAAA,gBAAQ,EAAC,GAAG,EAAE,KAAK,EAAE,MAAM,CAAC,QAAQ,CAAC,EAAE,CAAC,EAAE,MAAM,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,iCAAiC;YACjG,IAAA,gBAAK,EAAC,GAAG,EAAE,SAAS,EAAE,GAAG,CAAC,CAAC;YAC3B,OAAO,aAAa,CAAC,CAAC,uDAAuD;QAC/E,CAAC;KACF,CAAC;AACJ,CAAC,CACF,CAAC;AAEF;;GAEG;AACH,SAAgB,SAAS,CACvB,GAAe,EACf,KAAiB;IAKjB,MAAM,EAAE,GAAG,IAAA,wBAAgB,EAAC,GAAG,EAAE,KAAK,CAAC,CAAC;IACxC,OAAO,EAAE,IAAI,EAAE,EAAE,CAAC,OAAO,EAAE,IAAI,EAAE,EAAE,CAAC,OAAO,EAAE,CAAC;AAChD,CAAC"}