import { type Cipher } from './utils.ts';
/** FPE-FF1 format-preserving encryption */
export declare function FF1(radix: number, key: Uint8Array, tweak?: Uint8Array): {
    encrypt(x: number[]): number[];
    decrypt(x: number[]): number[];
};
/** Binary version of FPE-FF1 format-preserving encryption. */
export declare function BinaryFF1(key: Uint8Array, tweak?: Uint8Array): Cipher;
//# sourceMappingURL=ff1.d.ts.map