#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/home/<USER>/work/sight/sight-miner-gateway/sight-executor/node_modules/.pnpm/ts-node@10.9.2_@swc+core@1.13.1_@types+node@22.16.5_typescript@5.8.3/node_modules/ts-node/dist/node_modules:/home/<USER>/work/sight/sight-miner-gateway/sight-executor/node_modules/.pnpm/ts-node@10.9.2_@swc+core@1.13.1_@types+node@22.16.5_typescript@5.8.3/node_modules/ts-node/node_modules:/home/<USER>/work/sight/sight-miner-gateway/sight-executor/node_modules/.pnpm/ts-node@10.9.2_@swc+core@1.13.1_@types+node@22.16.5_typescript@5.8.3/node_modules:/home/<USER>/work/sight/sight-miner-gateway/sight-executor/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/home/<USER>/work/sight/sight-miner-gateway/sight-executor/node_modules/.pnpm/ts-node@10.9.2_@swc+core@1.13.1_@types+node@22.16.5_typescript@5.8.3/node_modules/ts-node/dist/node_modules:/home/<USER>/work/sight/sight-miner-gateway/sight-executor/node_modules/.pnpm/ts-node@10.9.2_@swc+core@1.13.1_@types+node@22.16.5_typescript@5.8.3/node_modules/ts-node/node_modules:/home/<USER>/work/sight/sight-miner-gateway/sight-executor/node_modules/.pnpm/ts-node@10.9.2_@swc+core@1.13.1_@types+node@22.16.5_typescript@5.8.3/node_modules:/home/<USER>/work/sight/sight-miner-gateway/sight-executor/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../ts-node/dist/bin.js" "$@"
else
  exec node  "$basedir/../ts-node/dist/bin.js" "$@"
fi
