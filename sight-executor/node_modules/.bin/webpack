#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/home/<USER>/work/sight/sight-miner-gateway/sight-executor/node_modules/.pnpm/webpack@5.99.6_@swc+core@1.13.1/node_modules/webpack/bin/node_modules:/home/<USER>/work/sight/sight-miner-gateway/sight-executor/node_modules/.pnpm/webpack@5.99.6_@swc+core@1.13.1/node_modules/webpack/node_modules:/home/<USER>/work/sight/sight-miner-gateway/sight-executor/node_modules/.pnpm/webpack@5.99.6_@swc+core@1.13.1/node_modules:/home/<USER>/work/sight/sight-miner-gateway/sight-executor/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/home/<USER>/work/sight/sight-miner-gateway/sight-executor/node_modules/.pnpm/webpack@5.99.6_@swc+core@1.13.1/node_modules/webpack/bin/node_modules:/home/<USER>/work/sight/sight-miner-gateway/sight-executor/node_modules/.pnpm/webpack@5.99.6_@swc+core@1.13.1/node_modules/webpack/node_modules:/home/<USER>/work/sight/sight-miner-gateway/sight-executor/node_modules/.pnpm/webpack@5.99.6_@swc+core@1.13.1/node_modules:/home/<USER>/work/sight/sight-miner-gateway/sight-executor/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../.pnpm/webpack@5.99.6_@swc+core@1.13.1/node_modules/webpack/bin/webpack.js" "$@"
else
  exec node  "$basedir/../.pnpm/webpack@5.99.6_@swc+core@1.13.1/node_modules/webpack/bin/webpack.js" "$@"
fi
