#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/home/<USER>/work/sight/sight-miner-gateway/sight-executor/node_modules/.pnpm/@swc+cli@0.6.0_@swc+core@1.13.1_chokidar@4.0.3/node_modules/@swc/cli/bin/node_modules:/home/<USER>/work/sight/sight-miner-gateway/sight-executor/node_modules/.pnpm/@swc+cli@0.6.0_@swc+core@1.13.1_chokidar@4.0.3/node_modules/@swc/cli/node_modules:/home/<USER>/work/sight/sight-miner-gateway/sight-executor/node_modules/.pnpm/@swc+cli@0.6.0_@swc+core@1.13.1_chokidar@4.0.3/node_modules/@swc/node_modules:/home/<USER>/work/sight/sight-miner-gateway/sight-executor/node_modules/.pnpm/@swc+cli@0.6.0_@swc+core@1.13.1_chokidar@4.0.3/node_modules:/home/<USER>/work/sight/sight-miner-gateway/sight-executor/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/home/<USER>/work/sight/sight-miner-gateway/sight-executor/node_modules/.pnpm/@swc+cli@0.6.0_@swc+core@1.13.1_chokidar@4.0.3/node_modules/@swc/cli/bin/node_modules:/home/<USER>/work/sight/sight-miner-gateway/sight-executor/node_modules/.pnpm/@swc+cli@0.6.0_@swc+core@1.13.1_chokidar@4.0.3/node_modules/@swc/cli/node_modules:/home/<USER>/work/sight/sight-miner-gateway/sight-executor/node_modules/.pnpm/@swc+cli@0.6.0_@swc+core@1.13.1_chokidar@4.0.3/node_modules/@swc/node_modules:/home/<USER>/work/sight/sight-miner-gateway/sight-executor/node_modules/.pnpm/@swc+cli@0.6.0_@swc+core@1.13.1_chokidar@4.0.3/node_modules:/home/<USER>/work/sight/sight-miner-gateway/sight-executor/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../@swc/cli/bin/swc.js" "$@"
else
  exec node  "$basedir/../@swc/cli/bin/swc.js" "$@"
fi
