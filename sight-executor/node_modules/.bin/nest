#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/home/<USER>/work/sight/sight-miner-gateway/sight-executor/node_modules/.pnpm/@nestjs+cli@11.0.7_@swc+cli@0.6.0_@swc+core@1.13.1_chokidar@4.0.3__@swc+core@1.13.1_@types+node@22.16.5/node_modules/@nestjs/cli/bin/node_modules:/home/<USER>/work/sight/sight-miner-gateway/sight-executor/node_modules/.pnpm/@nestjs+cli@11.0.7_@swc+cli@0.6.0_@swc+core@1.13.1_chokidar@4.0.3__@swc+core@1.13.1_@types+node@22.16.5/node_modules/@nestjs/cli/node_modules:/home/<USER>/work/sight/sight-miner-gateway/sight-executor/node_modules/.pnpm/@nestjs+cli@11.0.7_@swc+cli@0.6.0_@swc+core@1.13.1_chokidar@4.0.3__@swc+core@1.13.1_@types+node@22.16.5/node_modules/@nestjs/node_modules:/home/<USER>/work/sight/sight-miner-gateway/sight-executor/node_modules/.pnpm/@nestjs+cli@11.0.7_@swc+cli@0.6.0_@swc+core@1.13.1_chokidar@4.0.3__@swc+core@1.13.1_@types+node@22.16.5/node_modules:/home/<USER>/work/sight/sight-miner-gateway/sight-executor/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/home/<USER>/work/sight/sight-miner-gateway/sight-executor/node_modules/.pnpm/@nestjs+cli@11.0.7_@swc+cli@0.6.0_@swc+core@1.13.1_chokidar@4.0.3__@swc+core@1.13.1_@types+node@22.16.5/node_modules/@nestjs/cli/bin/node_modules:/home/<USER>/work/sight/sight-miner-gateway/sight-executor/node_modules/.pnpm/@nestjs+cli@11.0.7_@swc+cli@0.6.0_@swc+core@1.13.1_chokidar@4.0.3__@swc+core@1.13.1_@types+node@22.16.5/node_modules/@nestjs/cli/node_modules:/home/<USER>/work/sight/sight-miner-gateway/sight-executor/node_modules/.pnpm/@nestjs+cli@11.0.7_@swc+cli@0.6.0_@swc+core@1.13.1_chokidar@4.0.3__@swc+core@1.13.1_@types+node@22.16.5/node_modules/@nestjs/node_modules:/home/<USER>/work/sight/sight-miner-gateway/sight-executor/node_modules/.pnpm/@nestjs+cli@11.0.7_@swc+cli@0.6.0_@swc+core@1.13.1_chokidar@4.0.3__@swc+core@1.13.1_@types+node@22.16.5/node_modules:/home/<USER>/work/sight/sight-miner-gateway/sight-executor/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../@nestjs/cli/bin/nest.js" "$@"
else
  exec node  "$basedir/../@nestjs/cli/bin/nest.js" "$@"
fi
