{"compilerOptions": {"module": "commonjs", "declaration": true, "removeComments": true, "emitDecoratorMetadata": true, "experimentalDecorators": true, "allowSyntheticDefaultImports": true, "target": "ES2023", "sourceMap": true, "outDir": "./dist", "baseUrl": "./", "incremental": true, "skipLibCheck": true, "strictNullChecks": true, "forceConsistentCasingInFileNames": true, "noImplicitAny": false, "strictBindCallApply": false, "noFallthroughCasesInSwitch": false, "paths": {"@app/etcd": ["libs/etcd/src"], "@app/etcd/*": ["libs/etcd/src/*"], "@app/secret-manager": ["libs/secret-manager/src"], "@app/secret-manager/*": ["libs/secret-manager/src/*"], "@app/key-manager": ["libs/key-manager/src"], "@app/key-manager/*": ["libs/key-manager/src/*"], "@app/providers": ["libs/providers/src"], "@app/providers/*": ["libs/providers/src/*"], "@app/executor-registry": ["libs/executor-registry/src"], "@app/executor-registry/*": ["libs/executor-registry/src/*"], "@app/config": ["libs/config/src"], "@app/config/*": ["libs/config/src/*"], "@app/types": ["libs/types/src"], "@app/types/*": ["libs/types/src/*"]}}}